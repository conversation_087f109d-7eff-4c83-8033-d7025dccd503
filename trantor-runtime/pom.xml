<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-parent</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-runtime</artifactId>
    <packaging>pom</packaging>
    <description>新trantor统一对外暴露模块，业务模块只需要依赖该模块</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <modules>
        <module>trantor-runtime-sdk</module>
        <module>trantor-runtime-api</module>
        <module>trantor-runtime-impl</module>
        <module>trantor-runtime-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-runtime-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
