package io.terminus.trantor2.runtime.sdk.meta;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import org.springframework.lang.Nullable;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface MetaClient {
    /**
     * 根据模型标识获取模型
     *
     * @param key  模型标识
     * @param team 项目标识或 ID(兼容用，不推荐使用 ID), 可为空, 默认为 {@link TrantorContext#getTeamCode()}
     * @return 模型 {@link DataStructNode} nullable
     */
    @Nullable
    <T extends Serializable> DataStructNode getModel(@Nonnull String key, @Nullable T team);

    /**
     * 根据模型标识批量获取数据结构
     *
     * @param request <p>
     *                {@link QueryByKeysRequest#getTeam()} 项目标识或 ID(兼容用，不推荐使用 ID)
     *                可为空，默认为 {@link TrantorContext#getTeamCode()}
     * @return 数据结构对象集合
     * <p>
     * Note: 不校验模型是否一一存在
     * </p>
     */
    @Nonnull
    <T extends Serializable> Collection<DataStructNode> getModels(@Nonnull QueryByKeysRequest<T> request);

}
