package io.terminus.trantor2.runtime.sdk.meta;

import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.runtime.sdk.meta.ModelGetter;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MetaClientImpl implements MetaClient {
    private final ModelGetter modelGetter;

    @Override
    @Nullable
    public <T extends Serializable> DataStructNode getModel(@Nonnull String key, @Nullable T team) {
        return modelGetter.getModel(key, team);
    }

    @Override
    @Nonnull
    public <T extends Serializable> Collection<DataStructNode> getModels(@NotNull QueryByKeysRequest<T> request) {
        return modelGetter.getModels(request);
    }
}
