<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-code-engine</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-code-impl-common</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <framework.version>1.0.0-SNAPSHOT</framework.version>
        <mybatis.version>1.0.0-SNAPSHOT</mybatis.version>
        <guava.version>31.0-jre</guava.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-impl-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-service-api-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-code-api-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-impl-common</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

</project>
