package io.terminus.trantor2.code.api;

import io.terminus.common.api.model.Paging;
import io.terminus.sequence.facade.CodeRuleManageFacade;
import io.terminus.sequence.model.CodeRuleDeleteRequest;
import io.terminus.sequence.model.CodeRuleDetailRequest;
import io.terminus.sequence.model.CodeRulePagingRequest;
import io.terminus.sequence.model.CodeRuleSaveRequest;
import io.terminus.sequence.model.CodeRuleVO;
import io.terminus.trantor2.code.exception.CodeRuleException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 诸立达
 * @Date: 2023/2/20 3:34 下午
 */
@RestController
@RequiredArgsConstructor
public class CodeRuleApiImpl implements CodeRuleApi {
    private final CodeRuleManageFacade codeRuleManageFacade;

    @Override
    public Response<Long> create(CodeRuleSaveRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        request.setOriginOrgId(0L);
        request.setId(null);
        return toRes(codeRuleManageFacade.save(request));
    }

    @Override
    public Response<Long> update(CodeRuleSaveRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        request.setOriginOrgId(0L);
        return toRes(codeRuleManageFacade.save(request));
    }

    @Override
    public Response<Boolean> delete(CodeRuleDeleteRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        request.setOriginOrgId(0L);
        return toRes(codeRuleManageFacade.delete(request));
    }

    @Override
    public Response<Paging<CodeRuleVO>> page(CodeRulePagingRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        request.setOriginOrgId(0L);
        request.setAdmin(Boolean.TRUE);
        return toRes(codeRuleManageFacade.paging(request));
    }

    @Override
    public Response<CodeRuleVO> detail(CodeRuleDetailRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        request.setOriginOrgId(0L);
        return toRes(codeRuleManageFacade.findDetail(request));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private <T> Response<T> toRes(io.terminus.common.api.response.Response<T> res) {
        Response response = new Response();
        response.setSuccess(res.getSuccess());
        response.setData(res.getData());
        if (StringUtils.hasText(res.getErrorCode())) {
            Response.Error error = new Response.Error();
            error.setCode(res.getErrorCode());
            error.setMsg(res.getErrorMsg());
            response.setErr(error);
        } else if (null != res.getErr()) {
            Response.Error error = new Response.Error();
            error.setCode(res.getErr().getCode());
            error.setMsg(res.getErr().getMsg());
            response.setErr(error);
        }
        if (!response.isSuccess()) {
            throw new CodeRuleException(ErrorType.BAD_REQUEST, response.getErrMsg());
        }
        return response;
    }

}
