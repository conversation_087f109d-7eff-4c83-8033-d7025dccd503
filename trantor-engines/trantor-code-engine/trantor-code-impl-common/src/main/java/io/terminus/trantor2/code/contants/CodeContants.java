package io.terminus.trantor2.code.contants;

/**
 * @Author: 诸立达
 * @Date: 2023/5/9 1:56 下午
 */
public interface CodeContants {

    String MODULE_KEY = "ModuleKey";

    String TAKE_CODE_TYPE = "TakeCode";

    String REFRESH_DATE = "refreshDate";

    String CIRCLE_TYPE = "circleType";

    String TAKE_CODE_ROOT = "TakeCodeRoot";

    String MODULE_KEY_FIELD = "moduleKey";

    String DEFAULT_KEY = "defaultKey";

    String DEFAULT_RULE = "defaultRule";

    Long DEFAULT_TEAMID = 54L;

}
