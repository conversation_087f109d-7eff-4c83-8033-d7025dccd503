package io.terminus.trantor2.code.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * @Author: 诸立达
 * @Date: 2023/8/12 9:52 上午
 */
public class CodeRuleException extends TrantorBizException {

    public CodeRuleException(ErrorType errorType) {
        super(errorType);
    }

    public CodeRuleException(ErrorType errorType, String message) {
        super(errorType, message);
    }

    public CodeRuleException(ErrorType errorType, String message, Throwable cause) {
        super(errorType, message, cause);
    }

    protected CodeRuleException(ErrorType errorType, Throwable cause) {
        super(errorType, cause);
    }
}
