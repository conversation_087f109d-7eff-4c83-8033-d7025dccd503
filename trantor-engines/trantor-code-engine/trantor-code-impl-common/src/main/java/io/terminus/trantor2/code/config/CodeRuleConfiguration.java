package io.terminus.trantor2.code.config;

import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: 诸立达
 * @Date: 2023/3/6 3:05 下午
 */
@Configuration
@EnableDiscoveryClient
@ComponentScan(value = "io.terminus.trantor2.code")
public class CodeRuleConfiguration {


}
