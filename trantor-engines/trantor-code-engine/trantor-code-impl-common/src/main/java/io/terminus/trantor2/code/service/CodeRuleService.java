package io.terminus.trantor2.code.service;

import com.google.common.collect.Lists;
import io.terminus.trantor2.code.contants.CodeContants;
import io.terminus.trantor2.code.exception.CodeRuleException;
import io.terminus.trantor2.code.request.ClearSerialRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.api.dto.*;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * @Author: 诸立达
 * @Date: 2023/2/20 3:45 下午
 */
@Deprecated
@Service
@RequiredArgsConstructor
public class CodeRuleService {
    private final MetaEditService metaEditService;
    private final MetaQueryService metaQueryService;
    private final RedisTemplate redisTemplate;

    /**
     * @param editOpRequest
     */
    public void create(EditOpRequest editOpRequest) {
        //校验key唯一性
        validateKeyUnique(editOpRequest);

        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        editOpRequest.getChangedTreeNodes().get(0).getProps().putPOJO(CodeContants.REFRESH_DATE, new Date());
        metaEditService.submitOp(ctx, editOpRequest);

    }

    private void validateKeyUnique(EditOpRequest editOpRequest) {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        MetaTreeNodeExt metaTreeNodeExt = metaQueryService.queryInApp(ctx)
            .findOne(Field.key().equal(editOpRequest.getCurrentKey())).orElse(null);
        if (Objects.nonNull(metaTreeNodeExt)) {
            throw new CodeRuleException(ErrorType.RULE_KEY_MUST_UNIQUE, ErrorType.RULE_KEY_MUST_UNIQUE.getMessage());
        }
    }

    public void update(EditOpRequest editOpRequest) {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        metaEditService.submitOp(ctx, editOpRequest);
    }

    public void delete(EditOpRequest editOpRequest) {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        metaEditService.submitOp(ctx, editOpRequest);
    }


    private void refreshLastDate(MetaTreeNodeExt matchedRule, String moduleKey) {
        MetaEditAndQueryContext metaEditAndQueryContext = new MetaEditAndQueryContext(matchedRule.getTeamId(), matchedRule.getCreatedBy());

        EditOpRequest req = new EditOpRequest();
        req.setOpType(EditOpType.UpdateNode);
        req.setCurrentKey(matchedRule.getKey());
        MoveTarget moveTarget = new MoveTarget();
        moveTarget.setTargetKey(matchedRule.getParentKey());
        moveTarget.setTargetType(MoveTargetType.ChildFirst);
        req.setMoveTarget(moveTarget);

        MetaTreeNode currentNode = new MetaTreeNode();
        currentNode.setKey(matchedRule.getKey());
        currentNode.setName(matchedRule.getName());
        currentNode.setType(CodeContants.TAKE_CODE_TYPE);
        currentNode.setProps(matchedRule.getProps());
        currentNode.getProps().putPOJO(CodeContants.REFRESH_DATE, new Date());
        currentNode.setParentKey(matchedRule.getParentKey());
        req.setChangedTreeNodes(Lists.newArrayList(currentNode));
        req.setChangeOperation(MetaChangeOperation.StandardOperation);

        if (CodeContants.DEFAULT_KEY.equals(matchedRule.getKey())) {
            return;
        }
        if (Objects.isNull(TrantorContext.getCurrentUser())) {
            User current = new User();
            current.setId(matchedRule.getCreatedBy());
            TrantorContext.setCurrentUser(current);
        }
        metaEditService.submitOp(metaEditAndQueryContext, req);
    }

    public void clearSerial(ClearSerialRequest request) {
        String keyPattern = String.format("*serialKey:*:%s", request.getRuleKey());
        final byte[] key = keyPattern.getBytes(StandardCharsets.UTF_8);
        Set<byte[]> keys = (Set<byte[]>) redisTemplate.execute((RedisCallback<Set<byte[]>>) conn -> conn.keys(key));
        if (!CollectionUtils.isEmpty(keys)) {
            redisTemplate.execute((RedisCallback<Long>) connection -> connection.del(keys.toArray(new byte[0][])));
        }
    }
}
