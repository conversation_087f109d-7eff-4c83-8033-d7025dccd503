package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.nexus.dto.Asset;
import io.terminus.trantor2.nexus.dto.NexusComponent;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.test.tool.nexus.NexusSpringTest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@ExtendWith(SpringExtension.class)
class AbstractNexusApiClientTest implements NexusSpringTest {
    private static final String version = "1.0.0";
    private static final String extension = "extension";
    private static final String group = "group";

    @Autowired
    private NexusConfigProperties nexusConfigProperties;
    @Autowired
    private NexusApiClientFactoryImpl nexusApiClientFactory;

    @Order(1)
    @Test
    void uploadMaven_pub() {
        NexusApiClient client = nexusApiClientFactory.getClient(true);

        // search before upload
        List<NexusComponent> search = client.search(group, version, null, null);
        assertTrue(CollectionUtils.isEmpty(search));

        // upload
        client.uploadMaven(group, "uploadMaven_pub", version, generateResource(TestResource.of("key", "name")), extension, null);
        assertDoesNotThrow(() -> client.uploadMaven(group, "uploadMaven_pub", version, generateResource(TestResource.of("name", "key")), extension, null));

    }

    @Test
    @Order(2)
    void searchComponentAndAssetAndDownload() throws IOException, InterruptedException {
        Thread.sleep(1000L);
        NexusApiClient client = nexusApiClientFactory.getClient(true);

        // search
        List<NexusComponent> search = client.search(group, version, null, null);
        assertFalse(CollectionUtils.isEmpty(search));
        assertEquals(1, search.size());
        // search not exist
        List<NexusComponent> searchByName = client.search(group, version, "hhh", null);
        assertTrue(CollectionUtils.isEmpty(searchByName));

        // search asset
        Optional<Asset> assetOpt = search.get(0).getAssets().stream().filter(it -> it.getMaven2().getArtifactId().equals("uploadMaven_pub")
                && it.getMaven2().getExtension().equals(extension)).findFirst();
        assertTrue(assetOpt.isPresent());

        // download
        TestResource resource = JsonUtil.NON_INDENT.getObjectMapper().readValue(client.download(assetOpt.get().getDownloadUrl()), TestResource.class);
        assertEquals("key", resource.getKey());
        assertEquals("name", resource.getName());
    }


    @Test
    @Order(4)
    void deleteAssets() throws InterruptedException {
        NexusApiClient client = nexusApiClientFactory.getClient(true);
        List<String> collect = client.searchAssets(group, version, null, null).stream().map(Asset::getId).collect(Collectors.toList());
        client.deleteAssets(collect);

        Thread.sleep(1000L);
        List<Asset> assets = client.searchAssets(group, version, null, null);
        assertTrue(CollectionUtils.isEmpty(assets));
    }

    @Test
    @Order(5)
    void delete() throws InterruptedException {
        NexusApiClient client = nexusApiClientFactory.getClient(true);
        List<NexusComponent> search = client.search(group, version, null, null);
        assertFalse(CollectionUtils.isEmpty(search));
        assertEquals(1, search.size());
        client.delete(search.get(0).getId());

        Thread.sleep(1000L);
        search = client.search(group, version, null, null);
        assertTrue(CollectionUtils.isEmpty(search));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TestResource implements Serializable {
        private static final long serialVersionUID = 1193930114411033706L;
        private String name;
        private String key;

        public static TestResource of(String name, String key) {
            return new TestResource(name, key);
        }
    }

    private Resource generateResource(TestResource o) {
        try {
            String json = JsonUtil.NON_INDENT.getObjectMapper().writeValueAsString(o);
            return new ByteArrayResource(json.getBytes());
        } catch (Exception e) {
            throw new TrantorRuntimeException("failure while convert to resource");
        }
    }

}