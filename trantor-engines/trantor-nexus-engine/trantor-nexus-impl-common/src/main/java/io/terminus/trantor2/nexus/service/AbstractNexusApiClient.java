package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.nexus.constant.NexusConst;
import io.terminus.trantor2.nexus.dto.*;
import io.terminus.trantor2.properties.management.nexus.NexusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static io.terminus.trantor2.nexus.constant.NexusConst.DOT_CLASSIFIER;
import static io.terminus.trantor2.nexus.constant.NexusConst.DOT_EXTENSION;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractNexusApiClient implements NexusApiClient {
    protected final NexusProperties nexusProperties;
    protected final RestTemplate restTemplate;
    protected final String baseUrl;

    protected AbstractNexusApiClient(NexusProperties nexusProperties, RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        this.nexusProperties = nexusProperties;
        baseUrl = nexusProperties.getHost() + "/service/rest/v1/";
    }

    @Override
    public byte[] download(String downloadUrl) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        restTemplate.execute(downloadUrl, HttpMethod.GET, null, clientHttpResponse -> {
            try (InputStream inputStream = clientHttpResponse.getBody()) {
                IOUtils.copy(inputStream, outputStream);
            }
            return null;
        });
        return outputStream.toByteArray();
    }

    @Override
    public void delete(String componentId) {
        try {
            UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .pathSegment("components", componentId);
            restTemplate.delete(ub.build().toUriString());
        } catch (Exception e) {
            log.error("failed to delete component. componentId = {}", componentId, e);
            throw new TrantorRuntimeException("failed to delete component");
        }
    }

    @Override
    public void deleteAssets(List<String> assetIds) {
        try {
            if (assetIds.isEmpty()) {
                return;
            }
            assetIds.forEach(assetId -> {
                UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl)
                        .pathSegment("assets", assetId);
                restTemplate.delete(ub.build().toUriString());
            });
        } catch (Exception e) {
            log.error("failed to delete component. assets = {}", assetIds, e);
            throw new TrantorRuntimeException("failed to delete component");
        }
    }

    @Override
    public void uploadMaven(@NotNull Collection<Maven> mavens) {
        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .path("components")
                .queryParam("repository", nexusProperties.getRepository());

        if (CollectionUtils.isEmpty(mavens)) {
            return;
        }
        List<Maven> mavenList = new ArrayList<>(mavens);
        Maven asset1 = mavenList.get(0);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add(NexusConst.MAVEN2_GROUP_ID, asset1.getGroupId());
        body.add(NexusConst.MAVEN2_ARTIFACT_ID, asset1.getArtifactId());
        body.add(NexusConst.MAVEN2_VERSION, asset1.getVersion());
        for (int i = 0; i < mavenList.size(); i++) {
            Maven asset = mavenList.get(i);
            String assetPrefix = NexusConst.MAVEN2_ASSET + (i + 1);
            body.add(assetPrefix, asset.getResource());
            body.add(assetPrefix + DOT_EXTENSION, asset.getExtension());
            if (asset.getClassifier() != null) {
                body.add(assetPrefix + DOT_CLASSIFIER, asset.getClassifier());
            }
        }

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> response = restTemplate.exchange(ub.encode().build().toUriString(), HttpMethod.POST, requestEntity, String.class);
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Failed to upload raw file. status code = {}, body = {}", response.getStatusCodeValue(), response.getBody());
            throw new TrantorRuntimeException("failed to upload maven file");
        }
    }


    @Override
    public List<NexusComponent> search(@Nullable String group, @Nullable String version, @Nullable String name, @Nullable Sort sort) {
        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .path("search")
                .queryParam(NexusConst.REPOSITORY, nexusProperties.getRepository());

        if (group != null) {
            ub.queryParam(NexusConst.GROUP, group);
        }
        if (version != null) {
            ub.queryParam(NexusConst.VERSION, version);
        }
        if (name != null) {
            ub.queryParam(NexusConst.NAME, name);
        }
        if (sort != null) {
            ub.queryParam(NexusConst.SORT, sort.getSortBy());
            ub.queryParam(NexusConst.DIRECTION, sort.getDirection());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        return fetchWithContinuationToken(ub, headers, NexusComponents.class);
    }

    @Override
    public List<Asset> searchAssets(@NotNull String group, @Nullable String version, @Nullable String artifact, @Nullable String extension) {
        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .pathSegment("search", "assets")
                .queryParam(NexusConst.REPOSITORY, nexusProperties.getRepository())
                .queryParam(NexusConst.GROUP, group);

        if (version != null) {
            ub.queryParam(NexusConst.VERSION, version);
        }
        if (extension != null) {
            ub.queryParam(NexusConst.MAVEN_EXTENSION, extension);
        }
        if (artifact != null) {
            ub.queryParam(NexusConst.MAVEN_ARTIFACT_ID, artifact);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        return fetchWithContinuationToken(ub, headers, Assets.class);
    }

    private <T, R extends Continuable<T>> List<T> fetchWithContinuationToken(UriComponentsBuilder ub, HttpHeaders headers, Class<R> responseType) {
        List<T> items = new ArrayList<>();

        do {
            String url = ub.build().toUriString();
            ResponseEntity<R> response = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), responseType);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("failed to retrieve Nexus components. status code = {}, body = {}",
                        response.getStatusCodeValue(), response.getBody());
                throw new TrantorRuntimeException("failed to retrieve Nexus components");
            }
            R resp = response.getBody();
            if (resp != null) {
                Optional.ofNullable(resp.getItems()).ifPresent(items::addAll);
                String continuationToken = resp.getContinuationToken();
                if (StringUtils.isNotBlank(continuationToken)) {
                    ub.replaceQueryParam(NexusConst.CONTINUATION_TOKEN, continuationToken);
                } else {
                    break;
                }
            } else {
                break;
            }
        } while (true);
        return items;
    }
}
