package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
public class PublicNexusApiClientImpl extends AbstractNexusApiClient {
    protected PublicNexusApiClientImpl(NexusConfigProperties nexusConfigProperties,
                                       RestTemplate publicNexusRestTemplate) {
        super(nexusConfigProperties.getPub(), publicNexusRestTemplate);
    }

    @Override
    public Boolean getLocal() {
        return false;
    }
}
