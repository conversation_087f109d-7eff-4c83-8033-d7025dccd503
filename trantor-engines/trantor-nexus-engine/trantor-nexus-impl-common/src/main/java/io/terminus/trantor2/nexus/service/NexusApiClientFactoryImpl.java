package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class NexusApiClientFactoryImpl implements NexusApiClientFactory {
    private final List<NexusApiClient> nexusApiClientList;

    @Override
    public Optional<NexusApiClient> getClientOptional(Boolean isPublic) {
        return nexusApiClientList.stream()
                .filter(nexusApiClient -> isPublic != nexusApiClient.getLocal())
                .findFirst();
    }
}
