package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
@ConditionalOnProperty(name = "trantor2.nexus.local.enabled", havingValue = "true")
public class LocalNexusApiClientImpl extends AbstractNexusApiClient {
    protected LocalNexusApiClientImpl(NexusConfigProperties nexusConfigProperties,
                                      RestTemplate localNexusRestTemplate) {
        super(nexusConfigProperties.getLocal(), localNexusRestTemplate);
    }

    @Override
    public Boolean getLocal() {
        return true;
    }
}
