package io.terminus.trantor2.workflow.producer;

import io.terminus.trantor.workflow.common.config.WorkflowVariableConfig;
import io.terminus.trantor.workflow.runtime.v2.model.domain.WorkflowGroup;
import io.terminus.trantor.workflow.runtime.v2.mq.WorkflowGroupChangedEvent;
import io.terminus.trantor2.common.utils.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-08-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowGroupChangedEventPublisher {

    private final RedisTemplate<String, Object> redisTemplate;

    public void publish(WorkflowGroup workflowGroup) {
        WorkflowGroupChangedEvent event = new WorkflowGroupChangedEvent();
        event.setWorkflowGroup(workflowGroup);
        log.info("[WorkflowGroupChangedEventPublisher] publish message to mq topic [{}], workflow group: {}", WorkflowGroupChangedEvent.TOPIC, JsonUtil.toJson(workflowGroup));
        redisTemplate.convertAndSend(WorkflowVariableConfig.MQ_TOPIC, event);
    }

}
