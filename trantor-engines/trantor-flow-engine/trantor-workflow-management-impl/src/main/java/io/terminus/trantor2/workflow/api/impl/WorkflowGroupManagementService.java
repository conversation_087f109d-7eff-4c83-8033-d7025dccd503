package io.terminus.trantor2.workflow.api.impl;

import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.terminus.trantor.workflow.common.utils.JsonUtil;
import io.terminus.trantor.workflow.runtime.v2.model.domain.WorkflowGroup;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupDeleteRequest;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupDetailRequest;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupPagingRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.workflow.WorkflowGroupMeta;
import io.terminus.trantor2.workflow.producer.WorkflowGroupChangedEventPublisher;
import io.terminus.trantor2.workflow.props.WorkflowGroupMetaProps;
import io.terminus.trantor2.workflow.repo.WorkflowGroupMetaRepo;
import io.terminus.trantor2.workflow.vo.ExtendableWorkflowGroupVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/6/9 9:37 AM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowGroupManagementService {

    private final MetaQueryService queryService;

    private final TrantorIAMUserService userService;

    private final WorkflowGroupChangedEventPublisher publisher;

    private final WorkflowGroupMetaRepo managerRepo;

    public Response<Optional<WorkflowGroupMeta>> findByKey(@RequestBody WorkflowGroupDetailRequest request) {
        TrantorContext.setTeamId(request.getTeamId());
        return Response.ok(Optional.ofNullable(managerRepo.findOneByKey(request.getKey(), ResourceContext.ctxFromThreadLocal()).orElse(null)));
    }

    public Response<io.terminus.trantor.workflow.runtime.v1.app.dto.Paging<ExtendableWorkflowGroupVO>> paging(@RequestBody WorkflowGroupPagingRequest request) {
        List<Cond> conds = Lists.newArrayList();
        {
            conds.add(serviceTypeCond());
            if (StringUtils.isNotEmpty(request.getKeyword())) {
                conds.add(
                    Cond.or(
                        Field.key().like("%" + request.getKeyword() + "%"),
                        Field.name().like("%" + request.getKeyword() + "%")
                    )
                );
            }
            // 查询业务流名称
            if (StringUtils.isNotEmpty(request.getName())) {
                conds.add(Field.name().like("%" + request.getName() + "%"));
            }
            if (StringUtils.isNotEmpty(request.getKey())) {
                conds.add(Field.key().equal(request.getKey()));
            }
            if (StringUtils.isNotEmpty(request.getParentKey())) {
                conds.add(Field.parentKey().equal(request.getParentKey()));
            }
            // 查询创建时间区间
            if (Objects.nonNull(request.getStartTime())) {
                conds.add(Field.props(Date.class, "createdBy").greaterThanOrEqual(request.getStartTime()));
            }
            if (Objects.nonNull(request.getEndTime())) {
                conds.add(Field.props(Date.class, "createdBy").lessThanOrEqual(request.getEndTime()));
            }
            // 查询更新时间区间
            if (Objects.nonNull(request.getUpdateStartTime())) {
                conds.add(Field.props(Date.class, "updatedBy").greaterThanOrEqual(request.getUpdateStartTime()));
            }
            if (Objects.nonNull(request.getUpdateEndTime())) {
                conds.add(Field.props(Date.class, "updatedBy").lessThanOrEqual(request.getUpdateEndTime()));
            }
        }
        Cond cond = Cond.and(conds.toArray(new Cond[0]));

        int pageNumber = request.getPageNumber() == null ? 1 : request.getPageNumber();
        int pageSize = request.getPageSize() == null ? 20 : request.getPageSize();
        MetaEditAndQueryContext ctx = EditUtil.newCtx(request.getTeamId(),
            getUserId(false));

        Paging<WorkflowGroupMeta> result = managerRepo.findAll(
                cond,
                PageReq.of(pageNumber - 1, pageSize, Order.byModifiedAt().desc()),
                ResourceContext.newResourceCtx(ctx)
        );
        List<ExtendableWorkflowGroupVO> data = new ArrayList<>();
        if (result.getData() != null) {
            result.getData().forEach(it -> data.add(meta2vo(Lists.newArrayList(it))));
        }
        io.terminus.trantor.workflow.runtime.v1.app.dto.Paging<ExtendableWorkflowGroupVO> paging = new io.terminus.trantor.workflow.runtime.v1.app.dto.Paging<>();
        fillUserName(data);
        paging.setData(data);
        paging.setTotal(result.getTotal());
        return Response.ok(paging);
    }

    private void fillUserName(List<ExtendableWorkflowGroupVO> workflowGroups) {
        Set<Long> userIds = new HashSet<>();
        for (WorkflowGroup workflowGroup : workflowGroups) {
            if (workflowGroup.getCreatedBy() != null) {
                userIds.add(workflowGroup.getCreatedBy());
            }
            if (workflowGroup.getUpdatedBy() != null) {
                userIds.add(workflowGroup.getUpdatedBy());
            }
        }
        Map<Long, String> userMap = queryUserNames(userIds);
        workflowGroups.forEach(vo -> {
            if (vo.getCreatedBy() != null) {
                vo.setCreatedByUserName(userMap.get(vo.getCreatedBy()));
            }
            if (vo.getUpdatedBy() != null) {
                vo.setUpdatedByUserName(userMap.get(vo.getUpdatedBy()));
            }
        });
    }

    private Map<Long, String> queryUserNames(Set<Long> userIds) {
        Map<Long, String> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            try {
                List<User> users = userService.findByIds(userIds);
                if (CollectionUtils.isNotEmpty(users)) {
                    for (User user : users) {
                        userMap.put(user.getId(), StringUtils.defaultIfBlank(user.getNickname(), user.getUsername()));
                    }
                }
            } catch (Exception e) {
                log.error("query user error", e);
            }
        }
        return userMap;
    }

    public Response<Long> save(@RequestBody WorkflowGroup workflowGroup) {
        MetaEditAndQueryContext ctx = EditUtil.newCtx(workflowGroup.getTeamId(),
                getUserId(true));
        if (Objects.isNull(workflowGroup.getId())) {
            workflowGroup.setId(null); // disable snowflake id
            // create
            if (StringUtils.isBlank(workflowGroup.getParentKey())) {
                workflowGroup.setParentKey(getRootKey(ctx));
            }
            return Response.ok(managerRepo.create(service2meta(workflowGroup).get(0), ResourceContext.ctxFromThreadLocal()));
        } else {
            // update
            if (StringUtils.isBlank(workflowGroup.getParentKey())) {
                workflowGroup.setParentKey(getOriginParentKey(ctx, workflowGroup.getKey()));
            }
            managerRepo.update(service2meta(workflowGroup).get(0), ResourceContext.ctxFromThreadLocal());
            publisher.publish(workflowGroup);
            return Response.ok(workflowGroup.getId());
        }
    }

    private ExtendableWorkflowGroupVO meta2vo(List<WorkflowGroupMeta> metas) {
        WorkflowGroupMeta ent = metas.get(0);
        return JsonUtil.convertObject(ent, ExtendableWorkflowGroupVO.class);
    }

    private List<WorkflowGroupMeta> service2meta(WorkflowGroup approvalGroup) {
        WorkflowGroupMeta meta = new WorkflowGroupMeta();
        try {
            BeanUtils.copyProperties(meta, approvalGroup);

            WorkflowGroupMetaProps workflowGroupMetaProps = new WorkflowGroupMetaProps();
            BeanUtils.copyProperties(workflowGroupMetaProps, approvalGroup.getProps());
            meta.setResourceProps(workflowGroupMetaProps);
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        }
        return Lists.newArrayList(meta);
    }

    private String getOriginParentKey(MetaEditAndQueryContext ctx, String key) {
        return queryService.findByKey(ctx, key).orElseThrow(() -> new RuntimeException("service not found")).getParentKey();
    }

    private Cond serviceTypeCond() {
        return Field.type().equal("WorkflowGroup");
    }

    private String getRootKey(MetaEditAndQueryContext ctx) {
        return queryService.queryInTeam(ctx.getTeamId()).findFolderRootKey();
    }

    private Long getUserId(boolean check) {
        return TrantorContext.safeGetCurrentUser().map(User::getId).orElseGet(() -> {
            if (check) {
                throw new RuntimeException("user not login");
            }
            return null;
        });
    }

}
