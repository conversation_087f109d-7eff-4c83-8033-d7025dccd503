package io.terminus.trantor2.workflow.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.terminus.trantor.workflow.common.dto.Response;
import io.terminus.trantor.workflow.common.utils.RespHelper;
import io.terminus.trantor.workflow.runtime.v1.app.dto.Paging;
import io.terminus.trantor.workflow.runtime.v2.model.domain.WorkflowGroup;
import io.terminus.trantor.workflow.runtime.v2.model.domain.enums.WorkflowChannelValueType;
import io.terminus.trantor.workflow.runtime.v2.model.dto.channel.FormTemplate;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupDeleteRequest;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupDetailRequest;
import io.terminus.trantor.workflow.runtime.v2.model.request.WorkflowGroupPagingRequest;
import io.terminus.trantor.workflow.runtime.v2.model.vo.WorkflowGroupVO;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.workflow.WorkflowGroupMeta;
import io.terminus.trantor2.workflow.api.impl.WorkflowGroupManagementService;
import io.terminus.trantor2.workflow.vo.ExtendableWorkflowGroupVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/6/6 6:15 PM
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/trantor/workflow/v2/workflow-group")
public class WorkflowGroupManagementController {

    private final WorkflowGroupManagementService workflowGroupApi;

    @Operation(summary = "分页查询")
    @GetMapping(value = "/paging")
    public Response<Paging<ExtendableWorkflowGroupVO>> paging(WorkflowGroupPagingRequest request) {
        request.setTeamId(TrantorContext.getTeamId());
        Paging<ExtendableWorkflowGroupVO> approvalGroupPaging = RespHelper.or500(workflowGroupApi.paging(request));
        return Response.ok(approvalGroupPaging);
    }

    @Operation(summary = "查询详情")
    @GetMapping(value = "/{key}")
    public Response<WorkflowGroupVO> detail(@PathVariable String key) {
        WorkflowGroupDetailRequest request = new WorkflowGroupDetailRequest(key);
        request.setTeamId(TrantorContext.getTeamId());
        Optional<WorkflowGroupMeta> workflowGroupOptional = RespHelper.or500(workflowGroupApi.findByKey(request));
        if (workflowGroupOptional.isPresent()) {
            ExtendableWorkflowGroupVO workflowGroupVO = new ExtendableWorkflowGroupVO();
            WorkflowGroupMeta workflowGroup = workflowGroupOptional.get();
            BeanUtils.copyProperties(workflowGroup, workflowGroupVO);
            workflowGroupVO.setProps(workflowGroup.getResourceProps());
            FormTemplate formTemplate = workflowGroupVO.getProps().getFormTemplate();
            if (Objects.nonNull(formTemplate) && Objects.isNull(formTemplate.getChannelValueType())) {
                formTemplate.setChannelValueType(WorkflowChannelValueType.CONST);
            }
            return Response.ok(workflowGroupVO);
        } else {
            return Response.ok(null);
        }
    }

    @Operation(summary = "保存")
    @PostMapping(value = "/save")
    public Response<Long> save(@RequestBody WorkflowGroup workflowGroup) {
        workflowGroup.setTeamId(TrantorContext.getTeamId());
        return Response.ok(RespHelper.or500(workflowGroupApi.save(workflowGroup)));
    }

}
