package io.terminus.trantor2.workflow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor.workflow.runtime.v2.model.vo.WorkflowGroupVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExtendableWorkflowGroupVO extends WorkflowGroupVO {

    private static final long serialVersionUID = -575667360366148361L;
    @Schema(description = "是否为扩展资源")
    private Boolean extended;

    @Schema(description = "是否可被扩展")
    private Boolean extensible;

    @Schema(description = "是否为可定制扩展资源，可任意修改")
    private Boolean customExt;
}
