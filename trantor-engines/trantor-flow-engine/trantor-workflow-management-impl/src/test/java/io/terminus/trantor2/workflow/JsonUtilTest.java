package io.terminus.trantor2.workflow;

import io.terminus.trantor.workflow.common.utils.JsonUtil;
import io.terminus.trantor.workflow.runtime.v2.model.domain.WorkflowGroup;
import io.terminus.trantor2.workflow.props.WorkflowGroupMetaProps;
import org.apache.commons.beanutils.BeanUtils;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;

public class JsonUtilTest {

    @Test
    public void testCopy() {
        String json = "{\"id\":24030826,\"key\":\"contract$SUBMISSION_FOR_APPROVAL_OF_PROCUREMENT_CONTRACT\",\"parentKey\":\"contract$ungroup\",\"name\":\"采购合同审批\",\"props\":{\"desc\":null,\"relatedModel\":{\"modelKey\":\"contract$gen_ct_head_tr_workflow\",\"modelName\":null,\"nodeKey\":null,\"nodeTitle\":null},\"inputParams\":[{\"fieldKey\":\"data\",\"fieldName\":\"入参\",\"fieldType\":\"Model\",\"relatedModel\":{\"modelKey\":\"contract$gen_ct_head_tr_workflow\",\"modelName\":null}}],\"customVariables\":[{\"fieldKey\":\"test\",\"fieldName\":null,\"fieldType\":\"Model\",\"required\":null,\"relatedModel\":{\"modelKey\":\"contract$gen_ct_head_tr_workflow\",\"modelName\":null}}],\"channelType\":\"TRANTOR_WORKFLOW\",\"attachmentConfig\":{\"maxNumberOfFiles\":null,\"maxFileSize\":null,\"allowedFileFormats\":null,\"inputPrompt\":null},\"trantorEventListeners\":[{\"_row_id_\":\"z6gQbSC\",\"key\":null,\"name\":null,\"workflowEvent\":{\"eventType\":\"PROCESS_COMPLETED\"},\"relatedEvent\":null,\"relatedEventService\":null,\"relatedService\":{\"serviceKey\":\"contract$contract_flow_callback\",\"serviceName\":\"合同流程回调\"},\"carryApprovalRemark\":null,\"actionType\":\"SERVICE\"},{\"_row_id_\":\"TQPhBFx\",\"key\":null,\"name\":null,\"workflowEvent\":{\"eventType\":\"TASK_REFUSE\"},\"relatedEvent\":null,\"relatedEventService\":null,\"relatedService\":{\"serviceKey\":\"contract$contract_flow_callback\",\"serviceName\":\"合同流程回调\"},\"carryApprovalRemark\":false,\"actionType\":\"SERVICE\"}],\"nodeCallbackServiceConfig\":[{\"_row_id_\":\"j2Sg1ND\",\"key\":null,\"name\":\"回调\",\"workflowEvent\":null,\"relatedEvent\":null,\"relatedEventService\":null,\"relatedService\":{\"serviceKey\":\"contract$contract_node_callback\",\"serviceName\":\"合同节点回调\"},\"carryApprovalRemark\":null,\"actionType\":\"SERVICE\"}],\"decisionServiceConfig\":[{\"serviceKey\":\"contract$contract_smart_comparison\",\"serviceName\":\"合同智能比对\"},{\"serviceKey\":\"contract$vendor_risk_analysis\",\"serviceName\":\"供应商风险分析\"}]},\"teamId\":663,\"appId\":24009729,\"isDeleted\":null,\"createdBy\":473389403313477,\"createdByUserName\":null,\"createdAt\":1744019328000,\"updatedBy\":473389403313477,\"updatedByUserName\":null,\"updatedAt\":1744019328000,\"workflowList\":null,\"extended\":false,\"extensible\":false,\"customExt\":false,\"group\":\"合同-未分组\"}";
        WorkflowGroup workflowGroup = JsonUtil.fromJson(json, WorkflowGroup.class);
        WorkflowGroupMeta workflowGroupMeta = new WorkflowGroupMeta();
        try {
            BeanUtils.copyProperties(workflowGroupMeta, workflowGroup);

            WorkflowGroupMetaProps workflowGroupMetaProps = new WorkflowGroupMetaProps();
            BeanUtils.copyProperties(workflowGroupMetaProps, workflowGroup.getProps());
            workflowGroupMeta.setResourceProps(workflowGroupMetaProps);

        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
        System.out.println(JsonUtil.toJson(workflowGroupMeta));
    }

}
