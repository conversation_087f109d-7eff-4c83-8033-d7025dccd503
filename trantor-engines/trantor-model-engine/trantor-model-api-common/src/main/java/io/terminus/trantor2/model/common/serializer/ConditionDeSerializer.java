package io.terminus.trantor2.model.common.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.model.condition.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Condition对象反序列化器
 *
 * <AUTHOR>
 * @since 2022/8/26
 */
public class ConditionDeSerializer extends JsonDeserializer<Condition> {
    /**
     * Condition反序列化
     *
     * @param jsonParser
     * @param deserializationContext
     * @return Condition对象
     * @throws IOException
     */
    @Override
    public Condition deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
        throws IOException {
        ObjectCodec oc = jsonParser.getCodec();
        JsonNode node = oc.readTree(jsonParser);
        return getCondition(node);
    }

    private Condition buildCondition(JsonNode node) {
        return getCondition(node);
    }

    private Condition getCondition(JsonNode node) {
        Condition condition;
        JsonNode conditions = node.get("conditions");
        if (conditions == null) {
            // 原子
            String field = (String) getValue(node.get("field"));
            String namespace = (String) getValue(node.get("namespace"));
            ConditionType type = ConditionType.valueOf((String) getValue(node.get("type")));
            JsonNode valueNode = node.get("value");
            ConditionValue conditionValue = null;
            if (valueNode != null) {
                Object value = getValue(valueNode.get("value"));
                Object rightValue = getValue(valueNode.get("rightValue"));
                List<Object> values = (List<Object>) getValue(valueNode.get("values"));
                ConditionValueType conditionValueType = null;
                Object valueType = getValue(valueNode.get("type"));
                if (valueType != null) {
                    conditionValueType = ConditionValueType.valueOf(String.valueOf(valueType));
                }
                conditionValue = ConditionValue.builder()
                    .value(value)
                    .rightValue(rightValue)
                    .values(values)
                    .type(conditionValueType)
                    .build();
            }
            String ope = (String) getValue(node.get("nextOperator"));
            condition = new SingleCondition(field, type, conditionValue, ConditionLogicalOperator.find(ope));
            ((SingleCondition) condition).setNamespace(namespace);
        } else {
            // 条件组
            List<Condition> conditionList = new ArrayList<>();
            Iterator<JsonNode> elements = conditions.elements();
            while (elements.hasNext()) {
                JsonNode next = elements.next();
                Condition nextCondition = buildCondition(next);
                conditionList.add(nextCondition);
            }
            String ope = (String) getValue(node.get("nextOperator"));
            condition = new ConditionGroup(conditionList, ConditionLogicalOperator.find(ope));
        }
        return condition;
    }

    /**
     * @param jsonNode
     * @return value
     */
    public Object getValue(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        if (jsonNode.isTextual()) {
            return jsonNode.textValue();
        }
        if (jsonNode.isLong()) {
            return jsonNode.longValue();
        }
        if (jsonNode.isInt()) {
            return jsonNode.intValue();
        }
        if (jsonNode.isDouble() || jsonNode.isFloat()) {
            return jsonNode.doubleValue();
        }
        if (jsonNode.isArray()) {
            List<Object> values = new ArrayList<>();
            for (JsonNode node : jsonNode) {
                values.add(getValue(node));
            }
            return values;
        }
        if (jsonNode.isBoolean()) {
            return jsonNode.booleanValue();
        }
        if (jsonNode.isBigDecimal()) {
            return jsonNode.decimalValue();
        }
        if (jsonNode.isBigInteger()) {
            return jsonNode.bigIntegerValue();
        }
        return jsonNode.asText();
    }

}
