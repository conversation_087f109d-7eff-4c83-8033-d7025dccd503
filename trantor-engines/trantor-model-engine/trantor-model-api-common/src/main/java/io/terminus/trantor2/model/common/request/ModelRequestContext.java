package io.terminus.trantor2.model.common.request;

import io.terminus.trantor2.model.common.model.BasicObject;

/**
 * 模型引擎请求上下文
 */
public class ModelRequestContext {

    private static final ThreadLocal<BasicObject> CONTEXT = new ThreadLocal<>();

    public static BasicObject getContext() {
        return CONTEXT.get();
    }

    public static void setContext(BasicObject context) {
        CONTEXT.set(context);
    }
}
