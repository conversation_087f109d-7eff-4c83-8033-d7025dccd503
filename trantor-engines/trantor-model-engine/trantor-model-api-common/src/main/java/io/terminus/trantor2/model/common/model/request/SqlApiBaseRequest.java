package io.terminus.trantor2.model.common.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public abstract class SqlApiBaseRequest implements Serializable {
    /**
     * 选择数据源时使用
     */
    @NotNull
    private Long teamId;

    /**
     * 选择数据源时使用
     */
    @NotNull
    private String moduleKey;

    /**
     * 用于获取模型元数据使用，比如加字段加密 & 解密、脱敏等
     */
    @NotNull
    private String modelKey;

    /**
     * 待执行 SQL, 允许带命名的占位符
     */
    @NotNull
    private String sql;
}
