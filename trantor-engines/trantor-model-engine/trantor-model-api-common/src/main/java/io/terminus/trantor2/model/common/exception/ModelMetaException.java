package io.terminus.trantor2.model.common.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * 模型元信息管理异常
 */
public class ModelMetaException extends TrantorBizException {

    /**
     * 构造器
     *
     * @param errorType
     * @param message1
     */
    public ModelMetaException(ErrorType errorType, String message1) {
        super(errorType, message1);
    }

    public ModelMetaException(ErrorType errorType, String message1, Object[] args) {
        super(errorType, message1, args);
    }

    public ModelMetaException(ErrorType errorType, Object[] args) {
        super(errorType, args);
    }
    /**
     * 最终抛给用户的异常信息，若此拼接规则不满足需求，请重写此方法
     *
     * @return
     */
    @Override
    public String getComposedMessage() {
        return super.getMessage();
    }
}
