package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.AggregateOperation;
import io.terminus.trantor2.model.common.model.request.aggregate.AggregateFuncEnum;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 聚合请求，参数最终转换为聚合 SQL 查询，支持的聚合查询有:
 * - 求和: select sum(<field>) from <table> where <conditionField>=<conditionValue>
 * - 计数: select count(<field>) from <table> where <conditionField>=<conditionValue>
 * - 平均值: select avg(<field>) from <table> where <conditionField>=<conditionValue>
 * - 最大值: select max(<field>) from <table> where <conditionField>=<conditionValue>
 * - 最小值: select min(<field>) from <table> where <conditionField>=<conditionValue>
 */
@Data
@ToString(callSuper = true)
public class AggregateQueryRequest extends CountRequest implements AggregateOperation {
    /**
     * 聚合函数
     */
    private AggregateFuncEnum aggregateFunc;

    /**
     * 聚合字段，eg: SUM(amount) 里的 amount 字段
     */
    private String aggregateField;

    /**
     * 分组字段，eg: SELECT region, product, SUM(amount) FROM sales GROUP BY region, product;
     */
    private List<String> groupBy;

    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() +
            ":" + aggregateFunc.name() +
            ":" + aggregateField +
            (groupBy != null ? ":" + groupBy : "") +
            ":" + super.calculateObjectKey();
    }
}
