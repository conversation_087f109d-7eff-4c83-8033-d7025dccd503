package io.terminus.trantor2.model.common.model.condition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 原子条件
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Schema(description = "过滤条件")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SingleCondition extends Condition {

    /**
     * 所属关联关系的标识符，对于主模型的条件namespace为空
     */
    @Schema(description = "namespace")
    private String namespace;

    @Schema(description = "字段")
    private String field;

    @Schema(description = "过滤条件", enumAsRef = true)
    private ConditionType type;

    @Schema(description = "字段值对象")
    private ConditionValue value;

    @Builder
    public SingleCondition(
        String field, ConditionType type,
        ConditionValue value,
        ConditionLogicalOperator nextOperator) {
        super(nextOperator);
        this.field = field;
        this.type = type;
        this.value = value;
    }

    public SingleCondition(String field, ConditionType type, ConditionValue value) {
        this.field = field;
        this.type = type;
        this.value = value;
    }

    @JsonIgnore
    public boolean isMultistageField() {
        return field.contains(".");
    }

    /**
     * 计算实际条件字段数量
     *
     * @return 条件字段数量
     */
    @Override
    public int calculateConditionFieldSize() {
        if (value != null && value.getType() == ConditionValueType.FIELD) {
            return 0;
        }
        return type.getValueType().getConditionSize();
    }

    /**
     * 提取去除value之后的关键信息，用于标识同一种类型的条件结构
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return namespace + ":" + field + ":" + type + ":" + nextOperator;
    }
}
