package io.terminus.trantor2.model.common.model.condition;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.serializer.ConditionDeSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 条件抽象
 *
 * <AUTHOR>
 * @since 2022/8/26
 */
@NoArgsConstructor
@Data
@JsonDeserialize(using = ConditionDeSerializer.class)
public abstract class Condition implements Serializable {

    @Schema(description = "当前Condition与下一个Condition的逻辑关系")
    protected ConditionLogicalOperator nextOperator;

    public Condition(ConditionLogicalOperator nextOperator) {
        this.nextOperator = nextOperator;
    }

    /**
     * 计算实际条件字段数量
     *
     * @return 条件字段数量
     */
    public abstract int calculateConditionFieldSize();

    /**
     * 提取去除value之后的关键信息，用于标识同一种类型的条件结构
     *
     * @return 唯一标识
     */
    public abstract String calculateObjectKey();

}
