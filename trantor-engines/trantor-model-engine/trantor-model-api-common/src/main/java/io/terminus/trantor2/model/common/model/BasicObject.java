package io.terminus.trantor2.model.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.model.common.consts.MethodType;
import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import io.terminus.trantor2.model.common.model.condition.Pageable;
import io.terminus.trantor2.model.common.model.request.BatchCreateObject;
import io.terminus.trantor2.model.common.model.request.CreateObject;
import io.terminus.trantor2.model.common.model.request.DeleteObject;
import io.terminus.trantor2.model.common.model.request.Order;
import io.terminus.trantor2.model.common.model.request.Select;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基础信息
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
public class BasicObject implements Serializable {

    private static final long serialVersionUID = 2592998168455101546L;
    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 模型标识
     */
    private String modelAlias;

    /**
     * 事务ID
     */
    private String transactionId;

    /**
     * 关联关系
     */
    private transient Object extractRelation;

    /**
     * 当作为查询条件时，师傅跳过脱敏
     */
    private boolean skipDesensitization;

    /**
     * statement的参数（jpa）
     */
    @JsonIgnore
    private Object[] statementParameters;

    /**
     * 业务数据默认语言，数据查询时，根据此值与 TrantorContext.getLang() 是否一致来判断过滤条件是否要按 i18n 生效
     */
    private String defaultLocale;

    /**
     * 是否是QueryOperation
     *
     * @return true：是；false：否
     */
    public boolean isQuery() {
        return this instanceof QueryOperation;
    }

    public boolean isAggregate() {
        return this instanceof AggregateOperation;
    }

    /**
     * 是否是UpdateOperation
     *
     * @return true：是；false：否
     */
    public boolean isUpdate() {
        return this instanceof UpdateOperation;
    }

    public boolean isInsert() {
        return this instanceof CreateObject || isBatchInsert();
    }

    public boolean isBatchInsert() {
        return this instanceof BatchCreateObject;
    }

    /**
     * 是否是CountOperation
     *
     * @return true：是；false：否
     */
    public boolean isCount() {
        return this instanceof CountOperation;
    }

    public boolean isDelete() {
        return this instanceof DeleteObject;
    }

    public boolean isModify() {
        return isInsert() || isBatchInsert() || isUpdate() || isDelete();
    }

    /**
     * 高级筛选条件获取
     *
     * @return 高级筛选条件
     */
    public ConditionGroup getConditionGroup() {
        return null;
    }

    /**
     * 简单查询条件
     *
     * @return
     */
    public ConditionItems getConditionItems() {
        return null;
    }

    /**
     * 排序条件获取
     *
     * @return 排序条件
     */
    public List<Order> getOrderBy() {
        return null;
    }

    /**
     * 请求类型获取
     *
     * @return 请求类型
     */
    public MethodType getMethodType() {
        if (isAggregate()) {
            return MethodType.AGGREGATE;
        } else if (isQuery()) {
            return MethodType.QUERY;
        } else if (isUpdate()) {
            return MethodType.UPDATE;
        } else if (isCount()) {
            return MethodType.COUNT;
        } else if (isDelete()) {
            return MethodType.DELETE;
        } else if (isInsert()) {
            return MethodType.INSERT;
        }
        return MethodType.UPDATE;
    }

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    public String calculateObjectKey() {
        return getModelIdentifier();
    }

    /**
     * 唯一标识生成
     *
     * @return 唯一标识
     */
    public String getModelIdentifier() {
        return teamId + ":" + modelAlias;
    }

    /**
     * 获取分页对象
     *
     * @return 分页对象
     */
    public Pageable getPage() {
        return null;
    }

    /**
     * 获取查询字段
     *
     * @return 查询字段
     */
    public List<Select> getSelect() {
        return null;
    }

    public void setConditionGroup(ConditionGroup conditionGroup) {
        throw new UnsupportedOperationException();
    }
}
