package io.terminus.trantor2.model.common.model.request;
import io.terminus.trantor2.model.common.model.BasicObject;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * 创建
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class CreateObject extends BasicObject {

    private static final long serialVersionUID = 3928424969215921740L;

    /**
     * 模型数据，key：模型字段别名，value：字段值
     */
    private Map<String, Object> data;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":" + data.keySet();
    }

}
