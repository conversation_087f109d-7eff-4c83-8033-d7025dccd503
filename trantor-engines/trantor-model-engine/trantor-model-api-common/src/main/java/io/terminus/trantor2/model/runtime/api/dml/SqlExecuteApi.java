package io.terminus.trantor2.model.runtime.api.dml;

import io.terminus.trantor2.model.common.model.request.SqlRequest;

import java.util.List;
import java.util.Map;

/**
 * SqlExecuteApi
 *
 * <AUTHOR> Created on 2023/8/3 09:39
 */
public interface SqlExecuteApi {

    Map<String, Object> selectOne(SqlRequest request);

    List<Map<String, Object>> selectAll(SqlRequest request);

    /**
     * 创建/更新/删除操作
     *
     * @param request
     */
    void execute(SqlRequest request);
}
