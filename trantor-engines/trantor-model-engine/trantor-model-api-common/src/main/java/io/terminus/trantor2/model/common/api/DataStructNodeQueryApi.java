package io.terminus.trantor2.model.common.api;

import io.swagger.v3.oas.annotations.Parameter;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.meta.request.QueryByKeyRequest;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.meta.request.SimpleModelInfoRequest;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.SimpleDataStructNode;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface DataStructNodeQueryApi {

    default <T extends Serializable> Response<DataStructNode> findByKeyAndTeam(@Parameter(required = true) @PathVariable String key, T team) {
        QueryByKeyRequest<T> request = new QueryByKeyRequest<>();
        request.setTeam(team);
        return findByKeyAndTeam(key, request);
    }


    /**
     * 根据模型标识获取数据结构
     *
     * @param key  模型标识
     * @return 数据结构对象
     */
    @GetMapping("/find-by-key/{key}")
    <T extends Serializable> Response<DataStructNode> findByKeyAndTeam(@Parameter(required = true) @PathVariable String key, @SpringQueryMap QueryByKeyRequest<T> request);

    /**
     * 根据模型标识批量获取数据结构
     *
     * @param request <p>
     *                {@link QueryByKeysRequest#getTeam()} 项目标识，可为空，默认为 {@link TrantorContext#getTeamCode()}
     * @return 数据结构对象集合
     */
    @PostMapping("/find-by-keys")
    <T extends Serializable> Response<Collection<DataStructNode>> findByKeys(@RequestBody QueryByKeysRequest<T> request);

    @PostMapping("/list-simple-by-keys")
    <T extends Serializable> Response<Collection<SimpleDataStructNode>> listSimpleByKeys(@RequestBody SimpleModelInfoRequest<T> request);
}
