package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.BasicObject;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 批量创建
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class BatchCreateObject extends BasicObject {

    private static final long serialVersionUID = 3256312637716691982L;

    /**
     * 模型数据列表，key：模型字段别名，value：字段值
     */
    private List<Map<String, Object>> dataList;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":" + dataList.get(0).keySet();
    }

}
