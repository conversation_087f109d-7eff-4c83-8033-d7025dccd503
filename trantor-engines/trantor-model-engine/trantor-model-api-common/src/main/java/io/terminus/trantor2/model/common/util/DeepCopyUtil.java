package io.terminus.trantor2.model.common.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

public class DeepCopyUtil {

    @SuppressWarnings("unchecked")
    public static <T> T copy(T original) {
        if (original == null) {
            return null;
        }

        try {
            // 创建一个输出流，用于序列化对象
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);

            // 序列化原始对象
            objectOutputStream.writeObject(original);

            // 关闭输出流
            objectOutputStream.close();

            // 创建一个输入流，用于反序列化对象
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);

            // 反序列化并返回深拷贝的对象
            return (T) objectInputStream.readObject();
        } catch (Exception e) {
            // 处理异常，比如 ClassNotFoundException
            e.printStackTrace();
            return null;
        }
    }
}
