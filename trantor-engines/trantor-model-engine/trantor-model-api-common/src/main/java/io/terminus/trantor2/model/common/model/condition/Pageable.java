package io.terminus.trantor2.model.common.model.condition;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
public class Pageable implements Serializable {

    private static final long serialVersionUID = -172000200770987990L;

    private int no;
    private int size;
    private boolean count;

    /**
     * 可选值: prev/next，分别表示上一页/下一页
     */
    private String pagingAction;

    /**
     * 上一页查询时是上一页第一条数据 id, 下一页查询时是当前页数据的最后一条数据 id, 前端上一页/下一页查询数据时需要传递此值解决单表 100w+ 数据量时使用 offset 查询过慢的问题
     */
    private Long anchorId;

    public Pageable(int no, int size, boolean count) {
       this(no, size, count, null, null);
    }

    public Pageable(int no, int size, boolean count, String pagingAction, Long anchorId) {
        this.no = no;
        this.size = size;
        this.count = count;
        this.pagingAction = pagingAction;
        this.anchorId = anchorId;
    }

    public int offset() {
        return (no - 1) * size;
    }
}
