package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.condition.Pageable;
import io.terminus.trantor2.model.common.model.PageOperation;
import lombok.Data;
import lombok.ToString;

/**
 * 分页
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class PagingRequest extends QueryRequest implements PageOperation {

    private static final long serialVersionUID = -2030661831410709053L;

    /**
     * 分页
     */
    private Pageable page;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":"
            + page
            + super.calculateObjectKey();
    }

}
