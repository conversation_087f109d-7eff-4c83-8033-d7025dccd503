package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.condition.enums.Operator;
import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.model.common.model.CountOperation;
import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * 查询总数
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class CountRequest extends BasicObject implements CountOperation {

    private static final long serialVersionUID = -7645166231192094436L;

    /**
     * 高级筛选条件，与 conditionItems 互斥，只能二选一
     */
    private ConditionGroup conditionGroup;

    /**
     * 普通筛选条件, 与 conditionGroup 互斥，只能二选一
     * eg:
     *  "conditionItems": {
     *     "type": "ConditionItems",
     *     "logicOperator": "AND",
     *     "conditions": {
     *       "soCode": {
     *         "operator": "CONTAINS",
     *         "value": "200"
     *       },
     *       "soRefCode": {
     *         "operator": "CONTAINS",
     *         "value": "PO2023"
     *       }
     *     }
     *   }
     */
    private ConditionItems conditionItems;

    /**
     * <p>是否为普通筛选条件，添加此判断条件主要是普通筛选的情况下，在执行流程中数据权限会添加过滤条件至 conditionGroup，
     * 无法通过此变量是否为空判断是否为高级筛选</p>
     */
    private Boolean simpleQuery;

    /**
     * 方法唯一标识, 用于生成缓存生成 SQL的 key，请保证唯一
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        StringBuilder sb = new StringBuilder(this.getClass().getSimpleName());
        if (conditionItems != null && MapUtils.isNotEmpty(conditionItems.getConditions())) {
            StringBuilder conditionBuilder = new StringBuilder();
            for (Map.Entry<String, ConditionItem> condition : conditionItems.getConditions().entrySet()) {
                conditionBuilder.append(condition.getKey())
                        .append("->")
                        .append(condition.getValue().getOperator());
                if (condition.getValue().getOperator() == Operator.IN || condition.getValue().getOperator() == Operator.NOT_IN) {
                    List<Object> values = (List<Object>) condition.getValue().getValue();
                    conditionBuilder.append("->").append(values.size());
                }
            }
            sb.append(":").append(conditionBuilder).append(":").append(conditionItems.getLogicOperator());
        }
        if (conditionGroup != null) {
            sb.append(":").append(conditionGroup.calculateObjectKey());
        } else {
            sb.append(":null");
        }
        return sb.toString();
    }

    @Override
    public void setConditionGroup(ConditionGroup conditionGroup) {
        this.conditionGroup = conditionGroup;
    }

}
