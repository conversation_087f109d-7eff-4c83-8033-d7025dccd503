package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.model.common.model.QueryOperation;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/19
 */
@Data
@ToString(callSuper = true)
public class IdQueryRequest extends BasicObject implements QueryOperation {

    private static final long serialVersionUID = 1858119957592407477L;

    /**
     * 查询字段
     */
    private List<Select> select;

    /**
     * id值
     */
    private Object id;

    /**
     * 返回的数据是否需要 i18n
     */
    private boolean dataI18n;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":" + select;
    }
}
