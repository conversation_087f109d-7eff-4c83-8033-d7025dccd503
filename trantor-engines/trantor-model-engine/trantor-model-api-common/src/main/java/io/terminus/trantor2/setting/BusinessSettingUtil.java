package io.terminus.trantor2.setting;

import io.terminus.trantor2.meta.util.KeyUtil;
import org.apache.commons.lang3.StringUtils;

public class BusinessSettingUtil {
    public static String composeSettingKey(String moduleKey) {
        if (StringUtils.isEmpty(moduleKey)) {
            throw new RuntimeException("moduleKey is null when compose setting key");
        }

        return moduleKey + KeyUtil.MODULE_SEPARATOR + moduleKey.toLowerCase() + BusinessSettingConst.SETTING_SUFFIX;
    }
}
