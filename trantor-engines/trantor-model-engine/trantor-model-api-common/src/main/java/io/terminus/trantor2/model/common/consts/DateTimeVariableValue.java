package io.terminus.trantor2.model.common.consts;

import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/9/26
 */
public enum DateTimeVariableValue {

    TODAY {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().getMillis();
            long endTime = getMaxTimeOfDay(dateTime).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    TOMORROW {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().plusDays(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    LAST_DAY {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().minusDays(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    THIS_WEEK {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().withDayOfWeek(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).withDayOfWeek(WEEK_OF_DAYS).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    LAST_WEEK {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().minusDays(WEEK_OF_DAYS).withDayOfWeek(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).minusDays(WEEK_OF_DAYS).withDayOfWeek(WEEK_OF_DAYS).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    NEXT_WEEK {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().plusDays(WEEK_OF_DAYS).withDayOfWeek(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).plusDays(WEEK_OF_DAYS).withDayOfWeek(WEEK_OF_DAYS).getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    LAST_MONTH {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().minusMonths(1).withDayOfMonth(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).minusMonths(1).dayOfMonth().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    THIS_MONTH {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().withDayOfMonth(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).dayOfMonth().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    NEXT_MONTH {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().plusMonths(1).withDayOfMonth(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).plusMonths(1).dayOfMonth().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    LAST_YEAR {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().minusYears(1).withDayOfYear(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).minusYears(1).dayOfYear().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    THIS_YEAR {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().withDayOfYear(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).dayOfYear().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }
    },

    NEXT_YEAR {
        @Override
        public Pair<Date, Date> getStartAndEndTime() {
            DateTime dateTime = new DateTime(System.currentTimeMillis());
            long startTime = dateTime.withTimeAtStartOfDay().plusYears(1).withDayOfYear(1).getMillis();
            long endTime = getMaxTimeOfDay(dateTime).plusYears(1).dayOfYear().withMaximumValue().getMillis();
            Date start = new Date(startTime);
            Date end = new Date(endTime);
            return Pair.of(start, end);
        }


    };

    /**
     * 获取起始-结束时间
     *
     * @return 开始时间，结束时间
     */
    public abstract Pair<Date, Date> getStartAndEndTime();

    private static DateTime getMaxTimeOfDay(DateTime dateTime) {
        return dateTime.withTime(END_HOUR_OF_DAY, END_MINUTE_OF_HOUR, END_SECOND_OF_MINUTE, END_MILLS_OF_SECOND);
    }

    private static final int END_HOUR_OF_DAY = 23;

    private static final int END_MINUTE_OF_HOUR = 59;

    private static final int END_SECOND_OF_MINUTE = 59;

    private static final int END_MILLS_OF_SECOND = 999;

    private static final int WEEK_OF_DAYS = 7;

}
