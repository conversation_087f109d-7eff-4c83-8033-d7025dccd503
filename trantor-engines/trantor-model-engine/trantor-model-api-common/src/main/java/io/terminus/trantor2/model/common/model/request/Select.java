package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Select implements Serializable {
    private static final long serialVersionUID = 7775652669682303379L;

    /**
     * 模型字段别名/模型关联关系名
     */
    private String field;

    /**
     * 关联模型查询字段
     */
    private List<Select> select;

    /**
     * 子模型排序字段
     */
    private List<Order> orderBy;

    /**
     * 子模型查询时的过滤条件
     */
    private ConditionGroup conditionGroup;

    public Select(String field) {
        this.field = field;
    }

}
