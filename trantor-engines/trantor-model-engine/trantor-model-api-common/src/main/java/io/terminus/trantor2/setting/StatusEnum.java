package io.terminus.trantor2.setting;

/**
 * 状态枚举值约定
 */
public enum StatusEnum {
    DRAFT("草稿中"),
    INACTIVE("未启用"),
    ENABLED("已启用"),
    DISABLED("已停用"),
    DELETED("已删除");

    StatusEnum(String label) {
        this.label = label;
    }

    private String label;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
