package io.terminus.trantor2.model.common.consts;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
public enum DataQueryType {

    COUNT,
    AGGREGATE,
    PAGING,
    FIND,
    FIND_ONE,
    FIND_BY_ID,
    CREATE,
    BATCH_CREATE,
    UPDATE_BY_ID,
    BATCH_UPDATE_BY_ID,
    DELETE_BY_ID,
    BATCH_DELETE_BY_ID;

    private static final Set<DataQueryType> MESSAGE_AWARE_TYPE = new HashSet<>(
        Arrays.asList(
            CREATE,
            BATCH_CREATE,
            UPDATE_BY_ID,
            BATCH_UPDATE_BY_ID,
            DELETE_BY_ID,
            BATCH_DELETE_BY_ID
        )
    );

    /**
     * 当前类型是否可以发消息
     *
     * @param type
     * @return true:是；false：否
     */
    public static boolean couldSendMessage(DataQueryType type) {
        return MESSAGE_AWARE_TYPE.contains(type);
    }

    /**
     * 当前类型是否可以发Before消息
     *
     * @return true:是；false：否
     */
    public boolean needBeforeMessage() {
        return this == DataQueryType.UPDATE_BY_ID
            || this == DataQueryType.BATCH_UPDATE_BY_ID
            || this == DataQueryType.DELETE_BY_ID
            || this == DataQueryType.BATCH_DELETE_BY_ID;
    }

    /**
     * 当前类型是否可以发After消息
     *
     * @return true:是；false：否
     */
    public boolean needAfterMessage() {
        return this == DataQueryType.CREATE
            || this == DataQueryType.BATCH_CREATE
            || this == DataQueryType.UPDATE_BY_ID
            || this == DataQueryType.BATCH_UPDATE_BY_ID
            || this == DataQueryType.BATCH_DELETE_BY_ID;
    }

    /**
     * 当前请求是否为查询请求
     *
     * @return true:是；false：否
     */
    public boolean isSelect() {
        return this == COUNT
            || this == PAGING
            || this == FIND
            || this == FIND_ONE
            || this == FIND_BY_ID;
    }

    /**
     * 当前请求是否为更新请求
     *
     * @return true:是；false：否
     */
    public boolean isUpdate() {
        return this == UPDATE_BY_ID || this == BATCH_UPDATE_BY_ID;
    }

    /**
     * 当前请求是否为删除请求
     *
     * @return true:是；false：否
     */
    public boolean isDelete() {
        return this == DELETE_BY_ID || this == BATCH_DELETE_BY_ID;
    }

    /**
     * 当前请求是否为创建请求
     *
     * @return true:是；false：否
     */
    public boolean isCreate() {
        return this == CREATE || this == BATCH_CREATE;
    }
}
