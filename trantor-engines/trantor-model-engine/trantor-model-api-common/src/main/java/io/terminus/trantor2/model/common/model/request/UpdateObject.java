package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.UpdateOperation;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * 单条更新
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class UpdateObject extends CountRequest implements UpdateOperation {

    private static final long serialVersionUID = 6641706037882631502L;

    /**
     * id字段必填，没传的字段不会更新
     */
    private Map<String, Object> data;

    /**
     * 设置ID值
     *
     * @param id id值
     */
    public void setId(Object id) {
        data.put("id", id);
    }

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":" + data.keySet() + ":"
            + super.calculateObjectKey();
    }
}
