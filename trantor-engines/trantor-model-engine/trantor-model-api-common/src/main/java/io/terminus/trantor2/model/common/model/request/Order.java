package io.terminus.trantor2.model.common.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 排序字段
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "排序规则")
@Data
public class Order implements Serializable {

    private static final long serialVersionUID = 4159135084312148334L;

    @Schema(description = "字段")
    private String field;

    @Schema(description = "是否升序", defaultValue = "false", example = "false")
    private boolean asc;

}
