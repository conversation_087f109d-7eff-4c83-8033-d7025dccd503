package io.terminus.trantor2.model.common.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单条 SQL 数据执行时使用
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SqlApiRequest extends SqlApiBaseRequest {
    /**
     * 单条记录参数
     */
    private List<Object> parameters = new ArrayList<>();
}
