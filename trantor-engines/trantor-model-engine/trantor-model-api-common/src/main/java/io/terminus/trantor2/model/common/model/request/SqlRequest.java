package io.terminus.trantor2.model.common.model.request;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * SqlRequest
 *
 * <AUTHOR> Created on 2023/8/3 17:27
 */
@Setter
@Getter
public class SqlRequest implements Serializable {
    private static final long serialVersionUID = -143491731296494013L;

    /**
     * 选择数据源时使用
     */
    @NotNull
    private Long teamId;

    /**
     * 选择数据源时使用
     */
    @NotNull
    private String moduleKey;

    /**
     * 用于获取模型元数据使用，比如加字段加密 & 解密、脱敏等，必传字段
     */
    private String modelKey;

    /**
     * 待执行 SQL, 允许带命名的占位符
     */
    @NotNull
    private String sql;

    /**
     * 待执行 SQL 参数，根据 key 渲染 SQL 中的占位符
     */
    private Map<String, Object> parameters = new HashMap<>();
}
