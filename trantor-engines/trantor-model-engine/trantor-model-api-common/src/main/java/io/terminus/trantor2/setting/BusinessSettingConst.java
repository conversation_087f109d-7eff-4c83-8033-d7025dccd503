package io.terminus.trantor2.setting;

public class BusinessSettingConst {
    /**
     * 业务配置字段
     */
    public static final String FIELD_ID = "id";
    public static final String FIELD_GROUP_KEY = "groupKey";
    public static final String FIELD_GROUP_NAME = "groupName";
    public static final String FIELD_CODE = "code";
    public static final String FIELD_NAME = "name";
    public static final String FIELD_VALUE = "value";
    public static final String FIELD_DESCRIPTION = "description";
    public static final String FIELD_PROPERTIES = "properties";

    public static final String FIELD_ORIGIN_ORG_ID = "originOrgId";

    public static final String FIELD_DELETED = "deleted";

    public static final String FIELD_PARENT_CODE = "parentCode";

    public static final String FIELD_STATUS = "status";

    public static final String FIELD_CREATED_BY = "createdBy";

    public static final String FIELD_UPDATED_BY = "updatedBy";

    public static final String FIELD_CREATED_AT = "createdAt";

    public static final String FIELD_UPDATED_AT = "updatedAt";

    /**
     * 业务配置底层存储 key 后缀
     */
    public static final String SETTING_SUFFIX = "_settings";
}
