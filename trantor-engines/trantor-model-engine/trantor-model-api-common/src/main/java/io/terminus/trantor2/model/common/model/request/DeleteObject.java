package io.terminus.trantor2.model.common.model.request;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 删除
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class DeleteObject extends CountRequest {

    private static final long serialVersionUID = -1813718290728383842L;

    /**
     * 此条件与里层的 conditionGroups 互斥，根据 ids 或者 conditionGroups 条件进行数据删除
     */
    private List<Object> ids;

    /*
     * 是否物理删除
     */
    private boolean physicalDelete;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":"
            + super.calculateObjectKey();
    }

}
