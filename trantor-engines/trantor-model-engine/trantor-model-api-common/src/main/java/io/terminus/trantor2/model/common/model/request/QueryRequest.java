package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.QueryOperation;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 查询
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class QueryRequest extends CountRequest implements QueryOperation {

    private static final long serialVersionUID = 7413590706257060355L;

    /**
     * 查询字段
     */
    private List<Select> select;

    /**
     * 排序
     */
    private List<Order> orderBy;

    /**
     * 返回的数据是否需要 i18n
     */
    private boolean dataI18n;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":"
            + select
            + orderBy
            + super.calculateObjectKey();
    }

}
