package io.terminus.trantor2.model.common.model.condition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/30
 */
@Schema(description = "过滤条件值对象")
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ConditionValue implements Serializable {
    private static final long serialVersionUID = 6681603696624284908L;

    @Schema(description = "匹配值或者范围左值")
    private Object value;

    @Schema(description = "范围右值")
    private Object rightValue;

    @Schema(description = "匹配多值, 适用于 IN 和 NOT_IN")
    private List<Object> values;

    @Schema(description = "值类型")
    private ConditionValueType type;

    @JsonIgnore
    public boolean isFieldTypeConditionValue() {
        return type == ConditionValueType.FIELD;
    }

}
