package io.terminus.trantor2.model.common.model.request;

import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.model.common.model.UpdateOperation;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 批量更新
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@ToString(callSuper = true)
public class BatchUpdateObject extends BasicObject implements UpdateOperation {

    private static final long serialVersionUID = -5297885967746273316L;

    /**
     * 模型数据列表，key：模型字段别名，value：字段值
     */
    private List<Map<String, Object>> dataList;

    /**
     * 方法唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        return this.getClass().getSimpleName() + ":" + dataList.get(0).keySet();
    }

}
