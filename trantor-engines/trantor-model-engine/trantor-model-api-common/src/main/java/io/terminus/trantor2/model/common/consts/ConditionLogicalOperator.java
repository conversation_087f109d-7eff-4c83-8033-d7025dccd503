package io.terminus.trantor2.model.common.consts;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/8/2
 */
@Schema(description = "逻辑操作符", enumAsRef = true)
public enum ConditionLogicalOperator {

    /**
     * AND条件连接符
     */
    AND("And"),
    /**
     * OR条件连接符
     */
    OR("Or");

    @Getter
    private final String methodAlias;

    ConditionLogicalOperator(String word) {
        this.methodAlias = word;
    }

    /**
     * 获取模型连接符
     *
     * @param name
     * @return 连接符
     */
    public static ConditionLogicalOperator find(String name) {
        for (ConditionLogicalOperator value : values()) {
            if (value.methodAlias.equalsIgnoreCase(name)) {
                return value;
            }
        }
        return null;
    }

}
