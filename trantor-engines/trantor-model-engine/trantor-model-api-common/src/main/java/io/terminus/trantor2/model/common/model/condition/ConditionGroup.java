package io.terminus.trantor2.model.common.model.condition;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 条件组
 *
 * <AUTHOR>
 * @since 2022/8/2
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConditionGroup extends Condition {
    private static final long serialVersionUID = -7959927809166734843L;

    @Schema(description = "条件组")
    private List<Condition> conditions;

    public ConditionGroup(
        List<Condition> conditions,
        ConditionLogicalOperator nextOperator) {
        super(nextOperator);
        this.conditions = conditions;
    }

    /**
     * 计算实际条件字段数量
     *
     * @return 条件字段数量
     */
    @Override
    public int calculateConditionFieldSize() {
        if (CollectionUtils.isEmpty(conditions)) {
            return 0;
        }
        return conditions.stream()
            .map(Condition::calculateConditionFieldSize)
            .reduce(0, Integer::sum);
    }

    /**
     * 条件层级只有一级，同时每个条件组内只有单个条件
     *
     * @return true：是；false：否
     */
    public boolean checkIsSimpleCondition() {
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }
        return conditions.stream()
            .allMatch(condition -> condition instanceof ConditionGroup
                && ((ConditionGroup) condition).getConditions().size() == 1
                && ((ConditionGroup) condition).getConditions().stream()
                .allMatch(it -> it instanceof SingleCondition
                    && !((SingleCondition) it).getType().isJsonCondition()
                    && !isFieldTypeConditionValue(((SingleCondition) it).getValue()))
            );
    }

    public static boolean isFieldTypeConditionValue(ConditionValue value) {
        return value != null && value.getType() == ConditionValueType.FIELD;
    }

    public static ConditionGroup buildIdCondition(Object id) {
        if (id == null) {
            return null;
        }

       return new ConditionGroup(Collections.singletonList(new SingleCondition("id", ConditionType.EQ, ConditionValue.builder().value(id).build())));
    }

    /**
     * 提取去除value之后的关键信息，用于标识同一种类型的条件结构
     *
     * @return 唯一标识
     */
    @Override
    public String calculateObjectKey() {
        if (CollectionUtils.isEmpty(conditions)) {
            return "";
        }
        String conditionKey = conditions.stream()
            .map(Condition::calculateObjectKey)
            .collect(Collectors.joining(";"));
        return conditionKey + ":" + nextOperator;
    }

    /**
     * Condition内容替换
     *
     * @param beforeCondition
     * @param afterCondition
     */
    public void replaceCondition(Condition beforeCondition, ConditionGroup afterCondition) {
        for (int i = 0; i < conditions.size(); i++) {
            if (conditions.get(i).equals(beforeCondition)) {
                conditions.set(i, afterCondition);
            }
        }
    }
}
