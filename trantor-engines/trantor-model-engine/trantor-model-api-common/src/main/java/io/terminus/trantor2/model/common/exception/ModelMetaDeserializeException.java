package io.terminus.trantor2.model.common.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * 模型元信息反序列化异常
 */
@Deprecated
public class ModelMetaDeserializeException extends TrantorBizException {
    /**
     * 构造器
     *
     * @param message
     */
    public ModelMetaDeserializeException(String message) {
        super(ErrorType.MODEL_DESERIALIZE_ERROR, message, new Object[]{message});
    }

    /**
     * ModelMetaDeserializeException构造器
     *
     * @param message
     * @param e
     */
    public ModelMetaDeserializeException(String message, Throwable e) {
        super(ErrorType.MODEL_DESERIALIZE_ERROR, message, e, new Object[]{message});
    }
}
