package io.terminus.trantor2.setting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 模块下的持久化业务配置模型
 */
@Data
@Builder
public class BusinessSetting implements Serializable {
    @Schema(title = "自增 id")
    private Long id;

    @Schema(title = "配置标识")
    private String groupKey;

    @Schema(title = "配置名称")
    private String groupName;

    @Schema(title = "配置项编码")
    private String code;

    @Schema(title = "配置项名称")
    private String name;

    @Schema(title = "配置项值")
    private String value;

    @Schema(title = "配置项备注")
    private String description;

    @Schema(title = "配置项属性")
    private Map<String, Object> properties;

    @Schema(title = "父配置项 code")
    private String parentCode;

    @Schema(title = "逻辑删标识")
    private boolean deleted;

    @Schema(title = "版本号")
    protected int version;

    @Schema(title = "创建人")
    protected Long createdBy;

    @Schema(title = "创建时间")
    protected Date createdAt;

    @Schema(title = "更新人")
    protected Long updatedBy;

    @Schema(title = "更新时间")
    protected Date updatedAt;
}
