package io.terminus.trantor2.model.common.response;

import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AggregateRowMapper implements RowMapper<Object> {
    private final List<String> groupByFields;
    private final Map<String, Object> groupResult;

    public AggregateRowMapper(List<String> groupByFields) {
        this.groupByFields = groupByFields;
        this.groupResult = new HashMap<>();
    }

    @Override
    public Object mapRow(ResultSet rs, int rowNum) throws SQLException {
        // 如果有分组，将每一行结果转换为Map并添加到结果列表中
        Map<String, Object> rowResult = new HashMap<>();

        // 添加分组字段
        for (String field : groupByFields) {
            rowResult.put(field, rs.getObject(field));
        }

        // 添加聚合结果
        rowResult.put("aggregateValue", rs.getObject("aggregateValue")); // 使用实际的聚合列名

        return rowResult;
    }
}
