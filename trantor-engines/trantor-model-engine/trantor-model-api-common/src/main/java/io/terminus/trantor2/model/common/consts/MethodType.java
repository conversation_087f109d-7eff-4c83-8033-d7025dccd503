package io.terminus.trantor2.model.common.consts;

import io.terminus.trantor2.model.common.model.request.AggregateQueryRequest;
import io.terminus.trantor2.model.common.request.ModelRequestContext;
import io.terminus.trantor2.model.common.response.AggregateRowMapper;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2022/9/1
 */
public enum MethodType {

    QUERY("findBy", "SELECT  ", Collection.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            return "Ljava/util/Collection<L" + modelClassPath + ";>";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            return new BeanPropertyRowMapper(clazz);
        }
    }, COUNT("countBy", "SELECT COUNT(*) FROM ", Long.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            return "Ljava/lang/Long";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            return new SingleColumnRowMapper(Long.class);
        }
    }, AGGREGATE("none", "SELECT ", Collection.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            if (ModelRequestContext.getContext() instanceof AggregateQueryRequest aggregateQueryRequest) {
                if (CollectionUtils.isEmpty(aggregateQueryRequest.getGroupBy())) {
                    return "Ljava/lang/Object";
                } else {
                    return "Ljava/util/List;";
                }
            }
            return "Ljava/lang/Object";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            if (ModelRequestContext.getContext() instanceof AggregateQueryRequest aggregateQueryRequest) {
                if (CollectionUtils.isEmpty(aggregateQueryRequest.getGroupBy())) {
                    return new SingleColumnRowMapper(Object.class);
                } else {
                    return new AggregateRowMapper(aggregateQueryRequest.getGroupBy());
                }
            }

            return new SingleColumnRowMapper(Object.class);
        }
    }, INSERT("none", "INSERT INTO ", Boolean.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            return "Ljava/lang/Boolean";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            return new BeanPropertyRowMapper(Boolean.class);
        }
    }, UPDATE("none", "UPDATE ", Boolean.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            return "Ljava/lang/Boolean";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            return new BeanPropertyRowMapper(Boolean.class);
        }
    }, DELETE("none", "DELETE FROM ", Boolean.class) {
        @Override
        public String getReturnType(String modelClassPath) {
            return "Ljava/lang/Boolean";
        }

        @Override
        public RowMapper<Object> getRowMapper(Class<?> clazz) {
            return new BeanPropertyRowMapper(Boolean.class);
        }
    };

    @Getter
    private final String methodName;

    @Getter
    private final String sqlPrefix;

    @Getter
    private final Class<?> returnClassType;

    /**
     * 请求返回类型
     *
     * @param modelClassPath
     * @return 返回类型全限定类名
     */
    public String getReturnType(String modelClassPath) {
        throw new UnsupportedOperationException();
    }

    /**
     * 不同请求类型对应的rowMapper获取
     *
     * @param clazz
     * @return rowMapper
     */
    public RowMapper<Object> getRowMapper(Class<?> clazz) {
        throw new UnsupportedOperationException();
    }

    MethodType(String methodName, String sqlPrefix, Class<?> returnClassType) {
        this.methodName = methodName;
        this.sqlPrefix = sqlPrefix;
        this.returnClassType = returnClassType;
    }


}
