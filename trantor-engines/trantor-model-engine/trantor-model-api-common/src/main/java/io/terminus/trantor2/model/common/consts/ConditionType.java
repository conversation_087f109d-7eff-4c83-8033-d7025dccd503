package io.terminus.trantor2.model.common.consts;

import lombok.Getter;

import static io.terminus.trantor2.model.common.consts.ConditionType.ConditionValueType.*;

/**
 * <AUTHOR>
 * @since 2022/8/2
 */
public enum ConditionType {

    EQ("=", MATCH),
    NEQ("!=", MATCH),
    IN("in", COLLECTION),
    NOT_IN("not in", COLLECTION),
    LT("<", MATCH),
    LTE("<=", MATCH),
    GT(">", MATCH),
    GTE(">=", MATCH),
    NOT_CONTAINS("not like", MATCH),
    CONTAINS("like", MATCH),
    START_WITH("like", MATCH),
    END_WITH("like", MATCH),
    IS_NULL("is null", NONE),
    IS_NOT_NULL("is not null", NONE),
    BETWEEN_AND("between", ConditionValueType.RANGE),
    NOT_BETWEEN_AND("not between", ConditionValueType.RANGE),
    RANGE("range", ConditionValueType.RANGE),
    OUT_OF_RANGE("out of range", ConditionValueType.RANGE),
    JSON_CONTAINS("json_contains", MATCH),
    JSON_NOT_CONTAINS("json_contains", MATCH), JSON_LENGTH("json_length", MATCH);

    @Getter
    private final String word;

    @Getter
    private final ConditionValueType valueType;

    ConditionType(String word, ConditionValueType valueType) {
        this.word = word;
        this.valueType = valueType;
    }

    public enum ConditionValueType {

        MATCH(1), RANGE(2), NONE(0), COLLECTION(1);

        @Getter
        private final int conditionSize;

        ConditionValueType(int conditionSize) {
            this.conditionSize = conditionSize;
        }
    }

    /**
     * 是否为json函数查询
     *
     * @return true：是；false：否
     */
    public boolean isJsonCondition() {
        return this == JSON_CONTAINS || this == JSON_NOT_CONTAINS || this == JSON_LENGTH;
    }

}
