<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-model-engine</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-model-api-common</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jdbc</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-api-common</artifactId>
        </dependency>

        <!-- 原有模型引擎管理端的数据结构定义均移至 trantor-lang -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-lang</artifactId>
        </dependency>
        <!-- api 依赖 -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-api-common</artifactId>
        </dependency>
        <!-- 条件过滤依赖 -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-condition-api</artifactId>
        </dependency>
    </dependencies>
</project>
