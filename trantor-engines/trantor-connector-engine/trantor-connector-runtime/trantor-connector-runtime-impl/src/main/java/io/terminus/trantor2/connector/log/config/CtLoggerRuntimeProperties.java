package io.terminus.trantor2.connector.log.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * 线程池属性
 */
@Data
@ConfigurationProperties(prefix = "trantor2.connector.logger")
public class CtLoggerRuntimeProperties {



    /**
     * 线程池参数
     */
    @NestedConfigurationProperty
    private ExecutorProperties executor = new ExecutorProperties();

    /**
     * 日志轮转配置
     */
    @NestedConfigurationProperty
    private LogrotateProperties logrotate = new LogrotateProperties();


    @Data
    public static class ExecutorProperties {

        private Integer corePoolSize = 5;

        private Integer maxPoolSize = 10;

        private Integer keepAlive = 60;

        private Integer queueCapacity = 20;

        private Integer awaitTerminationSeconds = 60;
    }

    @Data
    public static class LogrotateProperties {

        /**
         * 备份大小
         */
        private Integer backUpSize = 7;

        /**
         * 轮转表达式(默认为每天3点)
         */
        private String rotateCron = "0 0 3 * * ?";


        /**
         * 每个表的limit大小（10w)
         */
        private Long tableSizeLimit = 100000L;
    }

}
