package io.terminus.trantor2.connector.log.config;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Throwables;
import io.terminus.trantor2.connector.log.CtRuntimeLogger;
import io.terminus.trantor2.connector.log.constants.CtLogConstants;
import io.terminus.trantor2.connector.log.logger.CtLogger;
import io.terminus.trantor2.connector.log.utils.CtLogHelper;
import io.terminus.trantor2.connector.repo.CtLogRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties(CtLoggerRuntimeProperties.class)
@ConditionalOnProperty(value = "trantor2.connector.logger.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class CtLoggerRuntimeConfig implements InitializingBean, DisposableBean {

    @Resource
    private CtLoggerRuntimeProperties ctLoggerRuntimeProperties;


    @Resource
    private CtLogRepo ctLogRepo;


    @Resource
    private RedissonClient redissonClient;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private CtLogHelper ctLogHelper;

    private ThreadPoolTaskScheduler taskScheduler;

    private ThreadPoolTaskExecutor logExecutor;


    @Bean
    @Lazy
    public CtLogger ctRuntimeLogger() {
        log.info("==> Using runtimeLogger");
        return new CtRuntimeLogger(ctLogRepo, logExecutor, ctLogHelper);
    }

    @Override
    public void afterPropertiesSet(){
        taskScheduler = getTaskScheduler();
        logExecutor = getLogExecutor();
        log.info("init logrotate task");
        CronTrigger cronTrigger = new CronTrigger(ctLoggerRuntimeProperties.getLogrotate().getRotateCron());
        taskScheduler.schedule(() -> {
            RLock lock = redissonClient.getLock(CtLogConstants.RECYCLE_CT_LOGGER_LOCK_KEY);
            try {
                boolean getLock = lock.tryLock(60, 180, TimeUnit.SECONDS);
                if (getLock) {
                    try {
                        logrotate();
                    } catch (Exception e) {
                        log.error("recycleLogTable err: {}", Throwables.getStackTraceAsString(e));
                    } finally {
                        if (lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("redissonClient tryLock err: {}", Throwables.getStackTraceAsString(e));
            }
        }, cronTrigger);
    }



    @Override
    public void destroy() {
        if (taskScheduler != null){
            taskScheduler.shutdown();
        }
        if (logExecutor != null){
            logExecutor.shutdown();
        }
    }

    private void renameTable() {
        String newTableName = StrUtil.format("{}_{}", CtLogConstants.CT_LOG_TABLE_NAME, DateUtil.format(new Date(), CtLogConstants.CT_LOG_BACKUP_NAME_FORMAT));
        jdbcTemplate.execute(StrUtil.format("RENAME TABLE {} TO {}", CtLogConstants.CT_LOG_TABLE_NAME, newTableName));
        log.info("rename table  {} --> {} success!", CtLogConstants.CT_LOG_TABLE_NAME, newTableName);
    }


    private void removeUnUseTable() {
        List<Map<String, Object>> rows = jdbcTemplate.queryForList(StrUtil.format("SHOW TABLES LIKE '{}_%'", CtLogConstants.CT_LOG_TABLE_NAME));
        if (CollectionUtils.isEmpty(rows)) return;
        // reverse sort tableName list by tableNameSuffix
        List<String> tableNamesDescByDate = rows.stream()
            .map(Map::values)
            .flatMap(Collection::stream)
            .sorted(Comparator.comparing(tableNameValue -> {
                String tableName = String.valueOf(tableNameValue);
                String tableDate = tableName.replaceAll(CtLogConstants.CT_LOG_TABLE_NAME + "_", "");
                try {
                    return DateUtil.parse(tableDate, CtLogConstants.CT_LOG_BACKUP_NAME_FORMAT);
                } catch (Exception e) {
                    log.error("removeUnUseTable parse table {} err: {}", tableName, Throwables.getStackTraceAsString(e));
                    // roll back to 0
                    return DateTime.of(0);
                }
            }).reversed()).map(String::valueOf).collect(Collectors.toList());
        if (tableNamesDescByDate.size() > ctLoggerRuntimeProperties.getLogrotate().getBackUpSize()) {
            for (int i = ctLoggerRuntimeProperties.getLogrotate().getBackUpSize(); i < tableNamesDescByDate.size(); i++) {
                jdbcTemplate.execute(String.format("Drop table %s", tableNamesDescByDate.get(i)));
                log.info("drop table {} success!", tableNamesDescByDate.get(i));
            }
        }
    }


    private ThreadPoolTaskExecutor getLogExecutor() {
        log.info("init connector log executor");
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        CtLoggerRuntimeProperties.ExecutorProperties executor = ctLoggerRuntimeProperties.getExecutor();
        threadPoolTaskExecutor.setCorePoolSize(executor.getCorePoolSize());
        threadPoolTaskExecutor.setMaxPoolSize(executor.getMaxPoolSize());
        threadPoolTaskExecutor.setQueueCapacity(executor.getQueueCapacity());
        threadPoolTaskExecutor.setKeepAliveSeconds(executor.getKeepAlive());
        threadPoolTaskExecutor.setThreadNamePrefix("ctLgExecutor--");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(executor.getAwaitTerminationSeconds());
        //多余的线程忽略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        //初始化线程池
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    private ThreadPoolTaskScheduler getTaskScheduler() {
        log.info("init connector logrotate scheduler executor");
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.setPoolSize(1);
        threadPoolTaskScheduler.initialize();
        return threadPoolTaskScheduler;
    }

    private void logrotate() {
        log.info("recycleLogTable at: {}", new Date());
        //todo:判断是否需要进行
        long total = ctLogRepo.count();
        if (total < ctLoggerRuntimeProperties.getLogrotate().getTableSizeLimit()) {
            log.info("ctLog table's size is {} < {},skip", total, ctLoggerRuntimeProperties.getLogrotate().getTableSizeLimit());
            return;
        }
        //rename old table
        renameTable();
        //create new table
        ctLogHelper.createNewTable();
        //check need to delete table
        removeUnUseTable();
    }


}
