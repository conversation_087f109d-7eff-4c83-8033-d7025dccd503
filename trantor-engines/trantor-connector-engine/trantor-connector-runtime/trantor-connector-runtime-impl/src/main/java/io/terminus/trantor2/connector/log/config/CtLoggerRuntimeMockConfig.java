package io.terminus.trantor2.connector.log.config;

import io.terminus.trantor2.connector.log.logger.CtLogger;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = "trantor2.connector.logger.enabled", havingValue = "false")
public class CtLoggerRuntimeMockConfig {
    @Bean
    public CtLogger ctRuntimeLogger() {
        return ctLogData -> {
        };
    }
}
