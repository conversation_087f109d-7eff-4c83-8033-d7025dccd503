package io.terminus.trantor2.connector.log;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.connector.log.logger.CtLogger;
import io.terminus.trantor2.connector.log.models.CtLog;
import io.terminus.trantor2.connector.log.models.data.CtLogData;
import io.terminus.trantor2.connector.log.utils.CtLogHelper;
import io.terminus.trantor2.connector.repo.CtLogRepo;
import io.terminus.trantor2.connector.utils.CtContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.BadSqlGrammarException;

import java.util.Date;
import java.util.concurrent.Executor;

//@Component
//@RequiredArgsConstructor

@Slf4j
public class CtRuntimeLogger implements CtLogger {

    private final CtLogRepo ctLogRepo;

    private final Executor executor;

    private final CtLogHelper ctLogHelper;


    public CtRuntimeLogger(CtLogRepo ctLogRepo, Executor executor, CtLogHelper ctLogHelper) {
        this.ctLogRepo = ctLogRepo;
        this.executor = executor;
        this.ctLogHelper = ctLogHelper;


    }


    public void doLog(CtLogData ctLogData) {
        Date logAt = new Date();
        String reqId = CtContext.getReqId();
        asyncExecuteWithContext(() -> {
            //lazy_handler:  use serialize in thread to make current thread no-blocking
            CtLog ctLog = CtLog.builder()
                .ctLogStage(ctLogData.stage())
                .reqId(reqId)
                .logAt(logAt)
                .data(ctLogData)
                .build();
            try {
                ctLogRepo.save(ctLog);
            } catch (BadSqlGrammarException bex) {
                //CREATE TABLE IF NOT EXISTS
                ctLogHelper.createNewTable();
                ctLogRepo.save(ctLog);
            }
        });
    }

    /**
     * copy trantorContext to allow run correctly
     *
     * @param runnable to run
     */
    private void asyncExecuteWithContext(Runnable runnable) {
        TrantorContext.Context context = TrantorContext.getContext();
        executor.execute(() -> {
            TrantorContext.setContext(context);
            runnable.run();
        });
    }


}

