package io.terminus.trantor2.connector.comp.http.hook;

public class HttpExecutorHookProxy {


    private final HttpExecutorHook targetHttpExecutorHook;

    public HttpExecutorHookProxy(HttpExecutorHook httpExecutorHook) {
        this.targetHttpExecutorHook = httpExecutorHook;
    }

    public void registerBeforeExecuteHook(HttpBeforeExecuteHook httpBeforeExecuteHook) {
        this.targetHttpExecutorHook.registerBeforeExecute(httpBeforeExecuteHook);
    }

    public void registerAfterExecuteHook(HttpAfterExecuteHook httpAfterExecuteHook) {
        this.targetHttpExecutorHook.registerAfterExecute(httpAfterExecuteHook);
    }


}
