package io.terminus.trantor2.connector.comp.http.hook;

import io.terminus.trantor2.connector.model.meta.http.HttpRequestProxy;
import io.terminus.trantor2.connector.model.meta.http.HttpResponse;

public class HttpExecutorHook {

    private HttpBeforeExecuteHook httpBeforeExecuteHook;

    private HttpAfterExecuteHook httpAfterExecuteHook;

    public HttpExecutorHook() {
    }

    public void registerBeforeExecute(HttpBeforeExecuteHook httpBeforeExecuteHook) {
        this.httpBeforeExecuteHook = httpBeforeExecuteHook;
    }

    public void registerAfterExecute(HttpAfterExecuteHook httpAfterExecuteHook) {
        this.httpAfterExecuteHook = httpAfterExecuteHook;
    }

    public void triggerBeforeExecute(HttpRequestProxy httpRequestProxy) {
        if (httpBeforeExecuteHook != null) {
            httpBeforeExecuteHook.execute(httpRequestProxy);
        }
    }

    public void triggerAfterExecute(HttpResponse httpResponse){
        if (httpAfterExecuteHook != null) {
            httpAfterExecuteHook.execute(httpResponse);
        }
    }


}
