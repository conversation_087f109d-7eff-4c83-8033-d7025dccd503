package io.terminus.trantor2.connector.repo;

import io.terminus.trantor2.connector.log.models.CtLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
@SuppressWarnings("unused")
public interface CtLogRepo extends JpaRepository<CtLog, Long>, JpaSpecificationExecutor<CtLog> {

    CtLog findByReqId(String reqId);


}
