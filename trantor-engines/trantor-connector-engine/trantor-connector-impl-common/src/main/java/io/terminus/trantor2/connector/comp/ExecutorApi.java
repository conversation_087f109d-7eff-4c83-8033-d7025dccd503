package io.terminus.trantor2.connector.comp;

import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.meta.CtTempType;

import java.util.Map;

/**
 * （连接器）执行api，各模版类型单独实现
 */

public interface ExecutorApi {
    /**
     * 模版类型
     *
     * @return 模版类型
     */
    CtTempType getTempType();

    /**
     * 模版类型-执行
     *
     * @param temp        模版
     * @param api         服务
     * @param inst        实例
     * @param inputValues 入参
     * @return response objø
     */
    Object execute(CtTemp temp, CtApi api, CtInst inst, Map<String, Object> inputValues);
}
