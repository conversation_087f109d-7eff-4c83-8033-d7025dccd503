package io.terminus.trantor2.connector.service;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.connector.log.logger.CtLogger;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.module.meta.CtInstConfig;

/**
 * 连接器调用上下文接口
 */
public interface CtEnvHolder {
    /**
     * 获取连接器实例
     *
     * @param instKey 连接器实例key
     * @return 连接器实例
     */
    CtInst getInst(String instKey);

    /**
     * 获取连接器实例-环境参数配置
     *
     * @param teamId teamId
     * @param instModuleKey 连接器 module key(parent key)
     * @return 连接器实例-环境参数配置
     */
    CtInstConfig getInstConfig(Long teamId, String instModuleKey);

    /**
     * 是否是运行态
     * @return 运行态?
     */
    boolean isRuntime();

    /**
     * 返回日志实例
     *
     * @return 日志实例
     */
    CtLogger getLogger();

    /**
     * response转换
     *
     * @param response response
     * @return response
     */
    default Response<Object> convert(Response<Object> response) {
        return response;
    }
}
