package io.terminus.trantor2.connector.comp;

import io.terminus.trantor2.connector.model.meta.CtTempType;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 执行器工厂
 */
@Component
public class ExecutorFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private Map<CtTempType, ExecutorApi> executorMap = null;

    /**
     * 根据模版返回执行器
     * @param tempType 模版类型
     * @return 执行器
     */
    public ExecutorApi getExecutor(CtTempType tempType) {
        return this.executorMap.get(tempType);
    }

    /**
     * 执行器初始化
     */
    @PostConstruct
    private void init() {
        this.executorMap = new HashMap<>();
        this.applicationContext.getBeansOfType(ExecutorApi.class)
            .values().forEach(executor -> this.executorMap.put(executor.getTempType(), executor));
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
