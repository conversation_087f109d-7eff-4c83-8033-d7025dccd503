package io.terminus.trantor2.connector.comp.http;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.meta.http.HttpExtParam;
import io.terminus.trantor2.connector.utils.CtContext;
import io.terminus.trantor2.connector.utils.ParamUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.http.HttpStatus;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Http基础组件，执行标准Http请求
 */
@Slf4j
@Component
public final class HttpBase implements DisposableBean {
    /**
     * http client 失效时间(ms), 2小时
     */
    private static final long EXPIRE_AFTER_ACCESS = 2 * 60 * 60 * 1000;
    /**
     * 固定任务定时执行, 60 minutes
     */
    private static final long FIX_SCHEDULE_TIME = 60;

    @RequiredArgsConstructor
    private static class HttpClientWrapper {
        @Getter
        private final String hash;
        private final CloseableHttpClient httpClient;
        private long lastAccessTime;

        public CloseableHttpClient getHttpClient() {
            this.lastAccessTime = System.currentTimeMillis();
            return httpClient;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - lastAccessTime >= EXPIRE_AFTER_ACCESS;
        }

        public void close(String key) {
            try {
                this.httpClient.close();
            } catch (IOException e) {
                log.warn("ct close httpclient error,name:{},msg:{}", key, e.getMessage());
            }
        }
    }

    private static final String DEFAULT_HTTP_CLIENT_NAME = "default";
    /**
     * 异步任务执行器
     */
    private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1);
    /**
     * http client map
     */
    private final Map<String, HttpClientWrapper> httpClientMap = new ConcurrentHashMap<>();
    /**
     * default http client
     */
    private final CloseableHttpClient defaultClient;

    public HttpBase() {
        HttpExtParam httpParam = HttpExtParam.builder()
            .singlePool(true)
            .maxTotal(200)
            .maxPerRoute(50)
            .connectTimeout(15)
            .socketTimeout(30)
            .connectionRequestTimeout(10)
            .build();
        defaultClient = createHttpClient(DEFAULT_HTTP_CLIENT_NAME, httpParam);
        //
        scheduledExecutor.scheduleAtFixedRate(this::removeExpiredClient,
            FIX_SCHEDULE_TIME, FIX_SCHEDULE_TIME, TimeUnit.MINUTES);
    }

    private void removeExpiredClient() {
        Set<String> expiredKeys = httpClientMap.keySet().stream()
            .filter(k -> httpClientMap.get(k).isExpired())
            .collect(Collectors.toSet());
        expiredKeys.forEach(k -> httpClientMap.remove(k).close(k));
        log.info("ct fixed task success.");
    }

    /**
     * create httpclient
     *
     * @param httpClientKey httpclient key
     * @param httpParam     param of httpclient
     * @return httpclient
     */
    private CloseableHttpClient createHttpClient(String httpClientKey, HttpExtParam httpParam) {
        log.info("reqId:{},start init http client,key:{}", CtContext.getReqId(), httpClientKey);
        try {
            SSLContextBuilder builder = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy());
            SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(builder.build());

            // 配置同时支持 HTTP 和 HTTPS
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", sslConnectionSocketFactory).build();
            // 初始化连接管理器
            PoolingHttpClientConnectionManager poolConnManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            // 将最大连接数增加到200，实际项目最好从配置文件中读取这个值
            poolConnManager.setMaxTotal(httpParam.getMaxTotal());
            // 设置最大路由
            poolConnManager.setDefaultMaxPerRoute(httpParam.getMaxPerRoute());
            // 根据默认超时限制初始化requestConfig
            RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(httpParam.getConnectionRequestTimeout() * 1000)
                .setSocketTimeout(httpParam.getSocketTimeout() * 1000)
                .setConnectTimeout(httpParam.getConnectTimeout() * 1000)
                .build();

            // 初始化httpClient
            CloseableHttpClient client = HttpClients.custom()
                // 设置连接池管理
                .setConnectionManager(poolConnManager)
                // 设置请求配置
                .setDefaultRequestConfig(requestConfig)
                // 设置重试次数
                .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                // 设置默认的CookieStore
                .build();
            log.info("reqId:{},ct init http client success,key:{}", CtContext.getReqId(), httpClientKey);
            return client;
        } catch (Exception e) {
            log.error("reqId:{},ct init http client error,key:{},msg:{}", CtContext.getReqId(), httpClientKey, e.getMessage());
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, e.getMessage(), e);
        }
    }

    /**
     * get httpclient from cache
     *
     * @param inst ct inst
     * @return http client
     */
    private CloseableHttpClient getHttpClient(CtInst inst) {
        if (inst.needSinglePool()) {
            return httpClientMap.compute(
                inst.fullKey(), (k, v) -> {
                    String hash = ParamUtil.hash(inst.httpExtParam());
                    if (Objects.isNull(v) || !Objects.equals(hash, v.getHash())) {
                        //create
                        CloseableHttpClient httpClient = createHttpClient(k, inst.httpExtParam());
                        if (!Objects.isNull(v)) {
                            //close
                            scheduledExecutor.execute(() -> v.close(k));
                        }
                        return new HttpClientWrapper(hash, httpClient);
                    }
                    return v;
                }).getHttpClient();
        } else {
            return defaultClient;
        }
    }

    public String get(CtInst inst, URI uri, Map<String, String> headers) {
        HttpGet get = new HttpGet(uri);
        HttpClientContext context = addHeader(get, headers);

        return execute(inst, get, context);
    }

    public String post(CtInst inst, URI uri, Map<String, String> headers, byte[] body) {
        HttpPost post = new HttpPost(uri);
        HttpClientContext context = addHeader(post, headers);
        post.setEntity(new ByteArrayEntity(body));

        return execute(inst, post, context);
    }

    public String put(CtInst inst, URI uri, Map<String, String> headers, byte[] body) {
        HttpPut put = new HttpPut(uri);
        HttpClientContext context = addHeader(put, headers);
        put.setEntity(new ByteArrayEntity(body));

        return execute(inst, put, context);
    }

    public String delete(CtInst inst, URI uri, Map<String, String> headers) {
        HttpDelete delete = new HttpDelete(uri);
        HttpClientContext context = addHeader(delete, headers);

        return execute(inst, delete, context);
    }

    public String head(CtInst inst, URI uri, Map<String, String> headers) {
        HttpHead head = new HttpHead(uri);
        HttpClientContext context = addHeader(head, headers);

        return execute(inst, head, context);
    }


    /**
     * add header, 处理Digest认证的header
     *
     * @param httpRequest http request
     * @param headers     headers
     * @return httpclient context
     */
    private HttpClientContext addHeader(HttpRequestBase httpRequest, Map<String, String> headers) {
        HttpClientContext context = null;
        //
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String name = entry.getKey();
                String value = entry.getValue();
                //
                if (name.equalsIgnoreCase("authorization") && value.startsWith("Digest ")) {
                    String[] tokens = value.substring(7).trim().split(" ");
                    //
                    CredentialsProvider provider = new BasicCredentialsProvider();
                    UsernamePasswordCredentials credentials = new UsernamePasswordCredentials(tokens[0], tokens[1]);
                    provider.setCredentials(AuthScope.ANY, credentials);
                    context = HttpClientContext.create();
                    context.setCredentialsProvider(provider);
                } else {
                    httpRequest.addHeader(name, value);
                }
            }
        }
        return context;
    }

    public String execute(CtInst inst, HttpRequestBase request, HttpClientContext httpClientContext) {
        StopWatch stopWatch = StopWatch.createStarted();
        try (CloseableHttpResponse response = getHttpClient(inst).execute(request, httpClientContext)) {
            stopWatch.stop();
            long costTime = stopWatch.getTime(TimeUnit.MILLISECONDS);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                String resBodyStr = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.error("reqId:{},ct http request fail,cost:{},resCode:{},{} {},res:{}",
                    CtContext.getReqId(), costTime, statusCode, request.getMethod(), request.getURI(), resBodyStr);
                throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, resBodyStr);
            }
            String resBodyStr = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            //
            log.info("reqId:{},ct http request success,cost:{}ms,\n{} {},\nres:{}", CtContext.getReqId(), costTime, request.getMethod(), request.getURI(), resBodyStr);
            CtContext.getLogger().log(response, resBodyStr);
            return resBodyStr;
        } catch (UnknownHostException | ConnectException ex) {
            //一般是服务端不可达,可能为服务间歇下机
            log.error("reqId:{},ct exec [server connect unreachable ]error,{} {},msg:{}", CtContext.getReqId(), request.getMethod(), request.getURI(), ex.getMessage());
            throw new CtException(ErrorType.CONNECTOR_INVOKE_UN_REACHABLE_ERROR, ex.getMessage(), ex);
        } catch (ConnectTimeoutException ex) {
            //连接超时 一般在建立连接阶段失败
            log.error("reqId:{},ct exec [server connect timeout ]error,{} {},msg:{}", CtContext.getReqId(), request.getMethod(), request.getURI(), ex.getMessage());
            throw new CtException(ErrorType.CONNECTOR_INVOKE_CONN_TIMEOUT_ERROR, ex.getMessage(), ex);
        } catch (SocketTimeoutException ex) {
            //socket读写超时 可能是读也可能是写 是否重试取决于场景
            log.error("reqId:{},ct exec [socket read or write timeout]error,{} {},msg:{}", CtContext.getReqId(), request.getMethod(), request.getURI(), ex.getMessage());
            throw new CtException(ErrorType.CONNECTOR_INVOKE_SOCKET_TIMEOUT_ERROR, ex.getMessage(), ex);
        } catch (IOException e) {
            //其他io
            log.error("reqId:{},ct exec error,{} {},msg:{}", CtContext.getReqId(), request.getMethod(), request.getURI(), e.getMessage());
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, e.getMessage(), e);
        } finally {
            if (!stopWatch.isStopped()) {
                stopWatch.stop();
            }
        }
    }

    /**
     * 回收清理
     */
    @Override
    public void destroy() {
        try {
            scheduledExecutor.shutdown();
        } catch (Exception ignored) {
        }
        try {
            defaultClient.close();
        } catch (IOException e) {
            log.warn("ct close httpclient error,name:{},msg:{}", DEFAULT_HTTP_CLIENT_NAME, e.getMessage());
        }
        httpClientMap.forEach((k, v) -> v.close(k));
        log.info("ct clean up over");
    }
}
