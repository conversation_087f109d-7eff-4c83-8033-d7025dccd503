package io.terminus.trantor2.connector.service;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.connector.comp.ExecutorApi;
import io.terminus.trantor2.connector.comp.ExecutorFactory;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.model.field.handler.FieldHandlerUtil;
import io.terminus.trantor2.connector.model.field.handler.MapHandler;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.meta.CtTempType;
import io.terminus.trantor2.connector.model.request.CtCallBaseRequest;
import io.terminus.trantor2.connector.model.request.CtCallRequest;
import io.terminus.trantor2.connector.model.request.CtDynCallRequest;
import io.terminus.trantor2.connector.utils.CtContext;
import io.terminus.trantor2.module.meta.CtInstConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 连接器服务执行入口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CtCallService {
    private final ExecutorFactory executorFactory;

    /**
     * 连接器(服务)执行入口
     *
     * @param request   调用请求
     * @param envHolder 当前执行环境(研发态/运行态)
     * @return 执行结果, 对于http请求，返回response content(string)
     */
    public Response<Object> doCall(CtCallBaseRequest request, CtEnvHolder envHolder) {
        String reqId = Optional.ofNullable(request).map(CtCallBaseRequest::getRequestId).orElse(null);
        if (StringUtils.isBlank(reqId)) {
            reqId = RandomStringUtils.randomAlphabetic(8);
            if (Objects.nonNull(request)) {
                request.setRequestId(reqId);
            }
        }
        log.info("reqId:{},ct exec,reqData:{}", reqId, request);
        //
        CtContext.init();
        CtContext.setReqId(reqId);
        CtContext.setRuntime(envHolder.isRuntime());
        CtContext.setLogger(envHolder.getLogger());
        CtContext.getLogger().log(request);
        StopWatch stopWatch = StopWatch.createStarted();
        Response<Object> response = null;
        try {
            response = invoke(request, envHolder);
            //
            CtContext.getLogger().log(response);
        } catch (CtException ex) {
            log.error("reqId:{},ct exec error", reqId, ex);
            response = Response.error(ex.getErrorType().getInnerCode(), ex.getMessage());
            CtContext.getLogger().log(ex);
        } catch (BusinessException bex) {
            log.error("reqId:{},ct exec error", reqId, bex);
            response = Response.error(bex.getErrorCode(), bex.getErrorMsg());
            //
            CtContext.getLogger().log(bex);
        } catch (Throwable ex) {
            log.error("reqId:{},ct exec error", reqId, ex);
            response = Response.error(ErrorType.SERVER_ERROR.getCode(), ex.getMessage());
            //
            CtContext.getLogger().log(ex);
        } finally {
            response = envHolder.convert(response);
            CtContext.clear();
        }
        stopWatch.stop();
        long costTime = stopWatch.getTime(TimeUnit.MILLISECONDS);
        response.setRequestId(reqId);
        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.info("reqId:{},ct exec,cost:{}ms,resData:{}", reqId, costTime, response);
            } else {
                log.info("reqId:{},ct exec,cost:{}ms", reqId, costTime);
            }
        } else {
            log.error("reqId:{},ct exec,cost:{}ms,reqData:{},resData:{}", reqId, costTime, request, response);
        }
        return response;
    }

    /**
     * 连接器(服务)-调用
     *
     * @param request   执行请求
     * @param envHolder 当前执行环境(研发态/运行态)
     * @return 执行结果
     */
    private Response<Object> invoke(CtCallBaseRequest request, CtEnvHolder envHolder) {
        if (Objects.isNull(request)) {
            log.error("ct exec, request is null");
            throw new CtException(ErrorType.BAD_REQUEST, "请求参数为空");
        }
        //
        request.checkParams();
        //inst
        CtInst inst = getInst(request, envHolder);
        if (!inst.getResourceProps().getEnabled()) {
            throw new CtException(ErrorType.CONNECTOR_INSTANCE_DISABLED, new String[]{inst.getKey()});
        }
        //temp
        CtTemp temp = inst.getResourceProps().getTemp();
        if (Objects.isNull(temp)) {
            throw new CtException(ErrorType.CONNECTOR_TEMPLATE_NOT_EXISTED, new String[]{inst.getKey()});
        }
        //api
        String apiKey = request.getApiKey();
        CtTempType tempType = temp.getTempType();
        CtApi ctApi = temp.getApi(apiKey);

        if (Objects.isNull(ctApi)) {
            throw new CtException(ErrorType.CONNECTOR_SERVICE_NOT_EXISTED,
                new String[]{temp.getKey() + ":" + apiKey});
        }
        //
        ExecutorApi executor = executorFactory.getExecutor(tempType);
        if (Objects.isNull(executor)) {
            throw new CtException(ErrorType.CONNECTOR_UNSUPPORTED_TEMPLATE_TYPE, new String[]{tempType.name()});
        }
        //
        if (Objects.isNull(ctApi.getInput())) {
            ctApi.setInput(new ArrayList<>());
        }
        //
        Map<String, Object> inputValues = Optional.ofNullable(request.getInput()).orElse(new HashMap<>());
        MapHandler.handle(ctApi.getInput(), inputValues, true);
        //
        Object res = executor.execute(temp, ctApi, inst, inputValues);
        res = FieldHandlerUtil.handle(ctApi.getOutput(), res, false);
        //
        return Response.ok(res);
    }

    /**
     * get ct inst
     *
     * @param request   request
     * @param envHolder 当前执行环境(研发态/运行态)
     * @return ct inst
     */
    private CtInst getInst(CtCallBaseRequest request, CtEnvHolder envHolder) {
        CtInst inst;
        if (request instanceof CtCallRequest) {
            //inst, 需要teamId，当前元数据接口无显式传递teamId接口，调用方需确保TrantorContext存在teamCode
            CtCallRequest ctCallRequest = (CtCallRequest) request;
            String instKey = ctCallRequest.getInstKey();
            inst = envHolder.getInst(instKey);
            if (Objects.isNull(inst)) {
                throw new CtException(ErrorType.CONNECTOR_INSTANCE_NOT_EXISTED, new String[]{instKey});
            }
            //加载inst config
            CtInstConfig config = envHolder.getInstConfig(inst.getTeamId(), inst.getParentKey());
            CtInst.CtInstProps props = inst.getResourceProps();
            props.setCtParams(config.getCtParams());
            props.setExtParams(config.getExtParams());
        } else {
            inst = ((CtDynCallRequest) request).getInst();
            if (Objects.isNull(inst.getResourceProps().getTemp())) {
                throw new ValidationException("模版不能为空");
            }
            inst.getResourceProps().setEnabled(true);
        }
        return inst;
    }
}
