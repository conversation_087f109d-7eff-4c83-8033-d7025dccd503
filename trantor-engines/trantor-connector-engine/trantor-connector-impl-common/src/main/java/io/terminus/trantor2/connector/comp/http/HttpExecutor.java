package io.terminus.trantor2.connector.comp.http;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.connector.comp.ExecutorApi;
import io.terminus.trantor2.connector.comp.http.hook.HttpExecutorHook;
import io.terminus.trantor2.connector.comp.http.hook.HttpExecutorHookProxy;
import io.terminus.trantor2.connector.convert.CommonMapper;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.exception.CtTokenInvalidException;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.field.CtObjectField;
import io.terminus.trantor2.connector.model.field.FormatType;
import io.terminus.trantor2.connector.model.field.handler.FieldHandlerUtil;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.meta.CtTempType;
import io.terminus.trantor2.connector.model.meta.auth.*;
import io.terminus.trantor2.connector.model.meta.http.*;
import io.terminus.trantor2.connector.utils.CtContext;
import io.terminus.trantor2.connector.utils.CtJsonUtil;
import io.terminus.trantor2.connector.utils.JsUtil;
import io.terminus.trantor2.connector.utils.ParamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Http类模版执行
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HttpExecutor implements ExecutorApi {

    private static final Cache<String, HttpRequest> HTTPREQUEST_CACHE;
    private final HttpBase httpBase;

    static {
        HTTPREQUEST_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS)
            .maximumSize(100)
            .build();
    }


    /**
     * token有效时间偏差，300秒
     */
    private static final int EXPIRE_INTERVAL = 300;
    /**
     * 连接器(实例)参数前缀
     */
    private static final String PREFIX_AUTH = "auth";
    /**
     * token请求返回数据前缀
     */
    private static final String PREFIX_AUTH_DATA = "auth_data";
    /**
     * 服务入参前缀
     */
    private static final String PREFIX_INPUT = "input";

    /**
     * local参数
     */
    private static final String PREFIX_LOCAL = "local";
    /**
     * 服务出参前缀，用于token令牌失效表达式
     */
    private static final String PREFIX_OUTPUT = "output";

    private static final String PREFIX_HOOK = "hook";
    /**
     * redis client
     */
    private final RedissonClient redissonClient;


    /**
     * 模版类型
     *
     * @return 模版类型
     */
    @Override
    public CtTempType getTempType() {
        return CtTempType.HTTP;
    }

    /**
     * 模版类型-执行
     * 对于token认证，如果token无效，重新执行并强制刷新token
     *
     * @param temp        模版
     * @param api         服务
     * @param inst        实例
     * @param inputValues 入参
     * @return response object
     */
    @Override
    public Object execute(CtTemp temp, CtApi api, CtInst inst, Map<String, Object> inputValues) {
        try {
            return executeInternal(temp, api, inst, inputValues, false);
        } catch (CtTokenInvalidException e) {
            log.warn("reqId:{},ct token invalid error, then force refresh token", CtContext.getReqId());
            return executeInternal(temp, api, inst, inputValues, true);
        }
    }

    private CtField alignInputField(List<CtField> inputs) {
        CtObjectField objectField = new CtObjectField(HttpExecutor.PREFIX_INPUT, inputs);
        if (CollectionUtils.isNotEmpty(inputs)) {
            objectField.setFormatType(inputs.get(0).getFormatType());
        }
        return objectField;
    }


    /**
     * 模版类型-执行主流程
     *
     * @param temp         模版
     * @param api          服务
     * @param inst         实例
     * @param inputValues  入参
     * @param forceRefresh 是否强制刷新token(仅对于token认证)
     * @return response object
     */
    private Object executeInternal(CtTemp temp, CtApi api, CtInst inst, Map<String, Object> inputValues, boolean forceRefresh) {
        Map<String, Object> context = initContext(inst.getResourceProps().getCtParams());
        //hook
        HttpExecutorHook httpExecutorHook = new HttpExecutorHook();
        context.put(PREFIX_HOOK, new HttpExecutorHookProxy(httpExecutorHook));
        Auth authTemp = temp.getAuthTemp();
        AuthType authType = authTemp.getAuthType();
        if (AuthType.TOKEN.equals(authType)) {
            //token request, 获取令牌
            combineTokenResToContext(inst, (TokenAuth) authTemp, context, forceRefresh);
        }
        //auth request, don't need to deal with body
        HttpRequest authRequest = getHttpRequest(((BaseAuth) authTemp).getAuthRequest(), null, context, false);
        //api request
        combineMapDataToContext(context, PREFIX_INPUT, inputValues);
        HttpRequest apiRequest = getHttpRequest(api.getApiRequest(), alignInputField(api.getInput()), context, true);
        //combine
        //apiRequest + authRequest, combine header & query param, not support body combine
        combineRequest(apiRequest, authRequest);
        // triggerBefore
        httpExecutorHook.triggerBeforeExecute(new HttpRequestProxy(apiRequest));
        String resBody = doExecute(inst, apiRequest);
        //todo: 目前response 只有body 所以只放body 后续可以扩展
        HttpResponse httpResponse = HttpResponse.builder()
            .body(resBody)
            .build();
        //后脚本
        httpExecutorHook.triggerAfterExecute(httpResponse);
        //将body转换
        Object resObj = convertOutput(api.getOutput(), httpResponse.body());
        //非强制刷新时，token认证异常拦截
        if (!forceRefresh && AuthType.TOKEN.equals(authType)) {
            checkInvalidToken((TokenAuth) authTemp, resObj);
        }
        return resObj;
    }

    /**
     * check invalid token
     *
     * @param tokenAuth token auth temp
     * @param resObj    api
     */
    private void checkInvalidToken(TokenAuth tokenAuth, Object resObj) {
        String expression = tokenAuth.getInvalidTokenExpression();
        if (StringUtils.isBlank(expression)) {
            return;
        }
        Map<String, Object> tempContext = new HashMap<>();
        tempContext.put(PREFIX_OUTPUT, resObj);
        Object isValid = JsUtil.executeExpression(expression, tempContext);
        if (Objects.equals(isValid, Boolean.TRUE)) {
            log.warn("reqId:{},ct invalid token, expression:{}", CtContext.getReqId(), expression);
            throw new CtTokenInvalidException(ErrorType.CONNECTOR_INVOKE_ERROR);
        }
    }

    /**
     * 出参转换
     * 根据出参定义，将data转换成内部约定的对象
     * 只处理字符串类型转换
     *
     * @param outputField output field
     * @param resStr      http output value
     * @return output obj
     */
    private Object convertOutput(CtField outputField, String resStr) {
        if (Objects.isNull(resStr)
            || StringUtils.isBlank(resStr)
            || Objects.isNull(outputField)
        ) {
            return resStr;
        }
        //
        Class<?> aClass = FieldHandlerUtil.getClassFromFieldType(outputField.getFieldType(), false);
        if (String.class.equals(aClass)) {
            return resStr;
        }
        FormatType formatType = outputField.getFormatType();
        return formatType.from(outputField, resStr);
    }

    /**
     * str -> http request
     *
     * @param requestStr http request str
     * @return http request
     */
    private HttpRequest getHttpRequest(String requestStr) {
        if (StringUtils.isEmpty(requestStr)) {
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, "request str is empty");
        }
        return HTTPREQUEST_CACHE.get(ParamUtil.hash(requestStr),
            key -> HttpRequestParser.parseHttpFile(requestStr).getRequests().get(0));
    }

    /**
     * http request str  -> HttpRequest
     *
     * @param requestStr http request str
     * @param context    context
     * @param dealBody   flag of deal with body
     * @return Http Request Obj
     */
    private HttpRequest getHttpRequest(String requestStr, CtField model, Map<String, Object> context, boolean dealBody) {
        HttpRequest request = CommonMapper.INSTANCE.clone(this.getHttpRequest(requestStr));
        HttpRequestParser.parse(request, model, context);
        if (!dealBody) {
            return request;
        }
        try {
            request.cleanBody();
        } catch (Exception e) {
            log.error("reqId:{},ct exec error,httpfile parse error:{}", CtContext.getReqId(), requestStr);
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, e);
        }
        return request;
    }

    /**
     * fromRequest -> toRequest
     * 如名称冲突，以目标目标优先
     *
     * @param toRequest   target request
     * @param fromRequest combine from request
     */
    private void combineRequest(HttpRequest toRequest, HttpRequest fromRequest) {
        combineHeader(toRequest, fromRequest);
        combineQueryParams(toRequest, fromRequest);
    }

    /**
     * combine header, fromRequest -> toRequest
     *
     * @param toRequest   target request
     * @param fromRequest combine from request
     */
    private void combineHeader(HttpRequest toRequest, HttpRequest fromRequest) {
        List<HttpHeader> fromHeaders = fromRequest.getHeaders();
        if (CollectionUtils.isEmpty(fromHeaders)) {
            return;
        }
        List<HttpHeader> toHeaders = toRequest.getHeaders();
        Set<String> toNameSet = toHeaders.stream()
            .map(HttpHeader::getName)
            .collect(Collectors.toSet());
        fromHeaders.forEach(header -> {
            if (!toNameSet.contains(header.getName())) {
                //can not use addHttpHeader(String, String)
                toRequest.addHttpHeader(header);
            }
        });
    }

    /**
     * combine query params, fromRequest -> toRequest
     *
     * @param toRequest   target request
     * @param fromRequest combine from request
     */
    private void combineQueryParams(HttpRequest toRequest, HttpRequest fromRequest) {
        URI fromURI = fromRequest.getRequestTarget().getUri();
        List<NameValuePair> fromPairs = new URIBuilder(fromURI).getQueryParams();
        if (CollectionUtils.isEmpty(fromPairs)) {
            return;
        }

        URIBuilder builder = new URIBuilder(toRequest.getRequestTarget().getUri());
        Set<String> toNameSet = builder.getQueryParams()
            .stream()
            .map(NameValuePair::getName)
            .collect(Collectors.toSet());
        fromPairs.forEach(pair -> {
            if (!toNameSet.contains(pair.getName())) {
                builder.addParameter(pair.getName(), pair.getValue());
            }
        });

        try {
            toRequest.getRequestTarget().setUri(builder.build());
        } catch (URISyntaxException e) {
            log.error("reqId:{},ct exec error,URI build error:{}", CtContext.getReqId(), builder);
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, e.getMessage(), e);
        }
    }

    /**
     * 执行单个http请求(http request)
     *
     * @param inst    ct inst
     * @param request http对象
     * @return 返回结果
     */
    private String doExecute(CtInst inst, HttpRequest request) {
        URI uri = request.getRequestTarget().getUri();
        Map<String, String> headers = getHeaders(request);
        String httpMethod = request.getMethod().getName();
        printRequest(request, httpMethod, uri, headers);
        String res;
        //
        CtContext.getLogger().log(request);
        switch (httpMethod) {
            case "POST":
                res = httpBase.post(inst, uri, headers, request.bodyBytes());
                break;
            case "PUT":
                res = httpBase.put(inst, uri, headers, request.bodyBytes());
                break;
            case "DELETE":
                res = httpBase.delete(inst, uri, headers);
                break;
            case "HEAD":
                res = httpBase.head(inst, uri, headers);
                break;
            default:
                res = httpBase.get(inst, uri, headers);
        }
        return res;
    }

    /**
     * HttpRequest header -> Map
     * add default content-type
     *
     * @param httpRequest http request obj
     * @return Map
     */
    private Map<String, String> getHeaders(HttpRequest httpRequest) {
        Map<String, String> headers = httpRequest.getHeadersMap();
        boolean flag = headers.keySet().stream()
            .noneMatch(key -> key.equalsIgnoreCase("Content-Type"));
        if (flag) {
            headers.put("Content-Type", "application/json");
        }
        return headers;
    }

    /**
     * 获取token请求结果，放入context
     * redis中存在的，redis中获取
     * redis不存在，或未配置有效时间，执行token请求。
     *
     * @param inst         ct inst
     * @param tokenAuth    auth token
     * @param context      token请求参数
     * @param forceRefresh 是否强制刷新token
     */
    private void combineTokenResToContext(CtInst inst, TokenAuth tokenAuth,
                                          Map<String, Object> context, boolean forceRefresh) {
        boolean isCache = StringUtils.isNotBlank(tokenAuth.getAccessTokenRequest().getTokenExpiresIn());
        Map<String, Object> authData = null;
        if (!forceRefresh && isCache) {
            authData = getTokenFromRedis(inst.accessTokenHash());
        }
        if (Objects.isNull(authData)) {
            authData = refreshToken(inst, tokenAuth, context, isCache);
        }
        log.info("reqId:{},ct get token, {}", CtContext.getReqId(), authData);
        combineMapDataToContext(context, PREFIX_AUTH_DATA, authData);
    }

    /**
     * get token(auth data) from redis
     *
     * @param key redis key
     * @return token(auth data)
     */
    private Map<String, Object> getTokenFromRedis(String key) {
        String tokenRes = (String) redissonClient.getBucket(key).get();
        if (Objects.nonNull(tokenRes)) {
            return CtJsonUtil.fromJson(tokenRes, new TypeReference<Map<String, Object>>() {
            });
        } else {
            log.info("reqId:{},ct get token from redis is null, key: {}", CtContext.getReqId(), key);
        }
        return null;
    }

    /**
     * refresh token(auth data)/execute token request
     *
     * @param inst      ct inst
     * @param tokenAuth auth token
     * @param context   token请求参数
     * @param isCache   是否缓存
     * @return token(auth data)
     */
    private Map<String, Object> refreshToken(CtInst inst, TokenAuth tokenAuth,
                                             Map<String, Object> context, boolean isCache) {
        TokenRequest accessTokenRequest = tokenAuth.getAccessTokenRequest();
        HttpRequest httpRequest = getHttpRequest(accessTokenRequest.getRequest(), null, context, true);
        String accessTokenRes = doExecute(inst, httpRequest);
        // todo： 验证目前只有json格式的token校验
        Map<String, Object> authData = CtJsonUtil.fromJson(accessTokenRes, new TypeReference<Map<String, Object>>() {
        });
        //验证token是否获取成功
        String successExpression = accessTokenRequest.getSuccessExpression();
        if (StringUtils.isNotBlank(successExpression)) {
            Map<String, Object> tempContext = new HashMap<>();
            tempContext.put(PREFIX_AUTH_DATA, authData);
            // 判断校验接口本身是否成功
            Object success = JsUtil.executeExpression(successExpression, tempContext);
            if (!Objects.equals(success, Boolean.TRUE)) {
                log.error("reqId:{},ct refresh access token error,exp:{},res:{}", CtContext.getReqId(),
                    successExpression, accessTokenRes);
                throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, "refresh access token error!");
            }
        }
        //更新缓存
        if (isCache) {
            Map<String, Map<String, Object>> tempContext = new HashMap<>();
            tempContext.put(PREFIX_AUTH_DATA, authData);
            int expiresIn = getExpiresIn(tempContext, accessTokenRequest.getTokenExpiresIn());
            if (expiresIn > 0) {
                RBucket<String> bucket = redissonClient.getBucket(inst.accessTokenHash());
                bucket.set(accessTokenRes, expiresIn, TimeUnit.SECONDS);
            }
        }
        return authData;
    }

    /**
     * 获取token有效期
     *
     * @param context      上下文
     * @param expiresInExp 配置字符串. 常量/jsonpath表达式/js表达式
     * @return token有效期
     */
    @SuppressWarnings("unchecked")
    private int getExpiresIn(Map<String, Map<String, Object>> context, String expiresInExp) {
        int expiresIn = 0;
        //
        String str = null;
        boolean isStr = true;
        if (StringUtils.isNumeric(expiresInExp)) {
            str = expiresInExp;
        } else if (expiresInExp.startsWith("{{")){
            str = ParamUtil.getParamValue(context, expiresInExp);
        } else {
            isStr = false;
            Object val = JsUtil.executeExpression(expiresInExp, (Map<String, Object>)(Map<String, ?>)context);
            if (Objects.nonNull(val) && val instanceof Number) {
                expiresIn = ((Number) val).intValue();
            } else {
                log.error("reqId:{},ct exec error,can not get expiresIn:{}, {}", CtContext.getReqId(), expiresInExp, val);
                throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, "can not get expiresIn from js, " + val);
            }
        }

        if (isStr) {
            try {
                expiresIn = Double.valueOf(str.trim()).intValue();
            } catch (NumberFormatException e) {
                log.error("reqId:{},ct exec error,can not get expiresIn:{}, {}", CtContext.getReqId(), expiresInExp, str);
                throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, e.getMessage(), e);
            }
        }

        if (expiresIn <= EXPIRE_INTERVAL) {
            log.error("reqId:{},ct exec error,expiresIn is too small:{}", CtContext.getReqId(), str);
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, "expiresIn is too small");
        }
        //
        return expiresIn - EXPIRE_INTERVAL;
    }

    private Map<String, Object> initContext(Map<String, Object> authParams) {
        Map<String, Object> context = new HashMap<>();
        context.put(PREFIX_AUTH, authParams);
        context.put(PREFIX_AUTH_DATA, new HashMap<>());
        context.put(PREFIX_INPUT, new HashMap<>());
        context.put(PREFIX_LOCAL, new HashMap<>());
        return context;
    }

    @SuppressWarnings("unchecked")
    private void combineMapDataToContext(Map<String, Object> context,
                                         String key, Map<String, Object> data) {
        Object o = context.get(key);
        if (o instanceof Map) {
            ((Map<String, Object>) o).putAll(data);
        }
    }


    private void printRequest(HttpRequest request, String httpMethod, URI uri, Map<String, String> headers) {
        if (log.isInfoEnabled()) {
            log.info("reqId:{},ct start http request:\n{} {}\nheader:{}\n {}", CtContext.getReqId(), httpMethod, uri, headers, request.getBodyText());
        }
    }
}
