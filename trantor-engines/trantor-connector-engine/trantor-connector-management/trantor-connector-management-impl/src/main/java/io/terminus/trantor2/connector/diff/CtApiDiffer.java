package io.terminus.trantor2.connector.diff;


import io.terminus.trantor2.connector.model.diff.*;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.field.CtFieldHelper;
import io.terminus.trantor2.connector.model.field.CtObjectField;
import io.terminus.trantor2.connector.model.meta.CtApi;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static io.terminus.trantor2.connector.diff.DiffHelper.diffFromCtField;

/**
 * CtApiDiffer
 * 以v1为基准对比v2的差异
 */
public class CtApiDiffer {

    private  int diffNumber = 0;

    private CtApi ctApi1;

    private CtApi ctApi2;

    private DiffMode diffMode = DiffMode.ALL;

    private String location;

    public static CtApiDiffer builder() {
        return new CtApiDiffer();
    }

    public CtApiDiffer ctApi1(CtApi ctApi1) {
        this.ctApi1 = ctApi1;
        return this;
    }

    public CtApiDiffer ctApi2(CtApi ctApi2) {
        this.ctApi2 = ctApi2;
        return this;
    }

    public CtApiDiffer diffMode(DiffMode diffMode) {
        this.diffMode = diffMode;
        return this;
    }

    public CtApiDiffer location(String location) {
        this.location = location;
        return this;
    }

    public ApiDiffReport diff() {
        if (ctApi1 == null || ctApi2 == null) {
            throw new IllegalArgumentException("ctApi1 or ctApi2 is null");
        }
        //比较key
        // key不同直接报错
        if (!Objects.equals(ctApi1.getKey(), ctApi2.getKey())) {
            throw new IllegalArgumentException(String.format("ctApi: ctApi1 key: %s is not equal to ctApi2 key: %s", ctApi1.getKey(), ctApi2.getKey()));
        }
        if (StringUtils.isBlank(location)) {
            location = ctApi1.getKey();
        }
        ApiDiffReport apiDiffReport = ApiDiffReport.builder()
            .location(location)
            .diffType(DiffType.UPDATE)
            .build();
        //基本信息
        apiDiffReport.setApiBasicDiffReport(apiBasicDiffReport());
        //入参
        apiDiffReport.setInputDiffReport(apiInputDiffReport(ctApi1.getInput(), ctApi2.getInput()));
        //出参
        apiDiffReport.setOutputDiffReport(apiOutputDiffReport(ctApi1.getOutput(), ctApi2.getOutput()));
        apiDiffReport.setDiffNumber(diffNumber);
        return apiDiffReport;


    }

    private ApiBasicDiffReport apiBasicDiffReport() {
        ApiBasicDiffReport apiBasicDiffReport = ApiBasicDiffReport.builder().
            diffDetails(new ArrayList<>()).
            build();
        if (DiffMode.Compact.equals(diffMode)) {
            return apiBasicDiffReport;
        }
        //基本信息
        //name
        if (!Objects.equals(ctApi1.getName(), ctApi2.getName())) {
            apiBasicDiffReport.getDiffDetails().add(DiffDetail.builder().
                before(ctApi1.getName()).
                after(ctApi2.getName()).
                detailPath("name").
                diffType(DiffType.UPDATE).
                message("name 不一致").
                build());
            increaseDiff();
        }

        return apiBasicDiffReport;
    }

    private CtFieldParamsDiffReport apiInputDiffReport(List<CtField> input1, List<CtField> input2) {
        CtFieldParamsDiffReport ctFieldParamsDiffReport = CtFieldParamsDiffReport.builder()
            .diffDetails(new ArrayList<>())
            .build();
        //differ
        CtFieldHelper.Differ differ = (diffType, ct1, ct2, diffPath) -> {
            switch (diffType) {
                case Create:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(null).
                            after(ct2.getFieldKey()).
                            detailPath(diffPath).
                            diffType(DiffType.ADD).
                            message("字段新增").
                            build());
                    increaseDiff();
                    break;
                case Delete:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(ct1.getFieldKey()).
                            after(null).
                            detailPath(diffPath).
                            diffType(DiffType.DELETE).
                            message("字段删除").build()
                    );
                    increaseDiff();
                    break;
                default:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(ct1.getFieldKey()).
                            after(ct2.getFieldKey()).
                            detailPath(diffPath).
                            diffType(DiffType.UPDATE).
                            message("更新").subDetails(diffFromCtField(ct1, ct2)).build());
                    increaseDiff();
                    break;
            }

        };
        CtFieldHelper.diffCtField(new CtObjectField("input", input1), new CtObjectField("input", input2), differ);
        return ctFieldParamsDiffReport;
    }

    private CtFieldParamsDiffReport apiOutputDiffReport(CtField output1, CtField output2) {
        CtFieldParamsDiffReport ctFieldParamsDiffReport = CtFieldParamsDiffReport.builder()
            .diffDetails(new ArrayList<>())
            .build();
        //differ
        CtFieldHelper.Differ differ = (diffType, ct1, ct2, diffPath) -> {
            switch (diffType) {
                case Create:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(null).
                            after(ct2.getFieldKey()).
                            detailPath(diffPath).
                            diffType(DiffType.ADD).
                            message("字段新增").
                            build());
                    increaseDiff();
                    break;
                case Delete:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(ct1.getFieldKey()).
                            after(null).
                            detailPath(diffPath).
                            diffType(DiffType.DELETE).
                            message("字段删除").build()
                    );
                    increaseDiff();
                    break;
                default:
                    ctFieldParamsDiffReport.getDiffDetails().add(
                        DiffDetail.builder().
                            before(ct1.getFieldKey()).
                            after(ct2.getFieldKey()).
                            detailPath(diffPath).
                            diffType(DiffType.UPDATE).
                            message("更新").subDetails(diffFromCtField(ct1, ct2)).build());
                    increaseDiff();
                    break;
            }

        };
        CtFieldHelper.diffCtField(output1, output2, differ);
        return ctFieldParamsDiffReport;
    }

    private void increaseDiff(int size) {
        diffNumber += size;
    }

    private void increaseDiff() {
        increaseDiff(1);
    }

}
