package io.terminus.trantor2.connector.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 线程池属性
 */
@Data
@ConfigurationProperties(prefix = "connector.log.executor")
public class ConnectorLogThreadPoolProperties {
    private Integer corePoolSize = 5;

    private Integer maxPoolSize = 10;

    private Integer keepAlive = 60;

    private Integer queueCapacity = 20;

    private Integer awaitTerminationSeconds = 60;

}
