package io.terminus.trantor2.connector.service.impl;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.log.logger.CtLogger;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.request.CtCallRequest;
import io.terminus.trantor2.connector.model.request.CtDynCallRequest;
import io.terminus.trantor2.connector.model.response.CtTestCallResponse;
import io.terminus.trantor2.connector.repo.CtInstRepo;
import io.terminus.trantor2.connector.service.CtCallService;
import io.terminus.trantor2.connector.service.CtEnvHolder;
import io.terminus.trantor2.connector.service.CtTestCallApi;
import io.terminus.trantor2.connector.utils.CtContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.CtInstConfig;
import io.terminus.trantor2.module.service.ConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 连接器(服务)测试调用实现-入口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CtTestCallApiImpl implements CtTestCallApi, CtEnvHolder {

    private final CtCallService callService;

    private final CtInstRepo instRepo;

    private final ConfigurationService configurationService;


    private final CtLogger ctManageLogger;


    /**
     * 连接器(服务)执行
     * (连接器实例已存在，该接口调用需要有团队上下文)
     *
     * @param callRequest 调用请求
     * @return 结果
     */
    @Override
    public Response<Object> call(CtCallRequest callRequest) {
        return callService.doCall(callRequest, this);
    }

    /**
     * 连接器(服务)执行
     * (连接器实例动态构造，无需团队上下文)
     *
     * @param dynCallRequest 调用请求
     * @return 结果
     */
    @Override
    public Response<Object> call(CtDynCallRequest dynCallRequest) {
        return callService.doCall(dynCallRequest, this);
    }

    /**
     * 获取连接器实例
     *
     * @param instKey 连接器实例key
     * @return 连接器实例
     */
    @Override
    public CtInst getInst(String instKey) {
        return instRepo.findOneByKey(instKey, ResourceContext.ctxFromThreadLocal())
            .orElseThrow(() -> new CtException(ErrorType.CONNECTOR_INSTANCE_NOT_EXISTED, new String[]{instKey}));
    }

    /**
     * 获取连接器实例-环境参数配置
     *
     * @param teamId        teamId
     * @param instModuleKey 连接器 module key(parent key)
     * @return 连接器实例-环境参数配置
     */
    @Override
    public CtInstConfig getInstConfig(Long teamId, String instModuleKey) {
        return configurationService.query(teamId, instModuleKey,
            ConfigType.Connector_Inst_Param);
    }

    /**
     * 是否是运行态
     *
     * @return 运行态?
     */
    @Override
    public boolean isRuntime() {
        return false;
    }

    @Override
    public CtLogger getLogger() {
        return ctManageLogger;
    }

    /**
     * response转换
     *
     * @param response response
     * @return response
     */
    @Override
    public Response<Object> convert(Response<Object> response) {
        Object resObj = response.getData();
        CtTestCallResponse testCallResponse = new CtTestCallResponse();
        testCallResponse.setResult(resObj);
        testCallResponse.setLogList(CtContext.getLogList());
        //
        response.setData(testCallResponse);
        return response;
    }
}
