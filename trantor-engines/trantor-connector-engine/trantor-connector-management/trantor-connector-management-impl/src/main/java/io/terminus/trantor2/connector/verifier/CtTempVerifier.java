package io.terminus.trantor2.connector.verifier;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.trantor2.application.dto.AppItemSaveRequest;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.connector.convertor.CtTempConvertor;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.field.CtFieldHelper;
import io.terminus.trantor2.connector.model.field.CtObjectField;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.meta.auth.Auth;
import io.terminus.trantor2.connector.model.meta.auth.AuthType;
import io.terminus.trantor2.connector.model.meta.auth.BaseAuth;
import io.terminus.trantor2.connector.model.meta.auth.TokenAuth;
import io.terminus.trantor2.connector.utils.CommonUtil;
import io.terminus.trantor2.connector.verifier.models.CtTempVerifyDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CtTempVerifier {

    // key的 pattern
    private static final Pattern VALID_MODULE_KEY_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]{0,49}$");

    private static final String VALID_MODULE_KEY_MESSAGE_TEMPLATE = "由小写字母,大写字母,数字和下划线组成,必须以字母开头,且总长度为不超过50";

    // param key的 pattern
    private static final Pattern VALID_PARAM_KEY_PATTERN = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]{0,49}$");

    private static final String VALID_PARAM_KEY_MESSAGE_TEMPLATE = "由小写字母,大写字母,数字和下划线组成,必须以字母或下划线开头,且总长度为不超过50";

    /**
     * 校验temp profile
     * 本质上就是temp的基本信息
     *
     * @param appItemSaveRequest itemSaveRequest 请求
     */
    public static void verifyCtTempProfile(AppItemSaveRequest appItemSaveRequest) {
        String key = appItemSaveRequest.getKey();
        String name = appItemSaveRequest.getName();
        List<CtTempVerifyDetail> details = new ArrayList<>();

        //key
        if (StringUtils.isBlank(key)) {
            details.add(CtTempVerifyDetail.builder()
                .path("模板标识")
                .message("模板标识不能为空")
                .build()
            );
        } else {
            //校验模板key
            if (!isValidModuleKey(key)) {
                details.add(CtTempVerifyDetail.builder()
                    .path("模板标识")
                    .message("模板标识" + VALID_MODULE_KEY_MESSAGE_TEMPLATE)
                    .build()
                );
            }
        }
        //name
        if (StringUtils.isBlank(name)) {
            details.add(CtTempVerifyDetail.builder()
                .path("模板名称")
                .message("模板名称不能为空")
                .build()
            );
        }

        //report
        reportError(details);
    }

    /**
     * 校验 temp version
     *
     * @param versionedCtTempContext 版本的CtTemp内容
     * @param isCheckApi             是否校验api
     */
    public static void verifyCtTempContextVersion(CtTempContent versionedCtTempContext, boolean isCheckApi) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
        if (versionedCtTempContext == null) {
            details.add(CtTempVerifyDetail.builder()
                .path("模板内容")
                .message("模板内容不能为空")
                .build()
            );
        } else {
            //转换为ctTemp后方便校验
            CtTemp ctTemp = CtTempConvertor.ctTempContext2CtTemp(versionedCtTempContext);
            //校验auth模板
            details.addAll(verifyCtTempAuth(ctTemp.getAuthTemp()));
            //校验params
            details.addAll(verifyCtTempParams(ctTemp.getParams()));
            if (isCheckApi) {
                //校验api
                if (CollectionUtils.isEmpty(ctTemp.getApis())) {
                    details.add(CtTempVerifyDetail.builder()
                        .path("模板关联的服务")
                        .message("模板关联的服务不能为空")
                        .build()
                    );
                } else {
                    details.addAll(verifyCtTempApis(ctTemp.getKey(), ctTemp.getApis()));
                }

            }
        }

        //reportError
        reportError(details);
    }

    /**
     * 校验auth
     *
     * @param auth auth
     * @return 错误详情
     */
    private static List<CtTempVerifyDetail> verifyCtTempAuth(Auth auth) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
        if (auth == null) {
            details.add(CtTempVerifyDetail.builder()
                .path("模板鉴权信息")
                .message("模板鉴权信息不能为空")
                .build()
            );
        } else {
            //校验auth
            BaseAuth baseAuth = (BaseAuth) auth;
            if (baseAuth.getAuthType() == null) {
                details.add(CtTempVerifyDetail.builder()
                    .path("模板鉴权方式")
                    .message("模板鉴权方式不能为空")
                    .build()
                );
            } else {
                //校验authRequest
                String authRequest = baseAuth.getAuthRequest();
                if (StringUtils.isBlank(authRequest)) {
                    details.add(CtTempVerifyDetail.builder()
                        .path("模板鉴权请求接口")
                        .message("无效的模板鉴权请求接口")
                        .build()
                    );
                }
                AuthType authType = baseAuth.getAuthType();
                //校验 token authRequest
                if (AuthType.TOKEN.equals(authType)) {
                    TokenAuth tokenAuth = (TokenAuth) baseAuth;
                    String tokenAuthRequest = tokenAuth.getAuthRequest();
                    if (StringUtils.isBlank(tokenAuthRequest)) {
                        details.add(CtTempVerifyDetail.builder()
                            .path("模板鉴权请求接口")
                            .message("当模板鉴权方式为token时,token鉴权请求接口不能为空")
                            .build()
                        );
                    }
                }
            }

        }
        return details;
    }

    /**
     * 校验params
     *
     * @param params params
     * @return 错误详情
     */
    private static List<CtTempVerifyDetail> verifyCtTempParams(List<CtField> params) {
        if (CollectionUtils.isNotEmpty(params)) {
            return checkCtFieldTree("模板参数", new CtObjectField("params", params));
        }
        return new ArrayList<>();
    }

    private static List<CtTempVerifyDetail> verifyCtTempApis(String location, List<CtApi> apis) {
        return apis.stream()
            .map(ctApi -> verifyCtTempApi(location, ctApi))
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    public static void verifyCtTempApi(CtApi ctApi) {
        List<CtTempVerifyDetail> details = verifyCtTempApi("", ctApi);
        reportError(details);
    }

    private static List<CtTempVerifyDetail> verifyCtTempApi(String location, CtApi ctApi) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
        location = StringUtils.isBlank(location) ? ctApi.getKey() : appendPath(location, ctApi.getKey());
        //verify basic info
        details.addAll(verifyCtTempApiBasicInfo(location, ctApi));
        //verify input
        details.addAll(verifyCtTempApiInput(location, ctApi));
        //verify output
        details.addAll(verifyCtTempApiOutput(location, ctApi));
        return details;
    }

    private static List<CtTempVerifyDetail> verifyCtTempApiBasicInfo(String location, CtApi ctApi) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
        //key
        if (StringUtils.isBlank(ctApi.getKey())) {
            details.add(CtTempVerifyDetail.builder()
                .path(appendPath(location, "服务标识"))
                .message("服务标识不能为空")
                .build()
            );
        } else {
            //校验apiKey
            if (!isValidModuleKey(ctApi.getKey())) {
                details.add(CtTempVerifyDetail.builder()
                    .path(appendPath(location, "服务标识"))
                    .message("服务标识" + VALID_MODULE_KEY_MESSAGE_TEMPLATE)
                    .build()
                );
            }
        }
        //name
        if (StringUtils.isBlank(ctApi.getName())) {
            details.add(CtTempVerifyDetail.builder()
                .path(appendPath(location, "服务名称"))
                .message("服务名称不能为空")
                .build()
            );
        }
        //request
        if (StringUtils.isBlank(ctApi.getApiRequest())) {
            details.add(CtTempVerifyDetail.builder()
                .path(appendPath(location, "服务请求"))
                .message("服务请求不能为空")
                .build()
            );
        }
        return details;
    }

    private static List<CtTempVerifyDetail> verifyCtTempApiInput(String location, CtApi ctApi) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
//        if (CollectionUtils.isEmpty(ctApi.getInput())) {
//            details.add(CtTempVerifyDetail.builder()
//                .path(appendPath(location, "服务入参"))
//                .message("服务入参不能为空")
//                .build()
//            );
//        } else {
//            details.addAll(checkCtFieldTree(appendPath(location, "服务入参"), new CtObjectField("input", ctApi.getInput())));
//        }
        if (CollectionUtils.isNotEmpty(ctApi.getInput())) {
            details.addAll(checkCtFieldTree(appendPath(location, "服务入参"), new CtObjectField("input", ctApi.getInput())));
        }
        return details;
    }

    private static List<CtTempVerifyDetail> verifyCtTempApiOutput(String location, CtApi ctApi) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
//        if (Objects.isNull(ctApi.getOutput())) {
//            details.add(CtTempVerifyDetail.builder()
//                .path(appendPath(location, "服务出参"))
//                .message("服务出参不能为空")
//                .build()
//            );
//        } else {
//            details.addAll(checkCtFieldTree(appendPath(location, "服务出参"), ctApi.getOutput()));
//        }
        if (Objects.nonNull(ctApi.getOutput())) {
            details.addAll(checkCtFieldTree(appendPath(location, "服务出参"), ctApi.getOutput()));
        }
        return details;
    }


    private static List<CtTempVerifyDetail> checkCtFieldTree(String location, CtField rootField) {
        //footprintMap: {footprint: {fieldKey}}
        Map<String, Set<String>> footprintMap = new HashMap<>();
        //duplicateCtFieldMap: {fieldPath: CtField}
        Map<String, CtField> duplicateCtFieldMap = new HashMap<>();
        //leftCtFieldMap: {fieldPath: CtField}
        Map<String, CtField> leftCtFieldMap = new HashMap<>();
        //find all duplicate path by walking on ctField
        //if multi field have same footprint,  these fields are duplicate
        CtFieldHelper.recursionCtFieldTree(rootField, (footprint, parent, current) -> {
            Set<String> checkFootPrintSet = footprintMap.getOrDefault(footprint, new HashSet<>());
            String currentPath = footprint + "." + current.getFieldKey();
            if (!checkFootPrintSet.contains(currentPath)) {
                //firstly,not found
                checkFootPrintSet.add(currentPath);
                // remain first one
                leftCtFieldMap.put(currentPath, current);
            } else {
                //duplication happen save duplicate
                duplicateCtFieldMap.put(currentPath, current);
            }
            footprintMap.put(footprint, checkFootPrintSet);
            return true;
        });
        List<CtTempVerifyDetail> details = new ArrayList<>();
        // 字段本身校验
        if (MapUtil.isNotEmpty(leftCtFieldMap)) {
            for (Map.Entry<String, CtField> entry : leftCtFieldMap.entrySet()) {
                details.addAll(checkCtField(appendPath(location, entry.getKey()), entry.getValue()));
            }
        }
        //重复字段
        if (MapUtil.isNotEmpty(duplicateCtFieldMap)) {
            details.addAll(duplicateCtFieldMap.keySet().stream()
                .map(fieldPath -> CtTempVerifyDetail.builder()
                    .path(location)
                    .message("重复的字段路径: " + fieldPath)
                    .build()
                ).collect(Collectors.toList()));
        }
        return details;
    }

    private static List<CtTempVerifyDetail> checkCtField(String location, CtField ctField) {
        List<CtTempVerifyDetail> details = new ArrayList<>();
        if (ctField == null) {
            return details;
        }
        //key
        if (StringUtils.isBlank(ctField.getFieldKey())) {
            details.add(CtTempVerifyDetail.builder()
                .path(location)
                .message("字段标识不能为空")
                .build()
            );
        } else {
            if (!isValidParamKey(ctField.getFieldKey())) {
                details.add(CtTempVerifyDetail.builder()
                    .path(location)
                    .message("字段标识" + VALID_PARAM_KEY_MESSAGE_TEMPLATE)
                    .build()
                );
            }
        }
        //name
        if (StringUtils.isBlank(ctField.getFieldName())) {
            details.add(CtTempVerifyDetail.builder()
                .path(location)
                .message("字段名称不能为空")
                .build()
            );
        }
        //type
        if (Objects.isNull(ctField.getFieldType())) {
            details.add(CtTempVerifyDetail.builder()
                .path(location)
                .message("字段类型不能为空")
                .build()
            );
        } else {
            //校验fieldType
            if (!CommonUtil.isConnectorFieldType(ctField.getFieldType())) {
                details.add(CtTempVerifyDetail.builder()
                    .path(location)
                    .message(String.format("字段类型[%s]不支持,字段类型必须在: [%s] 中", ctField.getFieldType(), StrUtil.join(",", CommonUtil.Connector_Field_Types)))
                    .build()
                );
            }
        }
        return details;
    }

    /**
     * key 有效的统一校验逻辑
     *
     * @param key
     * @return key
     */
    private static boolean isValidModuleKey(String key) {
        return VALID_MODULE_KEY_PATTERN.matcher(key).matches();
    }

    /**
     * param key 有效的统一校验逻辑
     *
     * @param key
     * @return
     */
    private static boolean isValidParamKey(String key) {
        return VALID_PARAM_KEY_PATTERN.matcher(key).matches();
    }

    /**
     * 上报错误
     */
    private static void reportError(List<CtTempVerifyDetail> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            StringBuilder errorBuilder = new StringBuilder();
            for (CtTempVerifyDetail detail : details) {
                if (StringUtils.isBlank(detail.getPath())) {
                    errorBuilder.append(String.format("%s", detail.getMessage()));
                } else {
                    errorBuilder.append(String.format("%s : %s", detail.getPath(), detail.getMessage()));
                }
                errorBuilder.append("\n");
            }
            errorBuilder.delete(errorBuilder.length() - 1, errorBuilder.length());
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{errorBuilder.toString()});
        }
    }

    private static String appendPath(String... path) {
        return StrUtil.join(" > ", Arrays.asList(path));
    }


}
