package io.terminus.trantor2.connector.functions.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelApi;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelRow;
import io.terminus.trantor2.connector.model.functions.excel.models.enums.ExcelApiBodySchema;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelTemp;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ExcelReadListener extends AnalysisEventListener<Map<Integer, String>> {

    private static final String ExcelAPiBound = "接口分组";

    private static final String ExcelAPiInputBound = "接口入参";

    private static final String ExcelAPiInputDataBound = "接口入参样例数据";

    private static final String ExcelAPiOutputBound = "接口出参";

    private static final String ExcelAPiOutputDataBound = "接口出参样例数据";

    private static final String ExcelAPiOtherBound = "接口其他信息";

    private String beforeApiFieldBound; //上一个apiField的边界

    private List<ExcelRow> apiInputRows;
    private String apiInputSampleData;

    private List<ExcelRow> apiOutPutRows;

    private String apiOutSampleData;

    private ExcelApi excelApi;

    private final ExcelTemp excelTemp;

    // 这里也可以注册一个回掉 实现 异步加载api接口列表
    public ExcelReadListener(ExcelTemp excelTemp) {
        this.excelTemp = excelTemp;
        if (CollectionUtils.isEmpty(excelTemp.getApis())) {
            excelTemp.setApis(new ArrayList<>());
        }
    }


    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }

    /**
     * invoke 会一行行的监听读取excel的事件
     * <p>
     * 1. index作为第一个sheet，api列表是后面的sheet
     * 2. 目前假定 两个 ExcelAPiBoundHead 之间夹的是 一个api,一个sheet 可能有多个api
     *
     * @param data
     * @param analysisContext
     */
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext analysisContext) {
        ReadSheetHolder readSheetHolder = analysisContext.readSheetHolder();
        Integer sheetNo = readSheetHolder.getSheetNo();
        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();
//        System.out.println(StrUtil.format("sheetNo:{} rowIndex:{}解析到一条数据:{}", sheetNo, rowIndex, CtJsonUtil.toJson(data)));
        //todo:目前只处理sheetNo >=1的
        if (sheetNo < 1) {
            return;
        }

        String columnKey = data.get(0);
        switch (columnKey) {
            case ExcelAPiBound:
                //apibound的时候需要进行控制
                excelApi = ExcelApi.builder()
                        .apiGroupTag(data.get(1))
                        .build();
                beforeApiFieldBound = columnKey;
                break;
            case "接口编号":
                excelApi.setApiKey(data.get(1));
                beforeApiFieldBound = columnKey;
                break;
            case "接口协议":
                excelApi.setSchema(ExcelApiBodySchema.valueOrJSON(data.get(1)));
                beforeApiFieldBound = columnKey;
                break;
            case "接口名称":
                excelApi.setApiName(data.get(1));
                beforeApiFieldBound = columnKey;
                break;
            case "接口路径":
                excelApi.setApiPath(ExcelHelper.handleApiUrlPath(data.get(1)));
                beforeApiFieldBound = columnKey;
                break;
            case "接口描述":
                excelApi.setApiDesc(data.get(1));
                beforeApiFieldBound = columnKey;
                break;
            case ExcelAPiInputBound:
                apiInputRows = new ArrayList<>();
                beforeApiFieldBound = columnKey;
                break;
            case ExcelAPiInputDataBound:
                //接口入参部分结束 接口入参
                beforeApiFieldBound = columnKey;
                break;
            case ExcelAPiOutputBound:
                apiOutPutRows = new ArrayList<>();
                beforeApiFieldBound = columnKey;
                break;
            case ExcelAPiOutputDataBound:
                beforeApiFieldBound = columnKey;
                break;
            default:
                if (StrUtil.isBlank(beforeApiFieldBound)) {
                    break;
                }
                //如果上一个apiBound是接口入参 则都是input部分
                switch (beforeApiFieldBound) {
                    case ExcelAPiInputBound:
                        apiInputRows.add(ExcelRow.builder()
                                .rowIndex(rowIndex)
                                .data(data)
                                .build()
                        );
                        break;
                    case ExcelAPiInputDataBound:
                        apiInputSampleData = data.get(0);
                        break;
                    case ExcelAPiOutputBound:
                        apiOutPutRows.add(ExcelRow.builder()
                                .rowIndex(rowIndex)
                                .data(data)
                                .build()
                        );
                        break;
                    case ExcelAPiOutputDataBound:
                        apiOutSampleData = data.get(0);
                        excelApi.setApiInput(ExcelHelper.getApiBody(apiInputRows, apiInputSampleData));
                        excelApi.setApiOutPut(ExcelHelper.getApiBody(apiOutPutRows, apiOutSampleData));
                        //api接口全部收集
                        excelTemp.getApis().add(excelApi);
                        //这个api的生命周期结束
                        apiRecycle();
                        //otherField
                        beforeApiFieldBound = ExcelAPiOtherBound;
                        break;
                }

        }

    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        super.extra(extra, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        ReadSheetHolder readSheetHolder = analysisContext.readSheetHolder();
        Integer sheetNo = readSheetHolder.getSheetNo();
//        System.out.println(StrUtil.format("sheetNo:{} 读取完成", sheetNo));
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return super.hasNext(context);
    }

    private void apiRecycle() {
        apiInputRows = null;
        apiInputSampleData = null;
        apiOutPutRows = null;
        apiOutSampleData = null;
        excelApi = null;

    }
}
