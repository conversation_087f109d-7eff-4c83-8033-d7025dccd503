package io.terminus.trantor2.connector.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.dto.AppItemListRequest;
import io.terminus.trantor2.application.dto.AppItemSaveRequest;
import io.terminus.trantor2.application.dto.AppItemWithVersionCreateRequest;
import io.terminus.trantor2.application.dto.VersionedAppItemSaveRequest;
import io.terminus.trantor2.application.dto.item.connector.CtTempWithVersionCreateRequest;
import io.terminus.trantor2.application.dto.item.connector.VersionedCtTempSaveRequest;
import io.terminus.trantor2.application.item.connector.CtTempBaseInfo;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.application.service.AbstractAppManagerService;
import io.terminus.trantor2.application.vo.TrantorApplicationItemVO;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.feature.VersionRange;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.connector.convertor.CtTempConvertor;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.functions.excel.ExcelHelper;
import io.terminus.trantor2.connector.functions.excel.ExcelReadListener;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelTemp;
import io.terminus.trantor2.connector.model.meta.*;
import io.terminus.trantor2.connector.model.meta.auth.AuthType;
import io.terminus.trantor2.connector.model.meta.auth.BaseAuth;
import io.terminus.trantor2.connector.model.meta.auth.TokenAuth;
import io.terminus.trantor2.connector.model.meta.http.HttpRequest;
import io.terminus.trantor2.connector.model.meta.http.HttpRequestParser;
import io.terminus.trantor2.connector.model.request.CtDynCallRequest;
import io.terminus.trantor2.connector.model.response.CtTestCallResponse;
import io.terminus.trantor2.connector.model.vo.CtApiVOForMgr;
import io.terminus.trantor2.connector.repo.CtTempRepo;
import io.terminus.trantor2.connector.repo.VersionedCtTempRepo;
import io.terminus.trantor2.connector.request.CtTempListRequest;
import io.terminus.trantor2.connector.service.CtTempApi;
import io.terminus.trantor2.connector.service.CtTestCallApi;
import io.terminus.trantor2.connector.utils.CommonUtil;
import io.terminus.trantor2.connector.utils.CtJsonUtil;
import io.terminus.trantor2.connector.utils.PagingUtil;
import io.terminus.trantor2.connector.verifier.CtTempVerifier;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.OSSConstant;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;

import jakarta.annotation.Nullable;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 连接器模版管理实现
 */
@Service
@Slf4j
public class CtTempApiImpl extends AbstractAppManagerService<CtTempPO, VersionedCtTempPO> implements CtTempApi {
    private static final String CONNECTOR_TEMPLATE_GROUP = "io.terminus.trantor.connectortemplate";
    private static final String TEMP_SUFFIX = "temp.json";
    private static final String API_SUFFIX = "api.json";
    private final CtTempRepo tempRepo;
    private final VersionedCtTempRepo versionedTempRepo;
    private final OSSService ossService;
    private final CtTestCallApi testCallApi;

    private static final String CT_PREFIX = "connector";

    protected CtTempApiImpl(ConsoleService consoleService,
                            NexusApiClientFactory nexusApiClientFactory,
                            NexusConfigProperties nexusConfigProperties,
                            TrantorIAMUserService trantorIAMUserService,
                            CtTempRepo tempRepo,
                            VersionedCtTempRepo versionedTempRepo,
                            OSSService ossService,
                            CtTestCallApi ctTestCallApi) {
        super(consoleService, nexusApiClientFactory, nexusConfigProperties, trantorIAMUserService);
        this.tempRepo = tempRepo;
        this.versionedTempRepo = versionedTempRepo;
        this.ossService = ossService;
        this.testCallApi = ctTestCallApi;
    }

    /**
     * 上传连接器模版
     *
     * @param ossFileUrl oss file url
     * @param appVersion app version
     */
    @Override
    @Transactional
    @Deprecated
    public void upload(String ossFileUrl, String appVersion) {
        CtTemp ctTemp = this.getCtTempFromOss(ossFileUrl);
        String key = ctTemp.getKey();
        //
        AppItemSaveRequest request = new AppItemSaveRequest();
        BeanUtils.copyProperties(ctTemp, request);
        request.setBaseInfo(this.transferBaseInfo(ctTemp));
        Optional<CtTempPO> optCtTempPO = this.tempRepo.findOneByKeyAndLocalTrue(key);
        request.setCreate(!optCtTempPO.isPresent());
        //
        VersionedAppItemSaveRequest versionedRequest = new VersionedAppItemSaveRequest();
        versionedRequest.setVersion(appVersion);
        versionedRequest.setKey(key);
        versionedRequest.setContent(this.transferContent(ctTemp));
        versionedRequest.setCreate(true);
        //
        this.saveAppOp(request);
        this.saveVersionedAppOp(versionedRequest);
    }

    public enum OperateType {
        CREATE,
        UPDATE,
        DELETE
    }

    /**
     * 服务详情
     *
     * @param key        模版标识
     * @param apiKey     标识
     * @param version    版本
     * @param enabled    是否启用
     * @param fromMarket 是否加载来自于中心市场的应用
     * @return 服务详情
     */
    @Override
    public CtApi apiDetail(String key, String apiKey, String version, boolean enabled, boolean fromMarket) {
        return getApiList(key, version, enabled, fromMarket).stream()
            .filter(api -> Objects.equals(api.getKey(), apiKey))
            .findFirst()
            .orElseThrow(() -> new CtException(ErrorType.CONNECTOR_SERVICE_NOT_EXISTED, new String[]{apiKey}));
    }

    /**
     * 服务测试
     *
     * @param key        模版标识
     * @param version    版本
     * @param enabled    是否启用
     * @param fromMarket 是否加载来自于中心市场的应用
     * @param request    请求参数
     * @return 测试结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public Response<CtTestCallResponse> apiTest(String key, String version, boolean enabled, boolean fromMarket, CtTempApiTestRequest request) {
        //check api validation
        CtApi ctApi = Optional.ofNullable(request.getCtApi())
            .map(CtApiVOForMgr::convert).orElse(null);
        if (Objects.isNull(ctApi)) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"ctApi in request can't be null"});
        }

        if (StringUtils.isBlank(ctApi.getKey())) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"key of ctApi in request can't be blank"});
        }
        //查询version对应的temp
        VersionedCtTempPO versionedCtTempPO = findVersion(key, version, enabled, fromMarket);
        if (Objects.isNull(versionedCtTempPO) || Objects.isNull(versionedCtTempPO.getContent())) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{String.format("from key: %s version: %s enabled: %s fromMarket: %s not found valid temp", key, version, enabled, fromMarket)});
        }
        CtTemp ctTemp = CtTempConvertor.versionedCtTempPO2CtTemp(versionedCtTempPO);
        ctTemp.setApis(Lists.newArrayList(ctApi));
        //创建dummyInst
        CtInst dummyInst = getDummyInst(ctTemp, request.getCtParams(), request.getExtParams());
        //构造request对象
        CtDynCallRequest ctDynCallRequest = CtDynCallRequest.builder()
            .apiKey(ctApi.getKey())
            .inst(dummyInst)
            .input(request.getInput())
            .build();
        Response<Object> call = testCallApi.call(ctDynCallRequest);
        // 接口本身返回200
        return (Response<CtTestCallResponse>) (Response<?>) Response.ok(call.getData());

    }


    @Override
    public CtTempExportResponse exportVersion(String key, String version, boolean enabled, boolean fromMarket) {
        //查询version对应的temp
        VersionedCtTempPO versionedCtTempPO = findVersion(key, version, enabled, fromMarket);
        if (Objects.isNull(versionedCtTempPO) || Objects.isNull(versionedCtTempPO.getContent())) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{String.format("from key: %s version: %s enabled: %s fromMarket: %s not found valid temp or temp context is null", key, version, enabled, fromMarket)});
        }
        //转换ctTemp
        CtTemp ctTemp = CtTempConvertor.versionedCtTempPO2CtTemp(versionedCtTempPO);
        //todo: 上传到oss
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
            //api
            for (CtApi api : ctTemp.getApis()) {
                //处理http
                if (StringUtils.isNotBlank(api.getApiRequest())) {
                    ZipEntry zipEntry = new ZipEntry(CommonUtil.standardHttpFileSuffix(api.getKey()));
                    zipOut.putNextEntry(zipEntry);
                    zipOut.write(api.getApiRequest().getBytes(StandardCharsets.UTF_8));
                    zipOut.closeEntry();
                    // remove request from api
                    api.setApiRequest(null);
                }
                ZipEntry zipEntry = new ZipEntry(CommonUtil.standardApiFileSuffix(api.getKey()));
                zipOut.putNextEntry(zipEntry);
                zipOut.write(CtJsonUtil.toJson(api).getBytes(StandardCharsets.UTF_8));
                zipOut.closeEntry();
            }
            //处理api
            ctTemp.setApis(null);
            //处理tempHttp
            if (Objects.nonNull(ctTemp.getAuthTemp())) {
                String httpxContext = "";
                BaseAuth baseAuth = (BaseAuth) ctTemp.getAuthTemp();
                if (AuthType.TOKEN.equals(baseAuth.getAuthType())) {
                    String tokenRequest = ((TokenAuth) baseAuth).getAccessTokenRequest().getRequest();
                    if (StringUtils.isNotBlank(tokenRequest)) {
                        tokenRequest = tokenRequest.trim();
                        if (!tokenRequest.startsWith("###")) {
                            httpxContext += "###token request\n";
                        }
                        httpxContext += tokenRequest + "\n\n";
                    } else {
                        httpxContext += "###token request\n\n";
                    }
                    //去除request
                    ((TokenAuth) baseAuth).getAccessTokenRequest().setRequest(null);
                }
                //AuthRequest
                String authRequest = baseAuth.getAuthRequest();
                if (StringUtils.isNotBlank(authRequest)) {
                    authRequest = authRequest.trim();
                    if (!authRequest.startsWith("###")) {
                        httpxContext += "###auth request\n";
                    }
                    httpxContext += authRequest;
                } else {
                    httpxContext += "###auth request\n";
                }
                // 去除 authRequest
                baseAuth.setAuthRequest(null);
                // temp-http
                ZipEntry zipEntry = new ZipEntry(CommonUtil.standardHttpFileSuffix(ctTemp.getKey()));
                zipOut.putNextEntry(zipEntry);
                zipOut.write(httpxContext.getBytes(StandardCharsets.UTF_8));
                zipOut.closeEntry();
            }
            //temp自己
            ZipEntry zipEntry = new ZipEntry(CommonUtil.standardTempFileSuffix(ctTemp.getKey()));
            zipOut.putNextEntry(zipEntry);
            zipOut.write(CtJsonUtil.toJson(ctTemp).getBytes(StandardCharsets.UTF_8));
            zipOut.closeEntry();
            zipOut.finish();
            // 上传
            try (InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray())) {
                String prefix = OSSConstant.CONSOLE_FILE_PREFIX;
                String fileUrl = ossService.uploadFileAndGetUrl(prefix + CT_PREFIX,
                    ctTemp.getKey() + ".zip",
                    inputStream,
                    "application/zip",
                    null, null);
                return CtTempExportResponse.builder()
                    .fileUrl(fileUrl)
                    .build();
            } catch (Exception e) {
                throw new RuntimeException("上传导出模板链接到OSS错误: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new CtException(ErrorType.CONNECTOR_TEMP_EXPORT_ERROR, new String[]{e.getMessage()});
        }
    }

    @Override
    public void importExcelToVersion(String key, String version, boolean enabled, boolean fromMarket, CtTempImportExcelRequest request) {
        //查询version对应的temp
        VersionedCtTempPO versionedCtTempPO = findVersion(key, version, enabled, fromMarket);
        if (Objects.isNull(versionedCtTempPO) || Objects.isNull(versionedCtTempPO.getContent())) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{String.format("from key: %s version: %s enabled: %s fromMarket: %s not found valid temp", key, version, enabled, fromMarket)});
        }
        if (CollectionUtils.isEmpty(versionedCtTempPO.getContent().getApis())) {
            versionedCtTempPO.getContent().setApis(new ArrayList<>());
        }
        CtTemp ctTemp = CtTempConvertor.versionedCtTempPO2CtTemp(versionedCtTempPO);
        Map<String, CtApi> ctApiMap = ctTemp.getApis()
            .stream()
            .collect(Collectors.toMap(CtApi::getKey, Function.identity(), (v1, v2) -> v2));
        InputStream excelInputStream = getOssInputStream(request.getFileUrl());
        ExcelTemp excelTemp = new ExcelTemp();
        ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(excelInputStream, new ExcelReadListener(excelTemp));
        //读取
        excelReaderBuilder.headRowNumber(0).doReadAll();
        List<CtApi> ctApis = excelTemp.getApis().stream()
            .map(api -> ExcelHelper.apiConvertor(api, request.getExcelFieldMergeType()))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ctApis)) {
            //保存api
            for (CtApi ctApi : ctApis) {
                if (ctApiMap.containsKey(ctApi.getKey())) {
                    //有 要改名字
                    ctApi.setKey(CommonUtil.reformKey(ctApi.getKey()));
                }
                versionedCtTempPO.getContent().getApis().add(CtJsonUtil.convert(ctApi, JsonNode.class));
            }
        }

        //保存
        VersionedAppItemSaveRequest req = VersionedAppItemSaveRequest.builder()
            .key(key)
            .version(version)
            .content(versionedCtTempPO.getContent())
            .create(false)
            .build();
        super.saveVersionedAppOp(req);
    }

    private CtInst getDummyInst(CtTemp ctTemp, Map<String, Object> ctParams, Map<String, Object> extParams) {
        CtInst.CtInstProps ctInstProps = new CtInst.CtInstProps();
        ctInstProps.setEnabled(true);
        ctInstProps.setTemp(ctTemp);
        ctInstProps.setCtParams(ctParams);
        ctInstProps.setExtParams(extParams);
        CtInst ctInst = new CtInst();
        ctInst.setKey("dummy");
        ctInst.setName("dummy");
        ctInst.setResourceProps(ctInstProps);
        return ctInst;
    }


    /**
     * 服务列表
     *
     * @param key        模版标识
     * @param version    版本
     * @param enabled    是否启用
     * @param fromMarket 是否加载来自于中心市场的应用
     * @param fuzzyValue 查找值
     * @return 服务列表
     */
    @Override
    public List<CtApi> apiFindAll(String key, String version, boolean enabled, boolean fromMarket, String fuzzyValue) {
        List<CtApi> apiList = getApiList(key, version, enabled, fromMarket);
        if (StringUtils.isNotBlank(fuzzyValue)) {
            String kw = fuzzyValue.trim();
            apiList = apiList.stream()
                .filter(api ->
                    api.getKey().contains(kw)
                        || api.getName().contains(kw)
                )
                .collect(Collectors.toList());
        }
        return apiList;
    }

    /**
     * 服务分页
     *
     * @param key        模版标识
     * @param version    版本
     * @param enabled    是否启用
     * @param fromMarket 是否加载来自于中心市场的应用
     * @param fuzzyValue 查找值
     * @return 服务列表
     */
    @Override
    public Paging<CtApi> apiPaging(String key, String version, boolean enabled, boolean fromMarket, String
        fuzzyValue,
                                   Integer pageNumber, Integer pageSize) {
        List<CtApi> apiList = apiFindAll(key, version, enabled, fromMarket, fuzzyValue);
        return PagingUtil.paging(apiList, pageNumber, pageSize);
    }

    private List<CtApi> getApiList(String key, String version, boolean enabled, boolean fromMarket) {
        VersionedCtTempPO versionedCtTempPO = findVersion(key, version, enabled, fromMarket);
        List<JsonNode> apis = Optional.ofNullable(versionedCtTempPO)
            .map(VersionedCtTempPO::getContent)
            .map(CtTempContent::getApis)
            .orElse(new ArrayList<>());
        return apis.stream()
            .map(jsonNode -> CtJsonUtil.convert(jsonNode, CtApi.class))
            .collect(Collectors.toList());
    }

    @Override
    public <R extends AppItemWithVersionCreateRequest> void createAppWithVersionOp(R request) {
        CtTempWithVersionCreateRequest req = (CtTempWithVersionCreateRequest) request;
        CtTemp ctTemp = this.getCtTempFromOss(req.getOssFileUrl());
        req.setKey(ctTemp.getKey());
        req.setName(ctTemp.getName());
        req.setIcon(ctTemp.getIcon());
        req.setDescription(ctTemp.getDescription());
        req.setBaseInfo(this.transferBaseInfo(ctTemp));
        req.setContent(this.transferContent(ctTemp));
        super.createAppWithVersionOp(req);
    }

    @Override
    public <R extends AppItemSaveRequest> void saveAppOp(R request) {
        //verify temp profile info
        CtTempVerifier.verifyCtTempProfile(request);
        super.saveAppOp(request);
    }

    @Override
    public void enable(String key, @NotNull String version) {
        //verify enable
        CtTempContent dbContent = getDBTempContent(key, version);
        CtTempVerifier.verifyCtTempContextVersion(dbContent, true);
        super.enable(key, version);
    }

    @Override
    public void releaseOp(String key, @NotNull String version, @Nullable VersionRange compatibilityRange) {
        //verify release
        CtTempContent dbContent = getDBTempContent(key, version);
        CtTempVerifier.verifyCtTempContextVersion(dbContent, true);
        super.releaseOp(key, version, compatibilityRange);
    }

    @Override
    public <R extends VersionedAppItemSaveRequest> void saveVersionedAppOp(R request) {
        if (request instanceof VersionedCtTempSaveRequest) {
            VersionedCtTempSaveRequest req = (VersionedCtTempSaveRequest) request;
            VersionedCtTempSaveRequest.ReqType reqType = req.getReqType();
            //
            switch (reqType) {
                case SAVE_TEMP_INFO:
                    //C U
                    CtTempConvertor.httpRequestVOToStr(request.getContent(), true);
                    // verify CtTempContent info
                    CtTempVerifier.verifyCtTempContextVersion(request.getContent(), false);
                    doSaveTempInfo(req);
                    break;
                case CREATE_API:
                    //U
                    CtTempConvertor.httpRequestVOToStr(request.getContent(), false);
                    req.setCreate(false);
                    doApiOp(req, OperateType.CREATE);
                    break;
                case UPDATE_API:
                    //U
                    CtTempConvertor.httpRequestVOToStr(request.getContent(), false);
                    req.setCreate(false);
                    doApiOp(req, OperateType.UPDATE);
                    break;
                case DEL_API:
                    //U
                    req.setCreate(false);
                    doApiOp(req, OperateType.DELETE);
                    break;
                case UPLOAD:
                    //C U
                    doVersionUpload(req);
                    break;
                case COPY_TEMP:
                    //C
                    CtTempConvertor.httpRequestVOToStr(request.getContent(), true);
                    req.setCreate(true);
                    doCopyTemp(req);
                    break;
                default:
                    throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"非法请求"});
            }
            //保底逻辑，如果接口分组不存在，则自动添加
            refreshApiTags(req);
        } else {
            //
            CtTempContent dbContent = getDBTempContent(request.getKey(), request.getVersion());
            if (Objects.nonNull(dbContent)) {
                request.setContent(dbContent);
            }
        }
        super.saveVersionedAppOp(request);
    }

    private JsonNode getApiFromReq(CtTempContent reqContent) {
        if (Objects.isNull(reqContent)) {
            return null;
        }
        List<JsonNode> apis = reqContent.getApis();
        if (CollectionUtils.isEmpty(apis)) {
            return null;
        }
        return apis.get(0);
    }

    private void doCopyTemp(VersionedCtTempSaveRequest req) {
        CtTempContent baseContent = getDBTempContent(req.getKey(), req.getFromVersion());
        if (Objects.nonNull(baseContent)) {
            CtTempContent reqContent = req.getContent();
            if (Objects.nonNull(reqContent)) {
                if (Objects.nonNull(reqContent.getParams())) {
                    baseContent.setParams(reqContent.getParams());
                }
                if (Objects.nonNull(reqContent.getAuthTemp())) {
                    baseContent.setAuthTemp(reqContent.getAuthTemp());
                }
                if (Objects.nonNull(reqContent.getApiTagDefs())) {
                    baseContent.setApiTagDefs(reqContent.getApiTagDefs());
                }
                if (Objects.nonNull(reqContent.getApis())) {
                    baseContent.setApis(reqContent.getApis());
                }
            }
            req.setContent(baseContent);
        }
    }

    private void doSaveTempInfo(VersionedCtTempSaveRequest req) {
        //保存版本、模版参数、认证模版
        CtTempContent dbContent = getDBTempContent(req.getKey(), req.getVersion());
        if (Objects.nonNull(dbContent)) {
            CtTempContent reqContent = req.getContent();
            if (Objects.isNull(reqContent)) {
                req.setContent(dbContent);
            } else {
                BeanUtils.copyProperties(dbContent, reqContent, CtTempContent.Fields.params,
                    CtTempContent.Fields.authTemp);
            }
        }
    }

    private void doApiOp(VersionedCtTempSaveRequest req, OperateType operateType) {
        JsonNode reqApi = getApiFromReq(req.getContent());
        if (Objects.isNull(reqApi)) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"服务不能为空"});
        }
        String apiKey = getText(reqApi, CtApi.Fields.key);
        if (StringUtils.isBlank(apiKey)) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"服务标识不能为空"});
        }
        CtTempContent dbContent = getDBTempContent(req.getKey(), req.getVersion());
        switch (operateType) {
            case CREATE:
                //校验api
                CtTempVerifier.verifyCtTempApi(CtJsonUtil.convert(reqApi, CtApi.class));
                //combineApi
                dbContent = combineApi(dbContent, reqApi, apiKey, false);
                break;
            case UPDATE:
                //校验api
                CtTempVerifier.verifyCtTempApi(CtJsonUtil.convert(reqApi, CtApi.class));
                //combineApi
                dbContent = combineApi(dbContent, reqApi, apiKey, true);
                break;
            case DELETE:
                dbContent = removeApi(dbContent, apiKey);
                break;

        }
        req.setContent(dbContent);
    }

    private void doVersionUpload(VersionedCtTempSaveRequest req) {
        if (Objects.isNull(req.getKey())) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"模版标识不能为空"});
        }
        String ossFileUrl = req.getOssFileUrl();
        if (StringUtils.isBlank(ossFileUrl)) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"OSS文件不能为空"});
        }
        CtTemp ctTemp = this.getCtTempFromOss(ossFileUrl);
        if (!req.getKey().equals(ctTemp.getKey())) {
            throw new CtException(ErrorType.CONNECTOR_TEMPLATE_NOT_MATCH, new String[]{req.getKey(), ctTemp.getKey()});
        }
        req.setContent(this.transferContent(ctTemp));
    }

    /**
     * api -> tempContent
     */
    private CtTempContent combineApi(CtTempContent tempContent, JsonNode api, String apiKey, boolean isUpdate) {
        if (Objects.isNull(tempContent)) {
            tempContent = new CtTempContent();
        }
        List<JsonNode> apis = tempContent.getApis();
        if (Objects.isNull(apis)) {
            List<JsonNode> jsonNodes = new ArrayList<>();
            jsonNodes.add(api);
            tempContent.setApis(jsonNodes);
        } else {
            for (int i = 0; i < apis.size(); i++) {
                String aKey = getText(apis.get(i), CtApi.Fields.key);
                if (apiKey.equals(aKey)) {
                    //update
                    if (isUpdate) {
                        apis.set(i, api);
                    } else {
                        throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{String.format("服务标识 %s 已存在", apiKey)});
                    }
                    return tempContent;
                }
            }
            //add
            apis.add(api);
        }
        return tempContent;
    }

    /**
     * remove api from tempContent
     */
    private CtTempContent removeApi(CtTempContent tempContent, String apiKey) {
        List<JsonNode> apis = tempContent.getApis();
        if (Objects.nonNull(apis)) {
            for (int i = 0; i < apis.size(); i++) {
                String aKey = getText(apis.get(i), CtApi.Fields.key);
                if (apiKey.equals(aKey)) {
                    //remove
                    apis.remove(i);
                    return tempContent;
                }
            }
        }
        throw new CtException(ErrorType.CONNECTOR_SERVICE_NOT_EXISTED, new String[]{apiKey});
    }

    private void refreshApiTags(VersionedCtTempSaveRequest req) {
        CtTempContent content = req.getContent();
        if (Objects.isNull(content)) {
            return;
        }
        List<JsonNode> apis = content.getApis();
        if (CollectionUtils.isEmpty(apis)) {
            return;
        }
        List<JsonNode> tagDefs = content.getApiTagDefs();
        if (Objects.isNull(tagDefs)) {
            tagDefs = new ArrayList<>();
            content.setApiTagDefs(tagDefs);
        }

        Set<String> tagSet = tagDefs.stream()
            .map(jsonNode -> getText(jsonNode, CtTag.Fields.key))
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());

        for (JsonNode api : apis) {
            String tag = getText(api, CtApi.Fields.tag);
            if (StringUtils.isNotBlank(tag) && !tagSet.contains(tag)) {
                tagSet.add(tag);
                tagDefs.add(CtJsonUtil.convert(CtTag.of(null, tag, tag, null), JsonNode.class));
            }
        }
    }

    /**
     * 模版(应用)列表
     */
    @Override
    public <REQ extends AppItemListRequest> List<TrantorApplicationItemVO> listItems(REQ request) {
        List<TrantorApplicationItemVO> resList = super.listItems(request);
        if (request instanceof CtTempListRequest) {
            List<String> searchTagList = ((CtTempListRequest) request).getTags();
            if (!CollectionUtils.isEmpty(searchTagList)) {
                Set<String> searchTagSet = new HashSet<>(searchTagList);
                resList = resList.stream().filter(item -> {
                    List<String> tags = ((CtTempBaseInfo) item.getBaseInfo()).getTags();
                    if (!CollectionUtils.isEmpty(tags)) {
                        return tags.stream().anyMatch(searchTagSet::contains);
                    }
                    return false;
                }).collect(Collectors.toList());
            }
        }
        return resList;
    }


    /**
     * ct temp -> CtTempBaseInfo
     *
     * @param temp ct temp
     * @return CtTempBaseInfo
     */
    private CtTempBaseInfo transferBaseInfo(CtTemp temp) {
        CtTempBaseInfo baseInfo = new CtTempBaseInfo();
        baseInfo.setTags(temp.getTags());
        baseInfo.setTempType(temp.getTempType().name());
        baseInfo.setDetailedDesc(temp.getDetailedDesc());
        baseInfo.setDocUrl(temp.getDocUrl());
        baseInfo.setVersionNo(temp.getVersionNo());
        return baseInfo;
    }

    /**
     * ct temp -> CtTempContent
     *
     * @param temp ct temp
     * @return CtTempContent
     */
    private CtTempContent transferContent(CtTemp temp) {
        CtTempContent content = new CtTempContent();
        content.setAuthTemp(CtJsonUtil.convert(temp.getAuthTemp(), JsonNode.class));
        content.setParams(CtJsonUtil.convert(temp.getParams(), new TypeReference<List<JsonNode>>() {
        }));
        content.setApiTagDefs(CtJsonUtil.convert(temp.getApiTagDefs(), new TypeReference<List<JsonNode>>() {
        }));
        content.setApis(CtJsonUtil.convert(temp.getApis(), new TypeReference<List<JsonNode>>() {
        }));
        return content;
    }

    /**
     * 下载文件并解析元数据
     *
     * @param ossFileUrl oss file url
     * @return 模版对象
     */
    private CtTemp getCtTempFromOss(String ossFileUrl) {
        Map<String, String> contentMap = getContentFromOssFile(ossFileUrl);
        CtTemp temp = null;
        Map<String, CtApi> apiMap = new HashMap<>();
        //根据文件名判断 *temp.json-模版文件; *api.json-接口文件; .http-对应http请求文件
        for (String fileName : contentMap.keySet()) {
            if (fileName.endsWith(API_SUFFIX)) {
                //api
                CtApi apiVO = CtJsonUtil.fromJson(contentMap.get(fileName), CtApi.class);
                String httpName = this.getHttpFileName(fileName);
                if (contentMap.containsKey(httpName)) {
                    apiVO.setApiRequest(contentMap.get(httpName));
                }
                //
                apiVO.checkParams();
                String apiFullKey = apiVO.fullKey();
                if (apiMap.containsKey(apiFullKey)) {
                    throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"存在重复的服务, " + apiFullKey});
                }
                apiMap.put(apiFullKey, apiVO);
            } else if (fileName.endsWith(TEMP_SUFFIX)) {
                //temp
                if (Objects.nonNull(temp)) {
                    throw new CtException(ErrorType.BAD_REQUEST, "存在多个模版文件");
                }
                temp = CtJsonUtil.fromJson(contentMap.get(fileName), CtTemp.class);
                String httpName = this.getHttpFileName(fileName);
                if (contentMap.containsKey(httpName)) {
                    String httpContent = contentMap.get(httpName);
                    if (AuthType.TOKEN.equals(temp.getAuthTemp().getAuthType())) {
                        //token
                        TokenAuth tokenAuth = (TokenAuth) temp.getAuthTemp();
                        List<HttpRequest> httpRequests = HttpRequestParser.parseHttpFile(httpContent).getRequests();
                        if (Objects.isNull(httpRequests) || httpRequests.size() != 2) {
                            throw new CtException(ErrorType.BAD_REQUEST, "模版http文件请求体为个数为2");
                        }
                        tokenAuth.getAccessTokenRequest().setRequest(httpRequests.get(0).getRequestCode());
                        tokenAuth.setAuthRequest(httpRequests.get(1).getRequestCode());
                    } else {
                        ((BaseAuth) temp.getAuthTemp()).setAuthRequest(httpContent);
                    }
                }
                //
                temp.checkParams();
            }
        }
        if (Objects.isNull(temp)) {
            throw new CtException(ErrorType.BAD_REQUEST, "不存在模版文件");
        }
        temp.setApis(new ArrayList<>(apiMap.values()));
        String tempKey = temp.getKey();
        temp.getApis().forEach(api -> api.setCtTempKey(tempKey));
        //
        return temp;
    }

    private String getHttpFileName(String fileName) {
        return fileName.substring(0, fileName.length() - 4) + "http";
    }

    private Map<String, String> getContentFromOssFile(String fileUrl) {
        Map<String, String> contentMap = new HashMap<>();
        //
        try {
            try (ZipInputStream zis = new ZipInputStream(this.getOssInputStream(fileUrl))) {
                for (ZipEntry entry = zis.getNextEntry(); entry != null; ) {
                    if (!entry.isDirectory() && checkFile(entry.getName())) {
                        contentMap.put(entry.getName().toLowerCase(),
                            StreamUtils.copyToString(zis, StandardCharsets.UTF_8));
                    }
                    zis.closeEntry();
                    entry = zis.getNextEntry();
                }
            }
            return contentMap;
        } catch (Exception e) {
            throw new CtException(ErrorType.CONNECTOR_UNKNOWN_ERROR, "获取模版文件失败", e);
        }
    }

    private InputStream getOssInputStream(String fileUrl) {
        String objName;
        try {
            objName = new URI(fileUrl).getPath();
            if (objName.startsWith("/")) {
                objName = objName.substring(1);
            }
        } catch (Exception e) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{"上传文件格式错误: " + fileUrl});
        }
        //公有读
        InputStream inputStream = null;
        try {
            inputStream = ossService.migrateFileOut(objName, false);
        } catch (Exception ignored) {
        }
        //私有读
        if (Objects.isNull(inputStream)) {
            try {
                inputStream = ossService.migrateFileOut(objName, true);
            } catch (Exception ignored) {
            }
        }
        if (Objects.isNull(inputStream)) {
            throw new CtException(ErrorType.CONNECTOR_UNKNOWN_ERROR, "无法从OSS获取文件: " + fileUrl);
        }
        return inputStream;
    }

    private CtTempContent getDBTempContent(String key, String version) {
        Optional<VersionedCtTempPO> dbApp = getVersionedRepo().findOneByKeyAndAppVersionAndLocal(
            key, version, true);
        return dbApp.map(VersionedCtTempPO::getContent).orElse(null);
    }

    private boolean checkFile(String fileName) {
        if (!StringUtils.endsWith(fileName, ".json")
            && !StringUtils.endsWith(fileName, ".http")) {
            return false;
        }
        String[] strList = fileName.split("/");
        return !StringUtils.startsWith(strList[strList.length - 1], ".");
    }

    private String getText(JsonNode node, String fieldName) {
        return Optional.ofNullable(node.get(fieldName))
            .filter(JsonNode::isTextual).map(JsonNode::asText).orElse(null);
    }

    @Override
    public ApplicationType getApplicationType() {
        return ApplicationType.CONNECTOR_TEMPLATE;
    }

    @Override
    public String getGroup() {
        return CONNECTOR_TEMPLATE_GROUP;
    }

    @Override
    public String getExtension() {
        return "json";
    }

    @Override
    public CtTempRepo getRepo() {
        return this.tempRepo;
    }

    @Override
    public VersionedCtTempRepo getVersionedRepo() {
        return this.versionedTempRepo;
    }
}
