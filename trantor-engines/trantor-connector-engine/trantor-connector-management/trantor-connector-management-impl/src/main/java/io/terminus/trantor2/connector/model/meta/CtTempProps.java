package io.terminus.trantor2.connector.model.meta;

import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.meta.auth.Auth;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Ct temp props
 */
@Getter
@Setter
public class CtTempProps {
    /**
     * 连接器分类标签,可多个
     */
    private List<String> tags;
    /**
     * 模版类型，默认HTTP模版
     */
    private CtTempType tempType = CtTempType.HTTP;
    /**
     * 详细描述(markdown)
     */
    private String detailedDesc;
    /**
     * 版本号,对应三方版本
     */
    private String versionNo;
    /**
     * 排序号
     */
    private String sort;
    /**
     * 文档url
     */
    private String docUrl;
    /**
     * (连接器)模版参数
     */
    private List<CtField> params;
    /**
     * (连接器)认证模版
     */
    private Auth authTemp;
    /**
     * 接口列表
     */
    private List<CtApi> apis;
}
