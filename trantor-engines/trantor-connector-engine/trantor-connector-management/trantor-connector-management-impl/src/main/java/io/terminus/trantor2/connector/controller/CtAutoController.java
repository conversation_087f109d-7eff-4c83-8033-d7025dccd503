package io.terminus.trantor2.connector.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.field.CtFieldConvertor;
import io.terminus.trantor2.connector.model.field.FormatType;
import io.terminus.trantor2.connector.model.functions.auto.request.ArgsConvertRequest;
import io.terminus.trantor2.connector.model.functions.auto.request.ArgsGenerateRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotNull;

@Tag(name = "连接器自动化工具", description = "auto")
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/console/connector/auto")
@Validated
public class CtAutoController {


    @PostMapping("/generate/api/args")
    @Operation(summary = "参数生成服务")
    public Response<CtField> generateApiArgs(
        @Parameter(description = "请求参数生成请求", required = true)
        @Validated
        @NotNull(message = "请求参数生成请求不能为空")
        @RequestBody ArgsGenerateRequest request
    ) {
        FormatType formatType = request.getFormatType();
        CtField ctField = formatType.from(request.getRaw());
        return Response.ok(ctField);
    }


    @PostMapping("/args/convert")
    @Operation(summary = "参数转换服务")
    public Response<Object> argsConvert(
        @Parameter(description = "参数转换服务请求", required = true)
        @Validated
        @NotNull(message = "参数转换服务请求不能为空")
        @RequestBody ArgsConvertRequest request
    ) {
        CtFieldConvertor convertTo = request.getConvertTo();
        return Response.ok(convertTo.generate(request.getArgs()));

    }
}
