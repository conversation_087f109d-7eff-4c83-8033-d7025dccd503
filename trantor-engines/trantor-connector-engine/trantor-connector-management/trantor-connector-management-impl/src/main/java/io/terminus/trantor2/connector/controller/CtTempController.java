package io.terminus.trantor2.connector.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.application.dto.AppItemListRequest;
import io.terminus.trantor2.application.vo.TrantorApplicationItemVO;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import io.terminus.trantor2.connector.convertor.CtTempConvertor;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.meta.*;
import io.terminus.trantor2.connector.model.meta.auth.AuthType;
import io.terminus.trantor2.connector.model.response.CtTestCallResponse;
import io.terminus.trantor2.connector.model.vo.CtApiListVO;
import io.terminus.trantor2.connector.model.vo.CtApiVOForMgr;
import io.terminus.trantor2.connector.request.CtTempListRequest;
import io.terminus.trantor2.connector.request.CtTempUploadRequest;
import io.terminus.trantor2.connector.service.impl.CtTempApiImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 连接器模版管理Controller
 */
@Tag(name = "连接器模版管理", description = "console")
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/console/connector/temp")
@Validated
public class CtTempController {
    private final CtTempApiImpl ctTempApi;

    /**
     * 上传连接器模版
     *
     * @param ctTempUploadRequest 上传模版请求
     */
    @PostMapping(path = "/upload")
    @Operation(summary = "上传模版", deprecated = true, description = "废弃，使用“应用市场”下的新建应用和版本接口或新建版本接口")
    @Deprecated
    public Response<Void> upload(
        @Validated
        @Parameter(description = "模版上传对象", required = true)
        @NotNull(message = "上传对象不能为空")
        @RequestBody CtTempUploadRequest ctTempUploadRequest) {
        ctTempApi.upload(ctTempUploadRequest.getOssFileUrl(), ctTempUploadRequest.getAppVersion());
        return Response.ok();
    }

    /**
     * 删除连接器模版
     *
     * @param key 模版key
     */
    @PostMapping(path = "/delete/{key}")
    @Operation(summary = "删除模版", deprecated = true, description = "废弃，使用“应用市场”下的下架接口")
    @Deprecated
    public Response<Void> deleteByKey(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key) {
        ctTempApi.unlistOp(key, null);
        return Response.ok();
    }

    /**
     * 根据key查找模版
     *
     * @param key 模版key
     * @return 模版对象
     */
    @GetMapping("/find/{key}")
    @Operation(summary = "模版详情")
    public Response<VersionedCtTempPO> findByKey(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false) String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true") boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket) {
        VersionedCtTempPO versionedCtTempPO = ctTempApi.findVersion(key, version, enabled, fromMarket);
        CtTempConvertor.httpRequestStrToVO(versionedCtTempPO.getContent());
        return Response.ok(versionedCtTempPO);
    }


    /**
     * 导入excel到某个key的version
     *
     * @param key 模版key
     * @return 模版对象
     */
    @PostMapping("/{key}/excel/import")
    @Operation(summary = "导入excel到某个版本")
    public Response<Void> importToKey(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false) String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true") boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket,
        @NotNull(message = "导入对象不能为空")
        @RequestBody CtTempImportExcelRequest request) {
        ctTempApi.importExcelToVersion(key, version, enabled, fromMarket, request);
        return Response.ok();
    }

    /**
     * 根据模板key导出
     *
     * @param key 模版key
     * @return 模版对象
     */
    @GetMapping("/export/{key}")
    @Operation(summary = "导出模板")
    public Response<CtTempExportResponse> exportByKey(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false) String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true") boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket) {
        return Response.ok(ctTempApi.exportVersion(key, version, enabled, fromMarket));
    }

    /**
     * 列表查询-连接器模版
     *
     * @param fuzzyValue 查找值
     * @return 列表
     */
    @GetMapping("/list")
    @Operation(summary = "列表查询", deprecated = true, description = "废弃，使用“应用市场”下的列表接口")
    @Deprecated
    public Response<List<TrantorApplicationItemVO>> findAll(
        @Parameter(description = "关键词")
        @RequestParam(required = false) String fuzzyValue,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket,
        @Parameter(description = "只加载开启,默认为 true")
        @RequestParam(required = false, defaultValue = "true") boolean filterEnabled) {
        AppItemListRequest request = new AppItemListRequest(fromMarket, filterEnabled, fuzzyValue);
        return Response.ok(ctTempApi.listItems(request));
    }

    @PostMapping("/list")
    @Operation(summary = "列表查询")
    @InjectUserInfos
    public Response<List<TrantorApplicationItemVO>> findAll(
        @NotNull(message = "查询对象不能为空")
        @RequestBody CtTempListRequest request) {
        return Response.ok(ctTempApi.listItems(request));
    }

    /**
     * 列表-模版标签定义
     */
    @GetMapping("/tag-defs")
    @Operation(summary = "模版标签定义列表")
    public Response<List<CtTag>> getTags() {
        return Response.ok(CtTag.getCtTags());
    }

    /**
     * 认证默认参数
     */
    @GetMapping("/auth/default-params/{authType}")
    @Operation(summary = "认证默认参数")
    public Response<List<CtField>> getAuthDefaultParams(
        @NotNull(message = "认证类型不能为空")
        @PathVariable AuthType authType) {
        return Response.ok(authType.getDefaultFields());
    }

    @GetMapping("/versions/{key}/api/{apiKey}")
    @Operation(summary = "服务详情")
    public Response<CtApiVOForMgr> apiDetail(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key,
        @Parameter(description = "服务标识", required = true)
        @NotBlank(message = "服务标识不能为空")
        @PathVariable String apiKey,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false) String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true") boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket
    ) {
        return Response.ok(CtApiVOForMgr.convert(ctTempApi.apiDetail(key, apiKey, version, enabled, fromMarket)));
    }

    @PostMapping("/versions/{key}/api/test")
    @Operation(summary = "服务测试")
    public Response<CtTestCallResponse> apiTest(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false) String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true") boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket,
        @NotNull(message = "查询对象不能为空")
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestBody CtTempApiTestRequest request
    ) {
        return ctTempApi.apiTest(key, version, enabled, fromMarket, request);
    }


    @GetMapping("/versions/{key}/api/list")
    @Operation(summary = "模版-版本-服务列表")
    public Response<List<CtApiListVO>> apiFindAll(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable
        String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false)
        String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true")
        boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true")
        boolean fromMarket,
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue
    ) {

        return Response.ok(CtApiListVO.convert(ctTempApi.apiFindAll(key, version, enabled, fromMarket, fuzzyValue)));
    }

    @GetMapping("/versions/{key}/api/paging")
    @Operation(summary = "服务分页")
    public Response<Paging<CtApiListVO>> apiPaging(
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @PathVariable
        String key,
        @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
        @RequestParam(required = false)
        String version,
        @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
        @RequestParam(required = false, defaultValue = "true")
        boolean enabled,
        @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
        @RequestParam(required = false, defaultValue = "true")
        boolean fromMarket,
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue,
        @Parameter(description = "页号")
        @RequestParam
        @NotNull(message = "页号不能为空")
        @Min(value = 1, message = "页号非法")
        Integer pageNumber,
        @Parameter(description = "每页条数")
        @RequestParam
        @NotNull(message = "每页条数能为空")
        @Min(value = 1, message = "每页条数非法")
        Integer pageSize
    ) {
        return Response.ok(CtApiListVO.convert(
            ctTempApi.apiPaging(key, version, enabled, fromMarket, fuzzyValue, pageNumber, pageSize)));
    }


}
