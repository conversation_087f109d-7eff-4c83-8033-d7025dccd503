package io.terminus.trantor2.connector.functions.ai;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.connector.model.functions.ai.models.OpenAiPlatForm;
import io.terminus.trantor2.connector.model.functions.ai.models.OpenAiPlatFormApi;
import io.terminus.trantor2.connector.model.functions.ai.models.Page;
import io.terminus.trantor2.connector.model.meta.CtApi;

import io.terminus.trantor2.connector.utils.CtJsonUtil;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class AICopilot {
    private static final OkHttpClient FETCH_AI_CLIENT = new OkHttpClient.Builder()
        .connectTimeout(120, TimeUnit.SECONDS)
        .readTimeout(120, TimeUnit.SECONDS)
        .writeTimeout(120, TimeUnit.SECONDS)
        .build();


    private static final MediaType AI_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    /**
     * 单个url分析接口
     */
    private static final String AI_ANALYZE_URL = "https://t-ai.app.terminus.io/api/trantor/service/engine/execute/ai_app_build$smart_connector_plus";

    /**
     * 查询ai平台列表接口
     */
    private static final String AI_QUERY_OPEN_AI_PLANTFORM_URL = "https://t-ai-portal.app.terminus.io/api/trantor/service/engine/execute/ai_app_build$openapi_platform_list_query";


    /**
     * 查询ai平台对应名称
     */
    private static final String AI_QUERY_OPEN_AI_PLANTFORM_API_URL = "https://t-ai-portal.app.terminus.io/api/trantor/service/engine/execute/ai_app_build$openapi_platform_api_list_query";


    private static final Integer teamId = 1;


    private static String packFetchAICtApiByUrlReq(String apiUrl) {
        Map<String, Object> res = new HashMap<>();
        res.put("teamId", teamId);
        Map<String, Object> params = new HashMap<>();
        params.put("api_url", apiUrl);
        res.put("params", params);
        return JsonUtil.toJson(res);
    }

    /**
     * @param apiUrl 支持网站的api的网页地址
     * @return 返回的api
     * @throws Exception 错误
     */
    public static CtApi fetchAICtApiByUrl(String apiUrl) throws Exception {
        String aiAnalyzeReq = packFetchAICtApiByUrlReq(apiUrl);
        RequestBody requestBody = RequestBody.create(AI_MEDIA_TYPE, aiAnalyzeReq);
        Request request = new Request.Builder()
            .url(AI_ANALYZE_URL)
            .post(requestBody)
            .build();
        try (Response response = FETCH_AI_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();
            if (body == null) {
                throw new RuntimeException("response body is empty");
            }
            String bodyStr = body.string();
            if (response.isSuccessful()) {
                io.terminus.trantor2.common.dto.Response<Map<String, Object>> ctApiResponse = JsonUtil.fromJson(bodyStr, new TypeReference<io.terminus.trantor2.common.dto.Response<Map<String, Object>>>() {
                });
                if (!ctApiResponse.isSuccess()) {
                    throw new RuntimeException(ctApiResponse.getErrMsg());
                }
                Map<String, Object> data = ctApiResponse.getData();
                String ctApiJsonStr = (String) data.get("json_schema");
                if (StringUtils.isBlank(ctApiJsonStr)) {
                    throw new RuntimeException("数据生成有误");
                }
                return CtJsonUtil.fromJson(ctApiJsonStr, CtApi.class);
            } else {
                throw new RuntimeException(String.format("unexpected response code: %s, response body: %s", response.code(), bodyStr));
            }
        } catch (Exception e) {
            throw new RuntimeException("AI系统异常(获取连接器服务实例): " + e.getMessage());
        }
    }

    private static String packFetchOpenAIPlatFormByName(String plantFormName) {
        Map<String, Object> res = new HashMap<>();
        res.put("teamId", teamId);
        Map<String, Object> params = new HashMap<>();
        params.put("platformName", plantFormName);
        res.put("params", params);
        return JsonUtil.toJson(res);
    }

    /**
     * @param platFormName 平台名称 不传则查询全部OpenAPI平台
     * @return 返回平台列表
     * @throws Exception 错误
     */
    public static List<OpenAiPlatForm> fetchOpenAIPlatFormByName(String platFormName) throws Exception {
        String openAIPlatFormByNameReq = packFetchOpenAIPlatFormByName(platFormName);
        RequestBody requestBody = RequestBody.create(AI_MEDIA_TYPE, openAIPlatFormByNameReq);
        Request request = new Request.Builder()
            .url(AI_QUERY_OPEN_AI_PLANTFORM_URL)
            .post(requestBody)
            .build();
        try (Response response = FETCH_AI_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();
            if (body == null) {
                throw new RuntimeException("response body is empty");
            }
            String bodyStr = body.string();
            if (response.isSuccessful()) {
                io.terminus.trantor2.common.dto.Response<List<OpenAiPlatForm>> res = JsonUtil.fromJson(bodyStr, new TypeReference<io.terminus.trantor2.common.dto.Response<List<OpenAiPlatForm>>>() {
                });

                if (!res.isSuccess()) {
                    throw new RuntimeException(res.getErrMsg());
                }
                return res.getData();
            } else {
                throw new RuntimeException(String.format("unexpected response code: %s, response body: %s", response.code(), bodyStr));
            }
        } catch (Exception e) {
            throw new RuntimeException("AI系统异常(查询支持AI的平台): " + e.getMessage());
        }
    }


    private static String fetchOpenAIPlatFormApisReq(String platFormName, String apiName) {
        Map<String, Object> res = new HashMap<>();
        res.put("teamId", teamId);
        Map<String, Object> params = new HashMap<>();
        params.put("platformName", platFormName);
        params.put("apiName", apiName);
        Map<String, Object> pageable = new HashMap<>();
        pageable.put("pageNo", 1);
        pageable.put("pageSize", 1000); //写死1000
        params.put("pageable", pageable);
        res.put("params", params);
        return JsonUtil.toJson(res);
    }


    /**
     * @param platFormName 平台名称 【必填】
     * @param apiName      api名称 【可选填】，不传则查询全部接口，否则模糊查询
     * @return 返回平台列表
     * @throws Exception 错误
     */
    public static List<OpenAiPlatFormApi> fetchOpenAIPlatFormApis(String platFormName, String apiName) throws Exception {
        String openAIPlatFormApisReq = fetchOpenAIPlatFormApisReq(platFormName, apiName);
        RequestBody requestBody = RequestBody.create(AI_MEDIA_TYPE, openAIPlatFormApisReq);
        Request request = new Request.Builder()
            .url(AI_QUERY_OPEN_AI_PLANTFORM_API_URL)
            .post(requestBody)
            .build();
        try (Response response = FETCH_AI_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();
            if (body == null) {
                throw new RuntimeException("response body is empty");
            }
            String bodyStr = body.string();
            if (response.isSuccessful()) {
                io.terminus.trantor2.common.dto.Response<Page<OpenAiPlatFormApi>> res = JsonUtil.fromJson(bodyStr, new TypeReference<io.terminus.trantor2.common.dto.Response<Page<OpenAiPlatFormApi>>>() {
                });

                if (!res.isSuccess()) {
                    throw new RuntimeException(res.getErrMsg());
                }
                List<OpenAiPlatFormApi> data = res.getData().getData();
                return CollectionUtils.isEmpty(data) ? new ArrayList<>() : data;
            } else {
                throw new RuntimeException(String.format("unexpected response code: %s, response body: %s", response.code(), bodyStr));
            }
        } catch (Exception e) {
            throw new RuntimeException("AI系统异常(查询平台api信息): " + e.getMessage());
        }
    }


}
