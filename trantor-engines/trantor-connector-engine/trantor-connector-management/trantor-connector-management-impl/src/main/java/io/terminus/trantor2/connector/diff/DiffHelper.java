package io.terminus.trantor2.connector.diff;

import cn.hutool.core.util.StrUtil;
import io.terminus.trantor2.connector.model.diff.DiffDetail;
import io.terminus.trantor2.connector.model.diff.DiffType;
import io.terminus.trantor2.connector.model.field.CtField;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class DiffHelper {




    protected static List<DiffDetail> diffFromCtField(CtField ct1, CtField ct2) {
        List<DiffDetail> diffDetails = new ArrayList<>();
        //key
        if (!Objects.equals(ct1.getFieldKey(), ct2.getFieldKey())) {
            diffDetails.add(DiffDetail.builder().
                before(ct1.getFieldKey()).
                after(ct2.getFieldKey()).
                detailPath("Key").
                message("字段Key不一致").
                diffType(DiffType.UPDATE).
                build());
        }
        //name
        if (!Objects.equals(ct1.getFieldName(), ct2.getFieldName())) {
            diffDetails.add(DiffDetail.builder().
                before(ct1.getFieldName()).
                after(ct2.getFieldName()).
                detailPath("名称").
                message("字段名称不一致").
                diffType(DiffType.UPDATE).
                build());
        }
        //字段类型
        if (!Objects.equals(ct1.getFieldType(), ct2.getFieldType())) {
            diffDetails.add(DiffDetail.builder().
                before(String.valueOf(ct1.getFieldType())).
                after(String.valueOf(ct2.getFieldType())).
                detailPath("字段类型").
                message("字段类型不一致").
                diffType(DiffType.UPDATE).
                build());
        }
        //required
        if (!Objects.equals(ct1.getRequired(), ct2.getRequired())) {
            diffDetails.add(DiffDetail.builder().
                before(String.valueOf(ct1.getRequired())).
                after(String.valueOf(ct2.getRequired())).
                detailPath("名称").
                message("字段名称不一致").
                diffType(DiffType.UPDATE).
                build());
        }
        //description
        if (!Objects.equals(ct1.getDescription(), ct2.getDescription())) {
            diffDetails.add(DiffDetail.builder().
                before(String.valueOf(ct1.getDescription())).
                after(String.valueOf(ct2.getDescription())).
                detailPath("备注").
                message("字段备注不一致").
                diffType(DiffType.UPDATE).
                build());
        }
        //formatType
        if (!Objects.equals(ct1.getFormatType(), ct2.getFormatType())) {
            diffDetails.add(DiffDetail.builder().
                before(String.valueOf(ct1.getFormatType())).
                after(String.valueOf(ct2.getFormatType())).
                detailPath("序列化类型").
                message("字段序列化类型不一致").
                diffType(DiffType.UPDATE).build());
        }
        return diffDetails;
    }


    protected static String appendPath(String... path) {
        return StrUtil.join(" > ", Arrays.asList(path));
    }


}
