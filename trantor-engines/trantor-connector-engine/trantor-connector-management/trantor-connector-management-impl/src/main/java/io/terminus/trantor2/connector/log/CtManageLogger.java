package io.terminus.trantor2.connector.log;

import io.terminus.trantor2.connector.log.logger.CtLogger;
import io.terminus.trantor2.connector.log.models.CtLog;
import io.terminus.trantor2.connector.log.models.data.CtLogData;
import io.terminus.trantor2.connector.utils.CtContext;

import java.util.Date;

public class CtManageLogger implements CtLogger {

    @Override
    public void doLog(CtLogData ctLogData) {
        Date logAt = new Date();
        String reqId = CtContext.getReqId();
        CtLog ctLog = CtLog.builder()
            .ctLogStage(ctLogData.stage())
            .reqId(reqId)
            .logAt(logAt)
            .data(ctLogData)
            .build();
        CtContext.log(ctLog);
    }
}
