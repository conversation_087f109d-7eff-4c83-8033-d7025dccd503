package io.terminus.trantor2.connector.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.dto.AppItemListRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Schema(description = "模版批量请求")
@AllArgsConstructor
@NoArgsConstructor
public class CtTempListRequest extends AppItemListRequest {
    @Schema(description = "模版分组")
    private List<String> tags;
}
