package io.terminus.trantor2.connector.model.meta;

import io.terminus.trantor2.application.item.connector.CtTempBaseInfo;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.application.po.TrantorAppItemPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

@Slf4j
@Generated
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("CONNECTOR_TEMPLATE")
public class CtTempPO extends TrantorAppItemPO<CtTempBaseInfo, CtTempContent> {
    private static final long serialVersionUID = -3314779458378315932L;
}
