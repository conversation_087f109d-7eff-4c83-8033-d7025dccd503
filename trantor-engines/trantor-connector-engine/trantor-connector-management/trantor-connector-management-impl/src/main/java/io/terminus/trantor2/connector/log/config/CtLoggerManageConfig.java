package io.terminus.trantor2.connector.log.config;

import io.terminus.trantor2.connector.log.CtManageLogger;
import io.terminus.trantor2.connector.log.logger.CtLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
//@ConditionalOnProperty(value = "connector.logger.strategy", havingValue = "manage", matchIfMissing = true)
@Slf4j
public class CtLoggerManageConfig {

    @Bean
    public CtLogger ctManageLogger() {
        log.info("==> Using manageLogger");
        return new CtManageLogger();
    }


}
