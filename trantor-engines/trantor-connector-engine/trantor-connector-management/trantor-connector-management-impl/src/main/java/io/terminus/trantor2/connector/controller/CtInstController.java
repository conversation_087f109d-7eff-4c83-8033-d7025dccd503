package io.terminus.trantor2.connector.controller;

import cn.hutool.core.map.MapUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.connector.model.diff.TempDiffReport;
import io.terminus.trantor2.connector.model.Constants;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.meta.CtInst;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.request.CtCallRequest;
import io.terminus.trantor2.connector.model.request.CtTestCallRequest;
import io.terminus.trantor2.connector.model.response.CtTestCallResponse;
import io.terminus.trantor2.connector.model.vo.CtApiListVO;
import io.terminus.trantor2.connector.model.vo.CtApiVOForUse;
import io.terminus.trantor2.connector.model.vo.CtTempVOForUse;
import io.terminus.trantor2.connector.service.CtInstApi;
import io.terminus.trantor2.connector.service.CtTestCallApi;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.CtInstConfig;
import io.terminus.trantor2.module.meta.EnvConfig;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 连接器实例管理Controller
 */
@Tag(name = "连接器实例管理")
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/console/connector/instance")
@Validated
public class CtInstController {
    private final CtInstApi ctInstApi;
    private final ConfigurationService configurationService;
    private final CtTestCallApi testCallApi;

    @PostMapping(path = "/create")
    @Operation(summary = "新建实例")
    public Response<Long> create(
        @Parameter(description = "实例对象", required = true)
        @NotNull(message = "实例对象不能为空")
        @RequestBody CtInst ctInst,
        @Parameter(description = "模版标识", required = true)
        @NotBlank(message = "模版标识不能为空")
        @RequestParam String tempKey,
        @Parameter(description = "版本")
        @RequestParam String version,
        @Parameter(description = "是否为中心市场模版, 默认为true")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket
    ) {
        ctInst.checkParams();
        //
        CtInst.CtInstProps props = ctInst.getResourceProps();
        if (Objects.isNull(props)) {
            props = new CtInst.CtInstProps();
            ctInst.setResourceProps(props);
        }
        CtTemp temp = new CtTemp();
        temp.setKey(tempKey);
        temp.setAppVersion(version);
        temp.setFromMarket(fromMarket);
        props.setTemp(temp);
        return Response.ok(ctInstApi.create(ctInst));
    }

    @PostMapping(path = "/update")
    @Operation(summary = "修改实例")
    public Response<CtInst> update(
        @Parameter(description = "实例对象", required = true)
        @NotNull(message = "实例对象不能为空")
        @RequestBody CtInst ctInst) {
        ctInst.checkParams();
        return Response.ok(convert(ctInstApi.update(ctInst), false));
    }

    @PostMapping(path = "/upgrade/{key}")
    @Operation(summary = "升级实例的模版快照")
    public Response<CtInst> upgrade(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key,
        @Parameter(description = "版本")
        @RequestParam String version,
        @Parameter(description = "是否为中心市场模版, 默认为true")
        @RequestParam(required = false, defaultValue = "true") boolean fromMarket
    ) {
        return Response.ok(convert(ctInstApi.upgrade(key, version, fromMarket), false));
    }

    @GetMapping(path = "/upgrade/diff/{key}")
    @Operation(summary = "升级实例的模版快照(diff)")
    public Response<TempDiffReport> upgradeDiff(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key
    ) {
        return Response.ok(ctInstApi.upgradeDiff(key));
    }

    @PostMapping(path = "/delete/{key}")
    @Operation(summary = "删除实例")
    public Response<Void> deleteByKey(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key) {
        ctInstApi.deleteByKey(key);
        return Response.ok();
    }

    @GetMapping("/find/{key}")
    @Operation(summary = "获取单个实例")
    public Response<CtInst> findByKey(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key) {
        return Response.ok(convert(ctInstApi.findByKey(key), true));
    }

    @GetMapping(path = "/paging")
    @Operation(summary = "分页查询")
    public Response<Paging<CtInst>> paging(
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue,
        @Parameter(description = "页号")
        @RequestParam
        @NotNull(message = "页号不能为空")
        @Min(value = 1, message = "页号非法")
        Integer pageNumber,
        @Parameter(description = "每页条数")
        @RequestParam
        @NotNull(message = "每页条数能为空")
        @Min(value = 1, message = "每页条数非法")
        Integer pageSize) {
        Paging<CtInst> res = ctInstApi.paging(fuzzyValue, pageNumber, pageSize);
        res.getData().forEach(ctInst -> convert(ctInst, false));
        return Response.ok(res);
    }

    @GetMapping("/list")
    @Operation(summary = "列表查询")
    public Response<List<CtInst>> findAll(
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue) {
        return Response.ok(ctInstApi.findAll(fuzzyValue)
            .stream().map(v -> convert(v, false))
            .collect(Collectors.toList()));
    }

    @GetMapping("/list-enabled")
    @Operation(summary = "列表查询(已启用)")
    public Response<List<CtInst>> findAllEnabled(
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue) {
        return Response.ok(
            ctInstApi.findAll(fuzzyValue).stream()
                .filter(ctInst -> ctInst.getResourceProps().getEnabled())
                .map(v -> convert(v, false))
                .collect(Collectors.toList())
        );
    }

    @PostMapping(path = "/enable/{key}")
    @Operation(summary = "启用实例")
    public Response<Void> enable(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key) {
        ctInstApi.enable(key);
        return Response.ok();
    }

    @PostMapping(path = "/disable/{key}")
    @Operation(summary = "停用实例")
    public Response<Void> disable(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @PathVariable String key) {
        ctInstApi.disable(key);
        return Response.ok();
    }

    @Operation(summary = "实例配置保存")
    @PostMapping(value = "/config/save")
    public Response<Void> bindModule(@RequestBody CtInstConfig config) {
        if (Objects.nonNull(config)) {
            Map<String, Object> ctParams = config.getCtParams();
            if (MapUtil.isNotEmpty(ctParams)) {
                CtInst ctInst = ctInstApi.findByKey(KeyUtil.newKeyUnderModule(config.getModuleKey(), config.getModuleKey()));
                CtTemp ctTemp = ctInst.getResourceProps().getTemp();
                List<CtField> params = ctTemp.getParams();
                CtInstConfig oldConfig = configurationService.query(TrantorContext.getTeamId(), config.getModuleKey(),
                    ConfigType.Connector_Inst_Param);
                Map<String, Object> oldParams = Objects.nonNull(oldConfig) ? oldConfig.getCtParams() : new HashMap<>();
                Map<String, Object> newParams = new HashMap<>();
                for (CtField param : params) {
                    String fieldKey = param.getFieldKey();
                    Object newValue = ctParams.get(fieldKey);
                    Object oldValue = oldParams.get(fieldKey);
                    // 有新的
                    if (newValue != null) {
                        //password
                        if (FieldType.Password.equals(param.getFieldType())) {
                            String newPass = (String) newValue;
                            //新的值传了有效值
                            if (StringUtils.isNotBlank(newPass)  && !Constants.Password_Mask.equals(newPass)) {
                                newParams.put(fieldKey, newValue);
                            } else {
                                //保留旧的
                                if (oldValue != null){
                                    newParams.put(fieldKey, oldValue);
                                }
                            }
                        } else {
                            newParams.put(fieldKey, newValue);
                        }
                        //有旧的
                    } else if (oldValue != null) {
                        newParams.put(fieldKey, oldValue);
                    }
                }
                config.setCtParams(newParams);
            }
        }
        //保存
        configurationService.save(config, TrantorContext.getTeamId(), config.getModuleKey(),
            ConfigType.Connector_Inst_Param);
        return Response.ok();
    }

    @GetMapping("/config/find/{moduleKey}")
    @Operation(summary = "获取单个实例配置")
    public Response<CtInstConfig> findConfigByKey(
        @Parameter(description = "连接器实例ParentKey", required = true)
        @NotBlank(message = "连接器实例ParentKey不能为空")
        @PathVariable String moduleKey) {
        CtInstConfig config = configurationService.query(TrantorContext.getTeamId(), moduleKey,
            ConfigType.Connector_Inst_Param);
        if (config == null) {
            return Response.ok(null);
        }
        Map<String, Object> ctParams = config.getCtParams();
        if (MapUtil.isNotEmpty(ctParams)) {
            CtInst ctInst = ctInstApi.findByKey(KeyUtil.newKeyUnderModule(moduleKey, moduleKey));
            CtTemp ctTemp = ctInst.getResourceProps().getTemp();
            List<CtField> params = ctTemp.getParams();
            for (CtField param : params) {
                String fieldKey = param.getFieldKey();
                if (ctParams.containsKey(fieldKey)) {
                    if (FieldType.Password.equals(param.getFieldType())) {
                        ctParams.put(fieldKey, Constants.Password_Mask);
                    }
                }

            }
        }
        return Response.ok(config);
    }

    @GetMapping("/config/list")
    @Operation(summary = "获取实例配置列表")
    public Response<List<EnvConfig>> findConfigAll() {
        Collection<EnvConfig> collection = configurationService.queryAll(
            TrantorContext.getTeamId(),
            ConfigType.Connector_Inst_Param).values();
        List<EnvConfig> configList = new ArrayList<>(collection);
        return Response.ok(configList);
    }

    @PostMapping("/api/test")
    @Operation(summary = "服务测试")
    @SuppressWarnings("unchecked")
    public Response<CtTestCallResponse> apiTest(
        @Parameter(description = "测试请求", required = true)
        @NotNull(message = "测试请求不能为空")
        @RequestBody CtTestCallRequest testCallRequest) {
        CtCallRequest callRequest = CtCallRequest.builder()
            .teamId(TrantorContext.getTeamId())
            .instKey(testCallRequest.getInstKey())
            .apiKey(testCallRequest.getApiKey())
            .input(testCallRequest.getInput())
            .build();
        Response<Object> call = testCallApi.call(callRequest);
        // 接口本身返回200
        return (Response<CtTestCallResponse>) (Response<?>) Response.ok(call.getData());
    }

    @GetMapping("/api/list")
    @Operation(summary = "服务列表")
    public Response<List<CtApiListVO>> apiFindAll(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @RequestParam String instKey,
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue
    ) {
        return Response.ok(CtApiListVO.convert(ctInstApi.apiFindAll(instKey, fuzzyValue)));
    }

    @GetMapping("/api/paging")
    @Operation(summary = "服务分页")
    public Response<Paging<CtApiListVO>> apiPaging(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @RequestParam String instKey,
        @Parameter(description = "关键词")
        @RequestParam(required = false)
        String fuzzyValue,
        @Parameter(description = "页号")
        @RequestParam
        @NotNull(message = "页号不能为空")
        @Min(value = 1, message = "页号非法")
        Integer pageNumber,
        @Parameter(description = "每页条数")
        @RequestParam
        @NotNull(message = "每页条数能为空")
        @Min(value = 1, message = "每页条数非法")
        Integer pageSize
    ) {
        return Response.ok(CtApiListVO.convert(ctInstApi.apiPaging(instKey, fuzzyValue, pageNumber, pageSize)));
    }

    @GetMapping("/api/detail")
    @Operation(summary = "服务详情")
    public Response<CtApiVOForUse> apiDetail(
        @Parameter(description = "实例标识", required = true)
        @NotBlank(message = "实例标识不能为空")
        @RequestParam String instKey,
        @Schema(description = "服务标识", required = true)
        @NotBlank(message = "服务标识不能为空")
        @RequestParam String apiKey
    ) {
        return Response.ok(CtApiVOForUse.convert(ctInstApi.apiDetail(instKey, apiKey)));
    }

    private CtInst convert(CtInst inst, boolean isDetail) {
        if (Objects.nonNull(inst)) {
            if (isDetail) {
                CtTemp temp = inst.getResourceProps().getTemp();
                inst.getResourceProps().setTemp(CtTempVOForUse.convert(temp));
            } else {
                inst.getResourceProps().setTemp(null);
            }
        }
        return inst;
    }


}
