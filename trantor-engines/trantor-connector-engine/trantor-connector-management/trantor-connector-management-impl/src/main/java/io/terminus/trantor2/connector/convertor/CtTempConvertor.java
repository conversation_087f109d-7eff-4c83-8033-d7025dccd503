package io.terminus.trantor2.connector.convertor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtTag;
import io.terminus.trantor2.connector.model.meta.CtTemp;
import io.terminus.trantor2.connector.model.meta.VersionedCtTempPO;
import io.terminus.trantor2.connector.model.meta.auth.*;
import io.terminus.trantor2.connector.model.vo.CtApiVOForMgr;
import io.terminus.trantor2.connector.model.vo.HttpRequestVO;
import io.terminus.trantor2.connector.utils.CtJsonUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@UtilityClass
public class CtTempConvertor {
    private static final String FIELD_AUTH_REQ_VO = BaseAuth.Fields.authRequest + "VO";
    private static final String FIELD_AUTH_REQ_STR = BaseAuth.Fields.authRequest;
    private static final String FIELD_TOKEN_REQ_VO = TokenRequest.Fields.request + "VO";
    private static final String FIELD_TOKEN_REQ_STR = TokenRequest.Fields.request;


    /**
     * versionedCtTempPO -> ctTemp
     * @param versionedCtTempPO versionedCtTempPO
     * @return  CtTemp
     */
    public static CtTemp versionedCtTempPO2CtTemp(VersionedCtTempPO versionedCtTempPO) {
        CtTempContent tempContent = versionedCtTempPO.getContent();
        if (tempContent == null) {
            return new CtTemp();
        }
        CtTemp ctTemp = ctTempContext2CtTemp(tempContent);
        ctTemp.setKey(versionedCtTempPO.getKey());
        ctTemp.setName(versionedCtTempPO.getName());
        return ctTemp;
    }

    /**
     * ctTemp -> versionedCtTempPO
     * @param tempContent tempContent
     * @return VersionedCtTempPO
     */
    public static CtTemp ctTempContext2CtTemp(CtTempContent tempContent) {
        return CtTemp.builder()
            .authTemp(CtJsonUtil.convert(tempContent.getAuthTemp(), Auth.class))
            .apis(CtJsonUtil.convert(tempContent.getApis(), new TypeReference<List<CtApi>>() {
            }))
            .apiTagDefs(CtJsonUtil.convert(tempContent.getApiTagDefs(), new TypeReference<List<CtTag>>() {
            }))
            .params(CtJsonUtil.convert(tempContent.getParams(), new TypeReference<List<CtField>>() {
            }))
            .build();
    }


    /**
     * http request str -> vo
     */
    public void httpRequestStrToVO(CtTempContent content) {
        if (Objects.isNull(content)) {
            return;
        }
        content.setApis(null);
        //
        Optional<JsonNode> authReqOptional = Optional.ofNullable(content.getAuthTemp())
            .map(jsonNode -> jsonNode.get(FIELD_AUTH_REQ_STR));
        if (authReqOptional.isPresent()) {
            HttpRequestVO requestVO = HttpRequestVO.of(
                CtJsonUtil.convert(authReqOptional.get(), String.class));
            //
            ObjectNode authTemp = (ObjectNode) content.getAuthTemp();
            authTemp.remove(FIELD_AUTH_REQ_STR);
            authTemp.set(FIELD_AUTH_REQ_VO, CtJsonUtil.convert(requestVO, JsonNode.class));
        }
        //
        Optional<JsonNode> tokenReqOptional = Optional.ofNullable(content.getAuthTemp())
            .map(jsonNode -> jsonNode.get(TokenAuth.Fields.accessTokenRequest))
            .map(jsonNode -> jsonNode.get(FIELD_TOKEN_REQ_STR));
        if (tokenReqOptional.isPresent()) {
            HttpRequestVO requestVO = HttpRequestVO.of(
                CtJsonUtil.convert(tokenReqOptional.get(), String.class));
            //
            ObjectNode accessTokenRequest = (ObjectNode) content.getAuthTemp().get(TokenAuth.Fields.accessTokenRequest);
            accessTokenRequest.remove(FIELD_TOKEN_REQ_STR);
            accessTokenRequest.set(FIELD_TOKEN_REQ_VO, CtJsonUtil.convert(requestVO, JsonNode.class));
        }
    }

    /**
     * http request vo -> str
     */
    public void httpRequestVOToStr(CtTempContent content, boolean isAuth) {
        if (Objects.isNull(content)) {
            return;
        }
        if (isAuth) {
            httpRequestVOToStrForAuth(content);
        } else {
            //first api
            httpRequestVOToStrForApi(content);
        }
    }

    private void httpRequestVOToStrForAuth(CtTempContent content) {
        Optional<JsonNode> authReqVoOptional = Optional.ofNullable(content.getAuthTemp())
            .map(jsonNode -> jsonNode.get(FIELD_AUTH_REQ_VO));
        if (authReqVoOptional.isPresent()) {
            HttpRequestVO requestVO = CtJsonUtil.convert(authReqVoOptional.get(), HttpRequestVO.class);
            //
            ObjectNode authTemp = (ObjectNode) content.getAuthTemp();
            authTemp.remove(FIELD_AUTH_REQ_VO);
            authTemp.set(FIELD_AUTH_REQ_STR, CtJsonUtil.convert(
                requestVO == null ? null : requestVO.toHttpRequestStr(), JsonNode.class));
        }
        //
        Optional<JsonNode> tokenReqVoOptional = Optional.ofNullable(content.getAuthTemp())
            .map(jsonNode -> jsonNode.get(TokenAuth.Fields.accessTokenRequest))
            .map(jsonNode -> jsonNode.get(FIELD_TOKEN_REQ_VO));
        if (tokenReqVoOptional.isPresent()) {
            HttpRequestVO requestVO = CtJsonUtil.convert(tokenReqVoOptional.get(), HttpRequestVO.class);
            //
            ObjectNode accessTokenRequest = (ObjectNode) content.getAuthTemp().get(TokenAuth.Fields.accessTokenRequest);
            accessTokenRequest.remove(FIELD_TOKEN_REQ_VO);
            accessTokenRequest.set(FIELD_TOKEN_REQ_STR, CtJsonUtil.convert(
                requestVO == null ? null : requestVO.toHttpRequestStr(), JsonNode.class));
        }
    }

    private void httpRequestVOToStrForApi(CtTempContent content) {
        Optional<JsonNode> apiReqVoOptional = Optional.ofNullable(content.getApis())
            .filter(CollectionUtils::isNotEmpty)
            .map(apis -> apis.get(0))
            .map(apis -> apis.get(CtApiVOForMgr.Fields.apiRequestVO));
        if (apiReqVoOptional.isPresent()) {
            HttpRequestVO requestVO = CtJsonUtil.convert(apiReqVoOptional.get(), HttpRequestVO.class);
            //
            ObjectNode apiRequest = (ObjectNode) content.getApis().get(0);
            apiRequest.remove(CtApiVOForMgr.Fields.apiRequestVO);
            apiRequest.set(CtApi.Fields.apiRequest, CtJsonUtil.convert(
                requestVO == null ? null : requestVO.toHttpRequestStr(), JsonNode.class));
        }
    }
}
