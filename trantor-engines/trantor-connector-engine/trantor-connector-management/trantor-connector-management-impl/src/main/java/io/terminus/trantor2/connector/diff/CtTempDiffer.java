package io.terminus.trantor2.connector.diff;

import io.terminus.trantor2.connector.model.diff.*;
import io.terminus.trantor2.connector.model.field.CtFieldHelper;
import io.terminus.trantor2.connector.model.field.CtObjectField;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.meta.CtTag;
import io.terminus.trantor2.connector.model.meta.CtTemp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static io.terminus.trantor2.connector.diff.DiffHelper.appendPath;
import static io.terminus.trantor2.connector.diff.DiffHelper.diffFromCtField;

/**
 * CtTempDiffer
 * 以v1为基准对比v2的差异
 */
public class CtTempDiffer {


    private CtTemp ctTemp1;

    private CtTemp ctTemp2;

    private DiffMode diffMode = DiffMode.ALL;
    private boolean useGroup = true;

    private int diffNumber = 0;


    public static CtTempDiffer builder() {
        return new CtTempDiffer();
    }


    public CtTempDiffer useGroup(boolean useGroup) {
        this.useGroup = useGroup;
        return this;
    }

    public CtTempDiffer ctTemp1(CtTemp ctTemp1) {
        this.ctTemp1 = ctTemp1;
        return this;
    }

    public CtTempDiffer ctTemp2(CtTemp ctTemp2) {
        this.ctTemp2 = ctTemp2;
        return this;
    }

    public CtTempDiffer diffMode(DiffMode diffMode) {
        this.diffMode = diffMode;
        return this;
    }

    public TempDiffReport diff() {
        if (Objects.isNull(ctTemp1) || Objects.isNull(ctTemp2)) {
            throw new IllegalArgumentException("ctTemp1 or ctTemp2 is null");
        }
        //比较key
        // key不同直接报错
        if (!Objects.equals(ctTemp1.getKey(), ctTemp2.getKey())) {
            throw new IllegalArgumentException(String.format("temp: ctemp1 key: %s is not equal to ctTemp2 key: %s", ctTemp1.getKey(), ctTemp2.getKey()));
        }
        TempDiffReport tempDiffReport = TempDiffReport.builder()
            .location(ctTemp1.getKey())
            .build();
        //tempBasicDiffReport
        tempDiffReport.setTempBasicDiffReport(tempBasicDiffReport());
        //tempParamsDiffReport
        tempDiffReport.setCtFieldParamsDiffReport(tempParamsDiffReport());

        // 用分组
        if (useGroup && DiffMode.ALL.equals(diffMode)) {
            //groupDiffReports
            List<GroupDiffReport> groupDiffReports = diffTempGroups();
            increaseDiff(groupDiffReports.stream().map(GroupDiffReport::getDiffNumber).reduce(0, Integer::sum));
            tempDiffReport.setGroupDiffReports(groupDiffReports);
        } else {
            //不用分组 直接用api
            List<ApiDiffReport> apiDiffReports = diffGroupApis(ctTemp1.getApis(), ctTemp2.getApis(), ctTemp1.getKey());
            increaseDiff(apiDiffReports.stream().map(ApiDiffReport::getDiffNumber).reduce(0, Integer::sum));
            tempDiffReport.setApiDiffReports(apiDiffReports);
        }
        tempDiffReport.setDiffNumber(diffNumber);
        return tempDiffReport;
    }

    private TempBasicDiffReport tempBasicDiffReport() {
        TempBasicDiffReport tempBasicDiffReport = TempBasicDiffReport.builder().diffDetails(new ArrayList<>()).build();
        if (DiffMode.Compact.equals(diffMode)) {
            return tempBasicDiffReport;
        }
        //基本信息
        //name
        if (!Objects.equals(ctTemp1.getName(), ctTemp2.getName())) {
            tempBasicDiffReport.getDiffDetails().add(DiffDetail.builder().before(ctTemp1.getName()).after(ctTemp2.getName()).detailPath("name").diffType(DiffType.UPDATE).message("name 不一致").build());
            increaseDiff();
        }
        return tempBasicDiffReport;

    }

    private CtFieldParamsDiffReport tempParamsDiffReport() {
        CtFieldParamsDiffReport ctFieldParamsDiffReport = CtFieldParamsDiffReport.builder().diffDetails(new ArrayList<>()).build();
        //differ
        CtFieldHelper.Differ differ = (diffType, cf1, cf2, diffPath) -> {
            switch (diffType) {
                case Create:
                    ctFieldParamsDiffReport.getDiffDetails().add(DiffDetail.builder().before(null).after(cf2.getFieldKey()).detailPath(diffPath).diffType(DiffType.ADD).message("字段新增").build());
                    increaseDiff();
                    break;
                case Delete:
                    ctFieldParamsDiffReport.getDiffDetails().add(DiffDetail.builder().before(cf1.getFieldKey()).after(null).detailPath(diffPath).diffType(DiffType.DELETE).message("字段删除").build());
                    increaseDiff();
                    break;
                default:
                    ctFieldParamsDiffReport.getDiffDetails().
                        add(DiffDetail.builder().
                            before(cf1.getFieldKey()).
                            after(cf2.getFieldKey()).
                            detailPath(diffPath).
                            diffType(DiffType.UPDATE).
                            message("字段更新").subDetails(diffFromCtField(cf1, cf2)).build());
                    increaseDiff();
                    break;
            }
        };
        CtFieldHelper.diffCtField(new CtObjectField("params", ctTemp1.getParams()), new CtObjectField("params", ctTemp2.getParams()), differ);
        return ctFieldParamsDiffReport;
    }

    private List<GroupDiffReport> diffTempGroups() {
        List<GroupDiffReport> groupDiffReports = new ArrayList<>();
        //找出ctemp1Tags 和 ctTemp2Tags的 增/删/改
        List<CtTag> ctemp1Tags = ctTemp1.getApiTagDefs() == null ? new ArrayList<>() : ctTemp1.getApiTagDefs();
        List<CtTag> ctTemp2Tags = ctTemp2.getApiTagDefs() == null ? new ArrayList<>() : ctTemp2.getApiTagDefs();
        Map<String, CtTag> ctemp1TagMap = ctemp1Tags.stream().collect(Collectors.toMap(CtTag::getKey, v -> v));
        Map<String, CtTag> ctTemp2TagMap = ctTemp2Tags.stream().collect(Collectors.toMap(CtTag::getKey, v -> v));
        //tag和api的关系
        List<CtApi> api1s = ctTemp1.getApis() == null ? new ArrayList<>() : ctTemp1.getApis();
        List<CtApi> api2s = ctTemp2.getApis() == null ? new ArrayList<>() : ctTemp2.getApis();
        Map<String, List<CtApi>> tagApiGroup1 = api1s.stream().collect(Collectors.groupingBy(CtApi::getTag));
        Map<String, List<CtApi>> tagApiGroup2 = api2s.stream().collect(Collectors.groupingBy(CtApi::getTag));
        //找出ctemp1Tags 和 ctTemp2Tags的 增/删/改
        for (Map.Entry<String, CtTag> entry : ctemp1TagMap.entrySet()) {
            CtTag ctemp1Tag = entry.getValue();
            CtTag ctTemp2Tag = ctTemp2TagMap.get(entry.getKey());
            if (Objects.isNull(ctTemp2Tag)) {
                //ctTemp2中没有ctemp1的tag --> ctemp1删除
                groupDiffReports.add(GroupDiffReport.builder().
                    diffType(DiffType.DELETE).
                    diffNumber(1).
                    location(appendPath(ctTemp1.getKey(), ctemp1Tag.getKey())).
                    build());
            } else {
                //都有
                //处理tag的基本信息变化
                List<CtApi> ctemp1TagApis = tagApiGroup1.getOrDefault(ctemp1Tag.getKey(), new ArrayList<>());
                List<CtApi> ctTemp2TagApis = tagApiGroup2.getOrDefault(ctTemp2Tag.getKey(), new ArrayList<>());
                String location = appendPath(ctTemp1.getKey(), ctemp1Tag.getKey());
                List<ApiDiffReport> apiDiffReports = diffGroupApis(ctemp1TagApis, ctTemp2TagApis, location);
                GroupBasicDiffReport groupBasicDiffReport = groupBasicDiffReport(ctemp1Tag, ctTemp2Tag);
                groupDiffReports.add(GroupDiffReport.
                    builder().
                    diffType(DiffType.UPDATE).
                    location(location).
                    groupBasicDiffReport(groupBasicDiffReport).
                    diffNumber(apiDiffReports.stream().map(ApiDiffReport::getDiffNumber).reduce(0, Integer::sum) + groupBasicDiffReport.getDiffDetails().size()).
                    apiDiffReports(apiDiffReports).
                    build());
            }
        }

        for (Map.Entry<String, CtTag> entry : ctTemp2TagMap.entrySet()) {
            CtTag ctTemp2Tag = entry.getValue();
            if (!ctemp1TagMap.containsKey(entry.getKey())) {
                //ctemp1中没有ctTemp2的tag  --> ctTemp2Tag新增
                groupDiffReports.add(GroupDiffReport.builder().diffType(DiffType.ADD).diffNumber(1).location(appendPath(ctTemp1.getKey(), ctTemp2Tag.getKey())).build());
            }
        }
        return groupDiffReports;
    }

    private GroupBasicDiffReport groupBasicDiffReport(CtTag ct1Tag, CtTag ct2Tag) {
        GroupBasicDiffReport groupBasicDiffReport = GroupBasicDiffReport.builder().
            diffDetails(new ArrayList<>()).
            build();
        //基本信息
        //name
        if (!Objects.equals(ct1Tag.getName(), ct2Tag.getName())) {
            groupBasicDiffReport.getDiffDetails().add(DiffDetail
                .builder().
                before(ct1Tag.getName()).
                after(ct2Tag.getName()).
                detailPath("name").
                diffType(DiffType.UPDATE).
                message("name变化").
                build());
        }
        return groupBasicDiffReport;
    }

    private List<ApiDiffReport> diffGroupApis(List<CtApi> ctApis1, List<CtApi> ctApis2, String location) {
        List<ApiDiffReport> apiDiffReports = new ArrayList<>();
        //找出ct1Tags 和 ct2Tags的 增/删/改
        Map<String, CtApi> ctApi1Map = ctApis1.stream().collect(Collectors.toMap(CtApi::getKey, v -> v));
        Map<String, CtApi> ctApi2Map = ctApis2.stream().collect(Collectors.toMap(CtApi::getKey, v -> v));
        for (Map.Entry<String, CtApi> entry : ctApi1Map.entrySet()) {
            CtApi ct1Api = entry.getValue();
            CtApi ct2Api = ctApi2Map.get(entry.getKey());
            if (Objects.isNull(ct2Api)) {
                // ct1Api删除
                apiDiffReports.add(ApiDiffReport.builder().
                    diffType(DiffType.DELETE).
                    diffNumber(1).
                    location(appendPath(location, ct1Api.getKey())).
                    build());
            } else {
                //都有处理api的基本信息
                ApiDiffReport diff = CtApiDiffer.builder()
                    .diffMode(diffMode)
                    .ctApi1(ct1Api)
                    .ctApi2(ct2Api)
                    .location(appendPath(location, ct1Api.getKey()))
                    .diff();
                if (diff.getDiffNumber() > 0){
                    apiDiffReports.add(diff);
                }


            }
        }
        for (Map.Entry<String, CtApi> entry : ctApi2Map.entrySet()) {
            CtApi ct2Api = entry.getValue();
            if (!ctApi1Map.containsKey(entry.getKey())) {
                //
                apiDiffReports.add(ApiDiffReport.builder().
                    diffType(DiffType.ADD).
                    diffNumber(1).
                    location(appendPath(location, ct2Api.getKey())).
                    build());
            }
        }
        return apiDiffReports;
    }


    private void increaseDiff(int size) {
        diffNumber += size;
    }

    private void increaseDiff() {
        increaseDiff(1);
    }
}
