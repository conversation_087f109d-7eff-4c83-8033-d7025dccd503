package io.terminus.trantor2.connector.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotBlank;

/**
 * 模版上传请求
 */
@Schema(description = "模版上传请求")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CtTempUploadRequest {
    @Schema(required = true, description = "oss文件路径")
    @NotBlank(message = "唯一标识不能为空")
    private String ossFileUrl;

    @Schema(description = "版本")
    @NotBlank(message = "版本不能为空")
    private String appVersion;
}
