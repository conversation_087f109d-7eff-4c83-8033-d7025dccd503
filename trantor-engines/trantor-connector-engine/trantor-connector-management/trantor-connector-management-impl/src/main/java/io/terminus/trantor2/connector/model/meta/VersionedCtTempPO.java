package io.terminus.trantor2.connector.model.meta;

import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.application.po.TrantorVersionedAppItemPO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * Ct temp PO
 */
@Slf4j
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("CONNECTOR_TEMPLATE")
public class VersionedCtTempPO extends TrantorVersionedAppItemPO<CtTempContent> {
    private static final long serialVersionUID = -7483035022655797435L;
}
