package io.terminus.trantor2.connector.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.application.item.connector.CtTempBaseInfo;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.connector.convertor.CtTempConvertor;
import io.terminus.trantor2.connector.diff.CtTempDiffer;
import io.terminus.trantor2.connector.exception.CtException;
import io.terminus.trantor2.connector.model.diff.DiffMode;
import io.terminus.trantor2.connector.model.diff.TempDiffReport;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.meta.*;
import io.terminus.trantor2.connector.model.meta.auth.Auth;
import io.terminus.trantor2.connector.repo.CtInstRepo;
import io.terminus.trantor2.connector.service.CtInstApi;
import io.terminus.trantor2.connector.utils.CtJsonUtil;
import io.terminus.trantor2.connector.utils.PagingUtil;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.meta.ModuleType;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.ModuleManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 连接器实例管理实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("unused")
public class CtInstApiImpl implements CtInstApi {

    private final CtInstRepo instRepo;
    private final ModuleManageService moduleManageService;
    private final MetaQueryService queryService;
    private final CtTempApiImpl ctTempApi;
    private final ConfigurationService configurationService;

    /**
     * 实例创建
     * 先创建连接器分组模块，再创建连接器实例
     *
     * @param ctInst 实例对象
     * @return id
     */
    @Override
    @Transactional
    public Long create(CtInst ctInst) {
        if (moduleManageService.exist(ctInst.getKey())) {
            throw new CtException(ErrorType.CONNECTOR_INSTANCE_EXISTED, new String[]{ctInst.getKey(), ctInst.getName()});
        }
        //校验，模版/参数
        saveGroup(ctInst, true);
        ctInst.setParentKey(ctInst.getKey());
        ctInst.setKey(fullInstKey(ctInst.getKey()));//连接器key格式为：key$key
        ctInst.getResourceProps().setEnabled(false);
        //
        CtTemp temp = ctInst.getResourceProps().getTemp();
        loadTempFromMarket(temp);
        //
        Long instId = instRepo.create(ctInst, ResourceContext.ctxFromThreadLocal());
        publishCreateCtInstEvent(ctInst);
        return instId;
    }

    /**
     * 从市场加载CtTemp
     *
     * @param temp CtTemp对象
     */
    private void loadTempFromMarket(CtTemp temp) {
        VersionedCtTempPO versionedTempPO = ctTempApi.findVersion(temp.getKey(), temp.getAppVersion(), true,
            temp.getFromMarket());
        if (Objects.isNull(versionedTempPO)) {
            throw new ValidationException(String.format("无法加载模版, key: %s, market: %s, version: %s", temp.getKey(),
                temp.getFromMarket(), temp.getAppVersion()));
        }
        merge(versionedTempPO, temp);
    }

    /**
     * versionedTempPO --> temp
     *
     * @param versionedTempPO versionedTempPO
     * @param temp            temp
     */
    private void merge(VersionedCtTempPO versionedTempPO, CtTemp temp) {
        CtTempPO tempPO = versionedTempPO.getApp();
        CtTempBaseInfo baseInfo = tempPO.getBaseInfo();

        temp.setAppVersion(versionedTempPO.getAppVersion());
        temp.setName(tempPO.getName());
        temp.setDescription(tempPO.getDescription());
        temp.setIcon(tempPO.getIcon());
        if (Objects.nonNull(baseInfo)) {
            temp.setTags(baseInfo.getTags());
            temp.setTempType(CtTempType.valueOf(baseInfo.getTempType()));
            temp.setDetailedDesc(baseInfo.getDetailedDesc());
            temp.setVersionNo(baseInfo.getVersionNo());
            temp.setDocUrl(baseInfo.getDocUrl());
        }
        //
        CtTempContent content = versionedTempPO.getContent();
        if (Objects.nonNull(content)) {
            temp.setParams(CtJsonUtil.convert(content.getParams(), new TypeReference<List<CtField>>() {
            }));
            temp.setAuthTemp(CtJsonUtil.convert(content.getAuthTemp(), Auth.class));
            temp.setApiTagDefs(CtJsonUtil.convert(content.getApiTagDefs(), new TypeReference<List<CtTag>>() {
            }));
            temp.setApis(CtJsonUtil.convert(content.getApis(), new TypeReference<List<CtApi>>() {
            }));
        }
    }

    /**
     * 实例修改
     *
     * @param ctInst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CtInst update(CtInst ctInst) {
        saveGroup(ctInst, false);

        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        CtInst dbCtInst = this.findByKey(ctInst.getKey());
        checkCtInstStatus(dbCtInst);
        //
        dbCtInst.setName(ctInst.getName());
        dbCtInst.setDescription(ctInst.getDescription());
        CtInst.CtInstProps dbProps = dbCtInst.getResourceProps();
        dbProps.setEnabled(false);
        instRepo.update(dbCtInst, ctx);
        //
        publishUpdateCtInstEvent(dbCtInst);
        return dbCtInst;
    }

    /**
     * 连接器实例升级（模版升级）
     *
     * @param key        (实例)key
     * @param version    (模版)版本
     * @param fromMarket 是否为中心市场模版，false为本地市场
     * @return 连接器实例
     */
    @Override
    @Transactional
    public CtInst upgrade(String key, String version, boolean fromMarket) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        CtInst dbCtInst = this.findByKey(key);
        checkCtInstStatus(dbCtInst);
        //
        String tempKey = Optional.ofNullable(dbCtInst.getResourceProps().getTemp())
            .map(CtTemp::getKey).orElseThrow(() ->
                new ValidationException("该实例数据异常,无法使用"));
        CtTemp temp = new CtTemp();
        temp.setKey(tempKey);
        temp.setAppVersion(version);
        temp.setFromMarket(fromMarket);

        loadTempFromMarket(temp);
        dbCtInst.getResourceProps().setTemp(temp);
        dbCtInst.getResourceProps().setEnabled(false);

        instRepo.update(dbCtInst, ctx);
        //
        publishUpdateCtInstEvent(dbCtInst);
        return dbCtInst;
    }

    /**
     * 实例删除
     *
     * @param key 实例标识
     */
    @Override
    @Transactional
    public void deleteByKey(String key) {
        CtInst dbCtInst = this.findByKey(key);
        checkCtInstStatus(dbCtInst);
        //删除配置
        configurationService.deleteConfig(TrantorContext.getTeamId(), dbCtInst.getParentKey());
        //删除
        deleteGroup(instModuleKey(key));
        publishDeleteCtInstEvent(dbCtInst);
    }

    /**
     * 根据key查找实例
     *
     * @param key 实例key
     * @return 实例VO
     */
    @Override
    public CtInst findByKey(String key) {
        return instRepo.findOneByKey(key, ResourceContext.ctxFromThreadLocal())
            .orElseThrow(() -> new CtException(ErrorType.CONNECTOR_INSTANCE_NOT_EXISTED, new String[]{key}));
    }

    /**
     * @param fuzzyValue 查找值
     * @param pageNumber 页号
     * @param pageSize   每页条数
     * @return 分页对象
     */
    @Override
    public Paging<CtInst> paging(String fuzzyValue, Integer pageNumber, Integer pageSize) {
        return instRepo.findAll(getCondFromFuzzy(fuzzyValue),
            PageReq.of(pageNumber - 1, pageSize, Order.byModifiedAt().desc()), ResourceContext.ctxFromThreadLocal());
    }

    /**
     * 查询
     *
     * @param fuzzyValue 查找值
     * @return 列表
     */
    @Override
    public List<CtInst> findAll(String fuzzyValue) {
        return instRepo.findAll(getCondFromFuzzy(fuzzyValue), ResourceContext.ctxFromThreadLocal());
    }

    /**
     * 启用
     *
     * @param key 实例key
     */
    @Override
    public void enable(String key) {
        updateStatus(key, true);
    }

    /**
     * 停用
     *
     * @param key 实例key
     */
    @Override
    public void disable(String key) {
        updateStatus(key, false);
    }

    /**
     * 服务列表
     *
     * @param instKey    实例key
     * @param fuzzyValue 查找值
     * @return 服务列表
     */
    @Override
    public List<CtApi> apiFindAll(String instKey, String fuzzyValue) {
        List<CtApi> apiList = this.getApiList(instKey);
        if (StringUtils.hasText(fuzzyValue)) {
            String kw = fuzzyValue.trim();
            return apiList.stream()
                .filter(api ->
                    api.getKey().contains(kw)
                        || api.getName().contains(kw)
                )
                .collect(Collectors.toList());
        }
        return apiList;
    }

    /**
     * 服务分页
     *
     * @param instKey    实例key
     * @param fuzzyValue 查找值
     * @param pageNumber 页号
     * @param pageSize   每页条数
     * @return 分页对象
     */
    @Override
    public Paging<CtApi> apiPaging(String instKey, String fuzzyValue, Integer pageNumber, Integer pageSize) {
        List<CtApi> apiList = this.apiFindAll(instKey, fuzzyValue);
        return PagingUtil.paging(apiList, pageNumber, pageSize);
    }

    /**
     * 服务详情
     *
     * @param instKey 实例key
     * @param apiKey  服务key
     * @return 服务对象
     */
    @Override
    public CtApi apiDetail(String instKey, String apiKey) {
        return getApiList(instKey).stream()
            .filter(api -> Objects.equals(api.getKey(), apiKey))
            .findFirst()
            .orElseThrow(() -> new CtException(ErrorType.CONNECTOR_SERVICE_NOT_EXISTED, new String[]{apiKey}));
    }

    @Override
    public TempDiffReport upgradeDiff(String instKey) {
        CtInst ctInst = findByKey(instKey);
        CtTemp oldTemp = ctInst.getResourceProps().getTemp();
        VersionedCtTempPO versionedTempPO = ctTempApi.findVersion(oldTemp.getKey(), null, true, false);
        if (Objects.isNull(versionedTempPO)) {
            throw new CtException(ErrorType.CONNECTOR_VALIDATION_FORMAT_ERROR, new String[]{String.format("无法加载模版[%s] 可能没有任何版本被启动", oldTemp.getKey())});
        }
        CtTemp newTemp = CtTempConvertor.versionedCtTempPO2CtTemp(versionedTempPO);
        return CtTempDiffer.builder()
            .ctTemp1(oldTemp)
            .ctTemp2(newTemp)
            .diffMode(DiffMode.Compact)
            .useGroup(false)
            .diff();
    }

    /**
     * 获取实例-连接器模版快照-服务列表
     *
     * @param instKey 实例key
     * @return 服务列表
     */
    private List<CtApi> getApiList(String instKey) {
        CtInst inst = instRepo.findOneByKey(instKey, ResourceContext.ctxFromThreadLocal())
            .orElseThrow(() -> new CtException(ErrorType.CONNECTOR_INSTANCE_NOT_EXISTED, new String[]{instKey}));
        CtTemp temp = inst.getResourceProps().getTemp();
        if (Objects.isNull(temp)) {
            throw new CtException(ErrorType.CONNECTOR_TEMPLATE_NOT_EXISTED, new String[]{instKey});
        }
        List<CtApi> apiList = temp.getApis();
        if (Objects.isNull(apiList)) {
            apiList = Collections.emptyList();
        }
        return apiList;
    }

    /**
     * 更新启用/停用状态
     *
     * @param key     实例key
     * @param enabled 状态
     */
    private void updateStatus(String key, Boolean enabled) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        CtInst dbCtInst = this.findByKey(key);
        if (!Objects.equals(dbCtInst.getResourceProps().getEnabled(), enabled)) {
            dbCtInst.getResourceProps().setEnabled(enabled);
            instRepo.update(dbCtInst, ctx);
            publishUpdateCtInstEvent(dbCtInst);
        }
    }

    /**
     * 生成模糊查询条件
     *
     * @param fuzzyValue 查找值
     * @return 条件
     */
    private Cond getCondFromFuzzy(String fuzzyValue) {
        Cond condType = Field.type().equal(MetaType.ConnectorInst.name());
        //
        if (StringUtils.hasText(fuzzyValue)) {
            fuzzyValue = "%" + fuzzyValue.trim() + "%";
            Cond condKey = Field.key().like(fuzzyValue);
            Cond condName = Field.name().like(fuzzyValue);
            condType = condType.and(Cond.or(condKey, condName));
        }
        return condType;
    }

    /**
     * 启用状态校验，启用时不能修改/删除
     *
     * @param ctInst 实例对象
     */
    private void checkCtInstStatus(CtInst ctInst) {
        if (ctInst.getResourceProps().getEnabled()) {
            throw new ValidationException("连接器实例已启用!");
        }
    }

    /**
     * 创建连接器分组模块
     *
     * @param ctInst   连接器实例
     * @param isCreate 是否新建?
     */
    private void saveGroup(CtInst ctInst, boolean isCreate) {
        MetaTreeNode root = queryService.queryInTeam(TrantorContext.getTeamId())
            .findRoot().
            orElseThrow(() -> new TrantorRuntimeException("root node not found"));
        //
        ModuleMeta group = new ModuleMeta();
        group.setName(ctInst.getName());
        group.setKey(isCreate ? ctInst.getKey() : instModuleKey(ctInst.getKey()));
        group.setTeamId(TrantorContext.getTeamId());
        group.setTeamCode(TrantorContext.getTeamCode());
        group.setResourceProps(new ModuleMeta.ModuleProps());
        group.getResourceProps().setType(ModuleType.Connector_Group);
        //
        group.setParentKey(root.getKey());

        moduleManageService.saveModuleMeta(group);
    }

    /**
     * 删除连接器分组，会强制删除分组下的连接器实例
     *
     * @param key 分组模块key
     */
    private void deleteGroup(String key) {
        moduleManageService.deleteModuleMeta(key);
    }

    /**
     * instKey$instKey, moduleKey = instKey
     *
     * @param instKey inst key
     * @return instKey$instKey
     */
    private String fullInstKey(String instKey) {
        return KeyUtil.newKeyUnderModule(instKey, instKey);
    }

    /**
     * inst module key
     *
     * @param fullInstKey instKey$instKey
     * @return instKey(inst module key)
     */
    private String instModuleKey(String fullInstKey) {
        return KeyUtil.moduleKey(fullInstKey);
    }

    private void publishCreateCtInstEvent(CtInst ctInst) {
        log.info("Ct inst created, key: {}", ctInst.getKey());
    }

    private void publishUpdateCtInstEvent(CtInst ctInst) {
        log.info("Ct inst updated, key: {}", ctInst.getKey());
    }

    private void publishDeleteCtInstEvent(CtInst ctInst) {
        log.info("Ct inst deleted, key: {}", ctInst.getKey());
    }

}
