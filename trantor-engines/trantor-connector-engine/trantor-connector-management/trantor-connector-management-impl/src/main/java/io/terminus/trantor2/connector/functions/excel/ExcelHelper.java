package io.terminus.trantor2.connector.functions.excel;


import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelApi;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelApiBody;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelApiBodyField;
import io.terminus.trantor2.connector.model.functions.excel.models.ExcelRow;
import io.terminus.trantor2.connector.model.functions.excel.models.enums.ExcelApiBodyFieldType;
import io.terminus.trantor2.connector.model.functions.excel.models.enums.ExcelApiBodySchema;
import io.terminus.trantor2.connector.model.functions.excel.models.enums.ExcelFieldMergeType;
import io.terminus.trantor2.connector.model.field.*;
import io.terminus.trantor2.connector.model.field.convertor.FieldBodyConvertor;
import io.terminus.trantor2.connector.model.field.convertor.FieldBodyConvertorFactory;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import org.apache.commons.collections.CollectionUtils;

import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ExcelHelper {
    private static final String FIELD_PATH_SEP = ".";
    private static final String ARRAY_BORDER_NAME = "__array__";


    public static String handleApiUrlPath(String urlStr) {
        if (StrUtil.isBlank(urlStr)) return urlStr;
        if (urlStr.startsWith("http")) {
            try {
                URL url = new URL(urlStr);
                return url.getPath(); //paths
            } catch (Exception ignored) {

            }
        }
        return urlStr;
    }

    public static ExcelApiBody getApiBody(List<ExcelRow> bodyRows, String sampleData) {
        ExcelApiBody excelApiBody = new ExcelApiBody();
        if (CollectionUtils.isNotEmpty(bodyRows) && bodyRows.size() > 1) {
            //第一行是模型的名称
            ExcelRow modelMeta = bodyRows.get(0);
            //反过来将名称和列映射
            Map<Integer, String> columnValueMap = modelMeta.getData();
            List<ExcelApiBodyField> fields = new ArrayList<>();

            for (int i = 1; i < bodyRows.size(); i++) {
                Map<Integer, String> modelFieldMap = bodyRows.get(i).getData();
                ExcelApiBodyField excelApiBodyField = new ExcelApiBodyField();
                for (Map.Entry<Integer, String> entry : modelFieldMap.entrySet()) {
                    Integer column = entry.getKey();
                    String fieldValue = entry.getValue();
                    String fieldMetaName = columnValueMap.get(column);
                    if (StrUtil.isBlank(fieldMetaName)) continue;
                    switch (fieldMetaName) {
                        case "字段名称":
                            excelApiBodyField.setFieldName(fieldValue);
                            break;
                        case "字段描述":
                            excelApiBodyField.setFieldDesc(fieldValue);
                            break;
                        case "字段类型":
                            excelApiBodyField.setFieldType(ExcelApiBodyFieldType.valueOrDefault(fieldValue, ExcelApiBodyFieldType.UNKNOWN));
                            break;
                        case "长度/日期时间格式":
                            excelApiBodyField.setFieldConst(fieldValue);
                            break;
                        case "路径":
                            excelApiBodyField.setFieldParentPath(StrUtil.isBlank(fieldValue) ? "" : fieldValue);
                            break;
                        case "是否必填":
                            if ("是".equals(fieldValue)) {
                                excelApiBodyField.setIsRequired(true);
                            } else if ("否".equals(fieldValue)) {
                                excelApiBodyField.setIsRequired(false);
                            } else {
                                excelApiBodyField.setIsRequired(false);
                            }
                            break;
                    }
                }
                fields.add(excelApiBodyField);
            }
            excelApiBody.setFields(fields);
        }
        excelApiBody.setSampleData(sampleData);
        return excelApiBody;
    }

    public static CtField initCtFieldByExcelField(ExcelApiBodyField excelApiBodyField, FormatType formatType) {
        ExcelApiBodyFieldType fieldType = excelApiBodyField.getFieldType();
        switch (fieldType) {
            case UNKNOWN:
                CtBaseField unknownField = new CtBaseField(excelApiBodyField.getFieldName(), null, formatType);
                unknownField.setRequired(excelApiBodyField.getIsRequired());
                unknownField.setDescription(excelApiBodyField.getFieldDesc());
                return unknownField;
            case STRING:
                CtBaseField textField = new CtBaseField(excelApiBodyField.getFieldName(), FieldType.Text, formatType);
                textField.setRequired(excelApiBodyField.getIsRequired());
                textField.setDescription(excelApiBodyField.getFieldDesc());
                return textField;
            case NUMBER:
                CtBaseField numberField = new CtBaseField(excelApiBodyField.getFieldName(), FieldType.Number, formatType);
                numberField.setRequired(excelApiBodyField.getIsRequired());
                numberField.setDescription(excelApiBodyField.getFieldDesc());
                return numberField;
            case DATETIME:
                CtBaseField dateTimeField = new CtBaseField(excelApiBodyField.getFieldName(), FieldType.DateTime, formatType);
                dateTimeField.setRequired(excelApiBodyField.getIsRequired());
                dateTimeField.setDescription(excelApiBodyField.getFieldDesc());
                return dateTimeField;
            case OBJECT:
                CtObjectField objectField = new CtObjectField(excelApiBodyField.getFieldName(), formatType, Lists.newArrayList());
                objectField.setRequired(excelApiBodyField.getIsRequired());
                objectField.setDescription(excelApiBodyField.getFieldDesc());
                return objectField;

            default:
                CtField embedField;
                switch (fieldType) {
                    case ARRAY_STRING:
                        embedField = new CtBaseField(CtFieldHelper.Embed_Element, FieldType.Text, formatType);
                        break;
                    case ARRAY_NUMBER:
                        embedField = new CtBaseField(CtFieldHelper.Embed_Element, FieldType.Number, formatType);
                        break;
                    case ARRAY_DATETIME:
                        embedField = new CtBaseField(CtFieldHelper.Embed_Element, FieldType.DateTime, formatType);
                        break;
                    case ARRAY_OBJECT:
                        embedField = new CtObjectField(CtFieldHelper.Embed_Element, formatType, Lists.newArrayList());
                        break;
                    default:
                        embedField = null;
                        break;
                }
                CtArrayField arrayField = new CtArrayField(excelApiBodyField.getFieldName(), formatType, embedField);
                arrayField.setRequired(excelApiBodyField.getIsRequired());
                arrayField.setDescription(excelApiBodyField.getFieldDesc());
                return arrayField;
        }
    }

    private static CtField mergedSampleAndField(CtField ctFieldFromSample, CtField ctFieldFromField) {
        //ctFieldFromField 会携带更多的信息  ctFieldFromSample 会携带更多的结构信息
        ctFieldFromSample.setDescription(ctFieldFromField.getDescription());
        ctFieldFromSample.setRequired(ctFieldFromField.getRequired());
        ctFieldFromSample.setSoapProps(ctFieldFromField.getSoapProps());
        return ctFieldFromSample;
    }

    /**
     * 结合fields 和schema补充样例数据
     * 在fields 引入了路径之后 fields 本身也可以被构造成ctField树
     * 所以本质上就变成了两个ctField树之间的一个更新问题
     *
     * @param apiBody api的body
     * @param schema  apiBodySampleData的模式
     * @return objectBody
     */
    public static CtObjectField convertApiBody(ExcelApiBody apiBody, ExcelApiBodySchema schema, ExcelFieldMergeType excelFieldMergeType) {
        List<ExcelApiBodyField> fields = apiBody.getFields();
        String sampleData = apiBody.getSampleData();
        if (StrUtil.isBlank(sampleData) && CollectionUtils.isEmpty(fields))
            return new CtObjectField("", "", Lists.newArrayList());
        FormatType formatType = schema.toFormatType();
        CtField sampleDataCtFieldTree = null;
        if (StrUtil.isNotBlank(sampleData)) {
            FieldBodyConvertor fieldBodyConvertor = FieldBodyConvertorFactory.getFieldConvertor(formatType);
            sampleDataCtFieldTree = fieldBodyConvertor.toBodyCtField(sampleData);
        }
        CtField fieldsCtFieldTree = convertExcelField(fields, formatType);
        CtField mergedResult = mergeCtFieldTree(sampleDataCtFieldTree, fieldsCtFieldTree, excelFieldMergeType);
        if (mergedResult instanceof CtObjectField) {
            return (CtObjectField) mergedResult;
        } else {
            return new CtObjectField("", formatType, Lists.newArrayList(mergedResult));
        }
    }

    public static CtField convertExcelField(List<ExcelApiBodyField> excelFields, FormatType formatType) {
        if (CollectionUtils.isEmpty(excelFields)) return null;
        List<ExcelApiBodyField> sortFieldsBySep = excelFields.stream()
                .sorted(Comparator.comparing(excelApiBodyField -> {
                    String fieldParentPath = excelApiBodyField.getFieldParentPath();
                    if (StrUtil.isBlank(fieldParentPath)) return 0; //空的时候返回0
                    return StrUtil.count(fieldParentPath, FIELD_PATH_SEP) + 1; //默认+1
                })).collect(Collectors.toList());

        ExcelApiBodyField firstField = sortFieldsBySep.get(0);
        //如果第一个元素是数组并且是__array__开头的 则为嵌套数组
        if (ARRAY_BORDER_NAME.equals(firstField.getFieldName())) {
            CtField ctField = initCtFieldByExcelField(firstField, formatType);
            if (ctField instanceof CtArrayField) {
                CtArrayField ctArrayField = (CtArrayField) ctField;
                //todo: 目前必须得是body 因为要和 利用 FieldBodyConvertor 的body 对应上
                ctArrayField.setFieldKey(CtFieldHelper.Body_Segment);
                ctArrayField.setFieldName(CtFieldHelper.Body_Segment);
                if (ctArrayField.getElement() != null) {
                    if (ctArrayField.getElement() instanceof CtObjectField) {
                        CtObjectField ctObjectField = (CtObjectField) ctArrayField.getElement();
                        collectObjectFields(sortFieldsBySep.subList(1, sortFieldsBySep.size()), ctObjectField, formatType, ARRAY_BORDER_NAME);
                    }
                }
                return ctArrayField;
            }
            return null;
        } else {
            CtObjectField ctObjectField = new CtObjectField(CtFieldHelper.Body_Segment, formatType, Lists.newArrayList());
            collectObjectFields(sortFieldsBySep, ctObjectField, formatType, "");
            return ctObjectField;
        }
    }

    private static void collectObjectFields(List<ExcelApiBodyField> sortFieldsBySep, CtObjectField rootField, FormatType formatType, String startPath) {
        Map<String, CtField> pathFieldCache = new HashMap<>();
        List<CtField> ctFields = rootField.getElements();
        if (CollectionUtils.isEmpty(ctFields)) {
            ctFields = new ArrayList<>();
            rootField.setElements(ctFields);
        }

        for (ExcelApiBodyField excelApiBodyField : sortFieldsBySep) {
            String fieldParentPath = String.valueOf(excelApiBodyField.getFieldParentPath());
            //如果为空 说明是第一层
            if (StrUtil.equals(fieldParentPath, startPath)) {
                CtField ctField = initCtFieldByExcelField(excelApiBodyField, formatType);
                ctFields.add(ctField);
                //第一层中如果发现是root 或者array 就放入
                if (ctField instanceof CtObjectField || ctField instanceof CtArrayField) {
                    pathFieldCache.put(ctField.getFieldKey(), ctField);
                }
            } else {
                CtField parentField = pathFieldCache.get(fieldParentPath);
                if (parentField != null) {
                    CtField ctField = initCtFieldByExcelField(excelApiBodyField, formatType);
                    if (ctField instanceof CtObjectField || ctField instanceof CtArrayField) {
                        pathFieldCache.put(fieldParentPath + FIELD_PATH_SEP + ctField.getFieldKey(), ctField);
                    }
                    if (parentField instanceof CtObjectField) {
                        CtObjectField ctObjectField = (CtObjectField) parentField;
                        ctObjectField.getElements().add(ctField);
                    } else if (parentField instanceof CtArrayField) {
                        CtArrayField ctArrayField = (CtArrayField) parentField;
                        if (ctArrayField.getElement() != null) {
                            if (ctArrayField.getElement() instanceof CtObjectField) {
                                CtObjectField ctObjectField = (CtObjectField) ctArrayField.getElement();
                                ctObjectField.getElements().add(ctField);
                            }
                        }

                    }
                }
            }
        }
    }


    /**
     * 同时递归两个CtField树
     *
     * @param sampleCtFieldTree      样本的ctFieldTree
     * @param tableFieldsCtFieldTree tableFields的ctFieldsTree
     * @param excelFieldMergeType    合并策略
     */
    public static CtField mergeCtFieldTree(CtField sampleCtFieldTree, CtField tableFieldsCtFieldTree, ExcelFieldMergeType excelFieldMergeType) {
        if (sampleCtFieldTree == null) return tableFieldsCtFieldTree;
        if (tableFieldsCtFieldTree == null) return sampleCtFieldTree;
        switch (excelFieldMergeType) {
            case SampleFirst:
                mergeToFirst(sampleCtFieldTree, tableFieldsCtFieldTree);
                return sampleCtFieldTree;
            case FieldTableFirst:
                mergeToFirst(tableFieldsCtFieldTree, sampleCtFieldTree);
                return tableFieldsCtFieldTree;
            default:
                mergeToFirst(sampleCtFieldTree, tableFieldsCtFieldTree);
                return sampleCtFieldTree;
        }
    }

    /**
     * 同时递归两个CtField树
     * 按照firstTree 为优先 使用 secondTree 作为补充
     * 最后结构还是firstTree结构,通过secondTree 对 firstTree进行补充。
     *
     * @param firstCtFieldTree  第一个ctFieldTree
     * @param secondCtFieldTree 第二个ctFieldTree
     */
    private static void mergeToFirst(CtField firstCtFieldTree, CtField secondCtFieldTree) {
        //key相同
        if (!Objects.equals(firstCtFieldTree.getFieldKey(), secondCtFieldTree.getFieldKey())) return;
        mergeToFirstWhenEqual(firstCtFieldTree, secondCtFieldTree);
        if (firstCtFieldTree instanceof CtObjectField) {
            CtObjectField firstObject = (CtObjectField) firstCtFieldTree;
            if (!(secondCtFieldTree instanceof CtObjectField)) {
                return;
            }
            CtObjectField secondObject = (CtObjectField) secondCtFieldTree;
            if (CollectionUtils.isEmpty(firstObject.getElements()) || CollectionUtils.isEmpty(secondObject.getElements())) {
                return;
            }
            Map<String, CtField> secondElementMap = secondObject.getElements()
                    .stream()
                    .collect(Collectors.toMap(CtField::getFieldKey, Function.identity(), (v1, v2) -> v2));
            for (CtField firstSubField : firstObject.getElements()) {
                CtField secondSubField = secondElementMap.get(firstSubField.getFieldKey());
                if (secondSubField != null) {
                    mergeToFirst(firstSubField, secondSubField);
                }
            }
        } else if (firstCtFieldTree instanceof CtArrayField) {
            CtArrayField firstArray = (CtArrayField) firstCtFieldTree;
            //array
            if (!(secondCtFieldTree instanceof CtArrayField)) {
                return;
            }
            CtArrayField secondArray = (CtArrayField) secondCtFieldTree;
            if (Objects.isNull(firstArray.getElement()) || Objects.isNull(secondArray.getElement())) {
                return;
            }
            mergeToFirst(firstArray.getElement(), secondArray.getElement());
        }
    }

    private static void mergeToFirstWhenEqual(CtField firstCtFieldTree, CtField secondCtFieldTree) {
        firstCtFieldTree.setDescription(StrUtil.isNotBlank(firstCtFieldTree.getDescription()) ? firstCtFieldTree.getDescription() : secondCtFieldTree.getDescription());
        firstCtFieldTree.setRequired(Objects.nonNull(firstCtFieldTree.getRequired()) ? firstCtFieldTree.getRequired() : secondCtFieldTree.getRequired());
        FieldType fieldType = Objects.nonNull(firstCtFieldTree.getFieldType()) ? firstCtFieldTree.getFieldType() : secondCtFieldTree.getFieldType();
        firstCtFieldTree.setFieldType(Objects.nonNull(fieldType) ? fieldType : FieldType.Object);
    }

    /**
     * 将excelApi 转换为 CtApi
     *
     * @param excelApi            excelApi
     * @param excelFieldMergeType excelFieldMergeType
     * @return CtApi
     */
    public static CtApi apiConvertor(ExcelApi excelApi, ExcelFieldMergeType excelFieldMergeType) {
        CtApi ctApi = CtApi.builder()
                .name(excelApi.getApiName())
                .key(StrUtil.isBlank(excelApi.getApiKey()) ? excelApi.getApiName() : excelApi.getApiKey())
                .description(excelApi.getApiDesc())
                .tag(excelApi.getApiGroupTag())
                .build();
        // 处理input
        CtObjectField input = convertApiBody(excelApi.getApiInput(), excelApi.getSchema(), excelFieldMergeType);
        // 对于input而言只用inputs数组
        ctApi.setInput(input.getElements());
        CtObjectField output = convertApiBody(excelApi.getApiOutPut(), excelApi.getSchema(), excelFieldMergeType);
        output.setFieldKey("output");
        output.setFieldName("output");
        ctApi.setOutput(output);
        return ctApi;
    }
}
