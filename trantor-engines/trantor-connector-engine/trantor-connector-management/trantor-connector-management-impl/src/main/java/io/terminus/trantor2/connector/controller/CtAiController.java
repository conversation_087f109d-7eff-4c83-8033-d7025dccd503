package io.terminus.trantor2.connector.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.connector.functions.ai.AICopilot;
import io.terminus.trantor2.connector.model.functions.ai.models.OpenAiPlatForm;
import io.terminus.trantor2.connector.model.functions.ai.models.OpenAiPlatFormApi;
import io.terminus.trantor2.connector.model.meta.CtApi;
import io.terminus.trantor2.connector.model.functions.ai.request.FetchCtApiRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Tag(name = "连接器ai管理", description = "ai")
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/console/connector/ai")
@Validated
public class CtAiController {


    @GetMapping("/platform/find")
    @Operation(summary = "查询支持AI的平台")
    public Response<List<OpenAiPlatForm>> findSupportPlatFormByName(
        @Parameter(description = "平台名称(模糊查询,为空查询所有平台)")
        @RequestParam(required = false) String platformName
    ) {
        try {
            return Response.ok(AICopilot.fetchOpenAIPlatFormByName(platformName));
        } catch (Exception e) {
            throw new ValidationException(String.format("查询平台信息失败，错误信息：%s", e.getMessage()));
        }
    }


    @GetMapping("/platform/apis/query")
    @Operation(summary = "查询平台api信息")
    public Response<List<OpenAiPlatFormApi>> queryPlatFormApis(
        @Parameter(description = "平台名称", required = true)
        @NotBlank(message = "平台名称不能为空")
        @RequestParam() String platformName,
        @Parameter(description = "api名称(模糊查询,为空查询所有api)")
        @RequestParam(required = false) String apiName
    ) {
        try {
            return Response.ok(AICopilot.fetchOpenAIPlatFormApis(platformName, apiName));
        } catch (Exception e) {
            throw new ValidationException(String.format("查询平台api信息失败，错误信息：%s", e.getMessage()));
        }
    }

    @PostMapping("/fetch/api")
    @Operation(summary = "获取连接器服务实例")
    public Response<CtApi> fetchCtApi(
        @Parameter(description = "获取连接器服务实例入参", required = true)
        @Validated
        @NotNull(message = "获取连接器服务实例入参不能为空")
        @RequestBody FetchCtApiRequest request
    ) {
        try {
            return Response.ok(AICopilot.fetchAICtApiByUrl(request.getUrl()));
        } catch (Exception e) {
            throw new ValidationException(String.format("获取连接器服务实例失败，错误信息：%s", e.getMessage()));
        }
    }


}
