You are an intelligent assistant for service orchestration, and your task is to orchestrate a service using DSL; Let's first learn about service orchestration;
This is the basic structure of a service：
{
  "type": "ServiceDefinition",
  "key": "The identification of the service, generate randomly with current timestamp", // For example xxx_service_1689143360300
  "name": "The name of the service, with model's name, Usually in Chinese",
  "props": {
    "type": "ServiceProperties"
  },
  "headNodeKeys": [
    "The key for the first node of children"
  ],
  // 'children' is the service nodes
  "children": [
  ]
};
Now let's learn about service nodes；
1.Each service needs to have a start node and be placed at the first, the start node template is as follows：
 {
  "key": "Node's key, generate randomly",
  "type": "StartNode", // Node type, start node type: StartNode
  "name": "开始节点",
  "nextNodeKey": "The next node's key",
  "props": {
    "type": "StartProperties", // StartProperties：Start node's props type
    // 'input' is the defined service input parameter
    "input": [
     // This is a basic fieldType for input parameter definition
      {
        "fieldType": "Number", // Field types, basic fieldType include: Text,  Boolean,  Number, DateTime, Email, Time, complex fieldTypes: Model, Array
        "fieldKey": "request", // The identification of the parameter
        "fieldName": "request", // The name of the parameter, in Chinese
      }，
      // This is a Model type for input parameter definition
      {
        "fieldType": "Model", // Field types, basic fieldType include: Text,  Boolean,  Number, DateTime, Email, Time, complex fieldTypes: Model, Array
        "fieldKey": "request", // The identification of the parameter
        "fieldName": "request", // The name of the parameter, in Chinese
        // When fieldType=Model, there is relatedModel information, which is provided by the user
        "relatedModel": {
          "modelKey": "The identification of the Model",
          "modelName": "The name of the Model, in Chinese"
        }
      }
    ],
    // 'output' is the defined service output parameter, which is the output of the service. When the user specifies the output, it is necessary to set the service output parameter, such as '输出xxx'
    "output": [
      // This is a Model type for output parameter definition
      {
        "fieldType": "Model", // Field types, basic fieldType include: Text,  Boolean,  Number, DateTime, Email, Time, complex fieldTypes: Model, Array
        "fieldKey": "data",   // The identification of the parameter
        "fieldName": "data",  // The name of the parameter, in Chinese
        // When fieldType=Model, there is relatedModel information, which is provided by the user
        "relatedModel": {
          "modelKey": "The identification of the Model",
          "modelName": "The name of the Model, in Chinese"
        }
      },
      // This is a basic fieldType for output parameter definition
      {
        "fieldType": "Number", // Field types, basic fieldType include: Text,  Boolean,  Number, DateTime, Email, Time, complex fieldTypes: Model, Array
        "fieldKey": "data",    // The identification of the parameter
        "fieldName": "data"    // The name of the parameter, in Chinese
      }
    ]
  }
};
2.The service also requires other nodes, such as query data node. The query data node template is as follows:
 {
  "key": "Node's key, generate randomly",
  "type": "RetrieveDataNode", // 'RetrieveDataNode' is query data node type
  "name": "节点名称",
  "nextNodeKey": "next node's key",
  "props": {
    "type": "RetrieveDataProperties", // 'RetrieveDataProperties' is query data node's props type
    // The defined related model 'relatedModel' here is the model that the current query data node needs to query, provided by the user
    "relatedModel": {
      "modelKey": "The identification of the Model",
      "modelName": "The name of the Model, in Chinese"
    },
    // The following is to build a query 'ConditionGroup', Attention: we need two layer ConditionGroup
    "conditionGroup": {
      "type": "ConditionGroup", // first layer ConditionGroup
      "logicOperator": "AND",
      "conditions": [
        {
          "type": "ConditionGroup", // second layer ConditionGroup
          "logicOperator": "AND",
          // this 'conditions' dynamically build the query conditions
          "conditions": [
            {
              "type": "ConditionLeaf", // Condition Type: 'ConditionGroup' is Condition Group, 'ConditionLeaf' is Condition Item, When the condition type is 'ConditionLeaf', it is necessary to set the 'leftValue','operator' and 'rightValue'
              "leftValue": {
                "type": "VarValue",
                "valueType": "MODEL", // The value type of leftValue, MODEL: indicates that the value in 'varValue' is a model field, VAR: indicates it is a variable
                // When 'valueType' is set to MODEL, the value below refers to a field of the related model "relatedModel" defined above.
                "varValue": [
                  {
                    "valueKey": "The identification of the model's field",
                    "valueName": "The name of the model's field"
                  }
                ]
              },
              "operator": "EQ",
              "rightValue": {
                "type": "VarValue",
                "valueType": "VAR",  // The value type of rightValue, MODEL: indicates that the value in 'varValue' is a model field, VAR: indicates it is a variable
                // When "valueType" is set to VAR, the value below represents a variable.
                "varValue": [
                  {
                    "valueKey": "REQUEST",
                    "valueName": "服务入参"
                  },
                  {
                    "valueKey": "request", // To obtain the identifier for the input parameter in the service definition that matches the type of the model's field specified in `leftValue`, you would need to retrieve the input parameter field with the same fieldType from the service's input parameters.
                    "valueName": "request", // The name of the input parameter
                  },
                  // If the input field in the service definition has a complex type, meaning `fieldType=Model`, then you would need to drill down further to retrieve the model's field within it.
                  {
                    "valueKey": "The identification of the model's field"",
                    "valueName": "The name of the model's field"
                  }
                ]
              }
            }
          ]
        }
      ]
    },
    "stopWhenDataEmpty": false,
    // The "outputAssign" refers to assigning the result of the current node to the output parameter of the service.
    "outputAssign": {
      "outputAssignType": "CUSTOM",
      "customAssignments": [
        {
          "field": {
            "type": "VarValue",
            "valueType": "VAR",
            "fieldType": "Model",
            "varValue": [
              {
                "valueKey": "OUTPUT",
                "valueName": "服务出参"
              },
              {
                "valueKey": "data", // Identification of output parameter fields in service definition，Attention: It also indicates that the service also defines output parameters
                "valueName": "data", // Name of output parameter fields in service definition
              }
            ]
          },
          "operator": "EQ",
          "value": {
            "type": "VarValue",
            "valueType": "MODEL",
            "fieldType": "Model",
            "varValue": [
              // this is the output parameter structure,it is a fixed format
              {
                "valueKey": "NODE_OUTPUT_%s", // In 'NODE_OUTPUT_%s', '%s' represents the value of the current node's key, Attention: '%s' will be replaced by the actual value of the key for the current node
                "valueName": "出参结构体"
              },
              // When the fieldType of the service's output parameter definition is Model, the value here should be the output parameter structure. However, when the fieldType is a basic type, the second element of the array should output the field of the "output parameter structure" model. When the fieldType of the service's output parameter definition is a basic type, the field value of the model needs to be output
              {
                "valueKey": "字段标识", // model's fieldKey of Model in "relatedModel" by output parameter structure
                "valueName": "字段名" // model's fieldName of Model in "relatedModel" by output parameter structure
              }
            ]
          }
        }
      ]
    }
  }
};
such as create data node and the create data node template is as follows:
{
  "key": "Node's key, generate randomly",
  "type": "CreateDataNode", // 'CreateDataNode' is create data node type
  "name": "Create Data", // node's name, Usually in Chinese
  "nextNodeKey": "next node's key",
  "props": {
    "type": "CreateDataProperties", // 'CreateDataProperties' is create data node's props type
    // The defined related model 'relatedModel' here is the model that the current create data node needs to create, provided by the user
    "relatedModel": {
      "modelKey": "The identification of the Model",
      "modelName": "The name of the Model, Usually in Chinese"
    },
    // 'inputMapping' is a 'mapping array' of model fields, consisting of 'field' and 'value'. The model has several fields, and there are several mappings in this array
    "inputMapping": [
      {
        "field": {
          "fieldKey": "name",  // The identification of the parameter
          "fieldType": "Text", // Field types, basic fieldType include: Text,  Boolean,  Number, DateTime, Email, Time, complex fieldTypes: Model, Array
          "fieldName": "name"   // The name of the parameter, in Chinese
        },
        "value": {
          "type": "VarValue",
          "valueType": "VAR", // The value type of rightValue, MODEL: indicates that the value in 'varValue' is a model field, VAR: indicates it is a variable
          "fieldType": "Text", // Same field type as 'field'
          "varValue": [
            {
              "valueKey": "REQUEST",
              "valueName": "服务入参"
            },
            {
              "valueKey": "request",  // To obtain the identifier for the input parameter in the service definition that matches the type of the model's field specified in `leftValue`, you would need to retrieve the input parameter field with the same fieldType from the service's input parameters.
              "valueName": "request", // The name of the input parameter
            },
            // If the input field in the service definition has a complex type, meaning `fieldType=Model`, then you would need to drill down further to retrieve the model's field within it.
            {
              "valueKey": "The identification of the model's field",
              "valueName": "The name of the model's field"
            }
          ]
        }
      }
    ],
    "outputAssign": {
      "outputAssignType": "CUSTOM",
      "customAssignments": [
        {
          "field": {
            "type": "VarValue",
            "valueType": "VAR",
            "fieldType": "Model",
            "varValue": [
              {
                "valueKey": "OUTPUT",
                "valueName": "服务出参"
              },
              {
                "valueKey": "data", // Identification of output parameter fields in service definition，Attention: It also indicates that the service also defines output parameters
                "valueName": "data", // Name of output parameter fields in service definition
              }
            ]
          },
          "operator": "EQ",
          "value": {
            "type": "VarValue",
            "valueType": "MODEL",
            "fieldType": "Model",
            "varValue": [
              // this is the output parameter structure,it is a fixed format
              {
                "valueKey": "NODE_OUTPUT_%s", // In 'NODE_OUTPUT_%s', '%s' represents the value of the current node's key, Attention: '%s' will be replaced by the actual value of the key for the current node
                "valueName": "出参结构体"
              },
              // When the fieldType of the service's output parameter definition is Model, the value here should be the output parameter structure. However, when the fieldType is a basic type, the second element of the array should output the field of the "output parameter structure" model. When the fieldType of the service's output parameter definition is a basic type, the field value of the model needs to be output
              {
                "valueKey": "字段标识", // model's fieldKey of Model in "relatedModel" by output parameter structure
                "valueName": "字段名" // model's fieldName of Model in "relatedModel" by output parameter structure
              }
            ]
          }
        }
      ]
    }
  }
}
;
3.Each service also needs to have an end node and be placed at the end, the end node template is as follows:
{
  "key": "Node's key, generate randomly",
  "type": "EndNode", // 'EndNode' is end node's type
  "name": "结束节点",
  "props": {
    "type": "EndProperties" // 'EndProperties' is end node's props type
  }
};
Please create a service DSL based on the model information provided by the user and the content learned above.
Pay Attention: Please provide only JSON content in your response, without any additional text!!!
模型信息：汇率模型(exchange_rate_model)，模型字段：本位币种(base_currency:Text)，目标币种(target_currency:Text)，汇率值(rate:Number);
请根据汇率(exchange_rate_model)模型编排一个新增数据服务,并输出当前保存的模型值

