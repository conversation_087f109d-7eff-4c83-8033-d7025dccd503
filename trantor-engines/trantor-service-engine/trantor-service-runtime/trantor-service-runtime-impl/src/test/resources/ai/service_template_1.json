{
  "type": "ServiceDefinition",
  "key": "服务的标识，为英文名",
  "name": "服务的标识，一般为中文名",
  "props": {
    "type": "ServiceProperties"
  },
  "headNodeKeys": [
    "为children的第一个对象的key的值"
  ],
  // "children" 下为节点信息
  "children": [
    {
      "key": "节点的key,请随时生成",
      "type": "StartNode", // 开始节点的类型，每个服务都需要有一个开始节点
      "name": "开始节点",
      "nextNodeKey": "下一个节点的key",
      "props": {
        "type": "StartProperties", // StartProperties：开始节点的props的类型
        // "input" 为定义的服务入参
        "input": [
          {
            "fieldType": "Model", // 为变量字段类型，字段类型的枚举：Text：文本，Boolean：布尔，Number：数字，DateTime：日期，Email：邮箱，Time：时间，Array：数组，Model：模型
            "fieldKey": "request", // 定义的参数标识
            "fieldName": "request", // 定义的参数名
            // "relatedModel": 当fieldType=Model时，关联的模型信息
            "relatedModel": {
              "modelKey": "模型标识",
              "modelName": "模型名"
            }
          }
        ],
        // "output" 是定义的服务出参
        "output": [
          // 这是一个复杂类型的出参定义
          {
            "fieldType": "Model",// 定义的出参类型,基本字段类型的枚举：Text：文本，Boolean：布尔，Number：数字，DateTime：日期，Email：邮箱，Time：时间，Array：数组， 复杂的字段类型：Model：模型
            "fieldKey": "data", // 定义的出参标识
            "fieldName": "data", // 定义的出参名
            // "relatedModel": 当fieldType=Model时，关联的模型信息,
            "relatedModel": {
              "modelKey": "模型标识",
              "modelName": "模型名"
            }
          },
          // 这是一个基本类型的出参定义
          {
            "fieldType": "Number",// 定义的出参类型,基本字段类型的枚举：Text：文本，Boolean：布尔，Number：数字，DateTime：日期，Email：邮箱，Time：时间，Array：数组， 复杂的字段类型：Model：模型
            "fieldKey": "data", // 定义的出参标识
            "fieldName": "data" // 定义的出参名
          }
        ]
      }
    },
    {
      "key": "节点的key,请随时生成",
      "type": "RetrieveDataNode", // “RetrieveDataNode”:为查询数据节点的类型
      "name": "节点名称",
      "nextNodeKey": "下一个节点key",
      "props": {
        "type": "RetrieveDataProperties", // “RetrieveDataProperties”:为查询数据节点的props类型
        // 这里的"relatedModel"是当前查询数据节点需要查询的模型
        "relatedModel": {
          "modelKey": "模型标识",
          "modelName": "模型名"
        },
        //  "conditionGroup": 条件组，第一层的条件组仍然是条件组
        "conditionGroup": {
          "type": "ConditionGroup", // 条件类型："ConditionGroup":条件组，
          "logicOperator": "AND", // 条件操作符，表示组和组之间的操作符
          // "conditions"：这里conditions里的信息都是"ConditionGroup"
          "conditions": [
            {
              "type": "ConditionGroup", // 条件类型："ConditionGroup":条件组，"ConditionLeaf":条件项
              "logicOperator": "AND",  // 条件操作符，表示"conditions"里的条件的操作符
              // "conditions"：条件组内信息
              "conditions": [
                {
                  "type": "ConditionLeaf", // 条件类型："ConditionGroup":条件组，"ConditionLeaf":条件项
                  // 当 条件类型 = ConditionLeaf时，需要设置 左值 操作符 右值
                  // "leftValue":左值
                  "leftValue": {
                    "type": "VarValue", // 值的类型，VarValue表示为变量
                    "valueType": "MODEL", // valueType的类型，MODEL：表示"varValue"里的值为模型字段，VAR：表示是变量
                    // 当 "valueType":MODEL时，下面的值，就是关联模型"relatedModel"的字段
                    "varValue": [
                      {
                        "fieldKey": "字段标识",
                        "fieldName": "字段名"
                      }
                    ]
                  },
                  "operator": "EQ", // 操作符：EQ为其中一种操作号
                  // "rightValue":右值
                  "rightValue": {
                    "type": "VarValue",
                    "valueType": "VAR",
                    // 当 "valueType":VAR是，下面的值表示是个变量，需要从服务入参中取相同类型的值
                    "varValue": [
                      {
                        "fieldKey": "REQUEST", // 这里表示从服务的入参中取值
                        "fieldName": "服务入参"
                      },
                      {
                        "fieldKey": "request", // 这里就是从 “开始节点”中的定义的"input"的参数key
                        "fieldName": "request",
                        // "relatedModel":为input中定义的关联模型
                        "relatedModel": {
                          "modelKey": "模型标识",
                          "modelName": "模型名"
                        }
                      },
                      {
                        "fieldKey": "字段标识",
                        "fieldName": "字段名"
                      }
                    ]
                  }
                }
              ]
            }
          ]
        },
        "stopWhenDataEmpty": false,
        // "outputAssign":当前节点的结果赋值，正常就是把当前节点的结果赋值给服务出参
        "outputAssign": {
          "outputAssignType": "CUSTOM",
          "customAssignments": [
            {
              "field": {
                "type": "VarValue",
                "valueType": "VAR",
                "fieldType": "Model",
                "varValue": [
                  {
                    "fieldKey": "OUTPUT", //这里表示在操作服务出参
                    "fieldName": "服务出参"
                  },
                  {
                    "fieldKey": "data", // 服务出参定义的fieldkey
                    "fieldName": "data", // 服务出参定义的fieldName
                    // 当定义的服务出参字段类型，fieldType=Model时才有"relatedModel"值，否则没有
                    "relatedModel": {
                      "modelKey": "模型标识",
                      "modelName": "模型名"
                    }
                  }
                ]
              },
              "operator": "EQ",
              "value": {
                "type": "VarValue",
                "valueType": "MODEL",
                "fieldType": "Model",
                "varValue": [
                  // 数组第一个元数据是出参结构体，当服务的出参定义的fieldType=Model时，这里的值应该是出参结构体，但是当fieldType是基本类型时，应该数组的第二个元素输出"出参结构体"模型的字段
                  {
                    "fieldKey": "NODE_OUTPUT_%s", // NODE_OUTPUT_%s中 %s是当前节点的key，出参结构体是固定格式
                    "fieldName": "出参结构体",// 这是固定名称
                    "fieldType": "Model",
                    // 这里的"relatedModel"就是当前节点前面定义的"relatedModel"信息
                    "relatedModel": {
                      "modelKey": "模型标识",
                      "modelName": "模型名"
                    }
                  },
                  // 当服务的出参定义的fieldType是基本类型是，需要输出模型的字段值
                  {
                    "fieldKey": "字段标识", // 出参结构体"relatedModel"模型的字段
                    "fieldName": "字段名"
                  }
                ]
              }
            }
          ]
        }
      }
    },
    {
      "key": "节点key,随机生成",
      "type": "EndNode", // "EndNode"：是服务的最后一个节点，每个服务的最后一个节点都是EndNode
      "name": "结束节点",
      "props": {
        "type": "EndProperties" // "EndNode"：是服务的最后一个节点，每个服务的最后一个节点都是EndNode
      }
    }
  ]
}
