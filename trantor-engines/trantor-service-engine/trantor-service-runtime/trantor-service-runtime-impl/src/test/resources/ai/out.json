{"children": [{"key": "start_node", "name": "开始节点", "nextNodeKey": "query_data_node", "props": {"input": [{"fieldKey": "id", "fieldName": "ID", "fieldType": "Number"}], "output": [{"fieldKey": "name", "fieldName": "名称", "fieldType": "Text"}, {"fieldKey": "price", "fieldName": "价格", "fieldType": "Number"}], "type": "StartProperties"}, "type": "StartNode"}, {"key": "query_data_node", "name": "查询数据节点", "nextNodeKey": "end_node", "props": {"conditionGroup": {"conditions": [{"conditions": [{"leftValue": {"type": "VarValue", "valueType": "MODEL", "varValue": [{"valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "id", "valueName": "ID"}]}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "AND", "type": "ConditionGroup"}, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "name", "valueName": "名称"}]}, "operator": "EQ", "value": {"type": "VarValue", "valueType": "MODEL", "varValue": [{"valueKey": "NODE_OUTPUT_query_data_node", "valueName": "出参结构体"}, {"valueKey": "name", "valueName": "名称"}]}}, {"field": {"fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "price", "valueName": "价格"}]}, "operator": "EQ", "value": {"type": "VarValue", "valueType": "MODEL", "varValue": [{"valueKey": "NODE_OUTPUT_query_data_node", "valueName": "出参结构体"}, {"valueKey": "price", "valueName": "价格"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": {"modelKey": "TERP_MIGRATE$test005", "modelName": "test005"}, "stopWhenDataEmpty": false, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"key": "end_node", "name": "结束节点", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["start_node"], "key": "service_key_001_20230712160441", "name": "查询测试005模型服务", "props": {"type": "ServiceProperties"}, "type": "ServiceDefinition"}