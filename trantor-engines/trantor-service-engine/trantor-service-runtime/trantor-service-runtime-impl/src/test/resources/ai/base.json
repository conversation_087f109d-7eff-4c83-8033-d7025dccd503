{
  "type": "ServiceDefinition",
  "key": "The identification of the service, Usually in English, it is an easy to understand and meaningful noun",
  "name": "The name of the service, Usually in Chinese, it is an easy to understand and meaningful noun",
  "props": {
    "type": "ServiceProperties"
  },
  "headNodeKeys": [
    "The key for the first node of children"
  ],
  // 'children' is the service nodes
  "children": []
}
