{
  "key": "<PERSON><PERSON>'s key, generate randomly",
  "type": "RetrieveDataNode", // 'RetrieveDataNode' is query data node type
  "name": "node's name, Usually in Chinese",
  "nextNodeKey": "next node's key",
  "props": {
    "type": "RetrieveDataProperties", // 'RetrieveDataProperties' is query data node's props type
    // The defined related model 'relatedModel' here is the model that the current query data node needs to query, provided by the user
    "relatedModel": {
      "modelKey": "The key of the Model",
      "modelName": "The name of the Model"
    },
    "dataType": "MODEL|ARRAY|PAGING", // Query type: when querying a single data, it is MODEL, when querying multiple data, it is ARRAY, and when querying in pagination, it is PAGING
    // The following is to build a query 'ConditionGroup', Attention: we need two layer ConditionGroup
    "conditionGroup": {
      "type": "ConditionGroup", // first layer ConditionGroup must be set
      "logicOperator": "AND",
      "conditions": [
        {
          "type": "ConditionGroup", // second layer ConditionGroup must be set
          "logicOperator": "AND",
          // the 'conditions' dynamically build the query conditions
          "conditions": [
            {
              "type": "ConditionLeaf", // Condition Type: 'ConditionGroup' is Condition Group, 'ConditionLeaf' is Condition Item, When the condition type is 'ConditionLeaf', it is necessary to set the 'leftValue','operator' and 'rightValue'
              "leftValue": {
                "type": "VarValue",
                "valueType": "MODEL", // The value type of leftValue, MODEL: indicates that the value in 'varValue' is a model field, VAR: indicates it is a variable
                // When 'valueType' is set to MODEL, the value below refers to a field of the related model "relatedModel" defined above.
                "varValue": [
                  {
                    "valueKey": "The key of the relatedModel's field",
                    "valueName": "The name of the relatedModel's field"
                  }
                ]
              },
              "operator": "EQ",
              "rightValue": {
                "type": "VarValue",
                "valueType": "VAR",  // The value type of rightValue, MODEL: indicates that the value in 'varValue' is a model field, VAR: indicates it is a variable
                // When "valueType" is set to VAR, the value below represents a variable. "varValue" 代表的是字段的层级，第一个层级'REQUEST'是固定的，后面则代表的是字段的层级，比如a.b.c则 varValue=[{valueKey:a,valueName:a},{valueKey:b,valueName:b},{valueKey:c,valueName:c}]
                "varValue": [
                  {
                    "valueKey": "REQUEST",
                    "valueName": "服务入参"
                  },
                  {
                    "valueKey": "request", // To obtain the identifier for the input parameter in the service definition that matches the type of the model's field specified in `leftValue`, you would need to retrieve the input parameter field with the same fieldType from the service's input parameters.
                    "valueName": "request" // The name of the input parameter
                  },
                  // If the input field in the service definition has a complex type, meaning `fieldType=Model`, then you would need to drill down further to retrieve the model's field within it.
                  {
                    "valueKey": "The key of the model's field",
                    "valueName": "The name of the model's field"
                  }
                ]
              }
            }
          ]
        }
      ]
    },
    // pageable configuration only exists when dataType='Paging'
    "pageable": {
      "varValue": [
        {
          "valueKey": "REQUEST",
          "valueName": "服务入参"
        },
        {
          "valueKey": "request", // To obtain the identifier for the input parameter in the service definition that matches the type of the model's field specified in `leftValue`, you would need to retrieve the input parameter field with the same fieldType from the service's input parameters.
          "valueName": "request" // The name of the input parameter
        },
        // If the input field in the service definition has a complex type, meaning `fieldType=Model`, then you would need to drill down further to retrieve the model's field within it.
        {
          "valueKey": "The key of the model's field",
          "valueName": "The name of the model's field"
        }
      ]
    },
    "stopWhenDataEmpty": false,
    // The "outputAssign" refers to assigning the result of the current node to the output parameter of the service.
    "outputAssign": {
      "outputAssignType": "CUSTOM",
      "customAssignments": [
        {
          "field": {
            "type": "VarValue",
            "valueType": "VAR",
            "fieldType": "Model",
            "varValue": [
              {
                "valueKey": "OUTPUT",
                "valueName": "服务出参"
              },
              {
                "valueKey": "data", // Identification of output parameter fields in service definition
                "valueName": "data" // Name of output parameter fields in service definition
              }
            ]
          },
          "operator": "EQ",
          "value": {
            "type": "VarValue",
            "valueType": "MODEL",
            "fieldType": "Model",
            "varValue": [
              // this is the output parameter structure,it is a fixed format，这个代表的是整个模型对象值
              {
                "valueKey": "NODE_OUTPUT_%s", // Attention: in 'NODE_OUTPUT_%s', '%s' represents the value of the current node's key.
                "valueName": "出参结构体"
              },
              // When the fieldType of the service's output parameter definition is Model, the value here should be the output parameter structure. However, when the fieldType is a basic type, the second element of the array should output the field of the "output parameter structure" model. When the fieldType of the service's output parameter definition is a basic type, the field value of the model needs to be output
              // 如果返回整个对象，则没有下面的定义
              {
                "valueKey": "字段标识", // model's fieldKey of Model in "relatedModel" by output parameter structure
                "valueName": "字段名" // model's fieldName of Model in "relatedModel" by output parameter structure
              }
            ]
          }
        }
      ]
    }
  }
}
