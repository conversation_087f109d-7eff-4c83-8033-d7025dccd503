{"type": "ServiceDefinition", "key": "exchange_rate_service", "name": "汇率查询服务", "props": {"type": "ServiceProperties"}, "headNodeKeys": ["start_node"], "children": [{"key": "start_node", "type": "StartNode", "name": "开始节点", "nextNodeKey": "retrieve_data_node", "props": {"type": "StartProperties", "input": [{"fieldType": "Model", "fieldKey": "request", "fieldName": "request", "relatedModel": {"modelKey": "exchange_rate_model", "modelName": "汇率模型"}}], "output": [{"fieldType": "Number", "fieldKey": "data", "fieldName": "data"}]}}, {"key": "retrieve_data_node", "type": "RetrieveDataNode", "name": "查询汇率", "nextNodeKey": "end_node", "props": {"type": "RetrieveDataProperties", "relatedModel": {"modelKey": "exchange_rate_model", "modelName": "汇率模型"}, "conditionGroup": {"type": "ConditionGroup", "logicOperator": "AND", "conditions": [{"type": "ConditionGroup", "logicOperator": "AND", "conditions": [{"type": "ConditionLeaf", "leftValue": {"type": "VarValue", "valueType": "MODEL", "varValue": [{"valueKey": "本位币种", "valueName": "本位币种"}]}, "operator": "EQ", "rightValue": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request", "relatedModel": {"modelKey": "exchange_rate_model", "modelName": "汇率模型"}}, {"valueKey": "本位币种", "valueName": "本位币种"}]}}, {"type": "ConditionLeaf", "leftValue": {"type": "VarValue", "valueType": "MODEL", "varValue": [{"valueKey": "目标币种", "valueName": "目标币种"}]}, "operator": "EQ", "rightValue": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request", "relatedModel": {"modelKey": "exchange_rate_model", "modelName": "汇率模型"}}, {"valueKey": "目标币种", "valueName": "目标币种"}]}}]}]}, "stopWhenDataEmpty": false, "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"field": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "data", "valueName": "data"}]}, "operator": "EQ", "value": {"type": "VarValue", "valueType": "MODEL", "fieldType": "Number", "varValue": [{"valueKey": "NODE_OUTPUT_retrieve_data_node", "valueName": "出参结构体", "fieldType": "Number", "relatedModel": {"modelKey": "exchange_rate_model", "modelName": "汇率模型"}}, {"valueKey": "汇率值", "valueName": "汇率值", "fieldType": "Number"}]}}]}}}, {"key": "end_node", "type": "EndNode", "name": "结束节点", "props": {"type": "EndProperties"}}]}