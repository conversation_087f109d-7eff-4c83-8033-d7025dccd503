{"type": "ServiceDefinition", "key": "tr_so_h_SAVE_MULTI_LEVEL", "name": "tr_so_h多级保存服务", "props": {"type": "ServiceProperties", "desc": "销售订单多级保存服务", "status": "ENABLE", "transactionPropagation": "REQUIRED", "transactionTimeout": 10000, "appId": 1, "teamId": 1}, "headNodeKeys": ["startNodeKey"], "children": [{"type": "StartNode", "key": "startNodeKey", "name": "开始节点", "props": {"type": "StartProperties", "name": "开始节点", "desc": "开始节点", "input": [{"fieldType": "Model", "id": null, "fieldKey": "request", "fieldName": "request", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "elements": null}], "globalVariable": [{"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "elements": null}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "lecribux302"}, {"type": "ExclusiveBranchNode", "key": "lecribux302", "name": "排他分支", "props": {"nodeKey": "lecribux302", "type": "ExclusiveBranchProperties", "nodeTitle": "排他分支", "nodeContent": null, "relatedModel": null, "conditionGroup": null, "modelConfig": [], "relatedModelKey": null}, "headNodeKeys": ["lecribux303", "lecribux304"], "nextNodeKey": "endNodeKey", "children": [{"type": "ConditionNode", "key": "lecribux303", "props": {"type": "ConditionProperties", "nodeKey": "lecribux303", "nodeTitle": "条件", "relatedModel": null, "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": "Text"}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null}]}]}, "modelConfig": [], "relatedModelKey": null}, "isInBranch": true, "nextNodeKey": "UpdateDataNodeKey"}, {"type": "ConditionNode", "key": "lecribux304", "props": {"type": "ConditionProperties", "nodeKey": "lecribux304", "nodeTitle": "条件", "nodeContent": "当入参-id 等于空", "relatedModel": null, "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": "Text"}, "operator": "IS_NULL", "rightValue": null, "rightValues": null}]}]}, "modelConfig": [], "relatedModelKey": null}, "isInBranch": true, "nextNodeKey": "CreateDataNodeKey"}, {"type": "UpdateDataNode", "key": "UpdateDataNodeKey", "name": "更新数据", "props": {"type": "UpdateDataProperties", "desc": "更新数据", "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "inputMapping": [{"field": {"fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "link_id", "fieldName": "销售组织和销售渠道关联", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "link_id", "valueName": "销售组织和销售渠道关联"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "slsorg_desc", "fieldName": "销售组织", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "slsorg_desc", "valueName": "销售组织"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "slsdc_desc", "fieldName": "销售渠道", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "slsdc_desc", "valueName": "销售渠道"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "so_l", "fieldName": "关联的销售订单行", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_l", "valueName": "关联的销售订单行"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "so_test", "fieldName": "关联的测试", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_test", "valueName": "关联的测试"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}], "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "id", "valueName": "ID"}], "constValue": null, "valueType": "MODEL", "fieldType": null}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "rightValues": null}]}]}}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "UpdateDataAssignNodeKey"}, {"type": "AssignNode", "key": "UpdateDataAssignNodeKey", "name": "赋值", "props": {"type": "AssignProperties", "desc": "赋值", "assignments": [{"field": {"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_h", "valueName": "tr_so_h"}], "relatedNode": {"nodeKey": "UpdateDataNodeKey", "nodeTitle": "创建数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "nextNodeKey": "so_lDeleteChildDataNodeKey"}, {"type": "DeleteDataNode", "key": "so_lDeleteChildDataNodeKey", "name": "删除子数据", "props": {"type": "DeleteDataProperties", "desc": "更新数据", "relatedModel": {"modelKey": "tr_so_l", "modelName": "tr_so_l"}, "conditionGroup": {"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "so_id", "valueName": "so_id"}], "constValue": null, "valueType": "MODEL", "fieldType": null}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "rightValues": null}]}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "so_lForUpdateLoopNodeKey"}, "nextNodeKey": "so_lForUpdateLoopNodeKey"}, {"type": "LoopNode", "key": "so_lForUpdateLoopNodeKey", "name": "循环创建so_l子行", "props": {"type": "LoopProperties", "desc": "循环创建子行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_l", "valueName": "关联的销售订单行"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建子数据", "props": {"type": "CreateDataProperties", "desc": "创建子数据", "relatedModel": {"modelKey": "tr_so_l", "modelName": "tr_so_l"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_id", "fieldKey": "so_id", "fieldName": "关联销售订单编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_l", "valueName": "tr_so_l"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "mat_id", "fieldKey": "mat_id", "fieldName": "物料编码", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "mat_id", "valueName": "物料编码"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "mat_desc", "fieldKey": "mat_desc", "fieldName": "物料名称", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "mat_desc", "valueName": "物料名称"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_qty_sls", "fieldKey": "soitem_qty_sls", "fieldName": "销售数量", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soitem_qty_sls", "valueName": "销售数量"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_exptdate", "fieldKey": "soitem_exptdate", "fieldName": "期望交货日期", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soitem_exptdate", "valueName": "期望交货日期"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_s", "fieldKey": "so_s", "fieldName": "关联的发货单", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_s", "valueName": "关联的发货单"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "so_sForUpdateLoopNodeKey"}, {"type": "LoopNode", "key": "so_sForUpdateLoopNodeKey", "name": "循环创建so_s孙行", "props": {"type": "LoopProperties", "desc": "循环创建孙行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_s", "valueName": "关联的发货单"}], "relatedNode": {"nodeKey": "so_lForUpdateLoopNodeKey", "nodeTitle": "上级循环节点"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建孙数据", "props": {"type": "CreateDataProperties", "desc": "创建孙数据", "relatedModel": {"modelKey": "tr_so_s", "modelName": "tr_so_s"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_id", "fieldKey": "so_id", "fieldName": "关联销售订单编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_id", "valueName": "关联销售订单编号"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_id", "fieldKey": "soitem_id", "fieldName": "关联销售订单行编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_s", "valueName": "tr_so_s"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soschl_exptdate", "fieldKey": "soschl_exptdate", "fieldName": "计划发货日期", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soschl_exptdate", "valueName": "计划发货日期"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soschl_qty_del", "fieldKey": "soschl_qty_del", "fieldName": "计划发货数量", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soschl_qty_del", "valueName": "计划发货数量"}], "relatedNode": {"nodeKey": "so_sForUpdateLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": "so_testDeleteChildDataNodeKey"}, {"type": "DeleteDataNode", "key": "so_testDeleteChildDataNodeKey", "name": "删除子数据", "props": {"type": "DeleteDataProperties", "desc": "更新数据", "relatedModel": {"modelKey": "tr_so_test", "modelName": "tr_so_test"}, "conditionGroup": {"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_h_id", "valueName": "tr_so_h_id"}], "constValue": null, "valueType": "MODEL", "fieldType": null}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "rightValues": null}]}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "so_testForUpdateLoopNodeKey"}, "nextNodeKey": "so_testForUpdateLoopNodeKey"}, {"type": "LoopNode", "key": "so_testForUpdateLoopNodeKey", "name": "循环创建so_test子行", "props": {"type": "LoopProperties", "desc": "循环创建子行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_test", "valueName": "关联的测试"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建子数据", "props": {"type": "CreateDataProperties", "desc": "创建子数据", "relatedModel": {"modelKey": "tr_so_test", "modelName": "tr_so_test"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_testForUpdateLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "tr_so_h_id", "fieldKey": "tr_so_h_id", "fieldName": "主表ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_test", "valueName": "tr_so_test"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": null}, {"type": "CreateDataNode", "key": "CreateDataNodeKey", "name": "创建数据", "props": {"type": "CreateDataProperties", "desc": "创建数据", "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "link_id", "fieldKey": "link_id", "fieldName": "销售组织和销售渠道关联", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "link_id", "valueName": "销售组织和销售渠道关联"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "slsorg_desc", "fieldKey": "slsorg_desc", "fieldName": "销售组织", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "slsorg_desc", "valueName": "销售组织"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "slsdc_desc", "fieldKey": "slsdc_desc", "fieldName": "销售渠道", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "slsdc_desc", "valueName": "销售渠道"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_l", "fieldKey": "so_l", "fieldName": "关联的销售订单行", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_l", "valueName": "关联的销售订单行"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_test", "fieldKey": "so_test", "fieldName": "关联的测试", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_test", "valueName": "关联的测试"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "CreateDataAssignNodeKey"}, {"type": "AssignNode", "key": "CreateDataAssignNodeKey", "name": "赋值", "props": {"type": "AssignProperties", "desc": "赋值", "assignments": [{"field": {"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_h", "valueName": "tr_so_h"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "nextNodeKey": "so_lLoopNodeKey"}, {"type": "LoopNode", "key": "so_lLoopNodeKey", "name": "循环创建so_l子行", "props": {"type": "LoopProperties", "desc": "循环创建子行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_l", "valueName": "关联的销售订单行"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建子数据", "props": {"type": "CreateDataProperties", "desc": "创建子数据", "relatedModel": {"modelKey": "tr_so_l", "modelName": "tr_so_l"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_id", "fieldKey": "so_id", "fieldName": "关联销售订单编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_l", "valueName": "tr_so_l"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "mat_id", "fieldKey": "mat_id", "fieldName": "物料编码", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "mat_id", "valueName": "物料编码"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "mat_desc", "fieldKey": "mat_desc", "fieldName": "物料名称", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "mat_desc", "valueName": "物料名称"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_qty_sls", "fieldKey": "soitem_qty_sls", "fieldName": "销售数量", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soitem_qty_sls", "valueName": "销售数量"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_exptdate", "fieldKey": "soitem_exptdate", "fieldName": "期望交货日期", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soitem_exptdate", "valueName": "期望交货日期"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_s", "fieldKey": "so_s", "fieldName": "关联的发货单", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_s", "valueName": "关联的发货单"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "循环创建so_l子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "so_sLoopNodeKey"}, {"type": "LoopNode", "key": "so_sLoopNodeKey", "name": "循环创建so_s孙行", "props": {"type": "LoopProperties", "desc": "循环创建孙行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_s", "valueName": "关联的发货单"}], "relatedNode": {"nodeKey": "so_lLoopNodeKey", "nodeTitle": "上级循环节点"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建孙数据", "props": {"type": "CreateDataProperties", "desc": "创建孙数据", "relatedModel": {"modelKey": "tr_so_s", "modelName": "tr_so_s"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "so_id", "fieldKey": "so_id", "fieldName": "关联销售订单编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "so_id", "valueName": "关联销售订单编号"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soitem_id", "fieldKey": "soitem_id", "fieldName": "关联销售订单行编号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_s", "valueName": "tr_so_s"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soschl_exptdate", "fieldKey": "soschl_exptdate", "fieldName": "计划发货日期", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soschl_exptdate", "valueName": "计划发货日期"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "soschl_qty_del", "fieldKey": "soschl_qty_del", "fieldName": "计划发货数量", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "soschl_qty_del", "valueName": "计划发货数量"}], "relatedNode": {"nodeKey": "so_sLoopNodeKey", "nodeTitle": "循环创建so_s子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": "so_testLoopNodeKey"}, {"type": "LoopNode", "key": "so_testLoopNodeKey", "name": "循环创建so_test子行", "props": {"type": "LoopProperties", "desc": "循环创建子行", "loopData": {"type": "VarValue", "constValue": null, "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "so_test", "valueName": "关联的测试"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": ["CreateChildDataNodeKey"], "children": [{"type": "CreateDataNode", "key": "CreateChildDataNodeKey", "name": "创建子数据", "props": {"type": "CreateDataProperties", "desc": "创建子数据", "relatedModel": {"modelKey": "tr_so_test", "modelName": "tr_so_test"}, "inputMapping": [{"field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_by", "fieldKey": "created_by", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_by", "valueName": "创建人"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_by", "fieldKey": "updated_by", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_by", "valueName": "更新人"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "created_at", "fieldKey": "created_at", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "created_at", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "updated_at", "fieldKey": "updated_at", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "updated_at", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "version", "fieldKey": "version", "fieldName": "版本号", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "version", "valueName": "版本号"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "deleted", "fieldKey": "deleted", "fieldName": "逻辑删除标识", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_LoopItem", "valueName": "循环变量"}, {"valueKey": "_item", "valueName": "_item"}, {"valueKey": "deleted", "valueName": "逻辑删除标识"}], "relatedNode": {"nodeKey": "so_testLoopNodeKey", "nodeTitle": "循环创建so_test子行"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"id": "tr_so_h_id", "fieldKey": "tr_so_h_id", "fieldName": "主表ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "tr_so_test", "valueName": "tr_so_test"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建主单数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null}], "renderType": null, "nextNodeKey": null}]}, {"type": "EndNode", "key": "endNodeKey", "name": "结束节点", "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null, "props": {"type": "EndProperties", "outputMapping": [{"id": null, "field": {"fieldType": "Model", "id": null, "fieldKey": "data", "fieldName": "data", "referenceValue": null, "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "tr_so_h", "modelName": "tr_so_h"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_GlobalVariable", "valueName": "全局变量"}, {"valueKey": "result", "valueName": "result"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "保存数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}}]}