[{"endpointId": 123, "namespace": null, "entityKey": "MODEL_A", "entityName": "MODEL_A", "fieldRules": [{"entityKey": "MODEL_A", "entityName": "MODEL_A", "entityParamKey": "MODEL_A_FIELD_1", "entityParamName": "MODEL_A_FIELD_1", "accessType": "WRITABLE"}, {"entityKey": "MODEL_A", "entityName": "MODEL_A", "entityParamKey": "MODEL_A_FIELD_2", "entityParamName": "MODEL_A_FIELD_2", "accessType": "READONLY"}, {"entityKey": "MODEL_A", "entityName": "MODEL_A", "entityParamKey": "MODEL_A_FIELD_3", "entityParamName": "MODEL_A_FIELD_3", "accessType": "INVISIBLE"}]}, {"endpointId": 123, "namespace": null, "entityKey": "MODEL_B", "entityName": "MODEL_B", "fieldRules": [{"entityKey": "MODEL_B", "entityName": "MODEL_B", "entityParamKey": "MODEL_B_FIELD_1", "entityParamName": "MODEL_B_FIELD_1", "accessType": "INVISIBLE"}, {"entityKey": "MODEL_B", "entityName": "MODEL_B", "entityParamKey": "MODEL_B_FIELD_2", "entityParamName": "MODEL_B_FIELD_2", "accessType": "READONLY"}, {"entityKey": "MODEL_B", "entityName": "MODEL_B", "entityParamKey": "MODEL_B_FIELD_3", "entityParamName": "MODEL_B_FIELD_3", "accessType": "INVISIBLE"}, {"entityKey": "MODEL_B", "entityName": "MODEL_B", "entityParamKey": "MODEL_B_FIELD_5", "entityParamName": "MODEL_B_FIELD_5", "accessType": "READONLY"}]}, {"endpointId": 123, "namespace": null, "entityKey": "MODEL_C", "entityName": "MODEL_C", "fieldRules": [{"entityKey": "MODEL_C", "entityName": "MODEL_C", "entityParamKey": "MODEL_C_FIELD_2", "entityParamName": "MODEL_C_FIELD_2", "accessType": "INVISIBLE"}, {"entityKey": "MODEL_C", "entityName": "MODEL_C", "entityParamKey": "MODEL_C_FIELD_4", "entityParamName": "MODEL_C_FIELD_4", "accessType": "INVISIBLE"}]}]