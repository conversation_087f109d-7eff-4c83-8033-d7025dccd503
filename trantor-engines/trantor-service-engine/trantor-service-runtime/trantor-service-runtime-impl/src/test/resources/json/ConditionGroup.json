{"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "name", "valueName": "name"}], "constValue": null, "valueType": "MODEL", "fieldType": null, "relatedNode": null}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": null, "constValue": "atttt", "valueType": "CONST", "fieldType": null, "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}}, "rightValues": null}]}