{"id": null, "key": "ERP_SCM$test_sql", "name": "test_sql", "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED", "aiService": null, "aiChatMode": null, "aiRoundsStrategy": null, "schedulerJob": null, "stateMachine": null}, "headNodeKeys": ["node_1httio8jq1"], "children": [{"key": "node_1httio8jq1", "type": "StartNode", "name": "开始", "nextNodeKey": "node_1hu2ic04c42", "props": {"type": "StartProperties", "input": [{"id": null, "fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": null}, {"id": null, "fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "conditions", "fieldAlias": "conditions", "fieldName": "条件对象", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": null, "relation": null}, {"id": null, "fieldKey": "logicOperator", "fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text", "description": null, "required": null, "defaultValue": null}]}, {"id": null, "fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "description": null, "required": null, "defaultValue": null, "element": null}, {"id": null, "fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}, {"id": null, "fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}]}], "output": null, "globalVariable": null, "nodeTitle": "开始"}}, {"key": "node_1hu2ic04c42", "type": "SPINode", "name": "调用Action", "nextNodeKey": "node_1httiokbr3", "props": {"nodeTitle": "调用Action", "type": "ActionProperties", "implementation": "ERP_SCM$INV_MVM_EXT_TYPE_DELETE_ACTION", "implementationName": "移动扩展类型删除", "output": null, "outputAssign": {"outputAssignType": "SYSTEM"}, "nodeKey": "node_1hu2ic04c42"}}, {"key": "node_1httiokbr3", "type": "SqlNode", "name": "在线SQL脚本", "nextNodeKey": "node_1httio8jq2", "props": {"type": "SqlProperties", "sqlScript": "SELECT * from test_sql_001\nwhere ${REQUEST.pageable}", "output": null, "outputAssign": null, "sqlPlaceholderMapping": null, "nodeTitle": "在线SQL脚本"}}, {"key": "node_1httio8jq2", "type": "EndNode", "name": "结束", "props": {"type": "EndProperties", "nodeTitle": "结束"}}], "input": [{"id": null, "fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": null}, {"id": null, "fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "conditions", "fieldAlias": "conditions", "fieldName": "条件对象", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": null, "relation": null}, {"id": null, "fieldKey": "logicOperator", "fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text", "description": null, "required": null, "defaultValue": null}]}, {"id": null, "fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "description": null, "required": null, "defaultValue": null, "element": null}, {"id": null, "fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}, {"id": null, "fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}]}], "output": null}