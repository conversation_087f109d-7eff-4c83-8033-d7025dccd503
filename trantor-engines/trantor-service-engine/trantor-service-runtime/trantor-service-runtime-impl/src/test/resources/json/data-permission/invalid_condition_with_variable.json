{"key": "6agRvdx5333333355", "conditions": [{"key": "BX5p7JiSY099iYK49rOJS", "conditions": [{"key": "ncQyfBjtiO3mfaqfTplzs", "param": "{\n            \"modelKey\": \"ERP_SCM$user\",\n            \"modelName\": \"ERP_SCM$user\",\n            \"columnKey\": \"createdBy\",\n            \"columnName\": \"创建人\",\n            \"columnType\": \"Number\",\n            \"next\": {\n              \"modelKey\": \"ERP_SCM$user\",\n              \"columnKey\": \"id\",\n              \"columnName\": \"ID\",\n              \"columnType\": \"Number\"\n            }\n          }", "operator": "EQ", "conditionValues": [{"valueType": "VARIABLE", "variableValue": {"name": "查询默认物料成本价", "value": "DefaultPriceMatBase()"}}]}]}, {"key": "6agRvdxby0SLUrvk21Yh5", "conditions": [{"key": "pnsWrIhtd-paAngVJQSaN", "param": "{\n            \"modelKey\": \"Jose_dev_mod$user\",\n            \"modelName\": \"Jose_dev_mod$user\",\n            \"columnKey\": \"createdBy\",\n            \"columnName\": \"创建人\",\n            \"columnType\": \"Model\"\n          }", "operator": "IN", "conditionValues": [{"valueType": "VALUE_RANGE", "variableValue": {"name": "null", "value": "Jose_dev_mod$zdwd2"}}], "nextLogicalOperator": "OR"}, {"key": "I4km2bBUVg2dqrLAGND9D", "param": "{\n            \"modelKey\": \"Jose_dev_mod$gen_data_auth_cf\",\n            \"columnKey\": \"name2\",\n            \"columnName\": \"name2\",\n            \"columnType\": \"Text\"\n          }", "operator": "EQ", "conditionValues": [{"values": ["12"], "valueType": "SPECIFY_VALUE"}]}]}]}