{"type": "ServiceDefinition", "key": "supplier_SAVE", "name": "供应商保存服务", "props": {"type": "ServiceProperties", "desc": "保存服务", "status": "ENABLE", "transactionPropagation": "REQUIRED", "transactionTimeout": 10000, "appId": 1, "teamId": 1}, "headNodeKeys": ["startNodeKey"], "children": [{"type": "StartNode", "key": "startNodeKey", "name": "开始节点", "props": {"type": "StartProperties", "name": "开始节点", "desc": "开始节点", "input": [{"fieldType": "Model", "id": null, "fieldKey": "request", "fieldName": "request", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "elements": null}], "globalVariable": [{"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "elements": null}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "lecribux302"}, {"type": "ExclusiveBranchNode", "key": "lecribux302", "name": "排他分支", "props": {"nodeKey": "lecribux302", "type": "ExclusiveBranchProperties", "nodeTitle": "排他分支", "nodeContent": null, "relatedModel": null, "conditionGroup": null, "modelConfig": [], "relatedModelKey": null}, "headNodeKeys": ["lecribux303", "lecribux304"], "nextNodeKey": "end0", "children": [{"type": "ConditionNode", "key": "lecribux303", "preNodeKey": "lecribux302", "renderType": "Condition", "props": {"type": "ConditionProperties", "nodeKey": "lecribux303", "nodeTitle": "条件", "relatedModel": null, "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": "Text"}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null}]}]}, "modelConfig": [], "relatedModelKey": null}, "isInBranch": true, "nextNodeKey": "UpdateDataNodeKey"}, {"type": "ConditionNode", "key": "lecribux304", "preNodeKey": "lecribux302", "renderType": "Condition", "props": {"type": "ConditionProperties", "nodeKey": "lecribux304", "nodeTitle": "条件", "nodeContent": "当 通用流程触发-通用流程入参-test 不等于 test", "relatedModel": null, "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": "Text"}, "operator": "IS_NULL", "rightValue": null, "rightValues": null}]}]}, "modelConfig": [], "relatedModelKey": null}, "isInBranch": true, "nextNodeKey": "CreateDataNodeKey"}, {"type": "CreateDataNode", "key": "CreateDataNodeKey", "name": "创建数据", "props": {"type": "CreateDataProperties", "desc": "创建数据", "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "inputMapping": [{"id": "lejo2xfu36", "field": {"id": "id", "fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"id": "lejo2xfu36", "field": {"id": "created<PERSON>y", "fieldKey": "created<PERSON>y", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created<PERSON>y", "valueName": "创建人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"id": "lejo2xfu36", "field": {"id": "updatedBy", "fieldKey": "updatedBy", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updatedBy", "valueName": "更新人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"id": "lejo2xfu36", "field": {"id": "createdAt", "fieldKey": "createdAt", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "createdAt", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"id": "lejo2xfu36", "field": {"id": "updatedAt", "fieldKey": "updatedAt", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updatedAt", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"id": "lejo2xfu36", "field": {"id": "materialId", "fieldKey": "materialId", "fieldName": "物料编码", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "materialId", "valueName": "物料编码"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "CreateDataAssignNodeKey"}, {"type": "AssignNode", "key": "CreateDataAssignNodeKey", "name": "赋值", "props": {"type": "AssignProperties", "desc": "赋值", "assignments": [{"field": {"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "supplier", "valueName": "供应商"}], "relatedNode": {"nodeKey": "CreateDataNodeKey", "nodeTitle": "创建数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}}, {"type": "UpdateDataNode", "key": "UpdateDataNodeKey", "name": "更新数据", "props": {"type": "UpdateDataProperties", "desc": "更新数据", "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "inputMapping": [{"field": {"fieldKey": "id", "fieldName": "ID", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "created<PERSON>y", "fieldName": "创建人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "created<PERSON>y", "valueName": "创建人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "updatedBy", "fieldName": "更新人", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updatedBy", "valueName": "更新人"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "createdAt", "fieldName": "创建时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "createdAt", "valueName": "创建时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "updatedAt", "fieldName": "更新时间", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "updatedAt", "valueName": "更新时间"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}, {"field": {"fieldKey": "materialId", "fieldName": "物料编码", "fieldType": "Text", "referenceValue": null, "description": "", "required": false}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "materialId", "valueName": "物料编码"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}], "conditionGroup": {"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionGroup", "nextOperator": "AND", "conditions": [{"type": "ConditionLeaf", "nextOperator": null, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "id", "valueName": "ID"}], "constValue": null, "valueType": "MODEL", "fieldType": null}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "id", "valueName": "ID"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "rightValues": null}]}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "UpdateDataAssignNodeKey"}}, {"type": "AssignNode", "key": "UpdateDataAssignNodeKey", "name": "赋值", "props": {"type": "AssignProperties", "desc": "赋值", "assignments": [{"field": {"fieldType": "Model", "id": null, "fieldKey": "result", "fieldName": "result", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "supplier", "valueName": "供应商"}], "relatedNode": {"nodeKey": "UpdateDataNodeKey", "nodeTitle": "创建数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}}]}, {"type": "EndNode", "key": "end0", "name": "结束节点", "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null, "props": {"type": "EndProperties", "outputMapping": [{"id": null, "field": {"fieldType": "Model", "id": null, "fieldKey": "data", "fieldName": "data", "referenceValue": null, "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "supplier", "modelName": "供应商"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_GlobalVariable", "valueName": "全局变量"}, {"valueKey": "result", "valueName": "result"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "保存数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}}]}