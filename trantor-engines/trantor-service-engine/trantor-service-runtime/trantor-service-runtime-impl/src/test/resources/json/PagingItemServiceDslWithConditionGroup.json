{"type": "ServiceDefinition", "key": "shopItem1_SEARCH", "name": "店铺商品列表查询", "props": {"type": "ServiceProperties", "desc": "店铺商品 列表查询", "status": "ENABLE", "transactionPropagation": "REQUIRED", "transactionTimeout": 10000, "appId": 1, "teamId": 1}, "headNodeKeys": ["startNodeKey"], "children": [{"type": "StartNode", "key": "startNodeKey", "name": "开始节点", "props": {"type": "StartProperties", "name": "开始节点", "desc": "开始节点", "input": [{"fieldType": "ConditionGroup", "id": null, "fieldKey": "conditionGroup", "fieldName": "条件组", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "shopItem1", "modelName": "店铺商品"}, "elements": null}, {"fieldType": "SortOrder", "id": null, "fieldKey": "sortOrder", "fieldName": "排序规则", "referenceValue": null, "description": null, "required": true, "defaultValue": null, "relatedModel": {"modelKey": "shopItem1", "modelName": "店铺商品"}, "elements": null}, {"fieldType": "Number", "id": null, "fieldKey": "pageNo", "fieldName": "pageNo", "referenceValue": null, "description": null, "required": true, "defaultValue": 1, "elements": null}, {"fieldType": "Number", "id": null, "fieldKey": "pageSize", "fieldName": "pageSize", "referenceValue": null, "description": null, "required": true, "defaultValue": 20, "elements": null}], "globalVariable": null}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "findDataNodePagingKey"}, {"type": "PagingDataNode", "key": "findDataNodePagingKey", "name": "查询数据分页", "props": {"type": "PagingDataProperties", "name": "查询数据分页", "desc": "查询数据分页", "relatedModel": {"modelKey": "shopItem1", "modelName": "店铺商品"}, "dataType": "MODEL", "conditionGroup": {"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionGroup", "nextOperator": null, "conditions": [{"type": "ConditionVariable", "nextOperator": null, "variable": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "conditionGroup", "valueName": "条件组"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}]}, "sortOrder": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "sortOrder", "valueName": "排序规则"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "isPaging": true, "pageNo": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "pageNo"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "pageSize": {"type": "VarValue", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "pageSize"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR", "fieldType": null}, "relations": null, "selectFields": null, "stopWhenDataEmpty": false}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": "end0"}, {"type": "EndNode", "key": "end0", "name": "结束节点", "props": {"type": "EndProperties", "outputMapping": [{"id": null, "field": {"fieldType": "Object", "id": null, "fieldKey": "data", "fieldName": "data", "referenceValue": null, "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "shopItem1", "modelName": "店铺商品"}, "elements": null}, "value": {"type": "VarValue", "varValue": [{"valueKey": "shopItem1", "valueName": "分页出参"}], "relatedNode": {"nodeKey": "findDataNodePagingKey", "nodeTitle": "查询数据"}, "constValue": null, "valueType": "VAR", "fieldType": null}}]}, "headNodeKeys": null, "children": null, "renderType": null, "nextNodeKey": null}]}