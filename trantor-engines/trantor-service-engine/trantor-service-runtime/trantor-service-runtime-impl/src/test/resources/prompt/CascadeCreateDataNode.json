{
  "key": "", // 节点Key,请随机生成12个字符,和其他节点Key不能重复
  "type": "CascadeCreateDataNode",
  "name": "", // 节点title, 请总结出该节点的主要功能，不超过20个汉字
  "nextNodeKey": "",// 下一个节点的Key
  "props": {
    "type": "CascadeCreateDataProperties",
    "relatedModel": {
      "modelKey": "",
      "modelName": "" // 需要保存的模型名称，需要用用户的输入中解析出来
    },
    "modelValue": null,
    "outputAssign": {
      "outputAssignType": "SYSTEM"
    }
  }
}
