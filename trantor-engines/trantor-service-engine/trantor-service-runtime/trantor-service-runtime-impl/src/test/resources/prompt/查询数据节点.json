{
  "key": "",
  "type": "RetrieveDataNode",
  "name": ${nodeName},
  "props": {
    "type": "RetrieveDataProperties",
    "relatedModel": { // 需要保存的模型信息，请根据节点名称的含义，从用户提供的服务入参'input'中解析出需要保存的模型
      "modelKey": "", // 模型key
      "modelName": "" // 模型名称
    },
    "dataType": "MODEL|ARRAY|PAGING", // 查询类型,查询单条数据：Model，查询多条数据是ARRAY，查询分页数据：PAGING
    "conditionGroup": {
      "type": "ConditionGroup",
      "logicOperator": "AND",
      "conditions": [  // 这里的‘conditions’ 是‘ConditionGroup’的集合
        {
          "type": "ConditionGroup", // 第二层的条件，type必须是"ConditionGroup"
          "logicOperator": "AND", // 逻辑运算符，且：AND, 或 OR
          "conditions": [
            {
              "type": "ConditionLeaf", // 条件类型：“ConditionGroup”是条件组，“ConditionLeaf”是条件项。当条件类型为“ConditionLeaf”时，需要设置“leftValue”、“operator”和“rightValue”
              "leftValue": {
                "type": "VarValue",
                "valueType": "MODEL",
                //以下根据用户需求设置被查询的模型的哪些字段被当成查询条件，例如根据id查询“采购订单”模型，则varValue就设置为采购订单模型的“id”字段信息,如：[{"valueKey": "id","valueName": "ID"}]
                "varValue": [
                  {
                    "valueKey":"", //被当成查询条件的模型字段key
                    "valueName":""//被当成查询条件的模型字段名称
                  }
                ],
                "fieldType":"" // 被当成查询条件的模型字段类型
              },
              "operator": "EQ",
              "rightValue": {
                "type": "VarValue",
                "valueType": "VAR",// leftValue的值类型，VAR：表示为变量
                // 当“valueType”设置为VAR时，下面的值表示一个变量。
                "varValue": [
                  {
                    "valueKey": "REQUEST", // ‘REQUEST’ 是固定值，表明varValue[1]是从服务的‘input’中选择变量
                    "valueName": "服务入参"
                  },
                  {
                    "valueKey": "", // 是服务中定义的入参，需要找到和“leftValue”中的模型字段类型相同的参数
                    "valueName":"" // 参数名
                  },
                  // 如果服务定义中的输入字段具有模型类型‘fieldType=Model’，则需要进一步深入以检索其中的模型字段，如果不是模型类型，则没有下面的path
                  {
                    "valueKey": "",// 模型字段key
                    "valueName":"" // 模型字段名称
                  }
                ]
              }
            }
          ]
        }
      ]
    },
    "stopWhenDataEmpty": false,
    "outputAssign": {
      "outputAssignType": "SYSTEM"
    }
  }
}
