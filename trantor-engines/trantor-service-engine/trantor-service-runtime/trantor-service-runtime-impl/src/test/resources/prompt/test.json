[
  {
    "fieldKey": "GLOBAL",
    "fieldAlias": "GLOBAL",
    "fieldName": "全局变量",
    "fieldType": "Object",
    "elements": [
      // 该字段可能就是需要推断出来的字段，‘fieldType’ 和被赋值变量类型相同，其次是根据字段名称含义大致相同
      {
        "fieldKey": "userId",
        "fieldName": "用户ID",
        "fieldType": "Number"
      }
    ]
  },
  {
    "fieldKey": "OUTPUT",
    "fieldAlias": "OUTPUT",
    "fieldName": "服务出参",
    "fieldType": "Object",
    "elements": [
      {
        "fieldKey": "user",
        "fieldName": "用户信息",
        "fieldType": "Model",
        "elements": [
          // 这个字段 和被赋值的 变量是相同的,因为被赋值的变量也在上下文，在推断的时候，需要排查掉
          {
            "fieldKey": "id",
            "fieldName": "ID",
            "fieldType": "Number"
          }
        ]
      }
    ]
  }
]
