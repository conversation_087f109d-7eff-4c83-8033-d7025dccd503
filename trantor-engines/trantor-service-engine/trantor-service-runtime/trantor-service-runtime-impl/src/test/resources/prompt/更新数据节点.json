{
  "key": "<PERSON><PERSON>'s key, generate randomly",
  "type": "CascadeUpdateDataNode", // 'CascadeUpdateDataNode' is create data node type
  "name": "<PERSON><PERSON>'s name, generate randomly,Usually in Chinese",
  "nextNodeKey": "next node's key",
  "props": {
    "type": "CascadeUpdateDataProperties", // 'CascadeUpdateDataProperties' is create data node's props type
    // The defined related model 'relatedModel' here is the model that the current create data node needs to create, provided by the user
    "relatedModel": {
      "modelKey": "The key of the Model",
      "modelName": "The name of the Model, Usually in Chinese"
    },
    // 'modelValue' is the model data value that needs to be updated
    "modelValue": {
      "type": "VarValue",
      "valueType": "VAR",
      "fieldType": "Model",
      "varValue": [
        {
          "valueKey": "REQUEST",
          "valueName": "服务入参"
        },
        {
          "valueKey": "request", // The key of the input parameter
          "valueName": "request" // The name of the input parameter
        }
      ]
    },
    "outputAssign": null
  }
}
