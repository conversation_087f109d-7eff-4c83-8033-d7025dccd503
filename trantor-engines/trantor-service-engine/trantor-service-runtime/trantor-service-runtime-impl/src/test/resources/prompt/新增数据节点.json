{
  "key": "",
  "type": "CascadeCreateDataNode",
  "name": "",
  "props": {
    "type": "CascadeCreateDataProperties",
    // The defined related model 'relatedModel' here is the model that the current create data node needs to create, provided by the user
    "relatedModel": {
      "modelKey": "", // 模型key
      "modelName": "The name of the Model"
    },
    // 'modelValue' is the model data value that needs to be created
    "modelValue": {
      "type": "VarValue",
      "valueType": "VAR",
      "fieldType": "Model",
      "varValue": [
        {
          "valueKey": "REQUEST",
          "valueName": "服务入参"
        },
        {
          "valueKey": "request",  // The key of the input parameter's field
          "valueName": "request" // The name of the input parameter's field
        }
      ]
    },
    "outputAssign": {
      "outputAssignType": "CUSTOM",
      "customAssignments": [
        {
          "field": {
            "type": "VarValue",
            "valueType": "VAR",
            "fieldType": "Model",
            "varValue": [
              {
                "valueKey": "OUTPUT",
                "valueName": "服务出参"
              },
              {
                "valueKey": "data", // Identification of output parameter fields in service definition，Attention: It also indicates that the service also defines output parameters
                "valueName": "data" // Name of output parameter fields in service definition
              }
            ]
          },
          "operator": "EQ",
          "value": {
            "type": "VarValue",
            "valueType": "MODEL",
            "fieldType": "Model",
            "varValue": [// this is the output parameter structure,it is a fixed format
              {
                "valueKey": "NODE_OUTPUT_%s", // In 'NODE_OUTPUT_%s', '%s' represents the value of the current node's key, Attention: '%s' will be replaced by the actual value of the key for the current node
                "valueName": "出参结构体"
              }
            ]
          }
        }
      ]
    }
  }
}
