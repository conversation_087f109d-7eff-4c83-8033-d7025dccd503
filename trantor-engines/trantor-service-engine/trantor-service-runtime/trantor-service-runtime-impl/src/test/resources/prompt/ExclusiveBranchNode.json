{
  "key": "", // 节点Key,请随机生成12个字符,和其他节点Key不能重复
  "type": "ExclusiveBranchNode",
  "name": "排他分支",
  "nextNodeKey": "", // 下一个节点的Key
  "props": {
    "type": "ExclusiveBranchProperties"
  },
  "headNodeKeys": [ // 这里是‘ExclusiveConditionNode’条件节点 和 ‘ConditionElseNode’条件默认节点 的Key的集合，注意：‘ConditionElseNode’的节点Key放在集合最后
  ],
  "children": [ // 排他分支内部的节点
    // ‘ExclusiveConditionNode’条件节点，该节点用于设置条件, 该条件分支后面可以继续设置其他节点，可以有多个条件节点，表明有多个分支
    {
      "key": "", // 节点Key,请随机生成12个字符,和其他节点Key不能重复
      "type": "ConditionNode",
      "name": "条件",// 节点title, 请总结出该节点的主要功能，不超过20个汉字
      "props": {
        "type": "ConditionProperties",
        "conditionGroup":null
      },
      "nextNodeKey": "" // 下一个节点的Key，表明当前分支的的下一个节点
    },
    // ‘ConditionElseNode’条件默认节点，表明是分支的默认分支，一个排他分支只能有一个默认分支
    {
      "key": "", // 节点Key,请随机生成12个字符,和其他节点Key不能重复
      "type": "ConditionElseNode",
      "name": "else",
      "props": {
        "type": "ConditionElseProperties"
      },
      "nextNodeKey": "" // 下一个节点的Key，表明当前分支的的下一个节点
    },
    // 这里还有其他节点，表明在某个分支下的节点
  ]
}
