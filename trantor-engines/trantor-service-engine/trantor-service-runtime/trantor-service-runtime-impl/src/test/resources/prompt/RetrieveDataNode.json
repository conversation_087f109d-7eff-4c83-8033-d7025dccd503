{
  "key": "", // 节点Key,请随机生成12个字符,和其他节点Key不能重复
  "type": "RetrieveDataNode",
  "name": "",// 节点title, 请总结出该节点的主要功能，不超过20个汉字
  "nextNodeKey": "", // 下一个节点的Key
  "props": {
    "type": "RetrieveDataProperties",
    "relatedModel": {
      "modelKey": "",
      "modelName": "" // 需要查询的模型名称，需要用用户的输入中解析出来
    },
    "dataType": "ARRAY", // 查询的类型，MODEL：表明查询单条数据，ARRAY：表明是查询批量数据，PAGING：表明是分页查询
    "stopWhenDataEmpty": false,
    "outputAssign": {
      "outputAssignType": "SYSTEM"
    }
  }
}
