// 一个完整服务的定义
export interface ServiceDefinition {
  type: 'ServiceDefinition';
  key: string; // 服务Key,(注意：YYYYY指的是根据服务中文名称转换成英文名称，如果本身就是英文，则不需要转换，多个单词之间用下划线分隔)
  name: string;// 服务名称，(注意：XXXXX指的是从用户的意图中提取服务名称，如果没有，则自动生成一个便于理解的服务中文名称)
  props: {
   type: 'ServiceProperties';
  };
  children: Node[]; // 该服务的节点集合;一个服务起码由两个以上节点组成：以开始节点(StartNode)开始，以结束节点(EndNode)结束，中间是一些符合业务逻辑含义的节点。
}

// 服务节点的定义
export interface Node {
  type: string; // 节点类型
  key: string; // 节点Key，请随机生成12个字符，和其他节点Key不能重复
  name: string; // 节点名称，请总结出该节点的主要功能，不超过20个汉字
  props: NodeProperties; // 节点属性
}

// StartNode：开始节点，一个编排服务中必须要有一个开始节点，且只能有一个
export interface StartNode extends Node {
  type:'StartNode';
  props: {
    input: Field[]; // 服务入参
    output: Field[];// 服务出参
  };
}

// CascadeCreateDataNode：保存数据节点
export interface CascadeCreateDataNode extends Node {
  type:'CascadeCreateDataNode';
  props: {
    relatedModel: { // 需要保存的模型
      modelKey: string;
      modelName: string;
    };
  };
}

export interface HasChildrenNode extends Node {
  children: Node[]; // 子节点，不能包含StartNode和EndNode
}

// 分支的条件节点，该节点只能用在排他分支节点(ExclusiveBranchNode)中
export interface BranchConditionNode extends HasChildrenNode {
}

// ConditionNode：分支节点，用于设置排它分支节点的条件；一个排它分支节点中可以用多个分支节点，仅用于排他分支'ExclusiveBranchNode'节点中，在排他分支中是头节点
export interface ConditionNode extends BranchConditionNode {
  type: 'ConditionNode';
  children: Node[]; // 分支下的节点
  props: {
    expression: string; // 条件表达式
  }
}

// ConditionElseNode：else分支节点，一个排它分支节点中有且只有一个else分支节点；仅用于排他分支节点'ExclusiveBranchNode'中，在排他分支中是头节点
export interface ConditionElseNode extends BranchConditionNode {
  type: 'ConditionNode';
  children: Node[]; // 分支下的节点
}

// ErrorNode: 异常节点，用来抛出一个异常错误信息。只会用在排它分支节点里。
export interface ErrorNode extends Node {
  type:'ErrorNode';
  props: {
    errorCode: string; // 错误码，请根据错误生成一个错误吗
    errorMsg: string; // 错误信息，请总结一个错误提示信息
  }
}

// EndNode: 结束节点，一个编排服务中必须要有一个结束节点，且只能有一个。
export interface EndNode extends Node {
  type: 'EndNode';
  props: {
    type: 'EndProperties'
  };
}

// ExclusiveBranchNode：排他分支，用于处理条件分支，处理类似if else的逻辑
export interface ExclusiveBranchNode extends Node {
  type: 'ExclusiveBranchNode';
  children: BranchConditionNode[]; // 排他分支的节点，仅只能包括条件节点和else节点
}

// 字段定义
export interface Field {
  fieldKey: string;
  fieldName: string;
}
