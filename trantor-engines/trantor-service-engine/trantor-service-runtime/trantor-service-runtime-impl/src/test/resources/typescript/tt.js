function validate(node) {

    if(node.children != null && node.children.length > 0) {
       var hasStart = false;
       var hasEnd = false;
       var hasConditionElseNode = false;
       var hasSwitchDefaultNode = false;

       for (var idx in node.children) {
           var child = node.children[idx];

           if (node.type === 'ExclusiveBranchNode') {
              if(child.type !== 'ConditionNode' && child.type !== 'ConditionElseNode') {
                  return true;
              }
              if (child.type === 'ConditionElseNode') {
                  hasConditionElseNode = true;
              }
           } else {
             if (child.type === 'ConditionNode' || child.type === 'ConditionElseNode') {
                 return true;
             }
           }

           if (node.type === 'SwitchNode') {
               if (child.type !== 'SwitchCaseNode' && child.type !== 'SwitchDefaultNode') {
                   return true;
               }
               if (child.type === 'SwitchDefaultNode') {
                   hasSwitchDefaultNode = true;
               }
           } else {
              if (child.type === 'SwitchCaseNode' || child.type === 'SwitchDefaultNode') {
                  return true;
              }
           }

           if (child.type === 'StartNode') {
              hasStart = true;
           }
           if (child.type === 'EndNode') {
              hasEnd = true;
           }
      }

      if (node.type === 'ExclusiveBranchNode' && !hasConditionElseNode) {
          node.children.push({type: 'ConditionElseNode',key: node.key+'_ConditionElseNode_0', name:'其他情况', props: {type: 'ConditionElseProperties'}});
      }

      if (node.type === 'SwitchNode' && !hasSwitchDefaultNode) {
          node.children.push({type: 'SwitchDefaultNode', key: node.key+'_SwitchDefaultNode_0', name:'默认', props: {type: 'SwitchDefaultProperties'}});
      }

      if (node.type === 'ServiceDefinition') {
          if (!hasStart) {
              return true;
          }
          node.children = node.children.filter(function(item) {
              return item.type !== 'EndNode';
          });
          node.children.push({type: 'EndNode', key: 'end0', name:'结束', props: {type: 'EndProperties'}});
      } else {
          if (hasStart || hasEnd) {
              node.children = node.children.filter(function(item) {
                    return item.type !== 'StartNode' && item.type !== 'EndNode';
              });
          }
      }

      for (var idx in node.children) {
        var child = node.children[idx];
        var cFlag = validate(child);
        if (cFlag) {
          return true;
        }
      }

      return false;
    }

    return false;
  }

  try {
    var def = JSON.parse(dsl,'io.terminus.trantor2.service.dsl.ServiceDefinition');
    def.type = 'ServiceDefinition';
    var flag = validate(def);
    if (flag) {
        return null;
    }
    return JSON.stringify(def);
   } catch (error) {
     return null;
   }
