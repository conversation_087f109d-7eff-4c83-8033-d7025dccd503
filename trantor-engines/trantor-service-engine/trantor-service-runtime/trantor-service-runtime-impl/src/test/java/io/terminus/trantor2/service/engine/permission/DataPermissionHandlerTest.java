package io.terminus.trantor2.service.engine.permission;

import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.iam.api.dto.condition.ConditionGroup;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.trantor2.common.TrantorCommonConstant;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.IamConditionConverter;
import io.terminus.trantor2.iam.service.TrantorIAMAdministratorService;
import io.terminus.trantor2.permission.api.common.exception.PermissionHandleException;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.runtime.api.service.DataPermissionLoader;
import io.terminus.trantor2.permission.runtime.api.service.FieldPermissionLoader;
import io.terminus.trantor2.service.common.utils.DataLoader;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.SingleQueryCondition;
import io.terminus.trantor2.service.engine.runtime.permission.DataPermissionHandlerImpl;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * 2024/9/10 15:16
 **/
public class DataPermissionHandlerTest {

    private DataPermissionLoader dataPermissionLoader;
    private PermissionConfigService permissionConfigService;
    private PortalToIamAppConverter portalToIamAppConverter;
    private MockedStatic<SpringContextHelper> springContextHelperMockedStatic;
    private MockedStatic<TrantorContext> trantorContextMockedStatic;
    private DataPermissionHandler dataPermissionHandler;
    private TrantorIAMAdministratorService iamAdministratorService;

    @Before
    public void before() {
        dataPermissionLoader = Mockito.mock(DataPermissionLoader.class);
        permissionConfigService = Mockito.mock(PermissionConfigService.class);
        portalToIamAppConverter = Mockito.mock(PortalToIamAppConverter.class);
        iamAdministratorService = Mockito.mock(TrantorIAMAdministratorService.class);

        springContextHelperMockedStatic = Mockito.mockStatic(SpringContextHelper.class);

        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(FieldPermissionLoader.class)).thenReturn(dataPermissionLoader);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(PortalToIamAppConverter.class)).thenReturn(portalToIamAppConverter);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(PermissionConfigService.class)).thenReturn(permissionConfigService);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(TrantorIAMAdministratorService.class)).thenReturn(iamAdministratorService);

        trantorContextMockedStatic = Mockito.mockStatic(TrantorContext.class);

        dataPermissionHandler = Mockito.spy(
                new DataPermissionHandlerImpl(dataPermissionLoader, permissionConfigService, portalToIamAppConverter, iamAdministratorService)
        );
    }

    @After
    public void after() {
        // 在每个测试方法之后关闭静态模拟
        springContextHelperMockedStatic.close();
        trantorContextMockedStatic.close();
    }

    /**
     * 测试场景：待鉴权数据对象为空
     * 预期结果：数据权限处理方法返回 null 对象
     */
    @Test
    public void testIfDataObjectIsNull() {
        boolean handleResult = dataPermissionHandler.handle(null, RandomUtil.randomString("permissionKey", 10));
        Assert.assertFalse(handleResult);
    }

    /**
     * 测试场景：在{@link TrantorContext}中存在{@link io.terminus.trantor2.permission.api.common.consts.PermissionConstants#DATA_PERMISSION_IGNORE}忽略数据权限标记
     * 预期结果：不执行数据鉴权，数据权限处理方法返回原始数据对象
     */
    @Test
    public void testIfHasDataPermissionIgnoreInTrantorContext() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        Map<String, Object> rpcContext = new HashMap<>();
        rpcContext.put(TrantorCommonConstant.DATA_PERMISSION_IGNORE, Boolean.TRUE);
        trantorContextMockedStatic.when(TrantorContext::getRpcContext).thenReturn(Optional.of(rpcContext));

        Assert.assertTrue(TrantorContext.getRpcContext()
                .map(rc -> rc.containsKey(TrantorCommonConstant.DATA_PERMISSION_IGNORE))
                .orElse(false));

        trantorContextMockedStatic.when(TrantorContext::isDataPermissionIgnored).thenReturn(true);

        boolean handleResult = dataPermissionHandler.handle(Mockito.mock(QueryModel.class), permissionKey);
        Assert.assertFalse(handleResult);
    }

    /**
     * 测试场景：未启用数据权限
     * 预期结果：不执行数据鉴权，数据权限处理方法返回原始数据对象
     */
    @Test
    public void testIfNotNeedExecDataPermission() {
        {
            // 模拟数据权限开关为关闭的场景
            Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.FALSE);
            // 模拟管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.TRUE);

            String permissionKey = RandomUtil.randomString("permissionKey", 10);

            boolean handleResult = dataPermissionHandler.handle(Mockito.mock(QueryModel.class), permissionKey);
            Assert.assertFalse(handleResult);
        }
        {
            // 模拟数据权限开关为关闭的场景
            Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.FALSE);
            // 模拟非管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

            String permissionKey = RandomUtil.randomString("permissionKey", 10);

            boolean handleResult = dataPermissionHandler.handle(Mockito.mock(QueryModel.class), permissionKey);
            Assert.assertFalse(handleResult);
        }
        {
            // 模拟数据权限开关为开启的场景
            Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
            // 模拟管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.TRUE);

            String permissionKey = RandomUtil.randomString("permissionKey", 10);

            boolean handleResult = dataPermissionHandler.handle(Mockito.mock(QueryModel.class), permissionKey);
            Assert.assertFalse(handleResult);
        }
    }

    /**
     * 测试场景：从{@link TrantorContext}读取信息失败
     * 预期结果：抛出读取{@link TrantorContext}属性失败时的异常
     */
    @Test
    public void testIfFailedToAccessTrantorContext() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        String permissionKey = RandomUtil.randomString("permissionKey", 10);

        PermissionHandleException exception = Assert.assertThrows(PermissionHandleException.class, () -> {
            dataPermissionHandler.handle(Mockito.mock(QueryModel.class), permissionKey);
        });
        Assert.assertTrue(Arrays.asList(
                "Current portal not found",
                "Current user not found"
        ).contains(exception.getCause().getMessage()));
    }

    /**
     * 测试场景：permissionKey 为空，且数据权限策略是宽松策略
     * 预期结果：不执行数据鉴权，{@link QueryModel#condition}不会被修改
     */
    @Test
    public void testIfPermissionKeyIsNullAndPolicyEnforcementModeIsPERMISSIVE() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟数据权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.PERMISSIVE);

        QueryModel queryModel = Mockito.mock(QueryModel.class);
        String permissionKey = null;
        Assert.assertNull(queryModel.getCondition());
        boolean handleResult = dataPermissionHandler.handle(queryModel, permissionKey);
        Assert.assertFalse(handleResult);
        Assert.assertNull(queryModel.getCondition());
    }

    /**
     * 测试场景：permissionKey 为空，且数据权限策略是严格策略
     * 预期结果：执行数据鉴权，{@link QueryModel#condition}属性会被篡改成 id EQ null 的条件组
     */
    @Test
    public void testIfPermissionKeyIsNullAndPolicyEnforcementModeIsENFORCING() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = null;
        boolean handleResult = dataPermissionHandler.handle(queryModel, permissionKey);
        Assert.assertTrue(handleResult);
        Assert.assertNotNull(queryModel.getCondition());
        SingleQueryCondition queryCondition = (SingleQueryCondition) queryModel.getCondition().getConditions().get(0);
        Assert.assertEquals(queryCondition.getField(), "id");
        Assert.assertEquals(queryCondition.getType(), Operator.EQ);
        Assert.assertNull(queryCondition.getValue());
        Assert.assertNull(queryCondition.getNextOperator());
    }

    /**
     * 测试场景：{@link DataPermissionLoader} 返回数据规则为空，且数据权限策略是严格策略
     * 预期结果：执行数据鉴权，{@link QueryModel#condition}属性会被篡改成 id EQ null 的条件组
     */
    @Test
    public void testIfConditionIsEmptyAndPolicyEnforcementModeIsENFORCING() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        // mock 返回数据规则为null
        Mockito.when(dataPermissionLoader.getUserDataPermissionConditionGroup(Mockito.anyLong(), Mockito.anyLong(), Mockito.eq(permissionKey))).thenReturn(null);

        boolean handleResult = dataPermissionHandler.handle(queryModel, permissionKey);
        Assert.assertTrue(handleResult);
        Assert.assertNotNull(queryModel.getCondition());
        SingleQueryCondition queryCondition = (SingleQueryCondition) queryModel.getCondition().getConditions().get(0);
        Assert.assertEquals(queryCondition.getField(), "id");
        Assert.assertEquals(queryCondition.getType(), Operator.EQ);
        Assert.assertNull(queryCondition.getValue());
        Assert.assertNull(queryCondition.getNextOperator());
    }

    /**
     * 测试场景：{@link DataPermissionLoader} 返回数据规则为空，且数据权限策略是宽松策略
     * 预期结果：不执行数据鉴权，{@link QueryModel#condition}不会被修改
     */
    @Test
    public void testIfConditionIsEmptyAndPolicyEnforcementModeIsPERMISSIVE() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.PERMISSIVE);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        // mock 返回数据规则为null
        Mockito.when(dataPermissionLoader.getUserDataPermissionConditionGroup(Mockito.anyLong(), Mockito.anyLong(), Mockito.eq(permissionKey))).thenReturn(null);

        boolean handleResult = dataPermissionHandler.handle(queryModel, permissionKey);
        Assert.assertFalse(handleResult);
        Assert.assertNull(queryModel.getCondition());
    }

    /**
     * 测试场景：测试使用非法condition规则的场景，condition右值带有变量值
     * 预期结果：抛出{@code IllegalStateException}异常，并且异常信息符合预期
     */
    @Test
    public void testIfInvalidConditionWithVariable() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        // 从文件读取mock condition数据
        ConditionGroup conditionGroup = DataLoader.loadByTypeReference("json/data-permission/invalid_condition_with_variable.json", new TypeReference<ConditionGroup>() {
        });

        Mockito.when(dataPermissionLoader.getUserDataPermissionConditionGroup(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(conditionGroup);

        PermissionHandleException exception = Assert.assertThrows(PermissionHandleException.class, () -> {
            dataPermissionHandler.handle(queryModel, permissionKey);
        });
        Assert.assertEquals("This should never happen, illegal ConditionValueType: VARIABLE", exception.getCause().getMessage());
    }

    /**
     * 测试场景：测试使用非法condition规则的场景，condition右值带有数据控权维度值
     * 预期结果：抛出{@code IllegalStateException}异常，并且异常信息符合预期
     */
    @Test
    public void testIfInvalidConditionWithValueRange() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        // 从文件读取mock condition数据
        ConditionGroup conditionGroup = DataLoader.loadByTypeReference("json/data-permission/invalid_condition_with_valueRange.json", new TypeReference<ConditionGroup>() {
        });

        Mockito.when(dataPermissionLoader.getUserDataPermissionConditionGroup(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(conditionGroup);

        PermissionHandleException exception = Assert.assertThrows(PermissionHandleException.class, () -> {
            dataPermissionHandler.handle(queryModel, permissionKey);
        });
        Assert.assertEquals("This should never happen, illegal ConditionValueType: VALUE_RANGE", exception.getCause().getMessage());
    }

    /**
     * 测试场景：测试使用合法condition规则的场景
     * 预期结果：
     */
    @Test
    public void testIfValidCondition() {
        // 模拟数据权限开关为开启的场景
        Mockito.when(permissionConfigService.isDataPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getDataPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        QueryModel queryModel = new QueryModel(RandomUtil.randomLong(), RandomUtil.randomString(10));
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        // 从文件读取mock condition数据
        ConditionGroup conditionGroup = DataLoader.loadByTypeReference("json/data-permission/valid_condition.json", new TypeReference<ConditionGroup>() {
        });

        String string = IamConditionConverter.toSqlString(conditionGroup);
        System.out.println(string);

        Mockito.when(dataPermissionLoader.getUserDataPermissionConditionGroup(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(conditionGroup);

        boolean handleResult = dataPermissionHandler.handle(queryModel, permissionKey);
        Assert.assertTrue(handleResult);
        Assert.assertNotNull(queryModel.getCondition());
        Assert.assertEquals(conditionGroup.getConditions().size(), queryModel.getCondition().getConditions().size());
    }

    private void initTrantorContext() {
        trantorContextMockedStatic.when(TrantorContext::getContext).thenReturn(new TrantorContext.Context());
        // 模拟获取当前用户信息
        User user = new User();
        user.setId(RandomUtil.randomLong());
        trantorContextMockedStatic.when(TrantorContext::safeGetCurrentUser).thenReturn(Optional.of(user));
        Portal portal = new Portal();
        portal.setCode(RandomUtil.randomString("portalCode", 10));
        trantorContextMockedStatic.when(TrantorContext::getCurrentPortalOptional).thenReturn(Optional.of(portal));
    }
}
