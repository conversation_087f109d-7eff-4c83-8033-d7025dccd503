package io.terminus.trantor2.service.engine.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.iam.api.enums.permission.FieldAccessType;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.UnauthorizedOperationException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.iam.service.TrantorIAMAdministratorService;
import io.terminus.trantor2.permission.api.common.exception.PermissionHandleException;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.runtime.api.service.FieldPermissionLoader;
import io.terminus.trantor2.service.common.utils.DataLoader;
import io.terminus.trantor2.service.engine.runtime.permission.FieldPermissionHandlerImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * 2024/7/15 13:39
 **/
public class FieldPermissionHandlerTest {

    private FieldPermissionLoader fieldPermissionLoader;
    private PortalToIamAppConverter portalToIamAppConverter;
    private PermissionConfigService permissionConfigService;
    private MockedStatic<SpringContextHelper> springContextHelperMockedStatic;
    private MockedStatic<TrantorContext> trantorContextMockedStatic;
    private FieldPermissionHandler fieldPermissionHandler;
    private TrantorIAMAdministratorService iamAdministratorService;

    @Before
    public void before() {
        // 在每个测试方法之前创建一个新的静态模拟
        fieldPermissionLoader = Mockito.mock(FieldPermissionLoader.class);
        portalToIamAppConverter = Mockito.mock(PortalToIamAppConverter.class);
        permissionConfigService = Mockito.mock(PermissionConfigService.class);
        iamAdministratorService = Mockito.mock(TrantorIAMAdministratorService.class);

        springContextHelperMockedStatic = Mockito.mockStatic(SpringContextHelper.class);

        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(FieldPermissionLoader.class)).thenReturn(fieldPermissionLoader);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(PortalToIamAppConverter.class)).thenReturn(portalToIamAppConverter);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(PermissionConfigService.class)).thenReturn(permissionConfigService);
        springContextHelperMockedStatic.when(() -> SpringContextHelper.getBean(TrantorIAMAdministratorService.class)).thenReturn(iamAdministratorService);

        trantorContextMockedStatic = Mockito.mockStatic(TrantorContext.class);

        fieldPermissionHandler = Mockito.spy(new FieldPermissionHandlerImpl(fieldPermissionLoader, permissionConfigService, portalToIamAppConverter, iamAdministratorService));
    }

    @After
    public void after() {
        // 在每个测试方法之后关闭静态模拟
        springContextHelperMockedStatic.close();
        trantorContextMockedStatic.close();
    }

    /**
     * 测试场景：待鉴权数据对象为空
     * 预期结果：字段权限处理方法返回 null 对象
     */
    @Test
    public void testIfDataObjectIsNull() {
        Object handleResult = fieldPermissionHandler.handle(null, RandomUtil.randomString("permissionKey", 10), ProtectStrategy.READABLE_PROTECTION);
        Assert.assertNull(handleResult);
    }

    /**
     * 测试场景：未启用字段权限
     * 预期结果：不执行字段鉴权，字段权限处理方法返回原始数据对象
     */
    @Test
    public void testIfNotNeedExecFieldPermission() {
        {
            // 模拟字段权限开关为关闭的场景
            Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.FALSE);
            // 模拟管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.TRUE);

            Object dataObject = new Object();
            String permissionKey = RandomUtil.randomString("permissionKey", 10);
            ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

            Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
            Assert.assertEquals(dataObject, handleResult);
        }
        {
            // 模拟字段权限开关为关闭的场景
            Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.FALSE);
            // 模拟非管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

            Object dataObject = new Object();
            String permissionKey = RandomUtil.randomString("permissionKey", 10);
            ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

            Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
            Assert.assertEquals(dataObject, handleResult);
        }
        {
            // 模拟字段权限开关为开启的场景
            Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
            // 模拟管理员用户的场景
            Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.TRUE);

            Object dataObject = new Object();
            String permissionKey = RandomUtil.randomString("permissionKey", 10);
            ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

            Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
            Assert.assertEquals(dataObject, handleResult);
        }
    }

    /**
     * 测试场景：从{@link TrantorContext}读取信息失败
     * 预期结果：抛出读取{@link TrantorContext}属性失败时的异常
     */
    @Test
    public void testIfFailedToAccessTrantorContext() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        Object dataObject = new Object();
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        PermissionHandleException exception = Assert.assertThrows(PermissionHandleException.class, () -> {
            fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        });
        Assert.assertTrue(Arrays.asList(
                "Current portal not found",
                "Current user not found"
        ).contains(exception.getCause().getMessage()));
    }

    /**
     * 测试场景：permissionKey 为空，且字段权限策略时宽松策略
     * 预期结果：不执行字段鉴权，字段权限处理方法返回原始数据对象
     */
    @Test
    public void testIfPermissionKeyIsNullAndPolicyEnforcementModeIsPERMISSIVE() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.PERMISSIVE);

        Object dataObject = new Object();
        String permissionKey = null;
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        Assert.assertEquals(dataObject, handleResult);
    }

    /**
     * 测试场景：permissionKey 为空，且字段权限策略是严格策略，且字段执行模式为可读保护
     * 预期结果：字段权限处理方法返回 null 对象
     */
    @Test
    public void testIfPermissionKeyIsNullAndPolicyEnforcementModeIsENFORCINGAndProtectStrategyIsREADABLE_PROTECTION() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        Object dataObject = new Object();
        String permissionKey = null;
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        Assert.assertNull(handleResult);
    }

    /**
     * 测试场景：permissionKey 为空，且字段权限策略是严格策略，且字段执行模式为可写保护
     * 预期结果：当请求参数中包含不可见或仅读权限字段时，抛出{@code ErrorType = ErrorType.NO_WRITE_PERMISSION_FOR_ALL_REQUEST_PARAMS
     * }的{@link UnauthorizedOperationException}越权操作异常
     */
    @Test
    public void testIfPermissionKeyIsNullAndPolicyEnforcementModeIsENFORCINGAndProtectStrategyIsWRITABLE_PROTECTION() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        Object dataObject = new Object();
        String permissionKey = null;
        ProtectStrategy protectStrategy = ProtectStrategy.WRITABLE_PROTECTION;

        UnauthorizedOperationException exception = Assert.assertThrows(UnauthorizedOperationException.class, () -> {
            fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        });
        Assert.assertEquals(ErrorType.NO_WRITE_PERMISSION_FOR_ALL_REQUEST_PARAMS, exception.getErrorType());
    }

    /**
     * 测试场景：字段权限规则列表为空，且字段权限策略是宽松策略
     * 预期结果：不执行字段鉴权，字段权限处理方法返回原始数据对象
     */
    @Test
    public void testIfFieldRulesIsEmptyAndPolicyEnforcementModeIsPERMISSIVE() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.PERMISSIVE);

        // 模拟数据规则查询为空
        Mockito.when(fieldPermissionLoader.getUserFieldPermissionRules(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(Collections.emptySet());

        Object dataObject = new Object();
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        Assert.assertEquals(dataObject, handleResult);
    }

    /**
     * 测试场景：字段权限规则列表为空，且字段权限策略是严格策略，且字段执行模式为可读保护
     * 预期结果：字段权限处理方法返回 null 对象
     */
    @Test
    public void testIfFieldRulesIsEmptyAndPolicyEnforcementModeIsENFORCINGAndProtectStrategyIsREADABLE_PROTECTION() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        // 模拟数据规则查询为空
        Mockito.when(fieldPermissionLoader.getUserFieldPermissionRules(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(Collections.emptySet());

        Object dataObject = new Object();
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        Assert.assertNull(handleResult);
    }

    /**
     * 测试场景：字段权限规则列表为空，且字段权限策略是严格策略，且字段执行模式为可写保护
     * 预期结果：当请求参数中包含不可见或仅读权限字段时，抛出{@code ErrorType = ErrorType.NO_WRITE_PERMISSION_FOR_ALL_REQUEST_PARAMS
     * }的{@link UnauthorizedOperationException}越权操作异常
     */
    @Test
    public void testIfFieldRulesIsEmptyAndPolicyEnforcementModeIsENFORCINGAndProtectStrategyIsWRITABLE_PROTECTION() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        // 模拟数据规则查询为空
        Mockito.when(fieldPermissionLoader.getUserFieldPermissionRules(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(Collections.emptySet());

        Object dataObject = new Object();
        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        ProtectStrategy protectStrategy = ProtectStrategy.WRITABLE_PROTECTION;

        UnauthorizedOperationException exception = Assert.assertThrows(UnauthorizedOperationException.class, () -> {
            fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);
        });
        Assert.assertEquals(ErrorType.NO_WRITE_PERMISSION_FOR_ALL_REQUEST_PARAMS, exception.getErrorType());
    }

    /**
     * 测试场景：字段权限规则列表不为空，且字段权限策略是严格策略，且字段执行模式为可读保护
     * 预期结果：处理后返回的数据参数列表过滤掉不可见权限字段
     */
    @Test
    public void testIfFieldRulesIsNotEmptyAndPolicyEnforcementModeIsENFORCINGAndProtectStrategyIsREADABLE_PROTECTION() {
        // 模拟字段权限开关为开启的场景
        Mockito.when(permissionConfigService.isFieldPermissionEnabled(Mockito.anyLong())).thenReturn(Boolean.TRUE);
        // 模拟非管理员用户的场景
        Mockito.when(iamAdministratorService.verifyUserIsAdministrator(Mockito.anyLong(), Mockito.anyLong())).thenReturn(Boolean.FALSE);

        initTrantorContext();

        long iamAppId = RandomUtil.randomLong();
        Mockito.when(portalToIamAppConverter.getIamAppIdFromContext()).thenReturn(iamAppId);
        Mockito.when(portalToIamAppConverter.getIamAppIdByPortalCode(Mockito.anyString())).thenReturn(iamAppId);
        // 模拟字段权限宽松模式
        Mockito.when(permissionConfigService.getFieldPermissionPolicyEnforcementMode(Mockito.anyLong())).thenReturn(PolicyEnforcementMode.ENFORCING);

        // 模拟数据规则
        Collection<FieldRule> fieldRules = DataLoader.loadByTypeReference("json/field-permission/service_response_field_rules.json", new TypeReference<Collection<FieldRule>>() {
        });
        Mockito.when(fieldPermissionLoader.getUserFieldPermissionRules(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(fieldRules);

        // 模拟数据对象
        Object dataObject = DataLoader.loadObj("json/field-permission/service_response_data.json", Object.class);
        ProtectStrategy protectStrategy = ProtectStrategy.READABLE_PROTECTION;

        String permissionKey = RandomUtil.randomString("permissionKey", 10);
        Object handleResult = fieldPermissionHandler.handle(dataObject, permissionKey, protectStrategy);

        Assert.assertNotNull(handleResult);
        {
            // 验证裁剪前数据对象是否存在不可见字段
            JsonNode dataObjectJsonNode = JsonUtil.INDENT.getObjectMapper().valueToTree(dataObject);
            Collection<String> fieldPathsBeforeProtect = extractAllFieldPath(dataObjectJsonNode, "");
            Collection<String> invisibleFieldPaths = extractAllInvisibleFieldPath(fieldRules, "");
            Assert.assertTrue(CollUtil.isNotEmpty(CollUtil.intersection(fieldPathsBeforeProtect, invisibleFieldPaths)));
        }
        {
            // 验证不可见字段是否已经全部裁剪
            JsonNode jsonNode = JsonUtil.INDENT.getObjectMapper().valueToTree(handleResult);
            Collection<String> fieldPathsAfterProtect = extractAllFieldPath(jsonNode, "");
            Collection<String> invisibleFieldPaths = extractAllInvisibleFieldPath(fieldRules, "");
            Assert.assertTrue(CollUtil.isEmpty(CollUtil.intersection(fieldPathsAfterProtect, invisibleFieldPaths)));
        }
        {
            // 验证数据规则是否成功添加到上下文
            Assert.assertNotNull(TrantorContext.getContext().getFieldPermission());
            Assert.assertEquals(fieldRules, TrantorContext.getContext().getFieldPermission());
        }
    }

    /**
     * 测试场景：请求参数执行可写权限保护
     * 预期结果：当请求参数中包含不可见或仅读权限字段时，抛出{@code ErrorType = ErrorType.NO_WRITE_PERMISSION_FOR_SPECIFIED_REQUEST_PARAM
     * }的{@link UnauthorizedOperationException}越权操作异常
     */
    @Test
    public void testWritableProtectionForRequestField() {
        Object requestData = DataLoader.loadObj("json/field-permission/service_request_data.json", Object.class);
        Collection<FieldRule> requestFieldRules = DataLoader.loadByTypeReference("json/field-permission/service_request_field_rules.json", new TypeReference<Collection<FieldRule>>() {
        });
        testWritableProtectionForRequestField(requestData, requestFieldRules);
    }

    /**
     * 测试场景：响应参数执行可读权限保护
     * 预期结果：响应数据参数列表过滤掉不可见权限字段
     */
    @Test
    public void testReadableProtectionForResponseField() {
        Object responseData = DataLoader.loadObj("json/field-permission/service_response_data.json", Object.class);
        Collection<FieldRule> responseFieldRules = DataLoader.loadByTypeReference("json/field-permission/service_response_field_rules.json", new TypeReference<Collection<FieldRule>>() {
        });
        testReadableProtectionForResponseField(responseData, responseFieldRules);
    }

    private void testWritableProtectionForRequestField(Object dataObject, Collection<FieldRule> fieldRules) {
        JsonNode jsonNode = dataObject instanceof JsonNode ? (JsonNode) dataObject : JsonUtil.INDENT.getObjectMapper().valueToTree(dataObject);
        UnauthorizedOperationException exception = Assert.assertThrows(UnauthorizedOperationException.class, () -> ProtectStrategy.WRITABLE_PROTECTION.process(jsonNode, fieldRules));
        Assert.assertEquals(ErrorType.NO_WRITE_PERMISSION_FOR_SPECIFIED_REQUEST_PARAM, exception.getErrorType());
    }

    private void testReadableProtectionForResponseField(Object dataObject, Collection<FieldRule> fieldRules) {
        JsonNode jsonNode = dataObject instanceof JsonNode ? (JsonNode) dataObject : JsonUtil.INDENT.getObjectMapper().valueToTree(dataObject);
        ProtectStrategy.READABLE_PROTECTION.process(jsonNode, fieldRules);
        Collection<String> fieldPathsAfterProtect = extractAllFieldPath(jsonNode, "");
        Collection<String> invisibleFieldPaths = extractAllInvisibleFieldPath(fieldRules, "");
        Assert.assertTrue(CollUtil.isEmpty(CollUtil.intersection(fieldPathsAfterProtect, invisibleFieldPaths)));
    }

    private Collection<String> extractAllFieldPath(JsonNode jsonNode, String pathPrefix) {
        Set<String> fieldPaths = new HashSet<>();
        if (jsonNode.isObject()) {
            fieldPaths.addAll(extractAllFieldPath((ObjectNode) jsonNode, pathPrefix));
        } else if (jsonNode.isArray()) {
            fieldPaths.addAll(extractAllFieldPath((ArrayNode) jsonNode, pathPrefix));
        }
        return fieldPaths;
    }

    private Collection<String> extractAllFieldPath(ObjectNode objectNode, String pathPrefix) {
        Set<String> fieldPaths = new HashSet<>();
        Iterator<Map.Entry<String, JsonNode>> iterator = objectNode.fields();
        while (iterator.hasNext()) {
            Map.Entry<String, JsonNode> next = iterator.next();
            String fieldPath = pathPrefix + next.getKey();
            fieldPaths.add(fieldPath);
            fieldPaths.addAll(extractAllFieldPath(next.getValue(), fieldPath + "."));
        }
        return fieldPaths;
    }

    private Collection<String> extractAllFieldPath(ArrayNode arrayNode, String pathPrefix) {
        Set<String> fieldPaths = new HashSet<>();
        for (JsonNode jsonNode : arrayNode) {
            fieldPaths.addAll(extractAllFieldPath(jsonNode, pathPrefix));
        }
        return fieldPaths;
    }

    private Collection<String> extractAllInvisibleFieldPath(Collection<FieldRule> fieldRules, String pathPrefix) {
        Set<String> invisibleFieldPaths = new HashSet<>();
        for (FieldRule fieldRule : fieldRules) {
            String fieldPath = pathPrefix + fieldRule.getEntityParamKey();
            if (FieldAccessType.INVISIBLE.equals(fieldRule.getAccessType())) {
                invisibleFieldPaths.add(fieldPath);
            }
            if (CollectionUtils.isNotEmpty(fieldRule.getChildren())) {
                invisibleFieldPaths.addAll(extractAllInvisibleFieldPath(fieldRule.getChildren(), fieldPath + "."));
            }
        }
        return invisibleFieldPaths;
    }

    private void initTrantorContext() {
        trantorContextMockedStatic.when(TrantorContext::getContext).thenReturn(new TrantorContext.Context());
        // 模拟获取当前用户信息
        User user = new User();
        user.setId(RandomUtil.randomLong(10));
        trantorContextMockedStatic.when(TrantorContext::safeGetCurrentUser).thenReturn(Optional.of(user));
        Portal portal = new Portal();
        portal.setCode(RandomUtil.randomString("portalCode", 10));
        trantorContextMockedStatic.when(TrantorContext::getCurrentPortalOptional).thenReturn(Optional.of(portal));
    }
}
