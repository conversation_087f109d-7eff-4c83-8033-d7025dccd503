package io.terminus.trantor2.service.engine.api.impl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.properties.ServiceProperties;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.web.support.ServiceExecuteRequestResolver;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.testcontainers.shaded.com.google.common.collect.Lists;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * ServiceExecuteApiImplTest
 *
 * <AUTHOR> Created on 2024/9/30 15:37
 */
@ExtendWith(SpringExtension.class)
class ServiceExecuteApiImplTest {

    @Mock
    private ServiceExecutor serviceExecutor;

    private MockMvc mockMvc;

    @BeforeEach
    public void setUp() {

        ServiceProperties serviceProperties = new ServiceProperties();
        serviceProperties.setAllowedUploadFiles(Lists.newArrayList("txt"));

        ServiceExecuteApiImpl serviceExecuteApiImpl = new ServiceExecuteApiImpl(
                serviceExecutor,
                null,
                null,
                serviceProperties);
        mockMvc = MockMvcBuilders.standaloneSetup(serviceExecuteApiImpl)
                .setCustomArgumentResolvers(new ServiceExecuteRequestResolver())
                .build();
    }

    @Test
    public void testUploadFile() throws Exception {
        // 创建模拟文件
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file",  // 表单字段名
                "test.txt",
                "text/plain",
                "This is a test file.".getBytes()
        );

        ServiceExecuteRequest executeRequest = new ServiceExecuteRequest();
        executeRequest.setTeamId(1L);
        executeRequest.setParams(MapUtil.of("a", "b"));
        executeRequest.setServiceKey("executeRequest");

        String requestJson = JsonUtil.toJson(executeRequest);

        mockMvc.perform(multipart("/api/trantor/service/engine/execute/file/TestUploadService")
                        .file(multipartFile)
                        .param("request", requestJson)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());

//        // 发送文件上传请求
//        mockMvc.perform(multipart("/api/trantor/service/engine/execute/file/TestUploadService")
//                .file(multipartFile)
//                .contentType(MediaType.MULTIPART_FORM_DATA)
//                .content(requestJson)
//                .contentType(MediaType.APPLICATION_JSON)
//        ).andExpect(status().isOk());
    }

//    @TestConfiguration
//    public static class TestConfig implements WebMvcConfigurer {
//
//        @Override
//        public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
//            argumentResolvers.add(new ServiceExecuteRequestResolver());
//        }
//    }
}
