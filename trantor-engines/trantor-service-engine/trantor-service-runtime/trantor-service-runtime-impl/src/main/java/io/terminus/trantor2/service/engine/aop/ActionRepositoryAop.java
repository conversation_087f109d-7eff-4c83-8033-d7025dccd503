package io.terminus.trantor2.service.engine.aop;

import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * ActionRepositoryAop
 *
 * <AUTHOR> Created on 2024/6/4 16:15
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class ActionRepositoryAop extends AbstractAuditingLogHandler {

    @Pointcut("execution(* io..*Repo.insert*(..)) || execution(* com..*Repo.insert*(..))")
    public void insertMethod() {
    }

    @Pointcut("execution(* io..*Repo.update*(..)) || execution(* com..*Repo.update*(..))")
    public void updateMethod() {

    }

    @Pointcut("execution(* io..*Repo.delete*(..)) || execution(* com..*Repo.delete*(..))")
    public void deleteMethod() {

    }

    @After("insertMethod()")
    public void insertAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, getCustomEventKeyOrDefault(CREATE_DATA_EVENT));
    }

    @After("updateMethod()")
    public void updateAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, getCustomEventKeyOrDefault(UPDATE_DATA_EVENT));
    }

    @After("deleteMethod()")
    public void deleteAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, getCustomEventKeyOrDefault(DELETE_DATA_EVENT));
    }

    private void oplogProcess(JoinPoint joinPoint, String eventKey) {
        if (Contexts.getGlobalInvocationContext() == null) {
            log.warn("[{}]Repository executed not in service engine, skip oplog process", Contexts.getTraceId());
            return;
        }
        if (!Contexts.getGlobalInvocationContext().getServiceEngine().getServiceProperties().isAuditingLogEnabled()) {
            log.warn("[{}]Repository executed auditingLog disabled, skip oplog process", Contexts.getTraceId());
            return;
        }
        try {
            Object[] args = joinPoint.getArgs();
            if (ArrayUtils.isEmpty(args)) {
                log.warn("[{}]Repository executed parameter is null, skip oplog process", Contexts.getTraceId());
                return;
            }
            Object param = args[0];
            if (param == null) {
                log.warn("[{}]Repository executed parameter is null, skip oplog process", Contexts.getTraceId());
                return;
            }

            Class<?> targetClass = joinPoint.getTarget().getClass();
            String tableName = getTableNameFromModelClass(targetClass);
            if (StringUtils.isBlank(tableName)) {
                log.warn("[{}]Repository executed tableName is null, skip oplog process", Contexts.getTraceId());
                return;
            }

            oplogProcess(tableName, param, eventKey);
        } catch (Exception e) {
            log.error("[{}]Repository executed, oplog process error", Contexts.getTraceId(), e);
        }
    }

    private String getTableNameFromModelClass(Class<?> targetClass) {
        Type[] genericInterfaces = targetClass.getGenericInterfaces();
        if (genericInterfaces.length == 0) {
            return null;
        }
        Class<?> genericInterface = (Class<?>) genericInterfaces[0];
        Type[] superInterfaces = genericInterface.getGenericInterfaces();
        if (superInterfaces.length == 0) {
            return null;
        }
        ParameterizedType superInterface = (ParameterizedType) superInterfaces[0];
        Type[] actualTypeArguments = superInterface.getActualTypeArguments();
        if (ArrayUtils.isEmpty(actualTypeArguments)) {
            return null;
        }
        Class<?> actualTypeArgumentClass = (Class<?>) actualTypeArguments[0];
        TableName tableNameAnnotation = actualTypeArgumentClass.getAnnotation(TableName.class);
        if (tableNameAnnotation == null) {
            return null;
        }
        return tableNameAnnotation.value();
    }
}
