package io.terminus.trantor2.service.engine.runtime.permission;

import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.UnauthorizedOperationException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.iam.service.TrantorIAMAdministratorService;
import io.terminus.trantor2.permission.api.common.exception.PermissionHandleException;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.runtime.api.service.FieldPermissionLoader;
import io.terminus.trantor2.service.engine.permission.FieldPermissionHandler;
import io.terminus.trantor2.service.engine.permission.ProtectStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Objects;

/**
 * 字段权限处理器
 *
 * <AUTHOR>
 * 2024/7/11 09:32
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class FieldPermissionHandlerImpl implements FieldPermissionHandler {

    private final FieldPermissionLoader fieldPermissionLoader;
    private final PermissionConfigService permissionConfigService;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final TrantorIAMAdministratorService iamAdministratorService;

    public Object handle(Object dataObject, String permissionKey, @NotNull ProtectStrategy protectStrategy) {
        try {
            if (Objects.isNull(dataObject)) {
                return null;
            }
            if (TrantorContext.isFieldPermissionIgnored()) { // 上下文存在字段权限忽略标记，不执行字段鉴权
                return dataObject;
            }
            Long iamAppId = portalToIamAppConverter.getIamAppIdFromContext();
            if (!permissionConfigService.isFieldPermissionEnabled(iamAppId)) {  // 门户级字段权限开关如果关闭，不需要执行字段鉴权
                return dataObject;
            }
            if (StringUtils.isBlank(permissionKey)) {   // 如果当前服务节点未配置字段权限规则
                return doHandleIfPermissionKeyIsInvalid(dataObject, protectStrategy);
            }
            boolean isAdmin = iamAdministratorService.verifyUserIsAdministrator(TrantorContext.getCurrentUserId(), iamAppId);
            if (isAdmin) {  // 当前用户如果是当前门户的管理员，不需要执行字段鉴权
                return dataObject;
            }
            return doHandle(dataObject, permissionKey, protectStrategy);
        } catch (UnauthorizedOperationException e) {
            throw e;
        } catch (Exception e) {
            throw new PermissionHandleException(ErrorType.FIELD_PERMISSION_HANDLE_ERROR, e);
        }
    }

    /**
     * 当前服务未配置字段权限规则时的处理逻辑
     */
    private Object doHandleIfPermissionKeyIsInvalid(@NotNull Object dataObject, @NotNull ProtectStrategy protectStrategy) {
        // 查询IAM权限策略实施模式
        PolicyEnforcementMode enforcementMode = permissionConfigService.getFieldPermissionPolicyEnforcementMode(portalToIamAppConverter.getIamAppIdFromContext());
        return handleIfNoQualifiedFieldRule(dataObject, protectStrategy, enforcementMode);
    }

    /**
     * 当前服务配置了有效的字段权限规则时的处理逻辑
     */
    private Object doHandle(@NotNull Object dataObject, @NotBlank String permissionKey, @NotNull ProtectStrategy protectStrategy) {

        Long iamAppId = portalToIamAppConverter.getIamAppIdFromContext();

        User user = TrantorContext.safeGetCurrentUser().orElseThrow(() -> new TrantorRuntimeException("Current user not found"));
        // 查询用户已授权字段权限条件规则
        Collection<FieldRule> fieldRules = fieldPermissionLoader.getUserFieldPermissionRules(user.getId(), iamAppId, permissionKey);
        if (CollectionUtils.isEmpty(fieldRules)) {
            // 查询IAM权限策略实施模式
            PolicyEnforcementMode enforcementMode = permissionConfigService.getFieldPermissionPolicyEnforcementMode(iamAppId);
            return handleIfNoQualifiedFieldRule(dataObject, protectStrategy, enforcementMode);
        }

        JsonNode jsonNode = dataObject instanceof JsonNode
                ? (JsonNode) dataObject
                : JsonUtil.INDENT.getObjectMapper().valueToTree(dataObject);
        // 执行字段鉴权
        protectStrategy.process(jsonNode, fieldRules);

        // 将字段权限规则放入上下文，方便后面塞入Response中
        addFieldPermissionToContext(fieldRules);

        return JsonUtil.convert(jsonNode, Object.class);
    }

    private void addFieldPermissionToContext(Collection<FieldRule> fieldRules) {
        TrantorContext.getContext().setFieldPermission(fieldRules);
    }

    /**
     * 当没有合适的字段规则时，按照字段权限实施模式（严格、宽松）执行不同处理逻辑
     *
     * @param dataObject      需要字段鉴权的原始数据对象
     * @param protectStrategy 字段权限鉴权策略
     * @param enforcementMode 字段权限实施模式
     * @return 返回鉴权后的数据对象
     */
    private Object handleIfNoQualifiedFieldRule(@NotNull Object dataObject,
                                                @NotNull ProtectStrategy protectStrategy,
                                                @NotNull PolicyEnforcementMode enforcementMode) {
        if (PolicyEnforcementMode.ENFORCING.equals(enforcementMode)) {
            if (ProtectStrategy.READABLE_PROTECTION.equals(protectStrategy)) {
                return null;
            } else {
                throw new UnauthorizedOperationException(ErrorType.NO_WRITE_PERMISSION_FOR_ALL_REQUEST_PARAMS);
            }
        } else {
            return dataObject;
        }
    }
}
