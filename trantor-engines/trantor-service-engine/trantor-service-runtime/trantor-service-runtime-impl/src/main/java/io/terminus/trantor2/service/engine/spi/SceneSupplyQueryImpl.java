package io.terminus.trantor2.service.engine.spi;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.runtime.spi.SceneSupplyQuery;
import io.terminus.trantor2.service.runtime.spi.bean.SceneSupply;
import io.terminus.trantor2.service.runtime.spi.bean.SceneSupplyRequest;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * SceneSupplyQueryImpl
 *
 * <AUTHOR> Created on 2024/4/23 18:22
 * @deprecated 请使用 {@link io.terminus.trantor2.service.common.component.spi.SceneSupplyQuery}
 */
@RequiredArgsConstructor
@Component
@Deprecated
public class SceneSupplyQueryImpl implements SceneSupplyQuery {

    private final io.terminus.trantor2.service.common.component.spi.SceneSupplyQuery sceneSupplyQuery;

    @Nullable
    @Override
    public SceneSupply query(SceneSupplyRequest request) {
        io.terminus.trantor2.service.common.component.spi.bean.SceneSupply result = sceneSupplyQuery.query(
            JsonUtil.convert(request, io.terminus.trantor2.service.common.component.spi.bean.SceneSupplyRequest.class));
        return JsonUtil.convert(result, SceneSupply.class);
    }
}
