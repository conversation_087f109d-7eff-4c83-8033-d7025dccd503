package io.terminus.trantor2.service.engine;

import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.service.engine.executor.interceptor.TransactionTemplate;
import io.terminus.trantor2.service.engine.impl.component.TransactionTemplateImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * ServiceRuntimeConfiguration
 *
 * <AUTHOR> Created on 2024/3/7 09:47
 */
@Configuration
public class ServiceRuntimeConfiguration {

    @Bean
    public TransactionTemplate getTransactionTemplate(
        @Qualifier("trantorPlatformTransactionManager") PlatformTransactionManager platformTransactionManager,
        TeamService teamService) {
        return new TransactionTemplateImpl(platformTransactionManager, teamService::getTeamCode);
    }

}
