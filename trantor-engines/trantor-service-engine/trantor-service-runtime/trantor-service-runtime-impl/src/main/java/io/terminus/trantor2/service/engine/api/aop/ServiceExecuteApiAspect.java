package io.terminus.trantor2.service.engine.api.aop;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.service.common.dto.ServiceRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * ServiceExecuteApiAspect
 *
 * <AUTHOR> Created on 2024/1/30 10:20
 */
@Slf4j
@Aspect
@Component
public class ServiceExecuteApiAspect {

    private final TeamService teamService;
    private final PortalService portalService;

    public ServiceExecuteApiAspect(TeamService teamService, PortalService portalService) {
        this.teamService = teamService;
        this.portalService = portalService;
    }

    @Around("execution(* io.terminus.trantor2.service.runtime.api.ServiceExecuteApi.execute*(..))")
    public Object aroundExecutingApi(ProceedingJoinPoint joinPoint) throws Throwable {
        boolean isInit = false;
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
            isInit = true;
        }
        ensureTraceId();
        ServiceRequest request = findRequest(joinPoint.getArgs());
        if (request != null) {
            ensureTrantorContext(request);
            ensureServiceRequest(request);
        }
        try {
            return joinPoint.proceed();
        } finally {
            if (isInit) {
                TrantorContext.clear();
            }
        }
    }

    private void ensureTraceId() {
        String traceId = TrantorContext.getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = TrantorContext.genTraceId();
        }
        TrantorContext.setTraceId(traceId);
        TrantorContext.setMDCTraceId();
    }

    private ServiceRequest findRequest(Object[] args) {
        if (args != null) {
            for (Object arg : args) {
                if (arg instanceof ServiceRequest) {
                    return (ServiceRequest) arg;
                }
            }
        }
        return null;
    }

    private void ensureServiceRequest(ServiceRequest request) {
        if (request.getTeamId() == null) {
            if (TrantorContext.getTeamId() != null) {
                request.setTeamId(TrantorContext.getTeamId());
            } else if (StringUtils.isNotBlank(request.getTeamCode())) {
                try {
                    request.setTeamId(teamService.getTeamIdByCode(request.getTeamCode()));
                } catch (Exception e) {
                    log.error("get team id error", e);
                }
            } else if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
                try {
                    request.setTeamId(teamService.getTeamIdByCode(TrantorContext.getTeamCode()));
                } catch (Exception e) {
                    log.error("get team id error", e);
                }
            }
        }

        if (request.getTeamCode() == null) {
            if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
                request.setTeamCode(TrantorContext.getTeamCode());
            } else if (request.getTeamId() != null) {
                try {
                    request.setTeamCode(teamService.getTeamCode(request.getTeamId()));
                } catch (Exception e) {
                    log.error("get team code error", e);
                }
            } else if (TrantorContext.getTeamId() != null) {
                try {
                    request.setTeamCode(teamService.getTeamCode(TrantorContext.getTeamId()));
                } catch (Exception e) {
                    log.error("get team code error", e);
                }
            }
        }
    }

    private void ensureTrantorContext(ServiceRequest request) {
        if (TrantorContext.getTeamId() == null && request.getTeamId() != null) {
            TrantorContext.setTeamId(request.getTeamId());
        }

        if (StringUtils.isBlank(TrantorContext.getTeamCode()) && StringUtils.isNotBlank(request.getTeamCode())) {
            TrantorContext.setTeamCode(request.getTeamCode());
        }

        if (StringUtils.isBlank(TrantorContext.getTeamCode()) && request.getTeamId() != null) {
            try {
                TrantorContext.setTeamCode(teamService.getTeamCode(request.getTeamId()));
            } catch (Exception e) {
                log.error("get team code error", e);
            }
        }

        if (!TrantorContext.getCurrentPortalOptional().isPresent() && StringUtils.isNotBlank(request.getPortalKey())) {
            try {
                Portal portal = getPortal(request.getPortalKey());
                if (portal != null) {
                    TrantorContext.setCurrentPortal(portal);
                }
            } catch (Exception e) {
                log.error("get portal error", e);
            }
        }
    }

    private Portal getPortal(String portalKey) {
        if (portalService == null) {
            return null;
        }
        Portal portal = portalService.findPortalByKey(portalKey);
        if (portal != null) {
            portal.setObtainType(Portal.ObtainType.refererHost);
            portalService.setRefererPathIfNecessary(portal);
        }
        return portal;
    }
}
