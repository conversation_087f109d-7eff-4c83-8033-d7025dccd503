package io.terminus.trantor2.service.engine.scheduler;

import io.terminus.common.scheduler.executor.JobExecutor;
import io.terminus.common.scheduler.model.JobEvent;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.chat.session.TaskParam;
import io.terminus.trantor2.service.engine.ai.core.httpclient.StreamingHandler;
import io.terminus.trantor2.service.engine.ai.core.message.Message;
import io.terminus.trantor2.service.engine.ai.core.message.MessageBuilder;
import io.terminus.trantor2.service.engine.ai.core.message.TaskEndContent;
import io.terminus.trantor2.service.engine.ai.core.notify.session.AsyncNotify;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * TaskStartJobExecutor - 处理TaskStartContent的异步任务执行器
 *
 * <AUTHOR> Created on 2025/6/11 15:00
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(prefix = "ai", name = "enable", havingValue = "true")
public class TrantorAgentTaskJobExecutor extends TrantorJobExecutor implements JobExecutor {

    private static final String EXEC_TYPE = "TAgentTask";
    private static final String SUCCESS_STATUS = "success";
    private static final String ERROR_STATUS = "error";

    private final ServiceExecutor serviceExecutor;
    private final AsyncNotify asyncNotify;

    @Override
    protected String getCurrentExecType() {
        return EXEC_TYPE;
    }

    @Override
    public Object doExecute(JobEvent event) {
        TaskExecutionResult result = executeTaskJob(event);
        if (result != null) {
            if (ERROR_STATUS.equals(result.getStatus())) {
                throw new RuntimeException(result.getOutput());
            } else {
                return "Task executed successfully";
            }
        }
        return "Task execution skipped";
    }

    @Override
    protected void setupContexts(JobEvent event) {
        TaskParam taskParam = parseTaskParam(event);
        if (taskParam != null) {
            // 设置Trantor上下文
            restoreTrantorContext(taskParam.getTrantorContext());
            restoreAIContext(taskParam.getAiContext());
        }
    }

    /**
     * 执行任务作业
     */
    private TaskExecutionResult executeTaskJob(JobEvent event) {
        TaskParam taskParam = parseTaskParam(event);
        if (taskParam == null) {
            log.warn("TaskParam参数为空，跳过执行");
            return null;
        }

        log.info("开始执行异步任务, taskId: {}, taskName: {}", taskParam.getTaskId(), taskParam.getTaskName());

        TaskExecutionResult result = executeTaskWithMetrics(taskParam);
        handleTaskResult(taskParam, result);

        log.info("异步任务执行完成, taskId: {}, 耗时: {}ms", taskParam.getTaskId(), result.getCostTimeMs());

        return result;
    }

    /**
     * 解析任务参数
     */
    private TaskParam parseTaskParam(JobEvent event) {
        try {
            return JsonUtil.fromJson(event.getParams(), TaskParam.class);
        } catch (Exception e) {
            log.error("解析TaskParam参数失败", e);
            return null;
        }
    }

    /**
     * 执行任务并统计指标
     */
    private TaskExecutionResult executeTaskWithMetrics(TaskParam taskParam) {
        Instant start = Instant.now();

        TaskExecutionResult result = new TaskExecutionResult();
        result.setTaskId(taskParam.getTaskId());
        result.setTaskName(taskParam.getTaskName());

        try {
            String output = executeTask(taskParam);
            result.setStatus(SUCCESS_STATUS);
            result.setOutput(output);
        } catch (Exception e) {
            log.error("执行异步任务时出错, taskId: {}, error: {}", taskParam.getTaskId(), e.getMessage(), e);
            result.setStatus(ERROR_STATUS);
            result.setOutput(e.getMessage() != null ? e.getMessage() : "未知错误");
        } finally {
            long costMillis = Duration.between(start, Instant.now()).toMillis();
            result.setCostTimeMs(costMillis);
        }

        return result;
    }

    /**
     * 处理任务执行结果
     */
    private void handleTaskResult(TaskParam taskParam, TaskExecutionResult result) {
        TaskEndContent content = createTaskEndContent(result);
        Message message = MessageBuilder.newBuilder()
                .autoMessageId()
                .conversationId(taskParam.getSessionId())
                .parentId(taskParam.getCurrentUserMessageId())
                .build(content);
        asyncNotify.sendNotice(taskParam.getBizKey(), taskParam.getCurrentUserId(), message);
    }

    /**
     * 创建任务结束内容
     */
    private TaskEndContent createTaskEndContent(TaskExecutionResult result) {
        TaskEndContent content = new TaskEndContent();
        content.setTaskId(result.getTaskId());
        content.setTaskName(result.getTaskName());
        content.setInvokeStatus(result.getStatus());
        content.setOutput(result.getOutput());
        content.setInvokeCostTime(result.getCostTimeMs() / 1000.0);
        return content;
    }

    /**
     * 执行具体的TaskStart任务逻辑
     *
     * @param taskParam 任务参数
     */
    @SneakyThrows
    private String executeTask(TaskParam taskParam) {
        String taskId = taskParam.getTaskId();
        String taskName = taskParam.getTaskName();
        String agentKey = taskParam.getBizKey();

        log.info("处理任务 - ID: {}, 名称: {}, agentKey:{}", taskId, taskName, agentKey);

        CountDownLatch streamingFinish = new CountDownLatch(1);

        Arguments arguments = Arguments.of(TrantorContext.getTeamId(), taskParam.getUserContent());
        arguments.setStreamingHandler(new StreamingHandler() {
        });

        ChatSession chatSession = (ChatSession) serviceExecutor.execute(agentKey, arguments);
        // 异步任务不要记录记忆
        chatSession.closeMemory();
        chatSession.addCloseListener((s) -> streamingFinish.countDown());

        streamingFinish.await();

        return chatSession.getFinalOutput();
    }

    @Override
    public Set<String> routeKeys() {
        return MetaContext.getCurrentDeployModules().stream()
                .map(ModuleInfo::getKey)
                .collect(Collectors.toSet());
    }

    @Override
    public String name() {
        return EXEC_TYPE;
    }

    /**
     * 任务执行结果
     */
    @Data
    private static class TaskExecutionResult {
        private String taskId;
        private String taskName;
        private String status;
        private String output;
        private long costTimeMs;
    }
}
