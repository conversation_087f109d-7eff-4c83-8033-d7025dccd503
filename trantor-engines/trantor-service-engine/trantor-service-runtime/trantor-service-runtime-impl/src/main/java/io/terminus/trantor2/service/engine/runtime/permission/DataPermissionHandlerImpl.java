package io.terminus.trantor2.service.engine.runtime.permission;

import cn.hutool.core.util.NumberUtil;
import io.terminus.iam.api.dto.condition.Condition;
import io.terminus.iam.api.dto.condition.ConditionGroup;
import io.terminus.iam.api.dto.condition.ConditionLogicalOperator;
import io.terminus.iam.api.dto.condition.ConditionOperator;
import io.terminus.iam.api.dto.condition.ConditionValue;
import io.terminus.iam.api.dto.condition.ConditionValueType;
import io.terminus.iam.api.dto.condition.SingleCondition;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.iam.service.TrantorIAMAdministratorService;
import io.terminus.trantor2.module.model.SelectedModelColumn;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.exception.PermissionHandleException;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.runtime.api.service.DataPermissionLoader;
import io.terminus.trantor2.service.dsl.enums.LogicOperator;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.enums.ValueType;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.GroupQueryCondition;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.QueryCondition;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.SingleQueryCondition;
import io.terminus.trantor2.service.engine.permission.DataPermissionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据权限处理器，在执行服务节点前通过此拦截器织入数据权限查询条件
 *
 * <AUTHOR>
 * 2024/6/13 14:21
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class DataPermissionHandlerImpl implements DataPermissionHandler {

    private final DataPermissionLoader dataPermissionLoader;
    private final PermissionConfigService permissionConfigService;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final TrantorIAMAdministratorService iamAdministratorService;

    @Override
    public boolean handle(QueryModel queryModel, String permissionKey) {
        try {
            if (Objects.isNull(queryModel)) {
                return false;
            }
            if (TrantorContext.isDataPermissionIgnored()) { // 上下文存在数据权限忽略标记，不执行数据鉴权
                return false;
            }
            Long iamAppId = portalToIamAppConverter.getIamAppIdFromContext();
            if (!permissionConfigService.isDataPermissionEnabled(iamAppId)) {   // 门户级数据权限开关如果关闭，不需要执行数据鉴权
                return false;
            }
            if (StringUtils.isBlank(permissionKey)) {   // 如果当前服务节点未配置数据权限规则
                return doHandleIfPermissionKeyIsInvalid(queryModel);
            }
            boolean isAdmin = iamAdministratorService.verifyUserIsAdministrator(TrantorContext.getCurrentUserId(), iamAppId);
            if (isAdmin) {  // 当前用户如果是当前门户的管理员，不需要执行数据鉴权
                return false;
            }
            return doHandle(queryModel, permissionKey);
        } catch (Exception e) {
            throw new PermissionHandleException(ErrorType.DATA_PERMISSION_HANDLE_ERROR, e);
        }
    }

    /**
     * 当前服务节点未配置数据权限规则时的处理逻辑
     */
    private boolean doHandleIfPermissionKeyIsInvalid(@NotNull QueryModel queryModel) {
        // 查询IAM权限策略实施模式
        PolicyEnforcementMode enforcementMode = permissionConfigService.getDataPermissionPolicyEnforcementMode(portalToIamAppConverter.getIamAppIdFromContext());
        if (PolicyEnforcementMode.ENFORCING.equals(enforcementMode)) {
            // 根据宽松或严格策略处理
            mergeQueryCondition(queryModel, generateDeniedAccessDataQueryCondition());
            return true;
        } else {
            return false;
        }
    }

    /**
     * 当前服务节点配置了有效的数据权限规则时的处理逻辑
     */
    private boolean doHandle(@NotNull QueryModel queryModel, @NotBlank String permissionKey) {

        Long iamAppId = portalToIamAppConverter.getIamAppIdFromContext();

        User user = TrantorContext.safeGetCurrentUser().orElseThrow(() -> new TrantorRuntimeException("Current user not found"));
        // 查询用户已授权数据权限条件规则
        ConditionGroup userDataPermissionConditionGroup = dataPermissionLoader.getUserDataPermissionConditionGroup(user.getId(), iamAppId, permissionKey);
        GroupQueryCondition groupQueryCondition = null;
        if (Objects.isNull(userDataPermissionConditionGroup)) {
            // 查询IAM权限策略实施模式
            PolicyEnforcementMode enforcementMode = permissionConfigService.getDataPermissionPolicyEnforcementMode(iamAppId);
            if (PolicyEnforcementMode.ENFORCING.equals(enforcementMode)) {
                groupQueryCondition = generateDeniedAccessDataQueryCondition();
            }
        } else {
            groupQueryCondition = convert(userDataPermissionConditionGroup);
        }
        if (Objects.isNull(groupQueryCondition)) {
            return false;
        }
        mergeQueryCondition(queryModel, groupQueryCondition);

        // 将数据权限规则放入上下文，方便后面塞入Response中
        addDataPermissionToContext(permissionKey, groupQueryCondition);

        return true;
    }

    private void addDataPermissionToContext(String permissionKey, GroupQueryCondition groupQueryCondition) {
        if (Objects.isNull(TrantorContext.getContext().getDataPermission())) {
            TrantorContext.getContext().setDataPermission(new LinkedHashMap<>());
        }
        TrantorContext.getContext().getDataPermission().put(permissionKey, groupQueryCondition);
    }

    private void mergeQueryCondition(QueryModel queryModel, GroupQueryCondition userDataPermissionQueryCondition) {
        // 合并数据查询条件
        if (Objects.isNull(queryModel.getCondition())) {
            queryModel.setCondition(userDataPermissionQueryCondition);
        } else {
            userDataPermissionQueryCondition.setNextOperator(LogicOperator.AND);
            GroupQueryCondition queryCondition = new GroupQueryCondition(Arrays.asList(userDataPermissionQueryCondition, queryModel.getCondition()), null);
            queryModel.setCondition(queryCondition);
        }
    }

    private List<QueryCondition> convert(List<? extends Condition> conditionList) {
        List<QueryCondition> collect = conditionList.stream().map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            collect.get(collect.size() - 1).setNextOperator(null);
        }
        return collect;
    }

    private QueryCondition convert(Condition condition) {
        if (condition instanceof ConditionGroup) {
            return convert((ConditionGroup) condition);
        } else {
            return convert((SingleCondition) condition);
        }
    }

    private GroupQueryCondition convert(ConditionGroup conditionGroup) {
        return new GroupQueryCondition(
                convert(conditionGroup.getConditions()),
                toConditionLogicalOperator(conditionGroup.getNextLogicalOperator()));
    }

    private SingleQueryCondition convert(SingleCondition singleCondition) {
        SingleQueryCondition singleQueryCondition = new SingleQueryCondition();
        SelectedModelColumn column = JsonUtil.fromJson(singleCondition.getParam(), SelectedModelColumn.class);
        singleQueryCondition.setNextOperator(toConditionLogicalOperator(singleCondition.getNextLogicalOperator()));
        singleQueryCondition.setField(column.getFullFieldKeyPath());
        singleQueryCondition.setType(toRelationalOperator(singleCondition.getOperator()));
        singleQueryCondition.setValueType(ValueType.CONST);
        if (!ConditionOperator.IS_NULL.equals(singleCondition.getOperator())
                && !ConditionOperator.IS_NOT_NULL.equals(singleCondition.getOperator())) {
            fillConditionValue(singleQueryCondition, singleCondition.getConditionValues());
        }
        correctConditionValueType(singleQueryCondition);
        return singleQueryCondition;
    }

    /**
     * 将条件值 conditionValues 填充到 SingleQueryCondition value/values/secondValue
     *
     * @param singleQueryCondition 原子查询条件
     * @param conditionValues      条件值包装对象
     */
    private void fillConditionValue(@NotNull SingleQueryCondition singleQueryCondition,
                                    List<ConditionValue> conditionValues) {
        List<Object> finalConditionValues;
        if (CollectionUtils.isEmpty(conditionValues)) {
            finalConditionValues = Collections.singletonList(null);
        } else {
            finalConditionValues = conditionValues.stream().map(conditionValue -> {
                if (ConditionValueType.SPECIFY_VALUE.equals(conditionValue.getValueType())
                        || ConditionValueType.SAMPLE_VALUE.equals(conditionValue.getValueType())) {
                    return conditionValue.getValues();
                } else {
                    // Never happen
                    throw new IllegalStateException("This should never happen, illegal ConditionValueType: " + conditionValue.getValueType());
                }
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }
        fillConditionValueByOperator(singleQueryCondition, finalConditionValues);
    }

    /**
     * 矫正条件值类型
     */
    private void correctConditionValueType(SingleQueryCondition singleCondition) {
        if (Objects.isNull(singleCondition)) {
            return;
        }
        if (Objects.nonNull(singleCondition.getValue())) {
            singleCondition.setValue(this.correctValueType(singleCondition.getValue()));
        }
        if (Objects.nonNull(singleCondition.getSecondValue())) {
            singleCondition.setSecondValue(this.correctValueType(singleCondition.getSecondValue()));
        }
        if (CollectionUtils.isNotEmpty(singleCondition.getValues())) {
            singleCondition.setValues(singleCondition.getValues().stream().map(this::correctValueType).collect(Collectors.toList()));
        }
    }

    /**
     * 纠正对象类型
     *
     * @param value 值对象
     * @return 返回正确类型的值对象
     */
    private Object correctValueType(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }

        if (value instanceof CharSequence) {
            CharSequence charSequence = (CharSequence) value;
            if (NumberUtil.isNumber(charSequence)) {
                if (NumberUtil.isInteger(charSequence.toString())) {
                    return Integer.valueOf(charSequence.toString());
                } else if (NumberUtil.isLong(charSequence.toString())) {
                    return Long.valueOf(charSequence.toString());
                } else if (NumberUtil.isDouble(charSequence.toString())) {
                    return Double.valueOf(charSequence.toString());
                }
            }
        } else if (value instanceof Date) {
            // 由于模型引擎时间查询只支持时间戳，所以需要将日期时间类型转换为13位毫秒时间戳整型
            return ((Date) value).getTime();
        } else if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }

        return value;
    }

    /**
     * 根据 Operator 关系运算符将条件值 conditionValues 填充到 SingleQueryCondition value/values/secondValue
     *
     * @param singleQueryCondition 原子查询条件
     * @param actualConditionValue 真实条件值
     */
    private void fillConditionValueByOperator(@NotNull SingleQueryCondition singleQueryCondition,
                                              @NotEmpty List<Object> actualConditionValue) {
        if (Operator.BETWEEN_AND.equals(singleQueryCondition.getType())) {
            singleQueryCondition.setValue(actualConditionValue.get(0));
            if (actualConditionValue.size() > 1) {
                singleQueryCondition.setSecondValue(actualConditionValue.get(1));
            }
        } else if (Operator.IN.equals(singleQueryCondition.getType())
                || Operator.NOT_IN.equals(singleQueryCondition.getType())) {
            singleQueryCondition.setValues(actualConditionValue);
        } else {
            singleQueryCondition.setValue(actualConditionValue.get(0));
        }
    }

    /**
     * @return 生成一个禁止访问数据的特定条件，该条件无法匹配任何记录，当用户无权访问数据时拼接
     */
    private GroupQueryCondition generateDeniedAccessDataQueryCondition() {
        // return where id = null
        SingleQueryCondition singleQueryCondition = new SingleQueryCondition("id", Operator.EQ, null, null, null, null);
        return new GroupQueryCondition(Collections.singletonList(singleQueryCondition));
    }

    private Operator toRelationalOperator(ConditionOperator operator) {
        if (Objects.isNull(operator)) {
            return null;
        }
        switch (operator) {
            case EQ:
                return Operator.EQ;
            case NEQ:
                return Operator.NEQ;
            case GT:
                return Operator.GT;
            case GTE:
                return Operator.GTE;
            case LT:
                return Operator.LT;
            case LTE:
                return Operator.LTE;
            case START_WITH:
                return Operator.START_WITH;
            case END_WITH:
                return Operator.END_WITH;
            case CONTAINS:
                return Operator.CONTAINS;
            case NOT_CONTAINS:
                return Operator.NOT_CONTAINS;
            case IN:
                return Operator.IN;
            case NOT_IN:
                return Operator.NOT_IN;
            case IS_NULL:
                return Operator.IS_NULL;
            case IS_NOT_NULL:
                return Operator.IS_NOT_NULL;
            case BETWEEN_AND:
                return Operator.BETWEEN_AND;
            default:
                // never happen
                throw new TrantorRuntimeException("Illegal ConditionOperator「" + operator + "」");
        }
    }

    private LogicOperator toConditionLogicalOperator(ConditionLogicalOperator conditionLogicalOperator) {
        if (Objects.isNull(conditionLogicalOperator)) {
            return null;
        } else if (ConditionLogicalOperator.AND.equals(conditionLogicalOperator)) {
            return LogicOperator.AND;
        } else if (ConditionLogicalOperator.OR.equals(conditionLogicalOperator)) {
            return LogicOperator.OR;
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal LogicOperator「" + conditionLogicalOperator + "」");
        }
    }
}
