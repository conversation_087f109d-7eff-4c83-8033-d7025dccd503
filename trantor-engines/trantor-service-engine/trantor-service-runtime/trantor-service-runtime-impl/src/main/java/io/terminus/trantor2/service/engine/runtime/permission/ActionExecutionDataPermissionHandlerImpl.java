package io.terminus.trantor2.service.engine.runtime.permission;

import io.terminus.common.api.request.AbstractRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.IamConditionConverter;
import io.terminus.trantor2.condition.ConditionGroup;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.runtime.api.service.DataPermissionLoader;
import io.terminus.trantor2.permission.runtime.api.service.IPermissionRuntimeService;
import io.terminus.trantor2.service.engine.permission.ActionExecutionDataPermissionHandler;
import io.terminus.trantor2.service.runtime.api.permission.ActionDataPermissionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * 2024/8/6 14:35
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class ActionExecutionDataPermissionHandlerImpl
        implements ActionDataPermissionHandler, ActionExecutionDataPermissionHandler {

    private final DataPermissionLoader dataPermissionLoader;
    private final IPermissionRuntimeService permissionRuntimeService;
    private final PortalToIamAppConverter portalToIamAppConverter;

    @Override
    public void handle(String permissionKey, Object params) {
        if (Objects.isNull(params)) {
            log.warn("Action request params is empty");
            return;
        }

        if (TrantorContext.isDataPermissionIgnored()) {
            // 上下文存在数据权限忽略标记，不执行数据鉴权
            return;
        }

        handle(permissionKey, conditionGroup -> {
            if (params instanceof Iterable) {
                Iterable<?> iterable = (Iterable<?>) params;
                for (Object arg : iterable) {
                    setDataPermissionConditionGroup(permissionKey, arg, conditionGroup);
                }
            } else {
                setDataPermissionConditionGroup(permissionKey, params, conditionGroup);
            }
        });
    }

    private void setDataPermissionConditionGroup(String permissionKey, Object params, ConditionGroup conditionGroup) {
        if (params instanceof AbstractRequest) {
            AbstractRequest request = (AbstractRequest) params;
            request.setDataPermissionConditionGroup(conditionGroup);

            // 将数据权限规则放入上下文，方便后面塞入Response中
            addDataPermissionToContext(permissionKey, conditionGroup);
        }
    }

    private void addDataPermissionToContext(String permissionKey, ConditionGroup conditionGroup) {
        if (Objects.isNull(TrantorContext.getContext().getDataPermission())) {
            TrantorContext.getContext().setDataPermission(new LinkedHashMap<>());
        }
        TrantorContext.getContext().getDataPermission().put(permissionKey, conditionGroup);
    }

    private void handle(String permissionKey, @NotNull Consumer<ConditionGroup> dataPermCondTransfer) {
        // 检查是否需要执行数据权限鉴权
        if (StringUtils.isBlank(permissionKey) || !permissionRuntimeService.needExecDataPermission()) {
            return;
        }

        Long iamAppId = portalToIamAppConverter.getIamAppIdFromContext();

        // 查询用户已授权数据权限条件规则
        User user = TrantorContext.safeGetCurrentUser().orElseThrow(() -> new TrantorRuntimeException("Current user not found"));
        io.terminus.iam.api.dto.condition.ConditionGroup userDataPermissionConditionGroup = dataPermissionLoader.getUserDataPermissionConditionGroup(user.getId(), iamAppId, permissionKey);
        if (Objects.isNull(userDataPermissionConditionGroup)) {
            return;
        }

        // 转换后填充到 AbstractRequest 对象
        io.terminus.trantor2.condition.ConditionGroup conditionGroup = IamConditionConverter.toConditionGroup(userDataPermissionConditionGroup);

        // 植入数据权限查询条件
        dataPermCondTransfer.accept(conditionGroup);
    }
}
