package io.terminus.trantor2.service.engine.api.impl;

import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.trantor2.doc.api.executor.StateApi;
import io.terminus.trantor2.service.engine.impl.astservice.state.StateMachineExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * StateMachineApiImpl
 *
 * <AUTHOR> Created on 2024/8/23 10:54
 */
@RequiredArgsConstructor
@Component
public class StateMachineApiImpl implements StateApi {

    private final StateMachineExecutor stateMachineExecutor;

    @Override
    public <T> T changeStatus(T model) {
        stateMachineExecutor.changeStatus(parseModeKey(model), model);
        return model;
    }

    @Override
    public <T> List<T> changeStatus(List<T> modelList) {
        stateMachineExecutor.changeStatus(parseModeKey(modelList), modelList);
        return modelList;
    }

    @Override
    public <T> T changeStatus(T model, Class<?> modelclass) {
        stateMachineExecutor.changeStatus(parseModeKey(modelclass), model);
        return model;
    }

    @Override
    public <T> List<T> changeStatus(List<T> modelList, Class<?> modelclass) {
        stateMachineExecutor.changeStatus(parseModeKey(modelclass), modelList);
        return modelList;
    }

    private static String parseModeKey(Object model) {
        Object obj;
        if (model instanceof Collection) {
            if (((Collection<?>) model).isEmpty()) {
                obj = new Object();
            } else {
                obj = ((Collection<?>) model).iterator().next();
            }
        } else {
            obj = model;
        }

        Class<?> modelClass = obj.getClass();
        return parseModeKey(modelClass);
    }

    private static String parseModeKey(Class<?> modelClass) {
        TableName tableName = modelClass.getAnnotation(TableName.class);
        return tableName != null ? tableName.value() : modelClass.getSimpleName();
    }
}
