package io.terminus.trantor2.service.engine.scheduler;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.scheduler.executor.JobExecutor;
import io.terminus.common.scheduler.model.JobEvent;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class TrantorServiceJobExecutor extends TrantorJobExecutor implements JobExecutor {
    private static final String DEFAULT_EXEC_TYPE = "TService";

    private final ServiceExecutor serviceExecutor;

    @Override
    protected String getCurrentExecType() {
        return DEFAULT_EXEC_TYPE;
    }

    @Override
    public Object doExecute(JobEvent event) {
        Object result = null;
        if (!event.getExtra().isEmpty()) {
            String serviceKey = event.getExtra().getOrDefault("metaKey", "").toString();
            // 兼容处理,metaKey取不到则取serviceKey
            if (StringUtils.isBlank(serviceKey)) {
                serviceKey = event.getExtra().getOrDefault("serviceKey", "").toString();
            }

            if (StringUtils.isBlank(serviceKey)) {
                Map<String, Object> params = JsonUtil.fromJson(event.getParams(), new TypeReference<Map<String, Object>>() {
                });
                log.info("scheduler job will invoke ServiceExecutor!!!");
                result = serviceExecutor.execute(serviceKey, Arguments.of(TrantorContext.getTeamId(), params));
            } else {
                log.warn("serviceKey is missing in context, cannot invoke ServiceExecutor");
            }
        } else {
            log.warn("current extra is missing, the context cannot be restored");
        }
        return result;
    }

    @Override
    public Set<String> routeKeys() {
        return MetaContext.getCurrentDeployModules().stream()
                .map(ModuleInfo::getKey)
                .collect(Collectors.toSet());
    }

    @Override
    public String name() {
        return DEFAULT_EXEC_TYPE;
    }
}
