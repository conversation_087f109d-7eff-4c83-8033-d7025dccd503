package io.terminus.trantor2.service.engine.aop;

import com.google.common.base.CaseFormat;
import io.terminus.operation.log.api.model.OperationLogModelRelationDO;
import io.terminus.operation.log.sdk.client.OperationLogClient;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.OplogInfo;
import io.terminus.trantor2.service.common.dto.ActionOpLog;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * AbstractAuditingLogHandler
 *
 * <AUTHOR> Created on 2024/12/13 14:09
 */
@Slf4j
abstract class AbstractAuditingLogHandler {

    protected static final String ID_FIELD = "id";
    protected static final String VERSION_FIELD = "updatedAt";
    protected static final String DELETED_FIELD = "deleted";
    protected static final String CREATE_DATA_EVENT = "CreateDataEvent";
    protected static final String UPDATE_DATA_EVENT = "UpdateDataEvent";
    protected static final String DELETE_DATA_EVENT = "DeleteDataEvent";
    protected static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");

    protected void oplogProcess(String tableName, Object parameter, String eventKey) {
        if (Contexts.getGlobalInvocationContext() == null) {
            log.warn("[{}]Repository executed not in service engine, skip oplog process", Contexts.getTraceId());
            return;
        }
        if (!Contexts.getGlobalInvocationContext().getServiceEngine().getServiceProperties().isAuditingLogEnabled()) {
            log.warn("[{}]Repository executed auditingLog disabled, skip oplog process", Contexts.getTraceId());
            return;
        }
        try {
            OperationLogModelRelationDO oplogModelRelation = OperationLogClient.getOplogModelRelation(tableName);
            if (oplogModelRelation == null) {
                return;
            }
            Map<String, ActionOpLog> eventOplogInfoMap = buildEventOplogInfoMap(tableName, eventKey, parameter, oplogModelRelation);
            if (MapUtils.isEmpty(eventOplogInfoMap)) {
                return;
            }
            Contexts.getGlobalInvocationContext().addActionLop(eventOplogInfoMap);
        } catch (Exception e) {
            log.error("[{}]Repository executed, oplog process error", Contexts.getTraceId(), e);
        }
    }

    private Map<String, ActionOpLog> buildEventOplogInfoMap(String modelKey,
                                                            String eventKey,
                                                            Object param,
                                                            OperationLogModelRelationDO oplogModelRelation) {
        if (param instanceof Map && CollectionUtils.isEmpty((Map<?, ?>) param)) {
            return Collections.emptyMap();
        }
        if (StringUtils.isEmpty(oplogModelRelation.getBizKeyField())) {
            return Collections.emptyMap();
        }
        try {
            Map<String, ActionOpLog> eventOplogInfoMap;
            if (param instanceof Collection && !CollectionUtils.isEmpty((Collection<?>) param)) {
                Collection<?> collectionParam = (Collection<?>) param;
                eventOplogInfoMap = new HashMap<>(collectionParam.size());
                collectionParam.forEach(item -> {
                    ActionOpLog eventOplogInfo = buildEventOplogInfo(item, eventKey, oplogModelRelation);
                    putLogInfo(eventOplogInfo, eventOplogInfoMap);
                });
            } else {
                eventOplogInfoMap = new HashMap<>(2);
                ActionOpLog eventOplogInfo = buildEventOplogInfo(param, eventKey, oplogModelRelation);
                putLogInfo(eventOplogInfo, eventOplogInfoMap);
            }
            return eventOplogInfoMap;
        } catch (Exception e) {
            log.error("Oplog event processor for model: {} error", modelKey, e);
        }
        return Collections.emptyMap();
    }

    private ActionOpLog buildEventOplogInfo(Object param, String eventKey, OperationLogModelRelationDO relationDO) {
        String bizKeyField = relationDO.getBizKeyField();
        ActionOpLog logInfo = new ActionOpLog();
        logInfo.setModelKey(relationDO.getModelKey());
        logInfo.setModelName(relationDO.getModelName());
        logInfo.setBizKeyField(bizKeyField);
        logInfo.setEventKey(eventKey);
        if (param instanceof Long) {
            logInfo.setBizId((Long) param);
            if (ID_FIELD.equals(bizKeyField) || StringUtils.isEmpty(logInfo.getBizKey())) {
                logInfo.setBizKey(logInfo.getBizId().toString());
            }
            return logInfo;
        }
        Object relationBizId = getFieldValue(param, CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, relationDO.getRelationKeyField()));
        logInfo.setRelationBizId(relationBizId == null ? null : Long.valueOf(relationBizId.toString()));
        Object bizFieldValue = getFieldValue(param, CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, bizKeyField));
        logInfo.setBizKey(bizFieldValue == null ? null : bizFieldValue.toString());
        Object idFieldValue = getFieldValue(param, ID_FIELD);
        logInfo.setBizId(idFieldValue == null ? null : compatibleLongValue(idFieldValue));
        if (StringUtils.isEmpty(logInfo.getBizKey()) && logInfo.getBizId() != null) {
            logInfo.setBizKey(logInfo.getBizId().toString());
        }
        Object versionFieldValue = getFieldValue(param, VERSION_FIELD);
        if (versionFieldValue != null) {
            Long versionMills = compatibleDateTimeValue(versionFieldValue);
            logInfo.setBizVersion(versionMills);
        }
        Object deletedFieldValue = getFieldValue(param, DELETED_FIELD);
        if (deletedFieldValue != null) {
            Long deletedFieldLongValue = compatibleLongValue(deletedFieldValue);
            if (deletedFieldLongValue != null && deletedFieldLongValue != 0) {
                logInfo.setEventKey(getCustomEventKeyOrDefault(DELETE_DATA_EVENT));
            }
        }
        return logInfo;
    }

    protected String getCustomEventKeyOrDefault(String defaultEvent) {
        if (TrantorContext.getContext() == null) {
            return defaultEvent;
        }
        boolean isCustomEventKey = false;
        String customEventKey = null;
        OplogInfo oplogInfo = TrantorContext.getContext().getOplogInfo();
        if (oplogInfo != null && StringUtils.isNotEmpty(customEventKey = oplogInfo.getCustomEventKey())) {
            isCustomEventKey = true;
        }
        return isCustomEventKey ? customEventKey : defaultEvent;
    }

    private void putLogInfo(ActionOpLog eventOplogInfo,
                            Map<String, ActionOpLog> eventOplogInfoMap) {
        eventOplogInfoMap.put(eventOplogInfo.getModelKey() + "@" + eventOplogInfo.getEventKey() + "@" + eventOplogInfo.getBizId(), eventOplogInfo);
    }

    private Long compatibleLongValue(Object o) {
        if (o instanceof Long) {
            return (Long) o;
        } else if (o instanceof BigDecimal) {
            return ((BigDecimal) o).longValue();
        } else if (o instanceof Integer) {
            return ((Integer) o).longValue();
        }
        return null;
    }

    private Long compatibleDateTimeValue(Object o) {
        if (o instanceof LocalDateTime) {
            long versionLongValue = ((LocalDateTime) o).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 四舍五入
            BigDecimal versionSeconds = new BigDecimal(versionLongValue).divide(ONE_THOUSAND, 0, RoundingMode.HALF_UP);
            return versionSeconds.longValue() * 1000;
        } else if (o instanceof Long) {
            return (Long) o;
        } else if (o instanceof Integer) {
            return ((Integer) o).longValue();
        }
        return null;
    }

    protected Object getFieldValue(Object model, String fieldName) {
        if (model == null) {
            return null;
        }
        if (model instanceof Map) {
            return ((Map<?, ?>) model).get(fieldName);
        }
        Field field = ReflectionUtils.findField(model.getClass(), fieldName);
        if (field == null) {
            return null;
        }
        field.setAccessible(true);
        return ReflectionUtils.getField(field, model);
    }
}
