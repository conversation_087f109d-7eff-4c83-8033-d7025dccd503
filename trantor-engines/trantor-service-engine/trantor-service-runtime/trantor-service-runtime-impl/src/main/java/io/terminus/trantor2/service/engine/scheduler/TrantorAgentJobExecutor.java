package io.terminus.trantor2.service.engine.scheduler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import io.terminus.common.scheduler.executor.JobExecutor;
import io.terminus.common.scheduler.model.JobEvent;
import io.terminus.notice.sdk.service.NoticeService;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.enums.BodyType;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.McpTool;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.dsl.properties.User;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentTrigger;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.core.httpclient.StreamingHandler;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.impl.helper.NoticeSenderHelper;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class TrantorAgentJobExecutor extends TrantorJobExecutor implements JobExecutor {
    private static final String DEFAULT_EXEC_TYPE = "TAgent";

    private final ServiceExecutor serviceExecutor;
    private final NoticeService noticeService;
    private final AiProperties aiProperties;

    @Override
    public Object doExecute(JobEvent event) {
        Object result = null;
        if (!event.getExtra().isEmpty()) {
            if (StringUtils.isNotBlank(event.getParams())) {
                AgentParams agentParams = JsonUtil.fromJson(event.getParams(), new TypeReference<AgentParams>() {
                });
                if (Objects.nonNull(agentParams)) {
                    String agentKey = event.getExtra().getOrDefault("metaKey", "").toString();
                    if (StringUtils.isNotBlank(agentKey)) {
                        result = executeAgentFromTrigger(event, agentKey, agentParams);
                        log.info("agent trigger execution result: {}", result);
                    } else {
                        log.warn("No agent found for trigger config {}", event.getParams());
                    }
                    /*
                    SkillTool tool = trigger.getTool();
                    if (Objects.nonNull(tool)) {
                        result = executeTool(tool);
                    } else {
                        log.warn("No tool found for trigger config {}", event.getParams());
                    }
                    */

                    // 如果配置了消息提醒，则发送消息
                    if (Objects.nonNull(agentParams.getNotificationConfig())) {
                        executeNotification(agentParams.getNotificationConfig(), result);
                    }
                } else {
                    log.error("No trigger found for params {}", event.getParams());
                }
            } else {
                log.warn("toolBind is missing in scheduler job context params");
            }
        } else {
            log.warn("current extra is missing, the context cannot be restored");
        }
        return result;
    }

    @Override
    protected String getCurrentExecType() {
        return DEFAULT_EXEC_TYPE;
    }

    private Object executeAgentFromTrigger(JobEvent event, String agentKey, AgentParams agentParams) {
        // 作为执行Agent的对话内容
        String userContent = agentParams.getConversationContent();
        if (StringUtils.isBlank(userContent)) {
            userContent = agentParams.getName();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("sessionId", agentParams.getSessionId());
        params.put("userContent", userContent);
        params.put("trigger", agentParams.getKey());
        params.put("skipEvaluation", true);

        if (agentParams.isSseMode()) {
            try {
                CountDownLatch streamingFinish = new CountDownLatch(1);

                Arguments arguments = Arguments.of(TrantorContext.getTeamId(), params);
                arguments.setStreamingHandler(new StreamingHandler() {});
                ChatSession chatSession = (ChatSession) serviceExecutor.execute(agentKey, arguments);
                chatSession.addCloseListener(s -> streamingFinish.countDown());

                streamingFinish.await();

                return chatSession.getFinalOutput();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return serviceExecutor.execute(agentKey, Arguments.of(TrantorContext.getTeamId(), params));
        }
    }

    @Getter
    @Setter
    public static class AgentParams {
        private String key;
        private String name;
        private String conversationContent;
        private AgentTrigger.NotificationConfig notificationConfig;
        private String sessionId;
        private boolean sseMode = false;
    }

    /**
     * 执行消息提醒服务
     *
     * @param notificationConfig
     * @param triggerExecResult
     */
    private void executeNotification(AgentTrigger.NotificationConfig notificationConfig, Object triggerExecResult) {
        try {
            final List<Long> userIds = convertUserIds(notificationConfig);
            final List<String> mobiles = convertMobiles(notificationConfig);
            final List<Long> stationLetterIds = convertStationLetterIds(notificationConfig);
            final List<String> emails = convertEmails(notificationConfig);
            final Map<String, Object> variables = getVariables(notificationConfig);
            final List<String> wecomRobotKeys = convertWecomRobotKeys(notificationConfig);
            // 设置触发器执行Agent得到的结果作为参数变量
            variables.put("agentOutput", triggerExecResult);
            // @formatter:off
            NoticeSenderHelper noticeSenderHelper = new NoticeSenderHelper(noticeService,
                                                                           notificationConfig.getNoticeSceneCode(),
                                                                           notificationConfig.getNoticeSceneName(),
                                                                           variables,
                                                                           userIds,
                                                                           mobiles,
                                                                           emails,
                                                                           stationLetterIds,
                                                                           wecomRobotKeys);
            // @formatter:on
            noticeSenderHelper.run();
        } catch (Exception e) {
            log.error("execute notice error: {}", e.getMessage(), e);
        }
    }

    private List<Long> convertUserIds(AgentTrigger.NotificationConfig notificationConfig) {
        final Set<Long> userIds = new HashSet<>();
        if (notificationConfig.getReceiverUsers() != null) {
            for (User user : notificationConfig.getReceiverUsers()) {
                userIds.add(user.getUserId());
            }
        }
        if (notificationConfig.getReceiverUserIds() != null) {
            for (Value varValue : notificationConfig.getReceiverUserIds()) {
                Object userId = ValueFactory.getValue(new BaseField(FieldType.Number), varValue, null);
                if (userId != null) {
                    userIds.add(((BigDecimal) userId).longValue());
                }
            }
        }
        return new ArrayList<>(userIds);
    }

    private List<String> convertMobiles(AgentTrigger.NotificationConfig notificationConfig) {
        final Set<String> result = new HashSet<>();
        if (notificationConfig.getReceiverMobiles() != null) {
            for (Value var : notificationConfig.getReceiverMobiles()) {
                Object r = ValueFactory.getValue(Field.ofByFieldType(FieldType.Text), var, null);
                if (r != null) {
                    result.add((String) r);
                }
            }
        }
        return new ArrayList<>(result);
    }

    private List<Long> convertStationLetterIds(AgentTrigger.NotificationConfig notificationConfig) {
        final Set<Long> result = new HashSet<>();
        if (notificationConfig.getReceiverStationLetterIds() != null) {
            for (Value var : notificationConfig.getReceiverStationLetterIds()) {
                Object id = ValueFactory.getValue(Field.ofByFieldType(FieldType.Number), var, null);
                if (id != null) {
                    result.add(((BigDecimal) id).longValue());
                }
            }
        }
        return new ArrayList<>(result);
    }

    private List<String> convertEmails(AgentTrigger.NotificationConfig notificationConfig) {
        final Set<String> result = new HashSet<>();
        if (notificationConfig.getReceiverEmails() != null) {
            for (Value var : notificationConfig.getReceiverEmails()) {
                Object r = ValueFactory.getValue(Field.ofByFieldType(FieldType.Text), var, null);
                if (r != null) {
                    result.add((String) r);
                }
            }
        }
        return new ArrayList<>(result);
    }

    private Map<String, Object> getVariables(AgentTrigger.NotificationConfig notificationConfig) {
        final Map<String, Object> variables = new HashMap<>();
        if (notificationConfig.getVariables() != null && !notificationConfig.getVariables().isEmpty()) {
            variables.putAll(ValueFactory.getValueForStringEntry(notificationConfig.getVariables(), null));
        }
        return variables;
    }

    private List<String> convertWecomRobotKeys(AgentTrigger.NotificationConfig notificationConfig) {
        if (StringUtils.isBlank(notificationConfig.getWecomRobotKey())) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(notificationConfig.getWecomRobotKey());
    }

    /**
     * 执行触发器配置工具
     *
     * @param tool
     * @return
     */
    private Object executeTool(SkillTool tool) {
        Object result = null;
        try {
            // 执行工具调用
            switch (tool.getType()) {
                case Service:
                    // @formatter:off
                    result = serviceExecutor.execute(tool.getKey(), Arguments.of(TrantorContext.getTeamId(),
                                                                                 convertField(((ServiceTool)tool).getInput())));
                    // @formatter:on
                    break;
                case Mcp:
                    // 调用t-ai2执行指定mcp的工具
                    String mcpToolExecutionDomain = "http://127.0.0.1:8000";
                    String mcpToolExecutionUrl = Objects.toString(aiProperties.getTAiDomain(), mcpToolExecutionDomain);
                    result = HttpClient.INSTANCE.post(String.format("%s/api/ai/mcp/run", mcpToolExecutionUrl), buildInvokeMcpParams((McpTool) tool));
                    break;
                case Http:
                    HttpTool httpTool = (HttpTool) tool;
                    String method = httpTool.getMethod();
                    // 只解析固定值，如果不是固定值则报错
                    final Map<String, String> header = getHttpHeader(httpTool.getHeaders());
                    final Map<String, Object> parameter = ValueFactory.getValueForStringEntry(httpTool.getParams(), null);
                    final Map<String, Object> pathVariables = ValueFactory.getValueForStringEntry(httpTool.getPathVariables(), null);
                    final String url = getHttpUrl(httpTool, pathVariables, parameter);
                    final Object bodyValue = getHttpBody(httpTool);
                    // @formatter:off
                    switch (method) {
                        case "GET":
                            result = HttpClient.INSTANCE.get(url, header, parameter);
                            break;
                        case "POST":
                            result = HttpClient.INSTANCE.post(url,header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                            break;
                        case "PUT":
                            result = HttpClient.INSTANCE.put(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                            break;
                        case "DELETE":
                            result = HttpClient.INSTANCE.delete(url, header, parameter);
                            break;
                        default:
                            log.warn("Unsupported http method {}", method);
                            break;
                    }
                    // @formatter:off
                    break;
                default:
                    log.warn("unknown tool bind type: {}, can't execution", tool.getType());
                    break;
            }
        } catch (Exception e) {
            log.error("execute agent trigger tool error: {}", e.getMessage(), e);
        }
        return result;
    }

    private String getHttpUrl(HttpTool httpTool, Map<String,Object> pathVariables, Map<String,Object> parameter) {
        final String url;
        if (httpTool.getUrl().contains("{") && httpTool.getUrl().contains("}")) {
            Map<String, Object> variables = new HashMap<>();
            variables.putAll(pathVariables);
            variables.putAll(parameter);
            url = Placeholder.PLACE_CURLY_BRACES_HOLDER.replaceHolder(httpTool.getUrl(), variables);
        } else {
            url = httpTool.getUrl();
        }
        return url;
    }

    private Map<String, String> getHttpHeader(List<StringEntry> headers) {
        final Map<String, String> headerValue = new HashMap<>();

        // 设置在页面上请求头
        if (CollectionUtils.isNotEmpty(headers)) {
            final Map<String, Object> customHeaderValue = ValueFactory.getValueForStringEntry(headers, null);
            for (Map.Entry<String, Object> entry : customHeaderValue.entrySet()) {
                // 判断是否存在，如果不存在，才添加
                if (headerValue.keySet().stream().noneMatch(k -> k.equalsIgnoreCase(entry.getKey()))) {
                    headerValue.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        return headerValue;
    }

    private Object getHttpBody(HttpTool httpTool) {
        if (httpTool.getBodyType() == BodyType.JSON) {
            return httpTool.getJsonBody();
        } else {
            return ValueFactory.getValue(null, httpTool.getBody(), null);
        }
    }

    private Map<String, Object> buildInvokeMcpParams(McpTool mcpTool) {
        // FIXME 接口请求入参的字段名称需要重新调整
        Map<String, Object> args = ValueFactory.getValueForStringEntry(mcpTool.getMcpServerArgs(), null);
        Map<String, Object> params = new HashMap<>(8);
        params.put("server_name", mcpTool.getName());
        params.put("server_version", mcpTool.getMcpServerVersion());
        params.put("server_args", args);
        if (CollectionUtils.isNotEmpty(mcpTool.getTools())) {
            params.put("tool_name", mcpTool.getTools().get(0).getName());
            params.put("tool_args", convertField(mcpTool.getTools().get(0).getInput()));
        }
        return params;
    }

    private <T> Map<String, T> convertField(List<Field> fields) {
        Map<String, T> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(fields)) {
            fields.forEach(field -> params.put(field.getFieldKey(), (T) field.getDefaultValue()));
        }
        return params;
    }

    @Override
    public Set<String> routeKeys() {
        return MetaContext.getCurrentDeployModules().stream()
                                                    .map(ModuleInfo::getKey)
                                                    .collect(Collectors.toSet());
    }

    @Override
    public String name() {
        return DEFAULT_EXEC_TYPE;
    }
}
