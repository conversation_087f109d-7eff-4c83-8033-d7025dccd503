package io.terminus.trantor2.service.engine.scheduler;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.api.util.MD5Utils;
import io.terminus.common.scheduler.executor.JobExecutor;
import io.terminus.common.scheduler.model.JobEvent;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.engine.ai.consts.AiHeader;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public abstract class TrantorJobExecutor implements JobExecutor {

    @Resource
    private RedissonClient redissonClient;

    protected abstract String getCurrentExecType();

    protected String getLockName(JobEvent event) {
        return event.getJobKey() + ":LOCK:" + MD5Utils.md5Hex(JsonUtil.toJson(event), "UTF-8");
    }

    @Override
    public Object execute(JobEvent event) throws Exception {
        if (event.getExecType().equals(getCurrentExecType())) {
            RLock lock = redissonClient.getLock(getLockName(event));
            if (lock.tryLock()) {
                initializeContexts();
                try {
                    setupContexts(event);
                    log.info("[{}]Executing job: {}, with params: {}", TrantorContext.getTraceId(), event.getJobKey(), event.getParams());
                    return doExecute(event);
                } finally {
                    cleanupContexts();
                    // 释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.warn("current Job is locked by other Job, skip execution: {}", event.getJobKey());
            }
        }
        return null;
    }

    protected abstract Object doExecute(JobEvent event);

    /**
     * 初始化上下文
     */
    protected void initializeContexts() {
        TrantorContext.init();
        AIContext.init();
    }

    /**
     * 设置上下文
     */
    protected void setupContexts(JobEvent event) {
        restoreTrantorContext(event.getExtra());
        restoreAIContext(event.getExtra());
    }

    /**
     * 清理上下文
     */
    protected void cleanupContexts() {
        TrantorContext.clear();
        AIContext.clear();
    }

    /**
     * 还原Trantor线程上下文内容
     */
    protected void restoreTrantorContext(Map<String, Object> context) {
        if (context != null) {
            if (context.get("traceId") != null) {
                String traceId = (String) context.get("traceId");
                TrantorContext.setTraceId(traceId);
            } else {
                String traceId = TrantorContext.genTraceId();
                TrantorContext.setTraceId(traceId);
                TrantorContext.setMDCTraceId();
            }

            if (context.containsKey("teamId")) {
                Number teamId = (Number) context.get("teamId");
                TrantorContext.setTeamId(teamId.longValue());
            } else {
                log.warn("teamId is missing in context");
            }
            if (context.containsKey("teamCode")) {
                String teamCode = (String) context.get("teamCode");
                TrantorContext.setTeamCode(teamCode);
            } else {
                log.warn("teamCode is missing in context");
            }

            if (context.containsKey("appId")) {
                Number appId = (Number) context.get("appId");
                TrantorContext.setModuleId(appId.longValue());
            } else {
                log.warn("appId is missing in context");
            }
            if (context.containsKey("appCode")) {
                String appCode = (String) context.get("appCode");
                TrantorContext.setModuleKey(appCode);
            } else {
                log.warn("appCode is missing in context");
            }

            if (context.containsKey("moduleKey")) {
                String moduleKey = (String) context.get("moduleKey");
                TrantorContext.setModuleKey(moduleKey);
            } else {
                log.warn("moduleKey is missing in context");
            }

            if (context.containsKey("portalCode")) {
                String portalCode = (String) context.get("portalCode");
                TrantorContext.setPortalCode(portalCode);
            } else {
                log.warn("portalCode is missing in context");
            }

            if (context.containsKey("userId")) {
                Number userId = (Number) context.get("userId");
                User user = new User();
                user.setId(userId.longValue());
                TrantorContext.setCurrentUser(user);
            } else {
                log.warn("userId is missing in context");
            }
        }
    }

    /**
     * 还原AI上下文内容
     */
    protected void restoreAIContext(Map<String, Object> context) {
        if (context != null) {
            // 还原请求头
            Map<String, String> headers = new HashMap<>();
            if (context.containsKey("headers")) {
                headers.putAll(JsonUtil.fromJson((String) context.get("headers"), new TypeReference<Map<String, String>>() {
                }));
                if (headers.containsKey("Referer")) {
                    String referer = headers.get("Referer");
                    try {
                        URI uri = new URI(referer);
                        headers.put(AiHeader.T_AI_CALLBACK_HEADER, String.format("%s://%s", uri.getScheme(), uri.getHost()));
                    } catch (Exception e) {
                        log.error("解析Referer信息获取域名失败", e);
                    }
                    headers.put(AiHeader.T_AI_SOURCE_REFERER_HEADER, referer);
                }
            }

            AIContext.getContext().setAiSourceHeaders(headers);
            TrantorContext.setHeaders(headers);
        }
    }
}
