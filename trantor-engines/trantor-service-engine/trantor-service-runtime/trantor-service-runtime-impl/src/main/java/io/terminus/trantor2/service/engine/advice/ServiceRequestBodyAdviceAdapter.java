//package io.terminus.trantor2.service.engine.advice;
//
//import io.terminus.trantor2.common.TrantorContext;
//import io.terminus.trantor2.common.dto.AppRequest;
//import io.terminus.trantor2.common.dto.TeamRequest;
//import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
//import io.terminus.trantor2.module.service.TeamService;
//import org.junit.jupiter.api.Order;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.HttpInputMessage;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.util.TypeUtils;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Type;
//
///**
// * ServiceRequestBodyAdviceAdapter
// *
// * <AUTHOR> Created on 2023/9/20 22:40
// */
//@Order(3)
//@SuppressWarnings("all")
//@RestControllerAdvice(basePackages = "io.terminus.trantor2.service")
//public class ServiceRequestBodyAdviceAdapter extends RequestBodyAdviceAdapter {
//
//    @Resource
//    private TeamService teamService;
//    @Resource
//    private ModuleRuntimeQueryService moduleService;
//
//    @Override
//    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
//        return TypeUtils.isAssignable(AppRequest.class, targetType)
//            || TypeUtils.isAssignable(TeamRequest.class, targetType);
//    }
//
//    @Override
//    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
//
//        if (body instanceof TeamRequest) {
//            if (TrantorContext.getTeamId() == null && ((TeamRequest) body).getTeamId() != null) {
//                TrantorContext.setTeamId(((TeamRequest) body).getTeamId());
//            }
//        }
//
//        if (TrantorContext.getTeamCode() == null && TrantorContext.getTeamId() != null) {
//            TrantorContext.setTeamCode(teamService.getTeamCode(TrantorContext.getTeamId()));
//        }
//
//        if (body instanceof AppRequest) {
//            if (TrantorContext.getPortalCode() == null && ((AppRequest) body).getPortalKey() != null) {
//                TrantorContext.setPortalCode(((AppRequest) body).getPortalKey());
//            }
//        }
//
//        return body;
//    }
//}
