package io.terminus.trantor2.service.engine.impl.component;

import cn.hutool.core.util.RandomUtil;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.management.meta.util.DateUtils;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.executor.interceptor.TransactionTemplate;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * TransactionTemplateImpl
 *
 * <AUTHOR> Created on 2024/3/7 16:31
 */
@Getter
@Slf4j
@RequiredArgsConstructor
public class TransactionTemplateImpl implements TransactionTemplate {

    private final PlatformTransactionManager transactionManager;
    private final Function<Long, String> teamCodeGetter;

    @Override
    public Object execute(TransactionInvocation invocation) {
        if (Objects.isNull(transactionManager)) {
            return invocation.invoke();
        }

        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            return invocation.invoke();
        }

        org.springframework.transaction.support.TransactionTemplate transactionTemplate
                = new org.springframework.transaction.support.TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(getPropagation(invocation.getTransactionPropagation()));

        // 服务开启事务时需要，根据这几个参数获取数据源
        if (TransactionContext.getTeamId() == null) {
            TransactionContext.setModuleKey(invocation.getServiceKey().moduleKey());
            if (TrantorContext.getTeamId() != null) {
                TransactionContext.setTeamId(TrantorContext.getTeamId());
                TransactionContext.setTeamCode(TrantorContext.getTeamCode());
            } else {
                TransactionContext.setTeamId(invocation.getServiceKey().getTeamId());
                TransactionContext.setTeamCode(teamCodeGetter.apply(invocation.getServiceKey().getTeamId()));
            }
        }

        // 事务id设置
        List<String> alreadyExistsTransactionId = TransactionContext.getTransactionId();
        String nowTransactionId = generateTransactionIdByService(invocation.getServiceKey());
        if (CollectionUtils.isEmpty(alreadyExistsTransactionId)) {
            log.info("service begin execute new tx, txId:{}", nowTransactionId);
        } else {
            log.info("service begin execute already exist tx, nowTxId:{},topTxId:{}", nowTransactionId, alreadyExistsTransactionId);
        }
        TransactionContext.addTransactionId(nowTransactionId);

        try {
            transactionTemplate.setName(nowTransactionId);
            return transactionTemplate.execute(status -> invocation.invoke());
        } finally {
            TransactionContext.deleteTransactionId(nowTransactionId);
            // 如果当前上下文中事务id为空了，则强制清空事务上下文
            TransactionContext.removeContextIfNecessary(nowTransactionId, true);
        }
    }

    private String generateTransactionIdByService(Key serviceKey) {
        return serviceKey.getKey()
                + "#" + DateUtils.formatDate(new Date(), "yyyyMMdd_HHmmss_SSS_")
                + "#" + RandomUtil.randomString(4);
    }

    private int getPropagation(Propagation propagation) {
        if (Objects.isNull(propagation)) {
            return org.springframework.transaction.support.TransactionTemplate.PROPAGATION_NOT_SUPPORTED;
        }
        switch (propagation) {
            case NOT_SUPPORTED:
                return org.springframework.transaction.support.TransactionTemplate.PROPAGATION_NOT_SUPPORTED;
            case REQUIRED:
                return org.springframework.transaction.support.TransactionTemplate.PROPAGATION_REQUIRED;
//            case REQUIRES_NEW:
//                return org.springframework.transaction.support.TransactionTemplate.PROPAGATION_REQUIRES_NEW;
            default:
                throw new ServiceException(ErrorType.SERVICE_EXECUTE_TRANSACTION_NOT_SUPPORT_PROPAGATION,
                        new Object[]{propagation});
        }
    }
}
