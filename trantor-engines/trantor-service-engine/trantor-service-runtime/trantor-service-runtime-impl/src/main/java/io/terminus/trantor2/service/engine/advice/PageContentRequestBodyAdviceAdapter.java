package io.terminus.trantor2.service.engine.advice;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.service.common.dto.ServiceView;
import io.terminus.trantor2.service.common.dto.PageContentRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Order;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.TypeUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
@Order(1)
@RestControllerAdvice(basePackages = "io.terminus.trantor2.service")
public class PageContentRequestBodyAdviceAdapter extends RequestBodyAdviceAdapter {
    @Override
    public boolean supports(
        @NotNull MethodParameter methodParameter,
        @NotNull Type targetType,
        @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return TypeUtils.isAssignable(PageContentRequest.class, targetType);
    }

    @NotNull
    @Override
    public Object afterBodyRead(
        @NotNull Object body,
        @NotNull HttpInputMessage inputMessage,
        @NotNull MethodParameter parameter,
        @NotNull Type targetType,
        @NotNull Class<? extends HttpMessageConverter<?>> converterType) {

        PageContentRequest request = (PageContentRequest) body;
        TrantorContext.setCurrentView(
            new ServiceView(
                request.getViewKey(),
                request.getContainerKey(),
                request.getSceneId(),
                request.getSceneKey(),
                request.getViewCondition()
            ));
        return body;
    }
}
