//package io.terminus.trantor2.service.engine.aop;
//
//import com.baomidou.mybatisplus.core.conditions.Wrapper;
//import com.baomidou.mybatisplus.core.toolkit.Constants;
//import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
//import io.terminus.trantor2.common.TrantorContext;
//import io.terminus.trantor2.common.dto.OplogInfo;
//import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import net.sf.jsqlparser.parser.CCJSqlParserUtil;
//import net.sf.jsqlparser.statement.Statement;
//import net.sf.jsqlparser.statement.delete.Delete;
//import net.sf.jsqlparser.statement.insert.Insert;
//import net.sf.jsqlparser.statement.update.Update;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.ibatis.binding.MapperMethod;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.ParameterMapping;
//import org.apache.ibatis.mapping.SqlCommandType;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Objects;
//
///**
// * ActionAuditingLogAop
// *
// * <AUTHOR> Created on 2024/12/10 11:36
// */
//@Slf4j
//@Component
//public class ActionAuditingLogAop extends AbstractAuditingLogHandler implements InnerInterceptor {
//
//    public ActionAuditingLogAop(@Autowired(required = false) MybatisPlusInterceptor interceptor) {
//        if (interceptor != null) {
//            interceptor.addInnerInterceptor(this);
//        }
//    }
//
//    @Override
//    public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) {
//        if (Contexts.getGlobalInvocationContext() == null) {
//            log.warn("[{}]Repository executed not in service engine, skip oplog process", Contexts.getTraceId());
//            return;
//        }
//        if (!Contexts.getGlobalInvocationContext().isAuditingLogEnabled()) {
//            log.warn("[{}]Repository executed auditingLog disabled, skip oplog process", Contexts.getTraceId());
//            return;
//        }
//
//        String tableName = getTableName(ms, parameter);
//        if (StringUtils.isBlank(tableName)) {
//            log.warn("[{}]Repository executed tableName is null, skip oplog process", Contexts.getTraceId());
//            return;
//        }
//
//        boolean isCustomEventKey = false;
//        String customEventKey = null;
//        OplogInfo oplogInfo = TrantorContext.getContext().getOplogInfo();
//        if (oplogInfo != null && StringUtils.isNotEmpty(customEventKey = oplogInfo.getCustomEventKey())) {
//            isCustomEventKey = true;
//        }
//        Object entityParameter = getEntityParameter(parameter);
//        if (SqlCommandType.INSERT == ms.getSqlCommandType()) {
//            oplogProcess(tableName, entityParameter, isCustomEventKey ? customEventKey : CREATE_DATA_EVENT);
//        } else if (SqlCommandType.UPDATE == ms.getSqlCommandType()) {
//            boolean isDelete = isDelete(ms, entityParameter);
//            if (isDelete) {
//                oplogProcess(tableName, entityParameter, isCustomEventKey ? customEventKey : DELETE_DATA_EVENT);
//            } else {
//                oplogProcess(tableName, entityParameter, isCustomEventKey ? customEventKey : UPDATE_DATA_EVENT);
//            }
//        } else if (SqlCommandType.DELETE == ms.getSqlCommandType()) {
//            oplogProcess(tableName, entityParameter, isCustomEventKey ? customEventKey : DELETE_DATA_EVENT);
//        }
//    }
//
//    private Object getEntityParameter(Object parameter) {
//        if (parameter instanceof MapperMethod.ParamMap) {
//            MapperMethod.ParamMap<?> paramMap = (MapperMethod.ParamMap<?>) parameter;
//            if (paramMap.containsKey(Constants.ENTITY)) {
//                return paramMap.get(Constants.ENTITY);
//            } else if (paramMap.containsKey(Constants.COLLECTION)) {
//                return paramMap.get(Constants.COLLECTION);
//            } else if (paramMap.containsKey(Constants.COLUMN_MAP)) {
//                return paramMap.get(Constants.COLUMN_MAP);
//            } else if (paramMap.containsKey(Constants.WRAPPER)) {
//                Wrapper<?> wrapper = (Wrapper<?>) paramMap.get(Constants.WRAPPER);
//                if (wrapper != null) {
//                    return wrapper.getEntity();
//                }
//            }
//        } else {
//            return parameter;
//        }
//        return null;
//    }
//
//    @SneakyThrows
//    private String getTableName(MappedStatement ms, Object parameter) {
//        BoundSql boundSql = ms.getBoundSql(parameter);
//        Statement statement = CCJSqlParserUtil.parse(boundSql.getSql());
//        if (statement instanceof Insert) {
//            return ((Insert) statement).getTable().getName();
//        } else if (statement instanceof Update) {
//            return ((Update) statement).getTable().getName();
//        } else if (statement instanceof Delete) {
//            return ((Delete) statement).getTable().getName();
//        }
//        return null;
//    }
//
//    private boolean isDelete(MappedStatement ms, Object parameter) {
//        String statementId = ms.getId();
//        if (StringUtils.containsIgnoreCase(statementId, "delete")) {
//            return true;
//        }
//        List<ParameterMapping> mappings = ms.getParameterMap().getParameterMappings();
//        if (mappings != null) {
//            if (mappings.stream().anyMatch(m -> StringUtils.contains(m.getProperty(), DELETED_FIELD))) {
//                Object deletedValue = getFieldValue(parameter, DELETED_FIELD);
//                // 不等于0，说明是删除操作
//                return deletedValue != null && !Objects.equals(deletedValue, 0L);
//            }
//        }
//        return false;
//    }
//}
