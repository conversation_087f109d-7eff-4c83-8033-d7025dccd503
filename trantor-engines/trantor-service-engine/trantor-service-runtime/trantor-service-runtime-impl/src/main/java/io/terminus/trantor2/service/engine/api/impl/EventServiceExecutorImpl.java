package io.terminus.trantor2.service.engine.api.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.terminus.common.api.util.JsonUtils;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.doc.api.context.EventRequest;
import io.terminus.trantor2.doc.api.executor.EventExecutor;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * EventServiceExecutorImpl
 *
 * <AUTHOR> Created on 2024/8/24 10:04
 */
@SuppressWarnings({"unchecked", "rawtypes"})
@RequiredArgsConstructor
@Component
public class EventServiceExecutorImpl implements EventExecutor {

    private final ServiceExecutor serviceExecutor;

    @Override
    public JSON execute(EventRequest eventRequest) {
        setPageInfoContext(eventRequest);

        String eventService = eventRequest.getEventCode() + "_SERVICE";
        Arguments arguments = Arguments.of(TrantorContext.getTeamId(), MapUtil.of("request", eventRequest.getParam()));

        Map result = (Map) serviceExecutor.execute(eventService, arguments);

        return convertResult(result);
    }

    @Override
    public JSON executeWithTransaction(EventRequest eventRequest) {
        setPageInfoContext(eventRequest);

        String eventService = eventRequest.getEventCode() + "_SERVICE";
        Arguments arguments = Arguments.of(TrantorContext.getTeamId(), MapUtil.of("request", eventRequest.getParam()));
        // 设置事务传播
        arguments.addAttribute(VariableKey.TRANSACTION_PROPAGATION, Propagation.REQUIRED);

        Map result = (Map) serviceExecutor.execute(eventService, arguments);

        return convertResult(result);
    }

    @Override
    public Response<JSON> serviceEventExecute(EventRequest eventRequest) {
        setPageInfoContext(eventRequest);

        String eventService = eventRequest.getEventCode() + "_SERVICE";
        Arguments arguments = Arguments.of(TrantorContext.getTeamId(), MapUtil.of("request", eventRequest.getParam()));
        arguments.addAttribute(VariableKey.TRANSACTION_PROPAGATION, Propagation.REQUIRED);
        Map result = (Map) serviceExecutor.execute(eventService, arguments);

        JSON data = convertResult(result);

        Response<JSON> response = Response.ok();

        // 返回为null时的特殊处理
        if (data instanceof JSONObject && ((JSONObject) data).getInnerMap().size() == 0) {
            return response;
        }

        // 构建返回对象 data和info
        response.setData(data);

        Object info = MapUtils.getObject(result, "info");
        if (info != null) {
            if (info instanceof Response.Info) {
                response.setInfo((Response.Info) info);
            } else {
                response.setInfo(JsonUtils.convert(info, Response.Info.class));
            }
        }

        return response;
    }

    @Nullable
    private static JSON convertResult(Map result) {
        if (result == null) {
            return null;
        }
        return result.get("data") == null ? null : (JSON) JSON.toJSON(result.get("data"));
    }

    private static void setPageInfoContext(EventRequest eventRequest) {
        if (eventRequest.getPageInfo() != null) {
            if (TrantorContext.getContext() == null) {
                TrantorContext.init();
            }
            TrantorContext.setPageInfo(eventRequest.getPageInfo());
        }
    }
}
