package io.terminus.trantor2.service.engine.aop;

import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.trantor2.model.common.model.request.BatchCreateObject;
import io.terminus.trantor2.model.common.model.request.BatchUpdateObject;
import io.terminus.trantor2.model.common.model.request.CreateObject;
import io.terminus.trantor2.model.common.model.request.UpdateObject;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.state.StateMachineExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * ModelDataRepositoryAop
 *
 * <AUTHOR> Created on 2024/4/26 15:07
 * see io.terminus.trantor2.doc.state.StateEngineExecutor
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class ModelDataRepositoryAop {

    private final StateMachineExecutor stateMachineExecutor;

    @Pointcut("execution(* io..*Repo.insert*(..)) || execution(* com..*Repo.insert*(..))")
    public void insertMethod() {
    }

    @Pointcut("execution(* io..*Repo.update*(..)) || execution(* com..*Repo.update*(..))")
    public void updateMethod() {
    }

    /**
     * Action线下代码的状态机的逻辑
     *
     * @param joinPoint joinPoint
     */
    @Before("insertMethod() || updateMethod()")
    public void changeStatus(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        if (log.isInfoEnabled()) {
            log.info("[{}][StateMachine] exec start, intercept method:{}", Contexts.getTraceId(), methodName);
        }

        Object[] args = joinPoint.getArgs();
        if (args.length != 1) {
            log.info("[{}][StateMachine]: {} args than one", Contexts.getTraceId(), methodName);
            return;
        }

        Object arg = args[0];
        String modelKey = parseModeKey(arg);
        stateMachineExecutor.changeStatus(modelKey, arg);
    }

    /**
     * 状态机的逻辑
     *
     * @param joinPoint joinPoint
     */
    @Before("execution(* io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi.*(..))")
    public void changeState(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        if (log.isInfoEnabled()) {
            log.info("[{}][StateMachine] exec start, intercept method:{}", Contexts.getTraceId(), methodName);
        }

        Object[] args = joinPoint.getArgs();
        if (args.length != 1) {
            if (log.isInfoEnabled()) {
                log.info("[{}][StateMachine]: {} args than one", Contexts.getTraceId(), methodName);
            }
            return;
        }

        Object arg = args[0];
        if (arg instanceof CreateObject) {
            stateMachineExecutor.changeStatus(((CreateObject) arg).getModelAlias(), ((CreateObject) arg).getData());
        } else if (arg instanceof BatchCreateObject) {
            stateMachineExecutor.changeStatus(((BatchCreateObject) arg).getModelAlias(), ((BatchCreateObject) arg).getDataList());
        } else if (arg instanceof UpdateObject) {
            stateMachineExecutor.changeStatus(((UpdateObject) arg).getModelAlias(), ((UpdateObject) arg).getData());
        } else if (arg instanceof BatchUpdateObject) {
            stateMachineExecutor.changeStatus(((BatchUpdateObject) arg).getModelAlias(), ((BatchUpdateObject) arg).getDataList());
        } else {
            if (log.isInfoEnabled()) {
                log.info("[{}][StateMachine]: {} args is not needed", Contexts.getTraceId(), methodName);
            }
        }
    }

    private static String parseModeKey(Object model) {
        Object obj;
        if (model instanceof Collection) {
            if (((Collection<?>) model).isEmpty()) {
                obj = new Object();
            } else {
                obj = ((Collection<?>) model).iterator().next();
            }
        } else {
            obj = model;
        }

        Class<?> modelClass = obj.getClass();

        TableName tableName = modelClass.getAnnotation(TableName.class);
        return tableName != null ? tableName.value() : modelClass.getSimpleName();
    }
}
