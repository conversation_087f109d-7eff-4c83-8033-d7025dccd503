package io.terminus.trantor2.service.engine.api.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.properties.ServiceProperties;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.ai.platform.service.AIService;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Service;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.loader.ServiceFactory;
import io.terminus.trantor2.service.engine.web.ServiceExecuteWebBase;
import io.terminus.trantor2.service.runtime.api.ServiceExecuteApi;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import io.terminus.trantor2.service.runtime.api.response.ServiceParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * ServiceExecuteApiImpl
 *
 * <AUTHOR> Created on 2023/3/3 14:08
 */
@SuppressWarnings("rawtypes")
@Slf4j
@RestController
@RequestMapping({"/api/trantor/service/engine", "/api/trantor/service/internal"})
public class ServiceExecuteApiImpl extends ServiceExecuteWebBase implements ServiceExecuteApi {

    public ServiceExecuteApiImpl(ServiceExecutor serviceExecutor,
                                 ServiceFactory serviceFactory,
                                 AIService aiService,
                                 ServiceProperties serviceProperties) {
        super(serviceExecutor, serviceFactory, aiService, serviceProperties);
    }

    @Deprecated
    @Override
    public Response<Object> execute(ServiceExecuteRequest request) {
        return execute(request.getServiceKey(), request);
    }

    @Override
    public Response<Object> execute(String serviceKey, ServiceExecuteRequest request) {
        try {
            Object result = serviceExecutor.execute(serviceKey, createArguments(request));
            return toResponse(result);
        } finally {
            clearPermissionInContext();  // 为防止线程复用时出现数据污染，不管服务执行成功还是异常，都要清除上下文中的权限信息
        }
    }

    @PostMapping(value = "/execute/service-params")
    public Response<ServiceParams> getServiceParams(@RequestBody ServiceExecuteRequest request) {
        Service service = serviceFactory.load(Key.of(request.getTeamId(), request.getServiceKey()));
        if (service != null) {
            return Response.ok(new ServiceParams(
                    ((ServiceDefinition) service.getMeta().getDefinition()).getInput(),
                    ((ServiceDefinition) service.getMeta().getDefinition()).getOutput()));
        } else {
            return Response.ok(new ServiceParams());
        }
    }

    private static Response<Object> toResponse(Object result) {
        Response<Object> response = Response.ok(result);
        ensureResponse(response, result);
//        setHttpStatus(response);
        wrapPermission(response);   // 包装当前服务的数据权限和字段权限规则
        return response;
    }

    @SuppressWarnings("Duplicates")
    private static void ensureResponse(Response<Object> response, Object result) {
        if (result instanceof Response) {
            Response res = (Response) result;

            // 把结果中的err和info返回到外层的Response中
            boolean success = res.isSuccess();
            Response.Info info = res.getInfo();
            Response.Error err = res.getErr();

            if (!success) {
                response.setSuccess(false);
                response.setErr(err);
            }

            if (info != null) {
                response.setInfo(info);
            }

            Map<String, Object> data = new HashMap<>(2);
            data.put(ServiceConst.DATA, res.getData());
            response.setData(data);

        } else if (result instanceof io.terminus.common.api.response.Response) {
            io.terminus.common.api.response.Response res = (io.terminus.common.api.response.Response) result;

            // 把结果中的err和info返回到外层的Response中
            boolean success = res.isSuccess();
            Response.Info info = res.getInfo() != null ? JsonUtil.convert(res.getInfo(), Response.Info.class) : null;
            Response.Error err = res.getErr() != null ? JsonUtil.convert(res.getErr(), Response.Error.class) : null;

            if (!success) {
                response.setSuccess(false);
                response.setErr(err);
            }

            if (info != null) {
                response.setInfo(info);
            }

            Map<String, Object> data = new HashMap<>(2);
            data.put(ServiceConst.DATA, res.getData());
            response.setData(data);

        } else if (result instanceof Map) {
            Map res = (Map) result;

            // 把结果中的err和info返回到外层的Response中
            Boolean success = MapUtil.getValue(ServiceConst.SUCCESS, res, Boolean.class, (true));
            Response.Info info = MapUtil.getValue(ServiceConst.RES_INFO, res, Response.Info.class, (true));
            Response.Error err = MapUtil.getValue(ServiceConst.RES_ERR, res, Response.Error.class, (true));

            if (Boolean.FALSE.equals(success) && err != null && !err.isEmpty()) {
                response.setSuccess(false);
                response.setErr(err);
            }

            if (info != null) {
                response.setInfo(info);
            }

            res.remove(ServiceConst.REQUEST_ID);
            res.remove(ServiceConst.SUCCESS);
            res.remove(ServiceConst.RES_INFO);
            res.remove(ServiceConst.RES_ERR);

            if (res.isEmpty()) {
                response.setData(null);
            }

        } else if (MapUtil.isMap(result)) {
            Map res = MapUtil.toMap(result);

            // 把结果中的err和info返回到外层的Response中
            Boolean success = MapUtil.getValue(ServiceConst.SUCCESS, res, Boolean.class, (true));
            Response.Info info = MapUtil.getValue(ServiceConst.RES_INFO, res, Response.Info.class, (true));
            Response.Error err = MapUtil.getValue(ServiceConst.RES_ERR, res, Response.Error.class, (true));

            if (Boolean.FALSE.equals(success) && err != null && !err.isEmpty()) {
                response.setSuccess(false);
                response.setErr(err);
            }

            if (info != null) {
                response.setInfo(info);
            }

            // 这个情况下，不去除info等字段，因为不知道具体的类型，如果强行convert，会改变值的原有类型
        }
    }

//    private static void setHttpStatus(Response<Object> response) {
//        if (response.isSuccess()) {
//            return;
//        }
//
//        if (RequestContextHolder.getRequestAttributes() instanceof ServletRequestAttributes) {
//            HttpServletResponse httpResponse = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
//            if (httpResponse != null) {
//                HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
//                if (response.getErr() != null && response.getErr().getCode() != null) {
//                    httpStatus = Arrays.stream(ErrorType.values()).filter(e -> e.getCode().equals(response.getErr().getCode()))
//                            .findFirst().orElse(ErrorType.SERVER_ERROR).getHttpCode();
//                }
//                httpResponse.setStatus(httpStatus.value());
//            }
//        }
//
//    }

    /**
     * 包装服务的数据权限和字段权限规则
     */
    private static void wrapPermission(final Response<Object> response) {
        Optional.ofNullable(TrantorContext.getContext()).ifPresent(context -> {
            response.setDataPermission(context.getDataPermission());
            response.setFieldPermission(context.getFieldPermission());
        });
    }

    /**
     * 清除上下文中的权限信息
     */
    private static void clearPermissionInContext() {
        Optional.ofNullable(TrantorContext.getContext()).ifPresent(context -> {
            context.setDataPermission(null);
            context.setFieldPermission(null);
        });
    }
}
