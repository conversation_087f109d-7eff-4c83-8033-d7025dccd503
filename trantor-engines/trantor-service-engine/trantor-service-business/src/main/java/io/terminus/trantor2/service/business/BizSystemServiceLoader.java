package io.terminus.trantor2.service.business;

import io.terminus.trantor2.service.engine.loader.SystemServiceLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * BizSystemServiceLoader
 *
 * <AUTHOR> Created on 2023/3/20 11:23
 * @see SystemServiceLoader
 */
@Slf4j
public class BizSystemServiceLoader extends SystemServiceLoader {

    public BizSystemServiceLoader() {
        super(BizSystemService.class);
    }

    @Override
    public int order() {
        return super.order() + 1;
    }
}
