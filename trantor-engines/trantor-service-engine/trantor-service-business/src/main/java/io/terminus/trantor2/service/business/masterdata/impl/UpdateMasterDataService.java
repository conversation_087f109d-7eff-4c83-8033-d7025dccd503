package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.business.BizServiceExecuteException;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.sysservice.AbstractSystemServiceImpl;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;
import io.terminus.trantor2.service.engine.impl.sysservice.UpdateDataByIdService;
import io.terminus.trantor2.service.engine.impl.sysservice.annotation.Validation;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * UpdateMasterDataService
 *
 * <AUTHOR> Created on 2023/3/22 15:35
 */
@Validation(skipFields = "status")
@RequiredArgsConstructor
@AutoService(BizSystemService.class)
public class UpdateMasterDataService extends AbstractSystemServiceImpl implements BizSystemService {

    public static final String KEY = "SYS_MasterData_UpdateDataService";

    private final UpdateDataByIdService updateDataByIdService;

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)更新主数据服务";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(MODEL_REQUEST);
    }

    @Override
    public void validateRequest(SystemServiceExecContext context) {
        super.validateRequest(context);
        super.validateRequest(VariableKey.ID, context.getFromRequest(VariableKey.ID));
    }

    @SuppressWarnings({"Duplicates"})
    @Override
    protected void execute(SystemServiceExecContext context) {
        Object id = context.getFromRequest(VariableKey.ID);
        // 查询数据
        QueryModel queryModel = ModelDataProcessHelper.buildIdCondition(context, false);
        Map<String, Object> masterData = context.getServiceEngine().getModelDataRepository().findOne(queryModel);
        if (masterData == null || masterData.isEmpty()) {
            throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_SAVE_OF_DATA_EMPTY, new Object[]{context.getModelKey(), id});
        }

//        if (hasStatusField(context.getModelKey(), context)) {
//            Object status = masterData.get(VariableKey.STATUS);
//            if (Objects.isNull(status)) {
//                throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_SAVE_OF_STATUS_EMPTY, new Object[]{context.getModelKey()});
//            }
//
//            final Status oldStatus;
//            if (status instanceof List) {
//                oldStatus = Status.of(((List) status).get(0).toString());
//            } else {
//                oldStatus = Status.of(status.toString());
//            }
//
//            validateStatus(oldStatus, context);
//        }

        // 更新数据
        updateDataByIdService.invoke(context.newArguments(context.getRequest()));
    }

//    protected void validateStatus(Status oldStatus, SystemServiceExecContext context) {
//        switch (oldStatus) {
//            case DEAFT:
//            case UNENABLED:
//            case DISENABLED:
//                break;
//            default:
//                throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_SAVE_OF_STATUS_INCORRECT, new Object[]{context.getModelKey(), oldStatus.getDesc()});
//        }
//    }
}
