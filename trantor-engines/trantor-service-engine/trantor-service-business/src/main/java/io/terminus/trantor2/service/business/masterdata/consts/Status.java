package io.terminus.trantor2.service.business.masterdata.consts;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 主数据状态枚举，这是约定的，只能是这些值
 *
 * <AUTHOR> Created on 2023/3/20 10:21
 */
@Getter
@AllArgsConstructor
public enum Status {

    DRAFT("草稿态"),
    INACTIVE("未启用"),
    ENABLED("已启用"),
    DISABLED("已停用"),
    DELETED("已删除"),

    // 以下是老的状态，兼容老的数据，不再使用
    @Deprecated
    DEAFT("草稿态"),
    @Deprecated
    UNENABLED("未启用"),
    @Deprecated
    DISENABLED("已停用"),
    ;

    private final String desc;

    public static Status of(String name) {
        Status status = Arrays.stream(Status.values()).filter(e -> e.name().equals(name)).findFirst().orElse(null);
        if (status == null) {
            throw new ServiceException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_SUPPORT_STATUS_ENUM, new Object[]{name});
        }
        return status;
    }
}
