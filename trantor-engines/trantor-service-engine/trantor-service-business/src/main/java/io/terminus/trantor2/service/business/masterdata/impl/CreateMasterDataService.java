package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import com.google.common.collect.Lists;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.sysservice.AbstractSystemServiceImpl;
import io.terminus.trantor2.service.engine.impl.sysservice.CreateDataService;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;
import io.terminus.trantor2.service.engine.impl.sysservice.annotation.Validation;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceResult;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * CreateMasterDataService
 *
 * <AUTHOR> Created on 2023/3/22 15:35
 */
@RequiredArgsConstructor
@Validation(skipFields = "status")
@AutoService(BizSystemService.class)
public class CreateMasterDataService extends AbstractSystemServiceImpl implements BizSystemService {

    public static final String KEY = "SYS_MasterData_CreateDataService";

    private final CreateDataService createDataService;

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)新增主数据服务";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(MODEL_REQUEST);
        definition.addOutput(MODEL_DATA_OUTPUT);
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        fillRequest(context.getRequest(), context.getModelKey(), context);
        SystemServiceResult result = createDataService.invoke(context.newArguments(context.getRequest()));
        context.setResult(result.getData());
    }

    private void fillRequest(Map<String, Object> data, String modelKey, SystemServiceExecContext context) {
        data.put(VariableKey.ID, null);
        if (hasStatusField(modelKey, context)) {
            if (isStatusMultiSelect(modelKey, context)) {
                if (isNewStatusEnabled()) {
                    data.put(VariableKey.STATUS, Lists.newArrayList(Status.INACTIVE.name()));
                } else {
                    data.put(VariableKey.STATUS, Lists.newArrayList(Status.UNENABLED.name()));
                }
            } else {
                if (isNewStatusEnabled()) {
                    data.put(VariableKey.STATUS, Status.INACTIVE.name());
                } else {
                    data.put(VariableKey.STATUS, Status.UNENABLED.name());
                }
            }
        }
    }
}
