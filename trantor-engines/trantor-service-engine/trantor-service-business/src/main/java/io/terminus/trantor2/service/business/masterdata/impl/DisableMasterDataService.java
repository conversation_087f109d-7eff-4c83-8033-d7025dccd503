package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.business.BizServiceExecuteException;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;

/**
 * EnableMasterDataService
 *
 * <AUTHOR> Created on 2023/3/20 09:49
 */
@AutoService(BizSystemService.class)
public class DisableMasterDataService extends UpdateStatusService {

    public static final String KEY = "SYS_MasterData_DisableDataService";

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)停用主数据服务";
    }

    @Override
    protected Status getStatus() {
        if (isNewStatusEnabled()) {
            return Status.DISABLED;
        } else {
            return Status.DISENABLED;
        }
    }

    @Override
    protected void validate(Status oldStatus, SystemServiceExecContext context) {
        if (!Status.ENABLED.equals(oldStatus)) {
            throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_DISABLE, new Object[]{context.getModelKey(), oldStatus.getDesc()});
        }
    }
}
