package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.sysservice.AbstractSystemServiceImpl;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;
import io.terminus.trantor2.service.engine.impl.sysservice.annotation.Validation;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceResult;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * CreateMasterDataService
 *
 * <AUTHOR> Created on 2023/3/22 15:35
 */
@Validation(skipFields = "status")
@RequiredArgsConstructor
@AutoService(BizSystemService.class)
public class SaveAndEnableMasterDataService extends AbstractSystemServiceImpl implements BizSystemService {

    public static final String KEY = "SYS_MasterData_SaveAndEnableDataService";

    private final UpdateMasterDataService updateMasterDataService;
    private final CreateAndEnableMasterDataService createAndEnableMasterDataService;

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(MODEL_REQUEST);
        definition.addOutput(MODEL_DATA_OUTPUT);
    }

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)保存并启用主数据服务";
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        Object id = context.getFromRequest(VariableKey.ID);
        if (id != null) {
            // 更新数据
            updateMasterDataService.invoke(context.newArguments(context.getRequest()));
            // 模型会在更新的时候，把ID移除掉，导致返回到前端的还是没有ID
            Map<String, Object> request = context.getRequest();
            request.put(VariableKey.ID, id);
            context.setResult(request);
        } else {
            // 新增数据
            SystemServiceResult result = createAndEnableMasterDataService.invoke(context.newArguments(context.getRequest()));
            context.setResult(result.getData());
        }
    }
}
