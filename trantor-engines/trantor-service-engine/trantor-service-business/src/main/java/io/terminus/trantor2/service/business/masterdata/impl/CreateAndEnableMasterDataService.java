package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import com.google.common.collect.Lists;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.sysservice.AbstractSystemServiceImpl;
import io.terminus.trantor2.service.engine.impl.sysservice.CreateDataService;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;
import io.terminus.trantor2.service.engine.impl.sysservice.annotation.Validation;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceResult;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * CreateMasterDataService
 *
 * <AUTHOR> Created on 2023/3/22 15:35
 */
@Validation(skipFields = "status")
@AutoService(BizSystemService.class)
public class CreateAndEnableMasterDataService extends AbstractSystemServiceImpl implements BizSystemService {

    public static final String KEY = "SYS_MasterData_CreateAndEnableDataService";

    @Resource
    private CreateDataService createDataService;

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)新增并启用主数据服务";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(MODEL_REQUEST);
        definition.addOutput(MODEL_DATA_OUTPUT);
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        fillRequest(context.getRequest(), context.getModelKey(), context);
        SystemServiceResult result = createDataService.invoke(context.newArguments(context.getRequest()));
        context.setResult(result.getData());
    }

    private void fillRequest(Map<String, Object> data, String modelKey, SystemServiceExecContext context) {
        data.put(VariableKey.ID, null);
        if (hasStatusField(modelKey, context)) {
            if (isStatusMultiSelect(modelKey, context)) {
                data.put(VariableKey.STATUS, Lists.newArrayList(Status.ENABLED.name()));
            } else {
                data.put(VariableKey.STATUS, Status.ENABLED.name());
            }
        }
    }
}
