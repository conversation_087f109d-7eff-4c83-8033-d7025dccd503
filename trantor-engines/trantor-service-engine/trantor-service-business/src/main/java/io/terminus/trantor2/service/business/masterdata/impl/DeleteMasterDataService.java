package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.business.BizServiceExecuteException;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.impl.sysservice.DeleteDataByIdService;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;
import lombok.RequiredArgsConstructor;

/**
 * EnableMasterDataService
 *
 * <AUTHOR> Created on 2023/3/20 09:49
 */
@RequiredArgsConstructor
@AutoService(BizSystemService.class)
public class DeleteMasterDataService extends UpdateStatusService {

    public static final String KEY = "SYS_MasterData_DeleteDataService";

    private final DeleteDataByIdService deleteDataByIdService;

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)删除主数据服务";
    }

    @Override
    protected Status getStatus() {
        return Status.DELETED;
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        if (hasStatusField(context.getModelKey(), context)) {
            super.execute(context);
        } else {
            Arguments createParam = context.newArguments(context.getRequest());
            deleteDataByIdService.invoke(createParam);
        }
    }

    @Override
    protected void validate(Status oldStatus, SystemServiceExecContext context) {
        switch (oldStatus) {
            case DRAFT:
            case DEAFT:
            case INACTIVE:
            case UNENABLED:
                break;
            default:
                throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_DELETE, new Object[]{context.getModelKey(), oldStatus.getDesc()});
        }
    }
}
