package io.terminus.trantor2.service.business.masterdata.impl;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.business.BizServiceExecuteException;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/21 10:57 上午
 */
public abstract class MultiUpdateStatusService extends UpdateStatusService {

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(IDS_REQUEST);
    }

    @Override
    protected void validateRequest(SystemServiceExecContext context) {
        super.validateRequest(VariableKey.IDS, context.getFromRequest(VariableKey.IDS));
    }

    @SuppressWarnings({"Duplicates"})
    @Override
    protected void execute(SystemServiceExecContext context) {
        QueryModel queryModel = ModelDataProcessHelper.buildIdsCondition(context, false);
        List<Map<String, Object>> masterDataList = context.getServiceEngine().getModelDataRepository().find(queryModel, ServiceConst.MAX_COUNT);

        if (masterDataList == null || masterDataList.isEmpty()) {
            throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_FOUND);
        }

        for (Map<String, Object> masterData : masterDataList) {
            updateStatus(masterData, context);
        }
    }

    protected abstract Status getStatus();

}
