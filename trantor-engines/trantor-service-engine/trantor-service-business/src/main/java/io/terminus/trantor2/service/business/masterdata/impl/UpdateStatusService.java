package io.terminus.trantor2.service.business.masterdata.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.business.BizServiceExecuteException;
import io.terminus.trantor2.service.business.BizSystemService;
import io.terminus.trantor2.service.business.masterdata.consts.Status;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.GenericModel;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.sysservice.AbstractSystemServiceImpl;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;

import java.util.HashMap;
import java.util.Map;

/**
 * UpdateStatusService
 *
 * <AUTHOR> Created on 2023/3/20 10:43
 */
@SuppressWarnings({"Duplicates"})
abstract class UpdateStatusService extends AbstractSystemServiceImpl implements BizSystemService {

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(ID_REQUEST);
    }

    @Override
    protected void validateRequest(SystemServiceExecContext context) {
        super.validateRequest(context);
        super.validateRequest(VariableKey.ID, context.getFromRequest(VariableKey.ID));
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        QueryModel queryModel = ModelDataProcessHelper.buildIdCondition(context, false);
        Map<String, Object> masterData = context.getServiceEngine().getModelDataRepository().findOne(queryModel);

        if (masterData == null || masterData.isEmpty()) {
            throw new BizServiceExecuteException(ErrorType.SERVICE_BIZ_MASTER_DATA_NOT_FOUND);
        }

        updateStatus(masterData, context);
    }

    protected void updateStatus(Map<String, Object> masterData, SystemServiceExecContext context) {
        // 更新状态
        Map<String, Object> updateData = new HashMap<>();
        updateData.put(VariableKey.ID, masterData.get(VariableKey.ID));
        if (isStatusMultiSelect(context.getModelKey(), context)) {
            updateData.put(VariableKey.STATUS, Lists.newArrayList(getStatus().name()));
        } else {
            updateData.put(VariableKey.STATUS, getStatus().name());
        }

        GenericModel genericModel = new GenericModel(context.getTeamId(), context.getModelKey(), updateData);
        QueryModel queryModel = new QueryModel(genericModel.getTeamId(), genericModel.getModelAlias());
        initUpdateDataPermissions(getServiceKey(), context, queryModel, genericModel);
        // 更新
        context.getServiceEngine().getModelDataRepository().updateById(context, genericModel, queryModel);
    }

    protected void validate(Status oldStatus, SystemServiceExecContext context) {
    }

    protected abstract Status getStatus();

}
