package io.terminus.trantor2.service.business;

import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.impl.component.ModelMetaQuery;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemService;
import io.terminus.trantor2.service.engine.impl.sysservice.SystemServiceExecContext;

import java.util.List;

/**
 * BizSystemService
 *
 * <AUTHOR> Created on 2023/3/20 09:47
 */
public interface BizSystemService extends SystemService {

    default boolean hasStatusField(String modelKey, SystemServiceExecContext context) {
        ModelMetaQuery modelMetaQuery = context.getServiceEngine().getModelMetaQuery();
        List<Field> fields = modelMetaQuery.getModelFields(context.getTeamId(), modelKey, false);
        if (fields != null) {
            return fields.stream().anyMatch(f -> VariableKey.STATUS.equalsIgnoreCase(f.getFieldKey()));
        }
        return false;
    }

    default boolean isStatusMultiSelect(String modelKey, SystemServiceExecContext context) {
        return context.getServiceEngine().getModelMetaQuery().isMultiSelect(context.getTeamId(), modelKey, VariableKey.STATUS);
    }
}
