package io.terminus.trantor2.service.business;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;

/**
 * BizServiceExecuteException
 *
 * <AUTHOR> Created on 2023/3/20 10:25
 */
public class BizServiceExecuteException extends ServiceExecuteException {
    private static final long serialVersionUID = 6748523903195718799L;

    public BizServiceExecuteException(ErrorType type) {
        super(type);
    }

    public BizServiceExecuteException(ErrorType type, Object[] params) {
        super(type, params);
    }

    public BizServiceExecuteException(ErrorType type, Object[] params, Throwable e) {
        super(type, params, e);
    }
}
