package io.terminus.trantor2.service.report.gateway.dto;

import lombok.Data;

import java.util.List;

@Data
public class TrantorReportBody {
    private Long teamId;
    private String teamCode;
    private String moduleKey;
    /**
     * Public/Private，默认Private
     */
    private String accessLevel;
    private String serviceKey;
    private String serviceName;
    private String description;
    private String url;
    private String method;
    private List<TrantorField> input;
    private String responseType;
    private List<TrantorField> output;
}
