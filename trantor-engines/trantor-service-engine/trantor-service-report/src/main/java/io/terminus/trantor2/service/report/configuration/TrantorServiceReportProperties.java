package io.terminus.trantor2.service.report.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Getter
@Setter
@ConfigurationProperties(prefix = "terminus.plugin.trantor-service.report")
public class TrantorServiceReportProperties {
    private boolean enabled;
    private Long teamId;
    private String teamCode;
    private String remoteEndpoint;
    private String endpoint;
    private Long timeout;
    private String accessLevel;
    private String moduleKey;
    private List<String> urlPaths;
    private List<String> classNames;
}
