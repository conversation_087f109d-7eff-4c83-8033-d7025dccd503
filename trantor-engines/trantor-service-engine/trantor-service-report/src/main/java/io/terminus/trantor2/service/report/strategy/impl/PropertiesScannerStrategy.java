package io.terminus.trantor2.service.report.strategy.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.trantor2.service.report.configuration.TrantorServiceReportProperties;
import io.terminus.trantor2.service.report.strategy.AbstractScannerStrategy;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.util.List;
import java.util.Map;

public class PropertiesScannerStrategy implements AbstractScannerStrategy {
    protected TrantorServiceReportProperties properties;

    @Override
    public List<Class<?>> scan(ApplicationContext applicationContext) {
        properties = applicationContext.getBean(TrantorServiceReportProperties.class);
        return Lists.newArrayList();
    }

    @Override
    public Map<RequestMappingInfo, HandlerMethod> process(List<Class<?>> classes) {
        return Maps.newHashMap();
    }

    @Override
    public Long getTeamId(Class<?> clazz) {
        return properties.getTeamId();
    }

    @Override
    public String getTeamCode(Class<?> clazz) {
        return properties.getTeamCode();
    }

    @Override
    public String getEndpoint(Class<?> clazz) {
        return properties.getEndpoint();
    }

    @Override
    public String getModuleKey(Class<?> clazz) {
        return properties.getModuleKey();
    }

    @Override
    public String getAccessLevel(Class<?> clazz) {
        return properties.getAccessLevel();
    }
}
