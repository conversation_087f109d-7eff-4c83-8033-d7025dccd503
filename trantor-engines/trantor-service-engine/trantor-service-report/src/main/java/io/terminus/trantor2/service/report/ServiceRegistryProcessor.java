package io.terminus.trantor2.service.report;

import com.google.common.collect.Maps;
import io.terminus.trantor2.service.report.configuration.TrantorServiceReportProperties;
import io.terminus.trantor2.service.report.strategy.AbstractScannerStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "terminus.plugin.trantor-service.report", name = "enabled", havingValue = "true")
public class ServiceRegistryProcessor {
    @Autowired
    private ServiceRegistryReporter reporter;

    @EventListener
    @Async
    public void listenerAppReadyEvent(ApplicationReadyEvent event) {
        Map<RequestMappingInfo, HandlerMethod> methodMapping = Maps.newConcurrentMap();
        List<AbstractScannerStrategy> strategies = new ArrayList<>();
        //  读取配置文件扫描接口映射
        TrantorServiceReportProperties reportProperties = AppContext.getBean(TrantorServiceReportProperties.class);
        if (Objects.nonNull(reportProperties.getClassNames()) && !reportProperties.getClassNames().isEmpty()) {
            // 类配置策略
            strategies.add(AbstractScannerStrategy.getInstance(AbstractScannerStrategy.ScanStrategy.CLASS_PROPERTIES_STRATEGY));
        }
        if (Objects.nonNull(reportProperties.getUrlPaths()) && !reportProperties.getUrlPaths().isEmpty()) {
            // URL路径配置策略
            strategies.add(AbstractScannerStrategy.getInstance(AbstractScannerStrategy.ScanStrategy.URL_PROPERTIES_STRATEGY));
        }
        // 注解扫描策略
        strategies.add(AbstractScannerStrategy.getInstance(AbstractScannerStrategy.ScanStrategy.ANNOTATION_STRATEGY));

        strategies.forEach(scannerStrategy -> {
            List<Class<?>> classes = scannerStrategy.scan(AppContext.getApplicationContext());
            methodMapping.putAll(scannerStrategy.process(classes));
        });

        // TODO 所有方法全都处理完毕之后发送Event事件通知下游逻辑处理接口的调用，发起Http请求逻辑接管自汝止，后续需要代码整理和重构
        if (!methodMapping.isEmpty()) {
            reporter.executePublish(reporter.collectRequestMapping(methodMapping));
        }
    }
}
