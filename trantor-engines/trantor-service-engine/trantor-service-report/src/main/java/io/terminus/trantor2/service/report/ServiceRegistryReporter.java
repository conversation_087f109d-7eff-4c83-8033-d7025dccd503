package io.terminus.trantor2.service.report;

import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Operation;
import io.terminus.trantor2.service.report.dto.RestInfo;
import io.terminus.trantor2.service.report.gateway.TrantorServiceReporter;
import io.terminus.trantor2.service.report.gateway.dto.TrantorReportBody;
import io.terminus.trantor2.service.report.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "terminus.plugin.trantor-service.report", name = "enabled", havingValue = "true")
public class ServiceRegistryReporter {
//    private static final String HTTP_REPORT_URL    = "/api/trantor/service/management/http-report";
//    private static final String LOCK_KEY           = "terminus:trantor2:http-report:lock";
//    private static final String UNBOX_INTEGER_TYPE = "int";
//    private static final String UNBOX_long_TYPE    = "long";
//    private static final String UNBOX_FLOAT_TYPE   = "float";
//    private static final String UNBOX_DOUBLE_TYPE  = "double";
//    private static final String UNBOX_SHORT_TYPE   = "short";
//    private static final String UNBOX_CHAR_TYPE    = "char";
//    private static final String UNBOX_BOOLEAN_TYPE = "boolean";
//    private static final String INTEGER_TYPE       = "java.lang.Integer";
//    private static final String LONG_TYPE          = "java.lang.Long";
//    private static final String FLOAT_TYPE         = "java.lang.Float";
//    private static final String DOUBLE_TYPE        = "java.lang.Double";
//    private static final String SHORT_TYPE         = "java.lang.Short";
//    private static final String CHARACTER_TYPE     = "java.lang.Character";
//    private static final String BOOLEAN_TYPE       = "java.lang.Boolean";
//    private static final String BIG_INTEGER_TYPE   = "java.math.BigInteger";
//    private static final String BIG_DECIMAL_TYPE   = "java.math.BigDecimal";
//    private static final String STRING_TYPE        = "java.lang.String";
//    private static final String DATE_TYPE          = "java.util.Date";
//    private static final String VOID_TYPE          = "java.lang.Void";

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

//    @Autowired
//    private RedissonClient redissonClient;

    @Autowired
    private TrantorServiceReporter trantorServiceReporter;

//    public enum FieldType {
//        /**
//         * 文本
//         */
//        Text,
//        /**
//         * 布尔值
//         */
//        Boolean,
//        /**
//         * 数字
//         */
//        Number,
//        /**
//         * 日期
//         */
//        Date,
//        /**
//         * 枚举
//         */
//        Enum,
//        /**
//         * 数组
//         */
//        Array,
//        /**
//         * 对象
//         */
//        Object,
//    }

//    @Builder
//    public static class Field {
//        private String fieldKey;
//        private String fieldAlias;
//        private String fieldName;
//        private String fieldType;
//    }

//    @Data
//    public static class InvokeRequestParams {
//        private Long teamId;
//        private String moduleKey;
//        private String serviceKey;
//        private String serviceName;
//        private String accessLevel;
//        private String method;
//        private String url;
//        private List<Field> input;
//        private List<Field> output;
//    }

    public List<RestInfo> collectRequestMapping(Map<RequestMappingInfo, HandlerMethod> mapping) {
        List<RestInfo> restInfoList = new ArrayList<>();
        mapping.forEach((requestMappingInfo, handlerMethod) -> {
            if (Objects.nonNull(requestMappingInfo.getPatternsCondition())) {
                for (String pattern : requestMappingInfo.getPatternsCondition().getPatterns()) {
                    for (RequestMethod requestMethod : requestMappingInfo.getMethodsCondition().getMethods()) {
                        String name = null;
                        if (handlerMethod.hasMethodAnnotation(Operation.class)) {
                            Operation apiOperationAnnotation = handlerMethod.getMethodAnnotation(Operation.class);
                            if (Objects.nonNull(apiOperationAnnotation)) {
                                name = apiOperationAnnotation.summary();
                            }
                        }
                        if (StringUtils.isBlank(name) && handlerMethod.hasMethodAnnotation(Operation.class)) {
                            Operation operationAnnotation = handlerMethod.getMethodAnnotation(Operation.class);
                            if (Objects.nonNull(operationAnnotation)) {
                                name = operationAnnotation.summary();
                            }
                        }
                        RestInfo restInfo = WebUtils.getByRequestMethod(name, pattern, handlerMethod, requestMethod);
                        restInfoList.add(restInfo);
                    }
                }
            }
        });
        return restInfoList;
    }

    public void executePublish(List<RestInfo> restInfoList) {
        // TODO 获取配置值，并针对配置值合法性规范做校验
        /*
        TrantorServiceReportProperties reportProperties = AppContext.getBean(TrantorServiceReportProperties.class);
        if(reportProperties.getTeamId() == null) {
            throw new IllegalStateException("trantor-service report team id is null");
        }
        */

        log.info("report task submit,total number={}", restInfoList.size());
        executorService.submit(()->{
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);
            for (RestInfo restInfo : restInfoList) {
                log.info("report begin, {}", restInfo);
                TrantorReportBody trantorReportBody = trantorServiceReporter.convertTrantorBody(restInfo);
                try {
                    trantorServiceReporter.reportRemote(trantorReportBody);
                    successCount.incrementAndGet();
                    log.info("report end, {}", JSON.toJSONString(trantorReportBody));
                } catch (Exception e) {
                    failedCount.incrementAndGet();
                    log.error("report error,{}", JSON.toJSONString(trantorReportBody), e);
                }
            }
            log.info("report task submit,success number={}, failed number={}",successCount.get(),  failedCount.get());
            // 执行完释放掉
            executorService.shutdown();
        });
    }

//    public void executePublish(Map<String, Map<String, InvokeRequestParams>> requestMapping) {
//        // 通过分布式锁控制在集群内就只有一个节点执行上报逻辑
//        RLock lock = redissonClient.getLock(LOCK_KEY);
//        try {
//            if (lock.tryLock()) {
//                RestTemplate restTemplate = new RestTemplate();
//                // 异步线程去执行耗时的上报逻辑
//                executorService.execute(() -> {
//                    // TODO 要改造成单个应用一次性批量上传，通过将上报信息持久化到文件，然后再上传文件的方式或将配置内容序列话为大字段的方式
//                    requestMapping.forEach((clazz, methodMapping) -> {
//                        methodMapping.forEach((methodName, invokeRequestParams) -> {
//                            RequestEntity<InvokeRequestParams> entity = RequestEntity.post(HTTP_REPORT_URL).body(invokeRequestParams);
////                            ResponseEntity<Response<Boolean>> restResponse = restTemplate.exchange(entity, new ParameterizedTypeReference<Response<Boolean>>(){});
////                            if (restResponse.getStatusCode().is2xxSuccessful()) {
////                                Response<Boolean> response = restResponse.getBody();
////                                if (Objects.nonNull(response) && response.isSuccess()) {
////                                } else {
////                                    log.warn("failed");
////                                }
////                            } else {
////                                log.warn("failed");
////                            }
//                        });
//                    });
//                    executorService.shutdown();
//                });
//            }
//        } catch (Exception | Error err) {
//            log.error("execute network request exception", err);
//        } finally {
//            lock.unlock();
//        }
//    }

//    private InvokeRequestParams buildHttpReportParams(long teamId,
//                                                      String endpoint,
//                                                      String moduleKey,
//                                                      String accessLevel,
//                                                      List<Field> input,
//                                                      List<Field> output,
//                                                      Class<?> clazz,
//                                                      Method method) {
//        String urlPrefix = getUrlPrefix(clazz);
//        InvokeRequestParams invokeRequestParams = new InvokeRequestParams();
//        invokeRequestParams.setTeamId(teamId);
//        invokeRequestParams.setModuleKey(moduleKey);
//        invokeRequestParams.setAccessLevel(accessLevel);
//        invokeRequestParams.setInput(input);
//        invokeRequestParams.setOutput(output);
//        if (method.isAnnotationPresent(GetMapping.class)) {
//        } else if (method.isAnnotationPresent(PostMapping.class)) {
//
//        } else if (method.isAnnotationPresent(RequestMapping.class)) {
//
//        }
//        return invokeRequestParams;
//    }

//    private String getUrlPrefix(Class<?> clazz) {
//        Annotation requestMappingAnnotation = clazz.getAnnotation(RequestMapping.class);
//        return null;
//    }

//    /**
//     * 断言字段类型
//     *
//     * @param type
//     * @return
//     */
//    public FieldType assertType(String type) {
//        switch (type) {
//            case UNBOX_INTEGER_TYPE:
//            case UNBOX_long_TYPE:
//            case UNBOX_SHORT_TYPE:
//            case UNBOX_FLOAT_TYPE:
//            case UNBOX_DOUBLE_TYPE:
//            case INTEGER_TYPE:
//            case LONG_TYPE:
//            case SHORT_TYPE:
//            case FLOAT_TYPE:
//            case DOUBLE_TYPE:
//            case BIG_INTEGER_TYPE:
//            case BIG_DECIMAL_TYPE:
//                return FieldType.Number;
//            case UNBOX_BOOLEAN_TYPE:
//            case BOOLEAN_TYPE:
//                return FieldType.Boolean;
//            case UNBOX_CHAR_TYPE:
//            case CHARACTER_TYPE:
//            case STRING_TYPE:
//                return FieldType.Text;
//            case DATE_TYPE:
//                return FieldType.Date;
//            default:
//                return FieldType.Object;
//        }
//    }
//
//    /**
//     * 构建Http接口输入DSL
//     *
//     * @param parameters
//     * @return
//     */
//    private List<Field> buildInput(Parameter[] parameters) {
//        List<Field> fields = new ArrayList<>();
//        for (Parameter parameter : parameters) {
//            String name = parameter.getName();
//            Class<?> type = parameter.getType();
//            Field field = new Field(name, name, name, assertType(type.getTypeName()).name());
//            fields.add(field);
//        }
//        return fields;
//    }

//    /**
//     * 构建Http接口输出DSL
//     *
//     * @param returnType
//     * @return
//     */
//    private List<Field> buildOutput(Type returnType) {
//        List<Field> fields = new ArrayList<>();
//        if (returnType instanceof ParameterizedType) {
//            ParameterizedType parameterizedType = (ParameterizedType) returnType;
//            Type[] typeArguments = parameterizedType.getActualTypeArguments();
//            if (1 == typeArguments.length) {
//                String className = "";
//                if (typeArguments[0] instanceof Class) {
//                    className = typeArguments[0].getTypeName();
//                } else {
//                    Type[] types = ((ParameterizedTypeImpl) typeArguments[0]).getActualTypeArguments();
//                    if (1 == types.length) {
//                        className = types[0].getTypeName();
//                    } else {
//                        log.warn("当前反射规则暂不支持");
//                    }
//                }
//                System.out.println(className);
//                switch (className) {
//                    case UNBOX_INTEGER_TYPE:
//                    case UNBOX_long_TYPE:
//                    case UNBOX_SHORT_TYPE:
//                    case UNBOX_FLOAT_TYPE:
//                    case UNBOX_DOUBLE_TYPE:
//                    case INTEGER_TYPE:
//                    case LONG_TYPE:
//                    case SHORT_TYPE:
//                    case FLOAT_TYPE:
//                    case DOUBLE_TYPE:
//                    case BIG_INTEGER_TYPE:
//                    case BIG_DECIMAL_TYPE:
//                    case BOOLEAN_TYPE:
//                    case UNBOX_CHAR_TYPE:
//                    case CHARACTER_TYPE:
//                    case STRING_TYPE:
//                    case DATE_TYPE:
//                    case VOID_TYPE:
//                        break;
//                    default:
//                        try {
//                            Class<?> clazz = Class.forName(className);
//                            Arrays.stream(clazz.getDeclaredFields())
//                                .filter(declaredField -> !declaredField.getName().equals("serialVersionUID"))
//                                .forEach(declaredField -> {
//                                    String fieldName = declaredField.getName();
//                                    FieldType fieldType = assertType(declaredField.getType().getName());
//                                    Field field = new Field(fieldName, fieldName, fieldName, fieldType.name());
//                                    fields.add(field);
//                                });
//                        } catch (ClassNotFoundException e) {
//                            log.error("", e);
//                        }
//                        break;
//                }
//            } else {
//                log.warn("当前反射规则暂不支持");
//            }
//        }
//        return fields;
//    }
}
