package io.terminus.trantor2.service.report.configuration;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackages = "io.terminus.trantor2.service.report")
@EnableConfigurationProperties(TrantorServiceReportProperties.class)
public class TrantorServiceReportAutoConfiguration {
}
