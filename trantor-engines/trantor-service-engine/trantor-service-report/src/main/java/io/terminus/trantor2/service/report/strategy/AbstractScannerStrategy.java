package io.terminus.trantor2.service.report.strategy;

import com.google.common.collect.Maps;
import io.terminus.trantor2.service.report.AppContext;
import io.terminus.trantor2.service.report.strategy.impl.AnnotationScannerStrategy;
import io.terminus.trantor2.service.report.strategy.impl.ClassPropertiesScannerStrategy;
import io.terminus.trantor2.service.report.strategy.impl.UrlPropertiesScannerStrategy;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.List;
import java.util.Map;

public interface AbstractScannerStrategy {
    enum ScanStrategy {
        ANNOTATION_STRATEGY,
        CLASS_PROPERTIES_STRATEGY,
        URL_PROPERTIES_STRATEGY
    }

    static AbstractScannerStrategy getInstance(ScanStrategy strategy) {
        switch (strategy) {
            case ANNOTATION_STRATEGY:
                return new AnnotationScannerStrategy();
            case CLASS_PROPERTIES_STRATEGY:
                return new ClassPropertiesScannerStrategy();
            case URL_PROPERTIES_STRATEGY:
                return new UrlPropertiesScannerStrategy();
            default:
                return null;
        }
    }

    List<Class<?>> scan(ApplicationContext applicationContext);

    Map<RequestMappingInfo, HandlerMethod> process(List<Class<?>> classes);

    Long getTeamId(Class<?> clazz);

    String getTeamCode(Class<?> clazz);

    String getEndpoint(Class<?> clazz);

    String getModuleKey(Class<?> clazz);

    String getAccessLevel(Class<?> clazz);

    default Map<RequestMappingInfo, HandlerMethod> filterHandlerMethodsWithClassNames(List<String> classNames) {
        Map<RequestMappingInfo, HandlerMethod> filterHandlerMethods = Maps.newHashMap();
        RequestMappingHandlerMapping handlerMapping = AppContext.getApplicationContext().getBean("requestMappingHandlerMapping",
                                                                                                 RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = handlerMapping.getHandlerMethods();
        handlerMethods.forEach((mappingInfo, handlerMethod) -> {
            if (filterClass(handlerMethod.getBeanType().getName(), classNames)) {
                filterHandlerMethods.put(mappingInfo, handlerMethod);
            }
        });
        return filterHandlerMethods;
    }

    default boolean filterClass(String className, List<String> classNames) {
        return classNames.stream()
                         .anyMatch(item -> {
                             boolean match = item.equals(className);
                             // 针对额外的注解由Spring生成的动态代理类进行匹配
                             if (item.contains("$$") || item.contains("$$EnhancerBySpringCGLIB")) {
                                 int index = item.indexOf("$$");
                                 String cglibClassOriginalName = item.substring(0, index);
                                 match = cglibClassOriginalName.equals(className);
                             }
                             return match;
                         });
    }
}
