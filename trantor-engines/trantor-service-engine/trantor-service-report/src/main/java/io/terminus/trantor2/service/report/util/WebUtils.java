package io.terminus.trantor2.service.report.util;

import com.google.common.base.CaseFormat;
import io.terminus.common.api.response.Response;
import io.terminus.trantor2.service.report.dto.Param;
import io.terminus.trantor2.service.report.dto.RestInfo;
import io.terminus.trantor2.service.report.enums.ReportFieldType;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.MethodParameter;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.method.HandlerMethod;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

@Slf4j
public class WebUtils {
    private static final DefaultParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
    private static final Set<Class<?>> NUMBER_TYPES = new HashSet<>();

    static {
        NUMBER_TYPES.add(byte.class);
        NUMBER_TYPES.add(short.class);
        NUMBER_TYPES.add(int.class);
        NUMBER_TYPES.add(long.class);
        NUMBER_TYPES.add(float.class);
        NUMBER_TYPES.add(double.class);
        NUMBER_TYPES.add(Byte.class);
        NUMBER_TYPES.add(Short.class);
        NUMBER_TYPES.add(Integer.class);
        NUMBER_TYPES.add(Long.class);
        NUMBER_TYPES.add(Float.class);
        NUMBER_TYPES.add(Double.class);
        NUMBER_TYPES.add(BigInteger.class);
        NUMBER_TYPES.add(BigDecimal.class);
    }

    public static RestInfo getByRequestMethod(String name, String path, HandlerMethod handlerMethod,
                                              RequestMethod requestMethod) {
        RestInfo restInfo = new RestInfo();
        List<Param> params = new ArrayList<>();
        StringBuilder pathBuilder = new StringBuilder();
        MethodParameter[] methodParameters = handlerMethod.getMethodParameters();
        Method method = handlerMethod.getMethod();
        for (MethodParameter methodParameter : methodParameters) {
            Class<?> parameterClass = methodParameter.getParameterType();
            RequestParam requestParam = methodParameter.getParameterAnnotation(RequestParam.class);
            PathVariable pathVariable = methodParameter.getParameterAnnotation(PathVariable.class);
            RequestBody requestBody = methodParameter.getParameterAnnotation(RequestBody.class);
            String parameterName = methodParameter.getParameterName();
            if (parameterName == null) {
                parameterName = parameterNameDiscoverer.getParameterNames(method)[methodParameter.getParameterIndex()];
            }
            if (requestParam != null) {
                Param param = new Param();
                if (StringUtils.hasText(requestParam.value())) {
                    param.setKey(requestParam.value());
                } else {
                    param.setKey(parameterName);
                }
                param.setParamType(convertDslFieldType(parameterClass));
                params.add(param);
                // 需要兼容Post请求的查询字符串
                if(requestMethod == RequestMethod.POST) {
                    // 如果是基础类型则作为查询字符串拼接在URL中
                    if (pathBuilder.length() > 0) {
                        pathBuilder.append(String.format("&%s={%s}", parameterName, parameterName));
                    } else {
                        pathBuilder.append(String.format("%s={%s}", parameterName, parameterName));
                    }
                }
            } else if (pathVariable != null) {
                Param param = new Param();
                param.setPathVariable(true);
                if (StringUtils.hasText(pathVariable.value())) {
                    param.setKey(pathVariable.value());
                } else {
                    param.setKey(parameterName);
                }
                param.setParamType(convertDslFieldType(parameterClass));
                params.add(param);
            } else if (requestBody != null) {
//                if (restInfo.getPostBodyType() == null) {
//                restInfo.setPostBodyType(parameterClass.getTypeName());
                List<Param> paramsByClassFields = convertComplexObject2Params(parameterClass);
                params.addAll(paramsByClassFields);
//                } else {
//                    log.warn("repeat body,{},method,{}, index={}", path, method.getName(), i);
//                }
            } else {
                if(requestMethod == RequestMethod.GET) {
                    ReportFieldType paramType = convertDslFieldType(parameterClass);
                    Param param = new Param();
                    param.setKey(parameterName);
                    param.setParamType(paramType);
                    if(paramType == ReportFieldType.Object) {
                        List<Param> paramsByClassFields = convertComplexObject2Params(parameterClass);
                        params.addAll(paramsByClassFields);
                    } else {
                        params.add(param);
                    }
                } else if(requestMethod == RequestMethod.POST) {
                    ReportFieldType paramType = convertDslFieldType(parameterClass);
                    switch (paramType) {
                        case Number:
                        case Boolean:
                        case String:
                        case Time:
                        case DateTime:
                        case Void:
                            // 如果是基础类型则作为查询字符串拼接在URL中
                            if (pathBuilder.length() > 0) {
                                pathBuilder.append(String.format("&%s={%s}", parameterName, parameterName));
                            } else {
                                pathBuilder.append(String.format("%s={%s}", parameterName, parameterName));
                            }
                            Param param = new Param();
                            param.setKey(parameterName);
                            param.setParamType(paramType);
                            params.add(param);
                            break;
                        case Object:
                        case Array:
                            // 如果是复杂类型则作为body内容
                            List<Param> paramsByClassFields = convertComplexObject2Params(parameterClass);
                            params.addAll(paramsByClassFields);
                            break;
                        default:
                            log.warn("unknown param field type class");
                            break;
                    }
                } else {
                    log.warn("no use param,{},method,{}, index={}", path, method.getName(), methodParameter.getParameterIndex());
                }
            }

        }
        restInfo.setParams(params);

        String serviceKey = getServiceKey(requestMethod, path);
        restInfo.setKey(serviceKey);
        String serviceName = path + "#" + requestMethod.name();
        if (StringUtils.hasText(name)) {
            restInfo.setName(name + "(" + serviceName + ")");
            restInfo.setDescription(name + "(" + serviceName + ")");
        } else {
            restInfo.setName(serviceName);
            restInfo.setDescription(serviceName);
        }
        if (pathBuilder.length() > 0) {
            restInfo.setPath(path + "?" + pathBuilder);
        } else {
            restInfo.setPath(path);
        }
        restInfo.setRequestMethod(requestMethod.name());

        Type resType;
        if (needUnbox(method)) {
            ParameterizedType genericReturnType = (ParameterizedType) method.getGenericReturnType();
            Type actualTypeArgument = genericReturnType.getActualTypeArguments()[0];
            restInfo.setResType(convertDslFieldType(actualTypeArgument));
            resType = actualTypeArgument;
        } else {
            resType = method.getReturnType();
        }
        ReportFieldType reportResType = convertDslFieldType(resType);
        restInfo.setResType(reportResType);
        if (reportResType == ReportFieldType.Object) {
            Class<?> resClass;
            if (resType instanceof ParameterizedType) {
                resClass = (Class<?>) ((ParameterizedType) resType).getRawType();
            } else {
                resClass = (Class<?>) resType;
            }
            restInfo.setResObjFieldList(convertComplexObject2Params(resClass));
        }
        return restInfo;
    }

    private static boolean needUnbox(Method method) {
        if(Response.class.isAssignableFrom(method.getReturnType())) {
            return true;
        }
        return "Response".equals(method.getReturnType().getSimpleName()) && method.getGenericReturnType() instanceof ParameterizedType;
    }

    private static List<Param> convertComplexObject2Params(Class<?> parameterClass) {
        // map的没法弄
        if (Map.class.isAssignableFrom(parameterClass)) {
            return Collections.emptyList();
        }
        List<Param> params = new ArrayList<>();
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(parameterClass);
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            if(propertyDescriptor.getWriteMethod() != null) {
                Param param = new Param();
                param.setPathVariable(false);
                param.setKey(propertyDescriptor.getName());
                param.setParamType(convertDslFieldType(propertyDescriptor.getPropertyType()));
                params.add(param);
            }
        }
        return params;
    }

    public static String getServiceKey(RequestMethod requestMethod, String originPath) {
        String[] allPathSeq = originPath.split("/");
        List<String> seqList = new ArrayList<>();
        for (String s : allPathSeq) {
            // path变量先忽略吧，不然实在太长了
            if (s.startsWith("{") && s.endsWith("}")) {
                continue;
            }
            if (StringUtils.hasText(s)) {
                String lowerCamel;
                if(s.contains("-")) {
                    lowerCamel = CaseFormat.LOWER_HYPHEN.to(CaseFormat.LOWER_CAMEL, s);
                } else {
                    lowerCamel = s;
                }
                String upperUnderscore = CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, lowerCamel);
                seqList.add(upperUnderscore);
            }
        }
        return Strings.join(seqList, '_') + "_" + requestMethod.name().toUpperCase();
    }

    private static ReportFieldType convertDslFieldType(Type type) {
        if (type instanceof Class) {
            Class<?> paramTypeClass = (Class<?>) type;
            if (isNumber(paramTypeClass)) {
                return ReportFieldType.Number;
            } else if (paramTypeClass == String.class) {
                return ReportFieldType.String;
            } else if (paramTypeClass == Boolean.class || paramTypeClass == boolean.class) {
                return ReportFieldType.Boolean;
            } else if (paramTypeClass == Void.class || paramTypeClass == void.class) {
                return ReportFieldType.Void;
            } else if (paramTypeClass.isArray() || Collection.class.isAssignableFrom(paramTypeClass)) {
                return ReportFieldType.Array;
            } else if (Date.class.isAssignableFrom(paramTypeClass) ||
                       java.sql.Date.class.isAssignableFrom(paramTypeClass) ||
                       LocalDate.class.isAssignableFrom(paramTypeClass) ||
                       LocalDateTime.class.isAssignableFrom(paramTypeClass)) {
                return ReportFieldType.DateTime;
            } else if (LocalTime.class.isAssignableFrom(paramTypeClass)) {
                return ReportFieldType.Time;
            } else {
                return ReportFieldType.Object;
            }
        }
        return ReportFieldType.Object;
    }

    public static boolean isNumber(Class<?> clazz) {
        if (clazz == null) {
            return false;
        } else {
            return NUMBER_TYPES.contains(clazz);
        }
    }
}
