package io.terminus.trantor2.service.report.gateway;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.trantor2.service.report.configuration.TrantorServiceReportProperties;
import io.terminus.trantor2.service.report.dto.Param;
import io.terminus.trantor2.service.report.dto.RestInfo;
import io.terminus.trantor2.service.report.enums.ReportFieldType;
import io.terminus.trantor2.service.report.gateway.dto.TrantorField;
import io.terminus.trantor2.service.report.gateway.dto.TrantorReportBody;
import io.terminus.trantor2.service.report.gateway.dto.TrantorResonse;
import io.terminus.trantor2.service.report.gateway.facade.TrantorServiceReportFacade;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/14 11:46
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(prefix = "terminus.plugin.trantor-service.report", name = "enabled", havingValue = "true")
public class TrantorServiceReporter {
    public static final Long DEFAULT_TIMEOUT = 15L;

    private TrantorServiceReportFacade trantorServiceReportFacade;

    private final TrantorServiceReportProperties trantorServiceReportProperties;

    public Long getDefaultTimeout() {
        // 避免因为网络延迟而导致的无法正常上报的问题，将超时时间作为可配置项，默认统一为15秒
        if (Objects.nonNull(trantorServiceReportFacade) && Objects.nonNull(trantorServiceReportProperties.getTimeout())) {
            return trantorServiceReportProperties.getTimeout();
        } else {
            return DEFAULT_TIMEOUT;
        }
    }

    private TrantorServiceReportFacade reportFacade() {
        if (trantorServiceReportFacade == null) {
            synchronized (TrantorServiceReporter.class) {
                // @formatter:off
                OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder()
                                                                         // .addInterceptor(new BasicAuthInterceptor(props.getUsername(), props.getPassword()))
                                                                         .connectTimeout(getDefaultTimeout(), TimeUnit.SECONDS)
                                                                         .readTimeout(getDefaultTimeout(), TimeUnit.SECONDS);
                OkHttpClient okHttpClient = httpClientBuilder.build();
                ObjectMapper mapper = new ObjectMapper();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                trantorServiceReportFacade = new Retrofit.Builder()
                                                         .client(okHttpClient)
                                                         .baseUrl(trantorServiceReportProperties.getRemoteEndpoint())
                                                         .addConverterFactory(JacksonConverterFactory.create(mapper))
                                                         .build()
                                                         .create(TrantorServiceReportFacade.class);
                // @formatter:on
            }
        }
        return trantorServiceReportFacade;
    }

//    @SneakyThrows
//    public TrantorReportBody report(RestInfo restInfo) {
//        TrantorReportBody trantorReportBody = convertTrantorBody(trantorServiceReportProperties, restInfo);
//
//        // 转化，这里是关键
//        reportRemote(trantorReportBody);
//
//        return trantorReportBody;
//
//    }

    @SneakyThrows
    public void reportRemote(TrantorReportBody trantorReportBody) {
        Call<TrantorResonse> report = reportFacade().httpReport(trantorReportBody);
        Response<TrantorResonse> execute = report.execute();

        if (!execute.isSuccessful()) {
            try (ResponseBody responseBody = execute.errorBody()) {
                if (responseBody == null) {
                    throw new IllegalStateException("Post error,code=" + execute.code());
                }
                throw new IllegalStateException("Post error,code=" + execute.code() + ", errorBody= " + responseBody.string());
            }
        }
        TrantorResonse trantorResonse = execute.body();
        if (trantorResonse == null) {
            throw new IllegalStateException("Post error");
        }
        if (!trantorResonse.isSuccess()) {
            throw new IllegalStateException(trantorResonse.getErrorCode() + ":" + trantorResonse.getErrorMsg());
        }
    }

    public TrantorReportBody convertTrantorBody(RestInfo restInfo) {
        return convertTrantorBody(trantorServiceReportProperties, restInfo);
    }

    private static TrantorReportBody convertTrantorBody(TrantorServiceReportProperties trantorServiceReportProperties, RestInfo restInfo) {
        TrantorReportBody trantorReportBody = new TrantorReportBody();
        if(StringUtils.isNotBlank(trantorServiceReportProperties.getTeamCode())) {
            trantorReportBody.setTeamCode(trantorServiceReportProperties.getTeamCode());
        } else {
            trantorReportBody.setTeamId(trantorServiceReportProperties.getTeamId());
        }
        String accessLevel = "Public";
        if (StringUtils.isNotBlank(trantorServiceReportProperties.getAccessLevel())) {
            accessLevel = trantorServiceReportProperties.getAccessLevel();
        }
        trantorReportBody.setAccessLevel(accessLevel);

        String moduleKey = "sys_common";
        if (StringUtils.isNotBlank(trantorServiceReportProperties.getModuleKey())) {
            moduleKey = trantorServiceReportProperties.getModuleKey();
        }
        trantorReportBody.setModuleKey(moduleKey);

        trantorReportBody.setServiceKey(restInfo.getKey());
        trantorReportBody.setServiceName(restInfo.getName());
        trantorReportBody.setDescription(restInfo.getDescription());
        trantorReportBody.setMethod(restInfo.getRequestMethod());
        trantorReportBody.setUrl(trantorServiceReportProperties.getEndpoint() + restInfo.getPath());

        List<TrantorField> fields = new ArrayList<>();
        for (Param param : restInfo.getParams()) {
            TrantorField trantorField = convertParam2TrantorField(param);

            fields.add(trantorField);

        }
        trantorReportBody.setInput(fields);

        // data
        trantorReportBody.setOutput(getOutput(restInfo));
        trantorReportBody.setResponseType("Object");

        return trantorReportBody;
    }


    private static List<TrantorField> getOutput(RestInfo restInfo) {
        TrantorField data = new TrantorField();
        data.setFieldKey("data");
        data.setFieldName("data");
        data.setFieldAlias("data");
        if (restInfo.getResType() == ReportFieldType.Void) {
            return Collections.emptyList();
        }
        data.setFieldType(convert2TrantorType(restInfo.getResType()));


        List<TrantorField> elements;

        if (restInfo.getResType() == ReportFieldType.Object) {
            elements = restInfo.getResObjFieldList().stream().map(TrantorServiceReporter::convertParam2TrantorField).collect(Collectors.toList());
        } else {
            elements = Collections.emptyList();
        }
        data.setElements(elements);

        return Collections.singletonList(data);
    }

    private static TrantorField convertParam2TrantorField(Param param) {
        TrantorField trantorField = new TrantorField();
        trantorField.setFieldKey(param.getKey());
        trantorField.setFieldAlias(param.getKey());
        trantorField.setFieldName(param.getKey());

        trantorField.setFieldType(convert2TrantorType(param.getParamType()));
        return trantorField;
    }


    private static String convert2TrantorType(ReportFieldType paramType) {
        String trantorFieldType;
        switch (paramType) {
            case Number:
                trantorFieldType = "Number";
                break;
            case Boolean:
                trantorFieldType = "Boolean";
                break;
            case String:
                trantorFieldType = "Text";
                break;
            case Array:
                trantorFieldType = "Array";
                break;
            case Time:
                trantorFieldType = "Time";
                break;
            case DateTime:
                trantorFieldType = "DateTime";
                break;
            case Object:
                trantorFieldType = "Object";
                break;
            case Void:
                trantorFieldType = null;
                break;
            default:
                throw new IllegalArgumentException("unknown paramType:" + paramType);
        }
        return trantorFieldType;
    }
}
