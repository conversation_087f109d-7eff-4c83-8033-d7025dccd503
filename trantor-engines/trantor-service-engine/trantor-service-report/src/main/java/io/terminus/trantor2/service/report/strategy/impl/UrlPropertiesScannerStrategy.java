package io.terminus.trantor2.service.report.strategy.impl;

import com.google.common.collect.Maps;
import io.terminus.trantor2.service.report.AppContext;
import org.springframework.context.ApplicationContext;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UrlPropertiesScannerStrategy extends PropertiesScannerStrategy {
    protected AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public List<Class<?>> scan(ApplicationContext applicationContext) {
        return super.scan(applicationContext);
    }

    @Override
    public Map<RequestMappingInfo, HandlerMethod> process(List<Class<?>> classes) {
        Map<RequestMappingInfo, HandlerMethod> filterHandlerMethods = Maps.newHashMap();
        List<String> urlPaths = properties.getUrlPaths();
        if (Objects.isNull(urlPaths) || urlPaths.isEmpty()) {
            return filterHandlerMethods;
        }
        RequestMappingHandlerMapping handlerMapping = AppContext.getApplicationContext().getBean("requestMappingHandlerMapping",
                                                                                                 RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = handlerMapping.getHandlerMethods();
        handlerMethods.forEach((mappingInfo, handlerMethod) -> {
            if (Objects.nonNull(mappingInfo.getPatternsCondition())) {
                for (String pattern : mappingInfo.getPatternsCondition().getPatterns()) {
                    if (filterPath(pattern, urlPaths)) {
                        filterHandlerMethods.put(mappingInfo, handlerMethod);
                    }
                }
            }
        });
        return filterHandlerMethods;
    }

    private boolean filterPath(String urlPath, List<String> urlPaths) {
        return urlPaths.stream()
                       .anyMatch(item -> pathMatcher.match(item, urlPath));
    }
}
