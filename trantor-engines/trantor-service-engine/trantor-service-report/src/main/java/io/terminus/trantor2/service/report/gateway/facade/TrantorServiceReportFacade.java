package io.terminus.trantor2.service.report.gateway.facade;

import io.terminus.trantor2.service.report.gateway.dto.TrantorReportBody;
import io.terminus.trantor2.service.report.gateway.dto.TrantorResonse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

public interface TrantorServiceReportFacade {
    @Headers({
            "Accept: application/json",
            "trantor2-userId: 1"
    })
    @POST("/api/trantor/service/management/http-report")
    Call<TrantorResonse> httpReport(@Body TrantorReportBody body);
}
