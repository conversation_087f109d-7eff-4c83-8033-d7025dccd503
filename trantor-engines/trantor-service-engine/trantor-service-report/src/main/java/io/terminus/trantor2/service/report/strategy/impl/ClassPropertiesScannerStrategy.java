package io.terminus.trantor2.service.report.strategy.impl;

import com.google.common.collect.Maps;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ClassPropertiesScannerStrategy extends PropertiesScannerStrategy {
    @Override
    public List<Class<?>> scan(ApplicationContext applicationContext) {
        return super.scan(applicationContext);
    }

    @Override
    public Map<RequestMappingInfo, HandlerMethod> process(List<Class<?>> classes) {
        Map<RequestMappingInfo, HandlerMethod> filterHandlerMethods = Maps.newHashMap();
        List<String> classNames = properties.getClassNames();
        if (Objects.isNull(classNames) || classNames.isEmpty()) {
            return filterHandlerMethods;
        }
        filterHandlerMethods = filterHandlerMethodsWithClassNames(classNames);
        return filterHandlerMethods;
    }
}
