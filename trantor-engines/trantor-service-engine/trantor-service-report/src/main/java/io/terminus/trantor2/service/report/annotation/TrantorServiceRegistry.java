package io.terminus.trantor2.service.report.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TrantorServiceRegistry {
    long teamId() default -1L;
    String teamCode() default "platform";
    String moduleKey() default "sys_common";
    String accessLevel() default "Private";
    String endpoint() default "http://127.0.0.1:8080";
    String remoteEndpoint() default "http://127.0.0.1:8080";
    String[] includeMethods() default {};
    String[] excludeMethods() default {};
    Class<?>[] packageClasses() default {};
}
