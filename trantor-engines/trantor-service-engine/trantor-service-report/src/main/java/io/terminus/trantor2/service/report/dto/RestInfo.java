package io.terminus.trantor2.service.report.dto;

import io.terminus.trantor2.service.report.enums.ReportFieldType;
import lombok.Data;

import java.util.List;

@Data
public final class RestInfo {
    private String requestMethod;
    private String path;
    private String key;
    private String name;
    private String description;
//    private String postBodyType;
    private List<Param> params;
    private ReportFieldType resType;

    private List<Param> resObjFieldList;
}
