package io.terminus.trantor2.service.report.strategy.impl;

import com.google.common.collect.Maps;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import io.terminus.trantor2.service.report.strategy.AbstractScannerStrategy;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class AnnotationScannerStrategy implements AbstractScannerStrategy {
    @Override
    public List<Class<?>> scan(ApplicationContext applicationContext) {
        return applicationContext.getBeansWithAnnotation(TrantorServiceRegistry.class)
                                 .entrySet()
                                 .parallelStream()
                                 .map(controller -> controller.getValue().getClass())
                                 .collect(Collectors.toList());
    }

    @Override
    public Map<RequestMappingInfo, HandlerMethod> process(List<Class<?>> classes) {
        Map<RequestMappingInfo, HandlerMethod> filterHandlerMethods = Maps.newHashMap();
        if (Objects.isNull(classes) || classes.isEmpty()) {
            return filterHandlerMethods;
        }
        List<String> classNames = classes.stream()
                                         .map(Class::getTypeName)
                                         .collect(Collectors.toList());
        filterHandlerMethods = filterHandlerMethodsWithClassNames(classNames);
        return filterHandlerMethods;
    }

    @Override
    public Long getTeamId(Class<?> clazz) {
        if (clazz.isAnnotationPresent(TrantorServiceRegistry.class)) {
            return clazz.getAnnotation(TrantorServiceRegistry.class).teamId();
        }
        return null;
    }

    @Override
    public String getTeamCode(Class<?> clazz) {
        if (clazz.isAnnotationPresent(TrantorServiceRegistry.class)) {
            return clazz.getAnnotation(TrantorServiceRegistry.class).teamCode();
        }
        return null;
    }

    @Override
    public String getEndpoint(Class<?> clazz) {
        if (clazz.isAnnotationPresent(TrantorServiceRegistry.class)) {
            return clazz.getAnnotation(TrantorServiceRegistry.class).endpoint();
        }
        return null;
    }

    @Override
    public String getModuleKey(Class<?> clazz) {
        if (clazz.isAnnotationPresent(TrantorServiceRegistry.class)) {
            return clazz.getAnnotation(TrantorServiceRegistry.class).moduleKey();
        }
        return null;
    }

    @Override
    public String getAccessLevel(Class<?> clazz) {
        if (clazz.isAnnotationPresent(TrantorServiceRegistry.class)) {
            return clazz.getAnnotation(TrantorServiceRegistry.class).accessLevel();
        }
        return null;
    }
}
