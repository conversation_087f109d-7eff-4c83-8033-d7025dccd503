package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.openai.core.JsonValue;
import com.openai.models.FunctionDefinition;
import com.openai.models.FunctionParameters;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.llm.tool.LlmToolCallFieldSchema;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工具调用执行器抽象类
 */
public abstract class ToolCallExecution {
    
    /**
     * 构建工具定义
     * 
     * @param tool 工具定义
     * @return ChatCompletionTool 工具描述
     */
    public abstract ChatCompletionTool buildTool(SkillTool tool);
    
    /**
     * 判断是否支持该工具
     * 
     * @param toolName 工具名称
     * @param tool 工具定义
     * @return 是否支持
     */
    public abstract boolean supports(String toolName, SkillTool tool);
    
    /**
     * 执行工具调用
     * 
     * @param tool 工具定义
     * @param toolName 工具名称
     * @param toolArguments 工具参数
     * @return 工具执行结果
     */
    public abstract String execute(SkillTool tool, String toolName, String toolArguments);

    public Map<String, JsonValue> buildToolCallInputSchema(List<Field> fields) {
        Map<String, JsonValue> additionalProperty = new HashMap<>();
        Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
        List<String> required = new ArrayList<>();
        for (Field field : fields) {
            if (Objects.nonNull(field.getRequired()) && field.getRequired()) {
                required.add(field.getFieldKey());
            }
            properties.put(field.getFieldKey(), buildFieldSchema(field, 1));
        }
        additionalProperty.put("type", JsonValue.from("object"));
        additionalProperty.put("properties", JsonValue.from(JsonUtil.toMap(properties)));
        additionalProperty.put("required", JsonValue.from(required));
        additionalProperty.put("additionalProperties", JsonValue.from(false));
        return additionalProperty;
    }

    public LlmToolCallFieldSchema buildFieldSchema(Field field, Integer depth) {
        switch (field.getFieldType()) {
            // @formatter:off
            case Array:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("array")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .items(buildFieldSchema(((ArrayField)field).getElement(), depth + 1))
                                             .build();
            case Object:
            case Pageable:
            case Paging:
            case ConditionGroup:
            case ConditionItems:
                if (Objects.nonNull(((ObjectField)field).getElements()) && !((ObjectField)field).getElements().isEmpty() && depth < 4) {
                    // OpenAI 官方不建议参数层级超过3层，否则模型可能无法准确生成参数
                    Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
                    List<Field> elements = ((ObjectField)field).getElements();
                    for (Field element : elements) {
                        properties.put(element.getFieldKey(), buildFieldSchema(element, depth + 1));
                    }
                    return LlmToolCallFieldSchema.builder()
                                                 .title(field.getFieldKey())
                                                 .type("object")
                                                 .description(StringUtils.defaultString(field.getDescription(), ""))
                                                 .properties(properties)
                                                 .additionalProperties(false)
                                                 .build();
                }
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("object")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .additionalProperties(false)
                                             .build();
            default:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("string")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .build();
            // @formatter:on
        }
    }

    public ChatCompletionTool buildChatCompletionTool(String toolName, String toolDesc, Map<String, JsonValue> additionalProperties) {
        // @formatter:off
        FunctionParameters functionParameters = FunctionParameters.builder()
                                                                  .additionalProperties(additionalProperties)
                                                                  .build();
        FunctionDefinition functionDefinition = FunctionDefinition.builder()
                                                                  .name(toolName)
                                                                  .description(toolDesc)
                                                                  .parameters(functionParameters)
                                                                  .build();
        // @formatter:on
        return ChatCompletionTool.builder().function(functionDefinition).build();
    }
}
