package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.fasterxml.jackson.core.type.TypeReference;
import com.openai.core.JsonValue;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.ClientCapabilities;
import io.modelcontextprotocol.spec.McpSchema.TextContent;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.McpTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * MCP工具执行器
 */
@Slf4j
@Component
public class McpToolExecution extends ToolCallExecution {

    private final AiProperties aiProperties;
    
    public McpToolExecution(AiProperties aiProperties) {
        this.aiProperties = aiProperties;
    }

    @Override
    public ChatCompletionTool buildTool(SkillTool tool) {
        if (!(tool instanceof McpTool mcpTool)) {
            return null;
        }
        
        List<ChatCompletionTool> tools = new ArrayList<>();
        mcpTool.getTools().forEach(subTool -> {
            List<Field> fields = subTool.getInput();
            Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
            log.info("build mcp server: [{}] tool: [{}({})] params json schema properties: {}", 
                    mcpTool.getName(), subTool.getKey(), subTool.getName(), additionalProperty);
            tools.add(buildChatCompletionTool(subTool.getKey(), subTool.getDesc(), additionalProperty));
        });
        
        // 需要注意，这里有点特殊，一个MCP工具可能包含多个子工具，但抽象方法只返回一个工具
        // 暂时返回第一个，实际使用时可能需要调整设计
        return tools.isEmpty() ? null : tools.get(0);
    }
    
    @Override
    public boolean supports(String toolName, SkillTool tool) {
        if (!(tool instanceof McpTool mcpTool)) {
            return false;
        }
        
        for (McpTool.SubTool subTool : mcpTool.getTools()) {
            if (subTool.getName().equals(toolName)) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    public String execute(SkillTool tool, String toolName, String toolArguments) {
        if (!(tool instanceof McpTool mcpTool)) {
            return "";
        }
        
        for (McpTool.SubTool subTool : mcpTool.getTools()) {
            if (subTool.getName().equals(toolName)) {
                log.info("调用MCP工具: mcp server: [{}], tool name: [{}], arguments: {}", 
                        mcpTool.getName(), toolName, toolArguments);
                String invokeResponse = invokeMcpTool(
                        mcpTool.getMcpServerEndpoint(), 
                        mcpTool.getName(), 
                        mcpTool.getMcpServerVersion(), 
                        toolName, 
                        toolArguments
                );
                log.info("调用MCP工具结果: {}", JsonUtil.toNonIndentJson(invokeResponse));
                return invokeResponse;
            }
        }
        
        return "";
    }
    
    /**
     * 调用MCP工具
     */
    private String invokeMcpTool(String mcpServerEndpoint, String mcpServerName, String mcpServerVersion, String toolName, String toolArguments) {
        McpAsyncClient client = null;
        try {
            // 构建MCP异步调用SSE客户端
            client = McpClient.async(HttpClientSseClientTransport.builder(mcpServerEndpoint).build())
                    .requestTimeout(Duration.ofMinutes(30))
                    .initializationTimeout(Duration.ofMinutes(15))
                    .capabilities(ClientCapabilities.builder().roots(false).build())
                    .build();

            client.initialize().block();

            Map<String, Object> args = JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {});
            // 发起调用
            CountDownLatch latch = new CountDownLatch(1);
            StringBuilder result = new StringBuilder();
            client.callTool(new CallToolRequest(toolName, args))
                    .subscribe(callToolResult -> {
                        callToolResult.content().forEach(content -> {
                            if (content instanceof TextContent) {
                                log.info("MCP工具调用结果: {}", ((TextContent) content).text());
                                result.append(((TextContent) content).text());
                                latch.countDown();
                            }
                        });
                    }, error -> {
                        log.error("mcp server: [{}], [{}] call mcp tool failed", mcpServerName, toolName, error);
                        latch.countDown();
                    });

            latch.await();

            return result.toString();
        } catch (Exception e) {
            log.error("调用MCP工具异常: {}", e.getMessage(), e);
            return JsonUtil.toJson(Map.of(
                "error", true,
                "message", "调用MCP工具异常: " + e.getMessage(),
                "type", e.getClass().getSimpleName()
            ));
        } finally {
            if (client != null) {
                try {
                    client.closeGracefully().block();
                } catch (Exception e) {
                    log.error("关闭连接时出错: {}", e.getMessage(), e);
                }
            }
        }
    }
    
    /**
     * 构建调用MCP工具的参数
     */
    public Map<String, Object> buildInvokeMcpArgs(String mcpServerName, String mcpServerVersion, String toolName, String toolArguments) {
        // 当MCP Client SDK 不可用时，可临时使用该方式调用MCP工具
        Map<String, Object> params = new HashMap<>(8);
        params.put("server_name", mcpServerName);
        params.put("server_version", mcpServerVersion);
        params.put("tool_name", toolName);
        params.put("tool_args", JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {
        }));
        return params;
    }
}