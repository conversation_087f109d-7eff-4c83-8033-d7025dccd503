package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.openai.core.JsonValue;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.BodyType;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.llm.util.HttpToolSchemaFieldUtil;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * HTTP工具执行器
 */
@Slf4j
@Component
public class HttpToolExecution extends ToolCallExecution {

    @Override
    public ChatCompletionTool buildTool(SkillTool tool) {
        if (!(tool instanceof HttpTool httpTool)) {
            return null;
        }
        
        List<Field> fields = new ArrayList<>();
        ServiceDefinition metadata = AIContext.getContext().getProgrammableExecutionContext().getMetadata();

        List<Field> headerElements = HttpToolSchemaFieldUtil.parse(metadata, httpTool.getHeaders());
        if (CollectionUtils.isNotEmpty(headerElements)) {
            fields.add(new ObjectField("header", headerElements));
        }

        List<Field> parameterElements = HttpToolSchemaFieldUtil.parse(metadata, httpTool.getParams());
        if (CollectionUtils.isNotEmpty(parameterElements)) {
            fields.add(new ObjectField("parameter", parameterElements));
        }

        if (httpTool.getBodyType().equals(BodyType.VALUE)) {
            fields.add(new ObjectField("body", HttpToolSchemaFieldUtil.getVariableField(metadata, httpTool.getBody())));
        }

        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
        log.info("build http tool: [{}({})] params json schema properties: {}", httpTool.getKey(), httpTool.getName(), additionalProperty);
        return buildChatCompletionTool(httpTool.getKey(), StringUtils.defaultString(httpTool.getDesc(), ""), additionalProperty);
    }
    
    @Override
    public boolean supports(String toolName, SkillTool tool) {
        return tool instanceof HttpTool httpTool && httpTool.getKey().equals(toolName);
    }

    @Override
    public String execute(SkillTool tool, String toolName, String toolArguments) {
        if (!(tool instanceof HttpTool httpTool)) {
            return "";
        }
        
        try {
            log.info("调用Http工具: [{}({})], arguments: {}", toolName, httpTool.getName(), toolArguments);
            String invokeResponse = invokeHttpTool(httpTool, toolArguments);
            log.info("调用Http工具结果: {}", JsonUtil.toNonIndentJson((invokeResponse)));
            return invokeResponse;
        } catch (Exception e) {
            log.error("调用HTTP工具异常: {}", e.getMessage(), e);
            return JsonUtil.toJson(Map.of(
                "error", true,
                "message", "调用HTTP工具异常: " + e.getMessage(),
                "type", e.getClass().getSimpleName()
            ));
        }
    }
    
    /**
     * HTTP调用参数
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HttpInvokeArguments implements Serializable {
        private static final long serialVersionUID = -3329293439221563905L;

        /**
         * HTTP请求头
         */
        private Map<String, String> header;
        
        /**
         * 查询参数
         */
        private Map<String, Object> parameter;
        
        /**
         * 请求体
         */
        private Map<String, Object> body;
    }
    
    /**
     * 调用HTTP工具
     */
    private String invokeHttpTool(HttpTool httpTool, String toolArguments) {
        HttpInvokeArguments arguments = null;
        try {
            arguments = JsonUtil.fromJson(toolArguments, HttpInvokeArguments.class);
        } catch (Exception e) {
            log.warn("解析 HttpInvokeArguments 失败: {}", e.getMessage());
            // 继续使用默认参数
        }

        // 获取请求头，优先使用传入的参数
        Map<String, String> header;
        if (arguments != null && arguments.getHeader() != null && !arguments.getHeader().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            header = getHttpHeader(httpTool.getHeaders());
            header.putAll(arguments.getHeader());
            log.info("使用LLM传入的请求头: {}", arguments.getHeader());
            log.info("混合使用请求头: {}", header);
        } else {
            header = getHttpHeader(httpTool.getHeaders());
        }

        // 获取查询参数，优先使用传入的参数
        Map<String, Object> parameter;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getParameter()) && !arguments.getParameter().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            parameter = ValueFactory.getValueForStringEntry(httpTool.getParams(), AIContext.getContext().getProgrammableExecutionContext());
            parameter.putAll(arguments.getParameter());
            log.info("使用LLM传入的查询参数: {}", arguments.getParameter());
            log.info("混合使用查询参数: {}", parameter);
        } else {
            parameter = ValueFactory.getValueForStringEntry(httpTool.getParams(), AIContext.getContext().getProgrammableExecutionContext());
        }

        Map<String, Object> pathVariables = ValueFactory.getValueForStringEntry(httpTool.getPathVariables(), 
                AIContext.getContext().getProgrammableExecutionContext());
        String url = getHttpUrl(httpTool, pathVariables, parameter);

        // 获取请求体，优先使用传入的参数
        Object bodyValue;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getBody()) && !arguments.getBody().isEmpty()) {
            bodyValue = arguments.getBody();
            log.info("使用LLM传入的请求体: {}", bodyValue);
        } else {
            bodyValue = getHttpBody(httpTool);
        }
        
        // 发起HTTP调用
        log.info("发起Http调用详细参数: [{}({})], header: [{}], parameter: [{}], pathVariables: {}, url: [{}], body: [{}]",
                httpTool.getKey(),
                httpTool.getName(),
                header,
                parameter,
                pathVariables,
                url,
                bodyValue);
                
        String result = null;
        String method = httpTool.getMethod();
        switch (method) {
            case "GET":
                result = HttpClient.INSTANCE.get(url, header, parameter);
                break;
            case "POST":
                result = HttpClient.INSTANCE.post(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "PUT":
                result = HttpClient.INSTANCE.put(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "DELETE":
                result = HttpClient.INSTANCE.delete(url, header, parameter);
                break;
            default:
                log.warn("Unsupported http method {}", method);
                break;
        }
        
        return result;
    }
    
    /**
     * 获取HTTP URL
     */
    private String getHttpUrl(HttpTool httpTool, Map<String, Object> pathVariables, Map<String, Object> parameter) {
        final String url;
        if (httpTool.getUrl().contains("{") && httpTool.getUrl().contains("}")) {
            Map<String, Object> variables = new HashMap<>();
            variables.putAll(pathVariables);
            variables.putAll(parameter);
            url = Placeholder.PLACE_CURLY_BRACES_HOLDER.replaceHolder(httpTool.getUrl(), variables);
        } else {
            url = httpTool.getUrl();
        }
        return url;
    }

    /**
     * 获取HTTP请求头
     */
    private Map<String, String> getHttpHeader(List<StringEntry> headers) {
        final Map<String, String> headerValue = new HashMap<>();

        // 设置在页面上请求头
        if (CollectionUtils.isNotEmpty(headers)) {
            final Map<String, Object> customHeaderValue = ValueFactory.getValueForStringEntry(headers, 
                    AIContext.getContext().getProgrammableExecutionContext());
            for (Map.Entry<String, Object> entry : customHeaderValue.entrySet()) {
                // 判断是否存在，如果不存在，才添加
                if (headerValue.keySet().stream().noneMatch(k -> k.equalsIgnoreCase(entry.getKey()))) {
                    headerValue.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        return headerValue;
    }

    /**
     * 获取HTTP请求体
     */
    private Object getHttpBody(HttpTool httpTool) {
        if (httpTool.getBodyType() == BodyType.JSON) {
            return httpTool.getJsonBody();
        } else {
            return ValueFactory.getValue(null, httpTool.getBody(), AIContext.getContext().getProgrammableExecutionContext());
        }
    }
}