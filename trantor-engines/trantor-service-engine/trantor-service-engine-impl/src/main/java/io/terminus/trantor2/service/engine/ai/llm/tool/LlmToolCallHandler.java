package io.terminus.trantor2.service.engine.ai.llm.tool;

import com.fasterxml.jackson.core.type.TypeReference;
import com.openai.core.JsonValue;
import com.openai.models.FunctionDefinition;
import com.openai.models.FunctionParameters;
import com.openai.models.chat.completions.ChatCompletionAssistantMessageParam;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import com.openai.models.chat.completions.ChatCompletionMessageToolCall;
import com.openai.models.chat.completions.ChatCompletionTool;
import com.openai.models.chat.completions.ChatCompletionToolMessageParam;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.ClientCapabilities;
import io.modelcontextprotocol.spec.McpSchema.TextContent;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.BodyType;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.McpTool;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSession;
import io.terminus.trantor2.service.engine.ai.llm.util.HttpToolSchemaFieldUtil;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

@Component
@Slf4j
@AllArgsConstructor
public class LlmToolCallHandler {
    private final AiProperties aiProperties;
    private final ServiceExecutor serviceExecutor;

    public ChatCompletionTool buildServiceTool(ServiceTool tool) {
        List<Field> fields = tool.getInput();
        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
        log.info("build service tool: [{}({})] params json schema properties: {}", tool.getKey(), tool.getName(), additionalProperty);
        return buildChatCompletionTool(KeyUtil.shortKey(tool.getKey()), tool.getDesc(), additionalProperty);
    }

    public List<ChatCompletionTool> buildMcpTool(McpTool tool) {
        List<ChatCompletionTool> tools = new ArrayList<>();
        tool.getTools().forEach(subTool -> {
            List<Field> fields = subTool.getInput();
            Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
            log.info("build mcp server: [{}] tool: [{}({})] params json schema properties: {}", tool.getName(), subTool.getKey(), subTool.getName(), additionalProperty);
            tools.add(buildChatCompletionTool(subTool.getKey(), subTool.getDesc(), additionalProperty));
        });
        return tools;
    }

    public ChatCompletionTool buildHttpTool(HttpTool tool) {
        List<Field> fields = new ArrayList<>();
        ServiceDefinition metadata = AIContext.getContext().getProgrammableExecutionContext().getMetadata();

        List<Field> headerElements = HttpToolSchemaFieldUtil.parse(metadata, tool.getHeaders());
        if (CollectionUtils.isNotEmpty(headerElements)) {
            fields.add(new ObjectField("header", headerElements));
        }

        List<Field> parameterElements = HttpToolSchemaFieldUtil.parse(metadata, tool.getParams());
        if (CollectionUtils.isNotEmpty(parameterElements)) {
            fields.add(new ObjectField("parameter", parameterElements));
        }

        if (tool.getBodyType().equals(BodyType.VALUE)) {
            fields.add(new ObjectField("body", HttpToolSchemaFieldUtil.getVariableField(metadata, tool.getBody())));
        }

        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
        log.info("build http tool: [{}({})] params json schema properties: {}", tool.getKey(), tool.getName(), additionalProperty);
        return buildChatCompletionTool(tool.getKey(), StringUtils.defaultString(tool.getDesc(), ""), additionalProperty);
    }

    public Map<String, JsonValue> buildToolCallInputSchema(List<Field> fields) {
        Map<String, JsonValue> additionalProperty = new HashMap<>();
        Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
        List<String> required = new ArrayList<>();
        for (Field field : fields) {
            if (Objects.nonNull(field.getRequired()) && field.getRequired()) {
                required.add(field.getFieldKey());
            }
            properties.put(field.getFieldKey(), buildFieldSchema(field, 1));
        }
        additionalProperty.put("type", JsonValue.from("object"));
        additionalProperty.put("properties", JsonValue.from(JsonUtil.toMap(properties)));
        additionalProperty.put("required", JsonValue.from(required));
        additionalProperty.put("additionalProperties", JsonValue.from(false));
        return additionalProperty;
    }

    public LlmToolCallFieldSchema buildFieldSchema(Field field, Integer depth) {
        switch (field.getFieldType()) {
            // @formatter:off
            case Array:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("array")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .items(buildFieldSchema(((ArrayField)field).getElement(), depth + 1))
                                             .build();
            case Object:
            case Pageable:
            case Paging:
            case ConditionGroup:
            case ConditionItems:
                if (Objects.nonNull(((ObjectField)field).getElements()) && !((ObjectField)field).getElements().isEmpty() && depth < 4) {
                    // OpenAI 官方不建议参数层级超过3层，否则模型可能无法准确生成参数
                    Map<String, LlmToolCallFieldSchema> properties = new HashMap<>();
                    List<Field> elements = ((ObjectField)field).getElements();
                    for (Field element : elements) {
                        properties.put(element.getFieldKey(), buildFieldSchema(element, depth + 1));
                    }
                    return LlmToolCallFieldSchema.builder()
                                                 .title(field.getFieldKey())
                                                 .type("object")
                                                 .description(StringUtils.defaultString(field.getDescription(), ""))
                                                 .properties(properties)
                                                 .additionalProperties(false)
                                                 .build();
                }
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("object")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .additionalProperties(false)
                                             .build();
            default:
                return LlmToolCallFieldSchema.builder()
                                             .title(field.getFieldKey())
                                             .type("string")
                                             .description(StringUtils.defaultString(field.getDescription(), ""))
                                             .build();
            // @formatter:on
        }
    }

    public ChatCompletionTool buildChatCompletionTool(String toolName, String toolDesc, Map<String, JsonValue> additionalProperties) {
        // @formatter:off
        FunctionParameters functionParameters = FunctionParameters.builder()
                                                                  .additionalProperties(additionalProperties)
                                                                  .build();
        FunctionDefinition functionDefinition = FunctionDefinition.builder()
                                                                  .name(toolName)
                                                                  .description(toolDesc)
                                                                  .parameters(functionParameters)
                                                                  .build();
        // @formatter:on
        return ChatCompletionTool.builder().function(functionDefinition).build();
    }

    public void attachToolCallMessage(ChatSession chatSession, ChatCompletionCreateParams.Builder builder, List<LlmToolCallMetadata> functionCalls, String auditSource) {
        ChatCompletionAssistantMessageParam.Builder toolCallAssistantMessageBuilder = ChatCompletionAssistantMessageParam.builder();
        functionCalls.forEach(toolCallMetadata -> {
            log.info("attach [{}({})] tool call message, args: {}", toolCallMetadata.getKey(), toolCallMetadata.getName(), toolCallMetadata.getArguments());
            toolCallAssistantMessageBuilder.addToolCall(toolCallMetadata.getToolCallArgumentsMessage());
        });

        builder.addMessage(toolCallAssistantMessageBuilder.build());

        handleToolOutput(functionCalls, auditSource);
        functionCalls.forEach(functionCall -> {
            builder.addMessage(functionCall.getToolCallOutputMessage());
        });
    }

    public void handleToolArguments(List<LlmToolCallMetadata> functionCalls) {
        for (LlmToolCallMetadata llmToolCallMetadata : functionCalls) {
            String toolCallId = llmToolCallMetadata.getCallId();
            String toolName = llmToolCallMetadata.getName();
            String toolArguments = llmToolCallMetadata.getArguments();
            log.info("llm tool call id: {}, tool name: {}, tool arguments: {}", toolCallId, toolName, toolArguments);
            // @formatter:off
            ChatCompletionMessageToolCall toolArgumentsMessage = ChatCompletionMessageToolCall.builder()
                                                                                              .id(toolCallId)
                                                                                              .function(ChatCompletionMessageToolCall.Function.builder()
                                                                                                                                              .name(toolName)
                                                                                                                                              .arguments(toolArguments)
                                                                                                                                              .build())
                                                                                              .build();
            // @formatter:on
            llmToolCallMetadata.setToolCallArgumentsMessage(toolArgumentsMessage);
        }
    }

    public void handleToolOutput(List<LlmToolCallMetadata> functionCalls, String auditSource) {
        for (LlmToolCallMetadata llmToolCallMetadata : functionCalls) {
            String toolCallId = llmToolCallMetadata.getCallId();
            String toolName = llmToolCallMetadata.getName();
            String toolArguments = llmToolCallMetadata.getArguments();

            String toolOutput = invokeTool(toolName, toolArguments, auditSource);
            llmToolCallMetadata.setToolOutput(toolOutput);
            // @formatter:off
            ChatCompletionToolMessageParam toolCallOutputMessage = ChatCompletionToolMessageParam.builder()
                                                                                                 .toolCallId(toolCallId)
                                                                                                 .content(toolOutput)
                                                                                                 .build();
            // @formatter:on
            llmToolCallMetadata.setToolCallOutputMessage(toolCallOutputMessage);
        }
    }

    public String invokeTool(String toolName, String toolArguments, String auditSource) {
        List<SkillTool> tools = AIContext.getContext().loadToolsMapping(auditSource);
        for (SkillTool tool : tools) {
            if (tool instanceof ServiceTool serviceTool) {
                // 编排服务的工具Key是带有模块前缀的，只需要匹配后半段即可，多模块同名的情况暂时不支持
                if (KeyUtil.shortKey(serviceTool.getKey()).equals(toolName)) {
                    Object payload = JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {
                    });
                    log.info("调用编排服务工具: [{}({})], arguments: {}", toolName, serviceTool.getName(), toolArguments);
                    Object invokeResponse = serviceExecutor.execute(tool.getKey(), Arguments.of(TrantorContext.getTeamId(), payload));
                    log.info("调用编排服务结果: {}", JsonUtil.toNonIndentJson((invokeResponse)));
                    return JsonUtil.toJson(invokeResponse);
                }
            } else if (tool instanceof McpTool mcpServer) {
                List<McpTool.SubTool> mcpTools = mcpServer.getTools();
                for (McpTool.SubTool mcpTool : mcpTools) {
                    if (mcpTool.getName().equals(toolName)) {
                        log.info("调用MCP工具: mcp server: [{}], tool name: [{}], arguments: {}", mcpServer.getName(), toolName, toolArguments);
                        String invokeResponse = invokeMcpTool(mcpServer.getMcpServerEndpoint(), mcpServer.getName(), mcpServer.getMcpServerVersion(), toolName, toolArguments);
                        log.info("调用MCP工具结果: {}", JsonUtil.toNonIndentJson((invokeResponse)));
                        return invokeResponse;
                    }
                }
            } else if (tool instanceof HttpTool httpTool) {
                if (httpTool.getKey().equals(toolName)) {
                    log.info("调用Http工具: [{}({})], arguments: {}", toolName, httpTool.getName(), toolArguments);
                    String invokeResponse = invokeHttpTool(httpTool, toolArguments);
                    log.info("调用Http工具结果: {}", JsonUtil.toNonIndentJson((invokeResponse)));
                    return invokeResponse;
                }
            } else {
                log.warn("not support tool type");
            }
        }
        return "";
    }

    public String invokeMcpTool(String mcpServerEndpoint, String mcpServerName, String mcpServerVersion, String toolName, String toolArguments) {
        McpAsyncClient client = null;
        try {
//            String mcpToolExecutionDomain = "http://127.0.0.1:8000";
//            String mcpToolExecutionUrl = Objects.toString(aiProperties.getTAiDomain(), mcpToolExecutionDomain);
//            Map<String, Object> params = buildInvokeMcpArgs(mcpServerName, mcpServerVersion, toolName, toolArguments);
//            return HttpClient.INSTANCE.post(String.format("%s/api/ai/mcp/run", mcpToolExecutionUrl), params);

            // 构建MCP异步调用SSE客户端
            client = McpClient.async(HttpClientSseClientTransport.builder(mcpServerEndpoint).build())
                              .requestTimeout(Duration.ofMinutes(30))
                              .initializationTimeout(Duration.ofMinutes(15))
                              .capabilities(ClientCapabilities.builder().roots(false).build())
                              .build();

            client.initialize().block();

            Map<String, Object> args = JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {});
            // 发起调用
            CountDownLatch latch = new CountDownLatch(1);
            StringBuilder result = new StringBuilder();
            client.callTool(new CallToolRequest(toolName, args))
                  .subscribe(callToolResult-> {
                      callToolResult.content().forEach(content -> {
                          if (content instanceof TextContent) {
                              log.info("MCP工具调用结果: {}", ((TextContent) content).text());
                              result.append(((TextContent) content).text());
                              latch.countDown();
                          }
                      });
                  }, error -> {
                      log.error("mcp server: [{}], [{}] call mcp tool failed", mcpServerName, toolName, error);
                      latch.countDown();
                  });

            latch.await();

            return result.toString();
        } catch (Exception e) {
            log.error("调用MCP工具异常: {}", e.getMessage());
            return "";
        } finally {
            if (client != null) {
                try {
                    client.closeGracefully().block();
                } catch (Exception e) {
                    log.error("关闭连接时出错: {}", e.getMessage());
                }
            }
        }
    }

    public Map<String, Object> buildInvokeMcpArgs(String mcpServerName, String mcpServerVersion, String toolName, String toolArguments) {
        // 当MCP Client SDK 不可用时，可临时使用该方式调用MCP工具
        Map<String, Object> params = new HashMap<>(8);
        params.put("server_name", mcpServerName);
        params.put("server_version", mcpServerVersion);
        params.put("tool_name", toolName);
        params.put("tool_args", JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {
        }));
        return params;
    }

    public String invokeHttpTool(HttpTool httpTool, String toolArguments) {
        HttpInvokeArguments arguments = null;
        try {
            arguments = JsonUtil.fromJson(toolArguments, HttpInvokeArguments.class);
        } catch (Exception e) {
            log.warn("解析 HttpInvokeArguments 失败: {}", e.getMessage());
            // 继续使用默认参数
        }

        // 获取请求头，优先使用传入的参数
        Map<String, String> header;
        if (arguments != null && arguments.getHeader() != null && !arguments.getHeader().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            header = getHttpHeader(httpTool.getHeaders());
            header.putAll(arguments.getHeader());
            log.info("使用LLM传入的请求头: {}", arguments.getHeader());
            log.info("混合使用请求头: {}", header);
        } else {
            header = getHttpHeader(httpTool.getHeaders());
        }

        // 获取查询参数，优先使用传入的参数
        Map<String, Object> parameter;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getParameter()) && !arguments.getParameter().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            parameter = ValueFactory.getValueForStringEntry(httpTool.getParams(), AIContext.getContext().getProgrammableExecutionContext());
            parameter.putAll(arguments.getParameter());
            log.info("使用LLM传入的查询参数: {}", arguments.getParameter());
            log.info("混合使用查询参数: {}", parameter);
        } else {
            parameter = ValueFactory.getValueForStringEntry(httpTool.getParams(), AIContext.getContext().getProgrammableExecutionContext());
        }

        Map<String, Object> pathVariables = ValueFactory.getValueForStringEntry(httpTool.getPathVariables(), AIContext.getContext().getProgrammableExecutionContext());
        String url = getHttpUrl(httpTool, pathVariables, parameter);

        // 获取请求体，优先使用传入的参数
        Object bodyValue;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getBody()) && !arguments.getBody().isEmpty()) {
            bodyValue = arguments.getBody();
            log.info("使用LLM传入的请求体: {}", bodyValue);
        } else {
            bodyValue = getHttpBody(httpTool);
        }
        String result = null;
        // @formatter:off
        log.info("发起Http调用详细参数: [{}({})], header: [{}], parameter: [{}], pathVariables: {}, url: [{}], body: [{}]",
                 httpTool.getKey(),
                 httpTool.getName(),
                 header,
                 parameter,
                 pathVariables,
                 url,
                 bodyValue);
        String method = httpTool.getMethod();
        switch (method) {
            case "GET":
                result = HttpClient.INSTANCE.get(url, header, parameter);
                break;
            case "POST":
                result = HttpClient.INSTANCE.post(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "PUT":
                result = HttpClient.INSTANCE.put(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "DELETE":
                result = HttpClient.INSTANCE.delete(url, header, parameter);
                break;
            default:
                log.warn("Unsupported http method {}", method);
                break;
        }
        // @formatter:off
        return result;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HttpInvokeArguments implements Serializable {
        private static final long serialVersionUID = -3329293439221563905L;

        private Map<String, String> header;
        private Map<String, Object> parameter;
        private Map<String, Object> body;
    }

    private String getHttpUrl(HttpTool httpTool, Map<String,Object> pathVariables, Map<String,Object> parameter) {
        final String url;
        if (httpTool.getUrl().contains("{") && httpTool.getUrl().contains("}")) {
            Map<String, Object> variables = new HashMap<>();
            variables.putAll(pathVariables);
            variables.putAll(parameter);
            url = Placeholder.PLACE_CURLY_BRACES_HOLDER.replaceHolder(httpTool.getUrl(), variables);
        } else {
            url = httpTool.getUrl();
        }
        return url;
    }

    private Map<String, String> getHttpHeader(List<StringEntry> headers) {
        final Map<String, String> headerValue = new HashMap<>();

        // 设置在页面上请求头
        if (CollectionUtils.isNotEmpty(headers)) {
            final Map<String, Object> customHeaderValue = ValueFactory.getValueForStringEntry(headers, AIContext.getContext().getProgrammableExecutionContext());
            for (Map.Entry<String, Object> entry : customHeaderValue.entrySet()) {
                // 判断是否存在，如果不存在，才添加
                if (headerValue.keySet().stream().noneMatch(k -> k.equalsIgnoreCase(entry.getKey()))) {
                    headerValue.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        return headerValue;
    }

    private Object getHttpBody(HttpTool httpTool) {
        if (httpTool.getBodyType() == BodyType.JSON) {
            return httpTool.getJsonBody();
        } else {
            return ValueFactory.getValue(null, httpTool.getBody(), AIContext.getContext().getProgrammableExecutionContext());
        }
    }
}
