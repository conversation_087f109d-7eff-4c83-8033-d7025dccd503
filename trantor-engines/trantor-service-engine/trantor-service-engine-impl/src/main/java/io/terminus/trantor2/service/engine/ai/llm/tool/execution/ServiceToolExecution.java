package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.fasterxml.jackson.core.type.TypeReference;
import com.openai.core.JsonValue;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 服务工具执行器
 */
@Slf4j
@Component
public class ServiceToolExecution extends ToolCallExecution {
    
    private final ServiceExecutor serviceExecutor;
    
    public ServiceToolExecution(ServiceExecutor serviceExecutor) {
        this.serviceExecutor = serviceExecutor;
    }

    @Override
    public ChatCompletionTool buildTool(SkillTool tool) {
        if (!(tool instanceof ServiceTool serviceTool)) {
            return null;
        }
        
        List<Field> fields = serviceTool.getInput();
        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
        log.info("build service tool: [{}({})] params json schema properties: {}", serviceTool.getKey(), serviceTool.getName(), additionalProperty);
        return buildChatCompletionTool(KeyUtil.shortKey(serviceTool.getKey()), serviceTool.getDesc(), additionalProperty);
    }
    
    @Override
    public boolean supports(String toolName, SkillTool tool) {
        return tool instanceof ServiceTool serviceTool && KeyUtil.shortKey(serviceTool.getKey()).equals(toolName);
    }

    @Override
    public String execute(SkillTool tool, String toolName, String toolArguments) {
        if (!(tool instanceof ServiceTool)) {
            return "";
        }
        
        try {
            Object payload = JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {});
            log.info("调用编排服务工具: [{}({})], arguments: {}", toolName, ((ServiceTool) tool).getName(), toolArguments);
            Object invokeResponse = serviceExecutor.execute(tool.getKey(), Arguments.of(TrantorContext.getTeamId(), payload));
            log.info("调用编排服务结果: {}", JsonUtil.toNonIndentJson((invokeResponse)));
            return JsonUtil.toJson(invokeResponse);
        } catch (Exception e) {
            log.error("调用编排服务工具异常: {}", e.getMessage(), e);
            return JsonUtil.toJson(Map.of(
                "error", true,
                "message", "调用编排服务工具异常: " + e.getMessage(),
                "type", e.getClass().getSimpleName()
            ));
        }
    }
}
