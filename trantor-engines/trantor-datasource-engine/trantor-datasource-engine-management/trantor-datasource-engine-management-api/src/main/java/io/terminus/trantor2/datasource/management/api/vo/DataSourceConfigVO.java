package io.terminus.trantor2.datasource.management.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.datasource.model.DataSourceConfigDTO;
import lombok.Data;

import java.util.List;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/9/19 1:50 PM
 **/
@Data
public class DataSourceConfigVO extends DataSourceConfigDTO {
    /**
     * 创建人名
     */
    @Schema(description = "创建人姓名")
    private String createdByUserName;

    /**
     * 更新人名
     */
    @Schema(description = "更新人姓名")
    private String updatedByUserName;

    /**
     * 应用模块
     */
    @Schema(description = "应用模块")
    private List<String> modules;

}
