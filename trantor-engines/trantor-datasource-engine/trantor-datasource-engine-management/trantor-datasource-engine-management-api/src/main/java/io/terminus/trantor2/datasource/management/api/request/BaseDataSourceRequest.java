package io.terminus.trantor2.datasource.management.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.dto.TeamRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Schema(description = "数据源信息查询结构")
public class BaseDataSourceRequest extends TeamRequest {
    private static final long serialVersionUID = 3168804758340869012L;

    /**
     * 数据源名
     */
    @Schema(description = "数据源名")
    private String dataSourceName;

    @Schema(description = "数据源类型")
    private String dbType;
}
