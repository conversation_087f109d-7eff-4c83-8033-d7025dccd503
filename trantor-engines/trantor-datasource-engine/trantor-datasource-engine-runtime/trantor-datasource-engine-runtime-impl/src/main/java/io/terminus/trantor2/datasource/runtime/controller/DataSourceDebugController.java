package io.terminus.trantor2.datasource.runtime.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.datasource.service.DataSourceConfigServiceImpl;
import io.terminus.trantor2.datasource.service.DataSourceDebugService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据源运维接口
 * TODO 目前该controller中的接口已加入iam白名单配置，可直接调用
 * 后续需要通过console触发接口调用，做用户登录校验，并且从iam白名单中删除该controller中对应接口的声明
 * <p>
 * iam白名单配置路径如下：
 * trantor-console-starter#application.yml#iam.web.request-white-list : - /api/trantor/datasource/debug/**
 * trantor-runtime-impl#trantor2-config.yml#iam.web.request-white-list : - /api/trantor/datasource/debug/**
 */
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "数据源运维接口")
public class DataSourceDebugController {
    @Autowired
    private DataSourceConfigServiceImpl dataSourceConfigService;

    @Autowired
    private DataSourceDebugService dataSourceDebugService;

//    /**
//     * todo 清理所有数据源缓存，代码暂时注释，如果后续有需求，需要考虑开放接口安全问题
//     *
//     * @return 成功or失败
//     */
//    @Schema(description = "数据源清理")
//    @PostMapping(value = "api/trantor/datasource/debug/clean")
//    public Response<Void> cleanDataSource() {
//        businessDMLDataSourceManager.clean();
//        return Response.ok();
//    }

    /**
     * 获取当前数据源同模块映射关系
     *
     * @return
     */
    @Operation(summary = "获取当前数据源同模块映射关系")
    @PostMapping(value = "api/trantor/datasource/debug/getModuleDatasourceMapping")
    public Response<Object> updateDataSource() {
        return Response.ok(JsonUtil.toJson(dataSourceConfigService.getModuleDatasourceMap()));
    }

    /**
     * 计算模型数据位于哪个物理库+物理表
     *
     * @return
     */
    @Operation(summary = "计算模型数据位于哪个物理表")
    @PostMapping(value = "api/trantor/datasource/debug/getDataCoordinate")
    public Response<String> getModelDataStoreTable(String teamCode, String modelKey, String fieldName, Object fieldValue) {
        return Response.ok(dataSourceDebugService.getDataCoordinate(teamCode, modelKey, fieldName, fieldValue));
    }
}
