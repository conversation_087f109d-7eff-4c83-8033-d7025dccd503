package io.terminus.trantor2.trigger.management.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.trigger.meta.TriggerMeta;
import io.terminus.trantor2.trigger.service.TriggerManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "触发器管理", description = "console")
@RestController
@RequestMapping("/api/trantor/console/trigger")
@RequiredArgsConstructor
public class TriggerController {

    private final TriggerManageService triggerManageService;

    @PostMapping("/enable")
    @Operation(summary = "启用")
    public Response<Boolean> enable(@RequestBody TriggerMeta trigger) {
        return Response.ok(triggerManageService.enable(trigger));
    }

    @PostMapping("/disable")
    @Operation(summary = "禁用")
    public Response<Boolean> disable(@RequestBody TriggerMeta trigger) {
        return Response.ok(triggerManageService.disable(trigger));
    }

}
