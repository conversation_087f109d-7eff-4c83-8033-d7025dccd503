package io.terminus.trantor2.trigger.management.configure;

import io.terminus.trantor2.properties.TriggerMQProperties;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({TriggerMQProperties.class})
@AutoConfigureOrder
public class TriggerManagementAutoConfiguration {

}
