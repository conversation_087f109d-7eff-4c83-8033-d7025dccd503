package io.terminus.trantor2.trigger.management.service.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.trigger.enums.TriggerStatus;
import io.terminus.trantor2.trigger.enums.TriggerType;
import io.terminus.trantor2.trigger.exception.TriggerException;
import io.terminus.trantor2.trigger.meta.EventDefinitionMeta;
import io.terminus.trantor2.trigger.meta.TriggerMeta;
import io.terminus.trantor2.trigger.repository.EventDefinitionRepo;
import io.terminus.trantor2.trigger.repository.TriggerRepo;
import io.terminus.trantor2.trigger.service.TriggerManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TriggerManageServiceImpl implements TriggerManageService {

    private final TriggerRepo triggerRepo;
    private final EventDefinitionRepo eventDefinitionRepo;

    @Override
    public Boolean enable(TriggerMeta triggerMeta) {
        TriggerMeta exist = triggerRepo.findOneByKey(triggerMeta.getKey(), ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId()))
            .orElseThrow(() -> new TriggerException(ErrorType.BAD_REQUEST, "参数异常"));
        if (exist.isEnable()) {
            return true;
        }
        exist.getResourceProps().setStatus(TriggerStatus.Enable);
        triggerRepo.update(exist, ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId()));
        return true;
    }

    @Override
    public Boolean disable(TriggerMeta triggerMeta) {
        TriggerMeta exist = triggerRepo.findOneByKey(triggerMeta.getKey(), ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId()))
            .orElseThrow(() -> new TriggerException(ErrorType.BAD_REQUEST, "参数异常"));
        if (!exist.isEnable()) {
            return true;
        }
        exist.getResourceProps().setStatus(TriggerStatus.Disable);
        triggerRepo.update(exist, ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId()));
        return true;
    }

    @Override
    public void quickCreate(String agentKey, String agentTriggerKey, String eventKey, String listenerName) {
        String key = String.format("%s_Trigger_%s", agentKey, agentTriggerKey);

        TriggerMeta.TriggerProps props = new TriggerMeta.TriggerProps();
        props.setStatus(TriggerStatus.Init);
        props.setType(TriggerType.Event);
        props.setTopic("topic");
        props.setRelatedServiceKey(agentKey);
        props.setRelatedEventDefKey(eventKey);

        ResourceContext resourceCtx = ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId());
        Optional<TriggerMeta> optional = triggerRepo.findOneByKey(key, resourceCtx);
        if (optional.isEmpty()) {
            TriggerMeta triggerMeta = new TriggerMeta();
            triggerMeta.setKey(key);
            reSetTriggerMeta(triggerMeta, listenerName, eventKey, props);
            triggerRepo.create(triggerMeta, resourceCtx);
            enable(triggerMeta);
        } else {
            TriggerMeta triggerMeta = optional.get();
            reSetTriggerMeta(triggerMeta, listenerName, eventKey, props);
            triggerRepo.update(triggerMeta, resourceCtx);
            enable(triggerMeta);
        }
    }

    private void reSetTriggerMeta(TriggerMeta triggerMeta, String listenerName, String eventKey, TriggerMeta.TriggerProps props) {
        triggerMeta.setName(listenerName);
        triggerMeta.setParentKey(getParentKeyByEventDefinitionMeta(eventKey));
        triggerMeta.setResourceProps(props);
    }

    public String getParentKeyByEventDefinitionMeta(String key) {
        String parentKey = KeyUtil.newKeyUnderModule(KeyUtil.moduleKey(key), KeyUtil.UNGROUP_FOLDER);
        EventDefinitionMeta eventDefinitionMeta = getEventDefinitionMeta(key);
        if (Objects.nonNull(eventDefinitionMeta) && Objects.nonNull(eventDefinitionMeta.getParentKey())) {
            return eventDefinitionMeta.getParentKey();
        } else {
            return parentKey;
        }
    }

    public EventDefinitionMeta getEventDefinitionMeta(String key) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId());
        return eventDefinitionRepo.findOneByKey(key, resourceCtx).orElse(null);
    }
}
