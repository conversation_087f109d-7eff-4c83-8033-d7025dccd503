package io.terminus.trantor2.trigger.meta;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.meta.resource.TeamBaseMeta;
import io.terminus.trantor2.service.dsl.properties.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EventDefinitionMeta extends TeamBaseMeta<EventDefinitionMeta.EventDefinitionProps> {

    @Override
    public MetaType getMetaType() {
        return MetaType.EventDefinition;
    }

    public static EventDefinitionMeta convert(MetaTreeNodeExt node) {
        EventDefinitionMeta meta = new EventDefinitionMeta();
        meta.setId(node.getId());
        meta.setParentKey(node.getParentKey());
        meta.setTeamId(node.getTeamId());
        meta.setTeamCode(node.getTeamCode());
        meta.setKey(node.getKey());
        meta.setName(node.getName());
        EventDefinitionProps moduleProps = JsonUtil.INDENT_NON_EMPTY.getObjectMapper().convertValue(node.getProps(), EventDefinitionProps.class);
        meta.setResourceProps(moduleProps);
        return meta;
    }

    @Data
    public static class EventDefinitionProps implements ResourceProps {

        /**
         * 消息Topic
         */
        private String topic;

        /**
         * 定义事件出参
         */
        private List<Field> params;

        /**
         * 依赖持久化业务模型key，用于业务分库时获取业务数据源
         */
        private String relatedModelKey;

    }
}
