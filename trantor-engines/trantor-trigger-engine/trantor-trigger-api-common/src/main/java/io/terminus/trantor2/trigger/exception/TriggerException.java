package io.terminus.trantor2.trigger.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class TriggerException extends TrantorBizException {

    public TriggerException(ErrorType errorType) {
        super(errorType);
    }

    public TriggerException(ErrorType errorType, String message) {
        super(errorType, message);
    }

    public TriggerException(ErrorType errorType, String message, String innerMsg) {
        super(errorType, message, innerMsg);
    }

    public TriggerException(ErrorType errorType, String message, String innerMsg, Throwable cause) {
        super(errorType, message, innerMsg, cause);
    }

    public TriggerException(ErrorType errorType, String message, Throwable cause) {
        super(errorType, message, cause);
    }

    public TriggerException(ErrorType errorType, Throwable cause) {
        super(errorType, cause);
    }

    @Override
    public String toString() {
        String s = getClass().getName();
        return s + ": " + getCode() + " [ " + getMessage() + " ]";
    }
}
