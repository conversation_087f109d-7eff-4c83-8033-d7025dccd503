package io.terminus.trantor2.trigger.meta;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.meta.resource.TeamBaseMeta;
import io.terminus.trantor2.trigger.enums.TriggerStatus;
import io.terminus.trantor2.trigger.enums.TriggerType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TriggerMeta extends TeamBaseMeta<TriggerMeta.TriggerProps> {

    @Override
    public MetaType getMetaType() {
        return MetaType.Trigger;
    }

    public static TriggerMeta convert(MetaTreeNodeExt node) {
        TriggerMeta meta = new TriggerMeta();
        meta.setId(node.getId());
        meta.setParentKey(node.getParentKey());
        meta.setTeamId(node.getTeamId());
        meta.setTeamCode(node.getTeamCode());
        meta.setKey(node.getKey());
        meta.setName(node.getName());
        TriggerProps props = JsonUtil.INDENT_NON_EMPTY.getObjectMapper().convertValue(node.getProps(), TriggerProps.class);
        meta.setResourceProps(props);
        return meta;
    }

    public boolean isEnable() {
        return TriggerStatus.Enable.equals(this.getResourceProps().getStatus());
    }

    @Data
    public static class TriggerProps implements ResourceProps {

        private TriggerType type;

        private TriggerStatus status;

        private String topic;

        private String relatedEventDefKey;

        private String relatedServiceKey;

    }

}
