package io.terminus.trantor2.scene.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.SceneType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;
import java.util.Optional;

/**
 * @param <C> 场景配置
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SceneDTO<C extends SceneConfig> extends IdDTO<Long> {
    private static final long serialVersionUID = -9209406433799140833L;

    @NotNull(message = "scene key must not be empty or null")
    @Schema(description = "唯一标识", required = true)
    private String key;

    @NotNull(message = "parentKey must not be empty or null")
    @Schema(description = "资源文件夹标识", required = true)
    private String parentKey;

    @NotNull(message = "scene name must not be empty or null")
    @Schema(description = "场景名称", required = true)
    private String name;

    @Schema(description = "场景描述")
    private String description;
    @Schema(description = "所属团队ID")
    private Long teamId;

    private SceneType type;

    @NotNull(message = "scene config must not be null")
    @Schema(description = "场景配置")
    private C sceneConfig;

    @Schema(description = "终端类型")
    private EndpointType endpointType;

    @Nonnull
    public Long getTeamIdOrElseThrow() {
        return Optional.ofNullable(Optional.ofNullable(getTeamId()).orElseGet(TrantorContext::getTeamId)).orElseThrow(
            () -> new ValidationException("unable to get teamId in request and context"));
    }
}
