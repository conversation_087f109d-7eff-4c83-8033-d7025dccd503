package io.terminus.trantor2.scene.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class LightParam {
    private Boolean light;
    private String view;

    public static LightParam notLight() {
        LightParam lightParam = new LightParam();
        lightParam.setLight(false);
        return lightParam;
    }

    public static LightParam ofLightAssignedView(@Nullable String view) {
        LightParam lightParam = new LightParam();
        lightParam.setLight(true);
        lightParam.setView(view);
        return lightParam;
    }

    public static LightParam of(Boolean light, String view) {
        LightParam lightParam = new LightParam();
        lightParam.setLight(light);
        lightParam.setView(view);
        return lightParam;
    }
}
