package io.terminus.trantor2.scene.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class SceneExistException extends TrantorBizException {
    private static final long serialVersionUID = 3671658355854157583L;

    public SceneExistException(Object param) {
        super(ErrorType.SCENE_EXISTED, new Object[]{param});
    }
}
