package io.terminus.trantor2.scene.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class SceneNotFoundException extends TrantorBizException {
    private static final long serialVersionUID = 8728895726388296000L;

    public SceneNotFoundException(Long id) {
        super(ErrorType.SCENE_NOT_FOUND, "scene not found, id:" + id, new Object[]{id});
    }

    public SceneNotFoundException(String key) {
        super(ErrorType.SCENE_NOT_FOUND, "scene not found, key:" + key, new Object[]{key});
    }
}
