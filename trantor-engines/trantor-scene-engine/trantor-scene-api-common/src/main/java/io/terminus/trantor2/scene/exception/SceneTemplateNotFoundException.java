package io.terminus.trantor2.scene.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class SceneTemplateNotFoundException extends TrantorBizException {
    private static final long serialVersionUID = -7847722728686412299L;

    public SceneTemplateNotFoundException(Long id) {
        super(ErrorType.SCENE_TEMPLATE_NOT_FOUND, "scene template not found, id:" + id, new Object[]{id});
    }

    public SceneTemplateNotFoundException(String key) {
        super(ErrorType.SCENE_TEMPLATE_NOT_FOUND, "scene template not found, key:" + key, new Object[]{key});
    }
}
