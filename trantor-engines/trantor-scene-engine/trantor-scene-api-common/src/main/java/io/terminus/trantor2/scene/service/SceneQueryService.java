package io.terminus.trantor2.scene.service;

import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.scene.dto.LightParam;
import io.terminus.trantor2.scene.meta.SceneMeta;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

/**
 * 废弃
 * console 使用 {@link io.terminus.trantor2.scene.service.SceneConsoleQueryService}
 * runtime 使用 {@link io.terminus.trantor2.scene.service.SceneRuntimeQueryService}
 * <AUTHOR>
 */
@Deprecated
public interface SceneQueryService {

    /**
     * 根据 key 集合查找场景
     *
     * @param keys 集合
     * @return 查询结果
     */
    Collection<SceneMeta> findAllByKeys(@NonNull MetaEditAndQueryContext ctx, @NonNull Collection<String> keys);

    /**
     * 根据 key 集合查找场景，视图不包含 schema，一般用于权限树
     *
     * @param keys 集合
     * @return 查询结果
     */

    default Collection<SceneMeta> findAllByKeysWithThinViews(@NonNull MetaEditAndQueryContext ctx, @NonNull Collection<String> keys) {
        return findAllByKeys(ctx, keys);
    }

    /**
     * 用Trantor上下文和场景标识标识查找场景
     *
     * @param key 标识
     * @return 查询结果包含视图
     */
    default Optional<SceneMeta> findByKey(@NonNull String key) {
        return findByKey(key, LightParam.notLight());
    }

    default Optional<SceneMeta> findLightByKey(@NonNull String key, @Nullable String view) {
        return findByKey(key, LightParam.ofLightAssignedView(view));
    }

    default Optional<SceneMeta> findByKey(@NonNull String key, @NonNull LightParam lightParam) {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        return findByKey(key, ctx, Arrays.asList(MetaType.Scene.name(), MetaType.View.name()), lightParam);
    }

    /**
     * 根据标识查找场景
     *
     * @param key     标识
     * @param ctx     上下文
     * @param typesIn 子节点类型包括 {@link MetaType#View}
     */
    default Optional<SceneMeta> findByKey(@NonNull String key, @NonNull MetaEditAndQueryContext ctx, @Nullable Collection<String> typesIn) {
        return findByKey(key, ctx, typesIn, LightParam.notLight());
    }


    /**
     * 根据标识查找场景
     *
     * @param key     标识
     * @param ctx     上下文
     * @param typesIn 子节点类型包括 {@link MetaType#View}
     * @param lightParam   是否为轻量级场景
     */
    Optional<SceneMeta> findByKey(@NonNull String key, @NonNull MetaEditAndQueryContext ctx,
                                  @Nullable Collection<String> typesIn, @NonNull LightParam lightParam);
}
