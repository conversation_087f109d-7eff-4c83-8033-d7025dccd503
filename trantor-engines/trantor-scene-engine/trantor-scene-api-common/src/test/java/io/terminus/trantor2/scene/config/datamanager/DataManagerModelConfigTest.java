package io.terminus.trantor2.scene.config.datamanager;

import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
class DataManagerModelConfigTest {
    @Test
    void getUsedModelAlias() {
        DataManagerModelConfig config = new DataManagerModelConfig();
        Assertions.assertEquals(0, config.getUsedModelAlias().size());
        config.setModelKey("a");
        Assertions.assertTrue(config.getUsedModelAlias().contains("a"));

        Map<ModelRelationTypeEnum, Set<DataManagerModelConfig>> relations = new HashMap<>();
        relations.put(ModelRelationTypeEnum.PARENT_CHILD, Stream.of(new DataManagerModelConfig("b"), new DataManagerModelConfig(null)).collect(Collectors.toSet()));
        relations.put(ModelRelationTypeEnum.LINK, Stream.of(new DataManagerModelConfig("c"), new DataManagerModelConfig(null)).collect(Collectors.toSet()));
        config.setRelations(relations);

        Assertions.assertEquals(3, config.getUsedModelAlias().size());
    }

    @Test
    void getUsedChildModelAlias() {
        DataManagerModelConfig config = new DataManagerModelConfig();
        config.setModelKey("a");
        Map<ModelRelationTypeEnum, Set<DataManagerModelConfig>> relations = new HashMap<>();
        config.setRelations(relations);
        Assertions.assertEquals(0, config.getUsedChildModelAlias().size());

        relations.put(ModelRelationTypeEnum.PARENT_CHILD, Stream.of(new DataManagerModelConfig("b"), new DataManagerModelConfig(null)).collect(Collectors.toSet()));
        relations.put(ModelRelationTypeEnum.LINK, Stream.of(new DataManagerModelConfig("c"), new DataManagerModelConfig(null)).collect(Collectors.toSet()));
        config.setRelations(relations);
        Assertions.assertEquals(1, config.getUsedChildModelAlias().size());
        Assertions.assertTrue(config.getUsedChildModelAlias().contains("b"));
    }
}
