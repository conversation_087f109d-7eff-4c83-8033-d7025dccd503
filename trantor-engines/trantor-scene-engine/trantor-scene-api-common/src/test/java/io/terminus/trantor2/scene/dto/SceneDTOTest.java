package io.terminus.trantor2.scene.dto;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.scene.config.SceneConfig;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class SceneDTOTest {


    @Test
    void getTeamId() {
        SceneDTO<SceneConfig> dto = new SceneDTO<>();
        assertDoesNotThrow(dto::getTeamId);
        assertNull(dto.getTeamId());

        TrantorContext.init();
        TrantorContext.setTeamId(1L);
        assertEquals(1L, dto.getTeamId());
        TrantorContext.clear();

        dto.setTeamId(2L);
        assertEquals(2L, dto.getTeamId());
    }

    @Test
    void getTeamIdOrElseThrow() {
        SceneDTO<SceneConfig> dto = new SceneDTO<>();
        assertThrows(ValidationException.class, dto::getTeamIdOrElseThrow);

        TrantorContext.init();
        TrantorContext.setTeamId(1L);
        assertEquals(1L, dto.getTeamIdOrElseThrow());
        TrantorContext.clear();

        dto.setTeamId(2L);
        assertEquals(2L, dto.getTeamIdOrElseThrow());
    }
}
