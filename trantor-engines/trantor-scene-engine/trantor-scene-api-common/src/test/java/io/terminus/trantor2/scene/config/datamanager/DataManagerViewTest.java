package io.terminus.trantor2.scene.config.datamanager;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.LongNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView.ViewType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
class DataManagerViewTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }
    @Test
    void getType() {
        DataManagerView dataManagerView = new DataManagerView();
        dataManagerView.setType(ViewType.LIST);
        assertEquals(ViewType.LIST, dataManagerView.getType());
        dataManagerView.setType(null);
        dataManagerView.setTitle("list");
        assertEquals(ViewType.LIST, dataManagerView.getType());
        dataManagerView.setTitle("detail");
        assertEquals(ViewType.DETAIL, dataManagerView.getType());
        dataManagerView.setTitle("edit");
        assertEquals(ViewType.FORM, dataManagerView.getType());
        dataManagerView.setTitle("abc");
        assertEquals(ViewType.CUSTOM, dataManagerView.getType());
    }
    @Test
    void testConvert() throws IOException {
        MetaTreeNode metaTreeNode = new MetaTreeNode();
        metaTreeNode.setKey("key");
        metaTreeNode.setName("name");
        String jsonString = "{\"content\": {}}";
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode props = (ObjectNode) objectMapper.readTree(jsonString);
        metaTreeNode.setProps(props);
        DataManagerView view = DataManagerView.convert(metaTreeNode);

        assertEquals("key", view.getKey());
        assertEquals("name", view.getTitle());

        props.replace("buttons", new LongNode(1L));
        metaTreeNode.setProps(props);
        Assertions.assertThrows(RuntimeException.class, () -> DataManagerView.convert(metaTreeNode));

        metaTreeNode.getProps().remove("content");
        metaTreeNode.getProps().remove("buttons");
        view = DataManagerView.convert(metaTreeNode);
        Assertions.assertNull(view.getContent());
    }
}
