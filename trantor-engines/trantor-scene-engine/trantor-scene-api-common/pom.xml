<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-scene-engine</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-scene-api-common</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-lang</artifactId>
        </dependency>

<!--        应该依赖 api-->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-api-common</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-module-engine-api-common</artifactId>
        </dependency>

        <!-- TODO: may not depend on cache -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-application-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>test-container</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
