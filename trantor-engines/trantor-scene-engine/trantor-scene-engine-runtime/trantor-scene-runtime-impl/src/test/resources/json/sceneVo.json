{"name": "场景名", "key": "<PERSON><PERSON><PERSON>", "sceneConfig": {"views": [{"key": "viewKey", "title": "视图名", "i18nConfig": {"i18nKeySet": ["创建", "删除", "删除成功"]}}, {"key": "viewKey2", "title": "视图名2"}], "i18nConfig": {"i18nKeySet": ["创建", "删除", "删除成功"]}}, "usedModels": [{"oid": "f23e8bb7edefddfc2b615ab52a6d9bac128c0f91466e9bbfd09ae19aa0d9d506", "key": "moduleB$bmodel", "name": "模型名", "access": "Private", "id": 3073974, "createdBy": 435397539820229, "createdAt": 1719282937905, "updatedBy": 435397539820229, "updatedAt": 1719283217319, "parentKey": "moduleB$ungroup", "teamId": 1203, "teamCode": "wjxtest", "appId": 3073920, "extensible": false, "searchModel": false, "alias": "moduleB$bmodel", "setting": false, "children": [{"key": "name", "name": "名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "name", "length": 256, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "numn", "name": "数字", "alias": "numn", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "数字", "columnName": "numn", "intLength": 20, "scale": 6, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "enume", "name": "枚举", "alias": "enume", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "枚举", "columnName": "enume", "length": 256, "dictPros": {"multiSelect": false, "properties": null, "dictValues": [{"_row_id_": "OF5Vo2y", "label": "熊猫", "value": "panda"}, {"_row_id_": "K51G0SG", "label": "斑马", "value": "zebra"}, {"_row_id_": "3sp5HbR", "label": "长颈鹿", "value": "giraffe"}]}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationKey": null, "relationType": "LINK", "currentModelAlias": "moduleB$bmodel", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "moduleB$user", "linkModelFieldAlias": null, "sync": false, "relationModelKey": "moduleB$user", "linkModelAlias": null}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationKey": null, "relationType": "LINK", "currentModelAlias": "moduleB$bmodel", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "moduleB$user", "linkModelFieldAlias": null, "sync": false, "relationModelKey": "moduleB$user", "linkModelAlias": null}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "origin_org_id", "name": "所属组织", "alias": "originOrgId", "props": {"fieldType": "NUMBER", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "所属组织", "columnName": "origin_org_id", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}], "metaType": "Model", "props": {"config": {"persist": false, "system": false, "self": false, "selfRelationFieldAlias": null}, "tableName": "bmodel", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "originOrgIdEnabled": true, "orderNumberEnabled": false, "shardingConfig": {"enabled": false, "shardingSuffixLength": 3, "shardingFields": null}, "searchModel": false, "mainFieldAlias": "name"}}]}