package io.terminus.trantor2.scene.runtime.aspect;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.PortalI18nConfig;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import io.terminus.trantor2.test.tool.ResourceHelper;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@DataJpaTest(
        includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = SceneVOAspect.class),
        },
        properties = "spring.flyway.enabled=false"
)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SceneVOAspectTest implements MysqlSpringTest {
    @Autowired
    private SceneVOAspect sceneVOAspect;
    @MockBean
    private I18nRuntimeTarget i18nRuntimeTarget;

    protected static final String team = "team";
    protected static final String portalCode = "portal";
    protected static final Long userId = 1L;
    protected static final String enUS = "en_US";

    @BeforeEach
    void init() {
        TrantorContext.init();
        TrantorContext.setPortalCode(portalCode);
        TrantorContext.setTeamCode(team);
        TrantorContext.setCurrentPortal(Portal.builder()
                .code(portalCode)
                .i18nConfig(new PortalI18nConfig(true, "zh_CN", Arrays.asList(
                        new PortalI18nConfig.PortalI18nLanguage("zh_CN", "中文"),
                        new PortalI18nConfig.PortalI18nLanguage(enUS, "美国英语")
                )))
                .build()
        );
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);

        ModuleMeta moduleMeta = new ModuleMeta();
        moduleMeta.setTeamCode(team);
        moduleMeta.setKey(portalCode);

        TrantorContext.setLang(enUS);

        Map<String, String> resourceMap = new HashMap<>();
        Map<String, String> modelResourceMap = new HashMap<>();
        Map<String, String> dorsResourceMap = new HashMap<>();
        modelResourceMap.put("model.熊猫", "panda");
        modelResourceMap.put("model.斑马", "zebra");
        dorsResourceMap.put("dors.折线图", "line chart");
        resourceMap.put("模型名", "model name");
        resourceMap.put("名称", "name");
        resourceMap.put("视图名", "view name");
        resourceMap.put("视图名2", "view name 2");
        resourceMap.put("场景名", "scene name");
        resourceMap.put("创建", "create");
        resourceMap.put("删除", "delete");
        resourceMap.putAll(modelResourceMap);
        resourceMap.putAll(dorsResourceMap);
        when(i18nRuntimeTarget.findI18nResources(Mockito.anyCollection()))
                .thenReturn(resourceMap);
        when(i18nRuntimeTarget.findI18nResourcesByPrefix("model."))
                .thenReturn(modelResourceMap);
        when(i18nRuntimeTarget.findI18nResourcesByPrefix("dors."))
                .thenReturn(dorsResourceMap);
    }


    @AfterEach
    void tearDown() {
        TrantorContext.clear();
    }


    @Test
    void enhanceI18nDataManagerSceneVO() {
        DataManagerSceneVO sceneVO = ResourceHelper.readValueFromResource(
                ObjectJsonUtil.MAPPER, getClass(), "json/sceneVo.json", DataManagerSceneVO.class
        );

        DataManagerSceneVO.RuntimeDataSceneConfig sceneConfig = sceneVO.getSceneConfig();
        DataManagerSceneVO.RuntimeDataSceneView view = sceneConfig.getViews().get(0);
        DataManagerSceneVO.RuntimeDataSceneView view2 = sceneConfig.getViews().get(1);
        DataStructNode model = sceneVO.getUsedModels().stream().findAny().get();
        assertEquals("场景名", sceneVO.getName());
        assertEquals("视图名", view.getTitle());
        assertEquals("模型名", model.getName());
        sceneVOAspect.enhanceI18nDataManagerSceneVO(sceneVO);

        assertEquals("scene name", sceneVO.getName());
        assertEquals("view name", view.getTitle());
        assertEquals("view name 2", view2.getTitle());
        assertEquals("model name", model.getName());
        assertEquals("create", view.getI18nConfig().getI18nResources().get("创建"));
        assertEquals("delete", view.getI18nConfig().getI18nResources().get("删除"));
        assertEquals("panda", model.getChildren().get(2).getProps().getDictPros().getDictValues().get(0).get("value"));
        assertFalse(view.getI18nConfig().getI18nResources().containsKey("删除成功"));
        assertTrue(view.getI18nConfig().getI18nResources().containsKey("折线图"));
        assertTrue(view2.getI18nConfig().getI18nResources().containsKey("折线图"));
    }

    @Test
    void enhanceI18nLightDataManagerSceneVO() {
        LightDataManagerSceneVO sceneVO = ResourceHelper.readValueFromResource(
                ObjectJsonUtil.MAPPER, getClass(), "json/sceneVo.json", LightDataManagerSceneVO.class
        );

        LightDataManagerSceneVO.LightRuntimeDataSceneConfig sceneConfig = sceneVO.getSceneConfig();
        DataManagerSceneVO.RuntimeDataSceneView view = sceneConfig.getViews().get(0);
        DataManagerSceneVO.RuntimeDataSceneView view2 = sceneConfig.getViews().get(1);
        DataStructNode model = sceneVO.getUsedModels().stream().findAny().get();
        assertEquals("场景名", sceneVO.getName());
        assertEquals("视图名", view.getTitle());
        assertEquals("模型名", model.getName());
        sceneVOAspect.enhanceI18nLightDataManagerSceneVO(sceneVO);

        assertEquals("scene name", sceneVO.getName());
        assertEquals("view name", view.getTitle());
        assertEquals("view name 2", view2.getTitle());
        assertEquals("model name", model.getName());
        assertEquals("create", view.getI18nConfig().getI18nResources().get("创建"));
        assertEquals("delete", view.getI18nConfig().getI18nResources().get("删除"));
        assertEquals("panda", model.getChildren().get(2).getProps().getDictPros().getDictValues().get(0).get("value"));
        assertFalse(view.getI18nConfig().getI18nResources().containsKey("删除成功"));
        assertTrue(view.getI18nConfig().getI18nResources().containsKey("折线图"));
        assertNull(view2.getI18nConfig());
    }

    @Test
    void enhanceI18nView() {
        DataManagerSceneVO sceneVO = ResourceHelper.readValueFromResource(
                ObjectJsonUtil.MAPPER, getClass(), "json/sceneVo.json", DataManagerSceneVO.class
        );
        List<DataManagerSceneVO.RuntimeDataSceneView> views = sceneVO.getSceneConfig().getViews();
        DataManagerSceneVO.RuntimeDataSceneView view = views.get(0);
        sceneVOAspect.enhanceI18nView(view);

        assertEquals("view name", view.getTitle());
        assertEquals("create", view.getI18nConfig().getI18nResources().get("创建"));
        assertEquals("delete", view.getI18nConfig().getI18nResources().get("删除"));
        assertFalse(view.getI18nConfig().getI18nResources().containsKey("删除成功"));
        assertTrue(view.getI18nConfig().getI18nResources().containsKey("折线图"));
    }
}