package io.terminus.trantor2.scene.runtime.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.model.ModelApiWrapper;
import io.terminus.trantor2.scene.runtime.SceneResourceHelper;
import io.terminus.trantor2.scene.runtime.convert.RuntimeSceneMapperImpl;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.service.SceneQueryService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class SceneRuntimeServiceImplTest {

    @Spy
    RuntimeSceneMapperImpl runtimeSceneMapper;

    @Mock
    SceneQueryService sceneQueryService;

    @Mock
    ModelApiWrapper modelApiWrapper;

    @InjectMocks
    DataManagerRuntimeServiceImpl service;

    @Test
    void findSceneMetaByKey() {
        String key = "key";
        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setKey(key);

        when(sceneQueryService.findByKey(Mockito.anyString())).thenReturn(Optional.of(sceneMeta));
        SceneMeta res = service.findSceneMetaByKey(key);
        assertEquals(res.getKey(), key);
    }

    @Test
    void findSceneMetaByKey_throwNotFound() {
        when(sceneQueryService.findByKey(Mockito.anyString())).thenReturn(Optional.empty());
        assertThrows(SceneNotFoundException.class, () -> service.findSceneMetaByKey("key"));
    }

    @Test
    void findSceneVoByKey() throws JsonProcessingException {
        SceneMeta sceneMeta = SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        when(sceneQueryService.findByKey(Mockito.anyString())).thenReturn(Optional.of(sceneMeta));
        when(modelApiWrapper.findByAliasLoosely(Mockito.anyCollection(), Mockito.anyLong())).thenReturn(Collections.emptyMap());

        DataManagerSceneVO vo = service.findSceneVoByKey("world");

        assertEquals("world", vo.getKey());
        assertEquals(0, vo.getUsedModels().size());
    }
}
