package io.terminus.trantor2.scene.runtime;

import io.terminus.trantor2.properties.runtime.ModelRuntimeProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2"})
@EntityScan(basePackages = {"io.terminus.trantor2.scene", "io.terminus.trantor2.meta"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.scene", "io.terminus.trantor2.meta"})
@EnableConfigurationProperties(ModelRuntimeProperties.class)
public class TestApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
