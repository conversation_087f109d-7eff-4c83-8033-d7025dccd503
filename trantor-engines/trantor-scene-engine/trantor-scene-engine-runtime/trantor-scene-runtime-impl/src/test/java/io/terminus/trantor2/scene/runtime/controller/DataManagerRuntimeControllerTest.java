package io.terminus.trantor2.scene.runtime.controller;

import io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DataManagerRuntimeControllerTest {

    @Mock
    private DataManagerRuntimeService service;

    @InjectMocks
    private DataManagerRuntimeController controller;

    @Test
    void getConfig() {
        DataManagerSceneVO vo = new DataManagerSceneVO();
        vo.setKey("key");
        when(service.findSceneVoByKey(Mockito.anyString())).thenReturn(vo);

        DataManagerSceneVO res = controller.getConfig("key");
        assertEquals(vo.getKey(), res.getKey());
    }
}
