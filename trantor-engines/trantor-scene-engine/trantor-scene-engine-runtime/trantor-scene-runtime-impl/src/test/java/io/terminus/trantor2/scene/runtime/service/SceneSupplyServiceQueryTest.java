package io.terminus.trantor2.scene.runtime.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.runtime.SceneResourceHelper;
import io.terminus.trantor2.service.dsl.properties.SelectField;
import io.terminus.trantor2.service.common.component.spi.bean.SceneSupply;
import io.terminus.trantor2.service.common.component.spi.bean.SceneSupplyRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class SceneSupplyServiceQueryTest {

    @Mock
    DataManagerRuntimeServiceImpl sceneService;

    @InjectMocks
    SceneSupplyServiceQuery query;

    @Test
    void query_by_without_SceneKey() {
        SceneSupplyRequest request = new SceneSupplyRequest();
        Assertions.assertNull(query.query(request));
    }

    @Test
    void query_by_without_ViewKey() {
        SceneSupplyRequest request = new SceneSupplyRequest();
        request.setSceneKey("hi");
        Assertions.assertNull(query.query(request));
    }

    @Test
    void query_by_without_modelKey() {
        SceneSupplyRequest request = new SceneSupplyRequest();
        request.setSceneKey("hi");
        request.setViewKey("view");
        Assertions.assertNull(query.query(request));
    }

    @Test
    void query_by_viewKey() throws JsonProcessingException {
        SceneSupplyRequest request = new SceneSupplyRequest();
        request.setSceneKey("world");
        request.setModelKey("model");
        request.setViewKey("view");
        SceneMeta sceneMeta = SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        when(sceneService.findViewByKey(request.getSceneKey())).thenReturn(((DataManagerSceneConfig) sceneMeta.getSceneConfig()).getView("view"));

        SceneSupply sceneSupply = query.query(request);
        Assertions.assertNull(sceneSupply);
    }

    @Test
    void query_by_container_with_selects() throws JsonProcessingException {
        SceneSupplyRequest request = new SceneSupplyRequest();
        request.setSceneKey("world");
        request.setModelKey("main");
        request.setViewKey("view");
        request.setContainerKey("container");
        SceneMeta sceneMeta = SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        Map<String, List<SelectField>> containerSelects = new HashMap<>();
        containerSelects.put("container", Collections.singletonList(new SelectField("id")));
        DataManagerView view = ((DataManagerSceneConfig) sceneMeta.getSceneConfig()).getView("view");
        view.setContainerSelect(containerSelects);

        when(sceneService.findViewByKey(Mockito.anyString())).thenReturn(view);

        SceneSupply sceneSupply = query.query(request);
        Assertions.assertEquals(1, sceneSupply.getSelect().size());

        Assertions.assertEquals("id", sceneSupply.getSelect().get(0).getField());
    }
}
