package io.terminus.trantor2.scene.runtime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.test.tool.ResourceHelper;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
public class SceneResourceHelper {
    private static final String RESOURCE_PATH = "json/";
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerSubtypes(DataManagerSceneConfig.class);
    }

    public static <T> T readValueFromResource(ObjectMapper mapper, @Nonnull String name, Class<T> valueType) throws JsonProcessingException {
        String json = ResourceHelper.getResourceAsString(SceneResourceHelper.class, RESOURCE_PATH + name);
        return mapper.readValue(json, valueType);
    }

    public static <T> T readValueFromResource(@Nonnull String name, Class<T> valueType) throws JsonProcessingException {
        String json = ResourceHelper.getResourceAsString(SceneResourceHelper.class, RESOURCE_PATH + name);
        return mapper.readValue(json, valueType);
    }
}
