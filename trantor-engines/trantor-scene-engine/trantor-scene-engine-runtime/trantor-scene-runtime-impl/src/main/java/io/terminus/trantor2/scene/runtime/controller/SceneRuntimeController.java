package io.terminus.trantor2.scene.runtime.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.scene.runtime.request.SceneBatchRequest;
import io.terminus.trantor2.scene.runtime.request.ViewSummaryRequest;
import io.terminus.trantor2.scene.runtime.service.SceneRuntimeService;
import io.terminus.trantor2.scene.runtime.vo.SceneInfo;
import io.terminus.trantor2.scene.runtime.vo.ViewSummary;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/trantor/portal/scene")
@Tag(name = "场景运行时", description = "portal")
@RequiredArgsConstructor
public class SceneRuntimeController {
    private final SceneRuntimeService sceneRuntimeService;

    @PostMapping("/find-by-model/{modelKey}")
    @Operation(summary = "根据模型反查场景", description = "根据模型反查模型所在项目用了该模型的场景")
    public Collection<SceneInfo> findByModel(@RequestBody(required = false) SceneBatchRequest request, @PathVariable String modelKey) {
        if (request == null) {
            request = new SceneBatchRequest();
        }
        return sceneRuntimeService.findUsedSceneByModel(modelKey, request);
    }


    @PostMapping("/view/summary")
    @Operation(summary = "视图摘要")
    public ViewSummary summaryView(@RequestBody ViewSummaryRequest request) {
        return sceneRuntimeService.summaryView(request);
    }
}
