package io.terminus.trantor2.scene.runtime.service;


import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.runtime.util.ConditionProcessor;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.common.dto.ViewCondition;
import io.terminus.trantor2.service.common.component.spi.SceneSupplyQuery;
import io.terminus.trantor2.service.common.component.spi.bean.SceneSupply;
import io.terminus.trantor2.service.common.component.spi.bean.SceneSupplyRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class SceneSupplyServiceQuery implements SceneSupplyQuery {
    private final DataManagerRuntimeService sceneService;

    @Override
    @Nullable
    @Transactional
    public SceneSupply query(SceneSupplyRequest request) {
        try {
            if (StringUtils.isAnyBlank(request.getViewKey(), request.getModelKey())) {
                return null;
            }
            DataManagerView view = sceneService.findViewByKey(request.getViewKey());
            if (view == null) {
                if (log.isDebugEnabled()) {
                    log.debug("scene supply service query, but view not found by key: {}", request.getViewKey());
                }
                return null;
            }
            SceneSupply sceneSupply = new SceneSupply();
            processSelect(sceneSupply, view, request.getContainerKey());
            processConditionGroup(sceneSupply, view, request.getViewCondition());

            log.debug("scene supply service query: {}", sceneSupply);
            return sceneSupply;
        } catch (Exception e) {
            log.warn("scene supply service query exception: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 处理查询字段
     */
    private void processSelect(@Nonnull SceneSupply sceneSupply, @Nonnull DataManagerView view, @Nonnull String containerKey) {
        if (StringUtils.isBlank(containerKey)) {
            return;
        }
        sceneSupply.setSelect(view.getContainerSelect().get(containerKey));
    }

    /**
     * 处理默认筛选条件
     */
    private void processConditionGroup(@Nonnull SceneSupply sceneSupply, @Nonnull DataManagerView view, @Nullable ViewCondition viewCondition) {
        if (viewCondition == null) {
            return;
        }
        String conditionKey = viewCondition.getConditionKey();
        if (StringUtils.isNotBlank(conditionKey)
                && MapUtils.isNotEmpty(view.getConditionGroups())
                && view.getConditionGroups().containsKey(conditionKey)) {
            ConditionGroup conditionGroup = view.getConditionGroups().get(conditionKey);
            ConditionProcessor.rebuildConditionGroup(viewCondition.getRightValues(), conditionGroup);
            sceneSupply.setConditionGroup(conditionGroup);
        }
    }
}
