package io.terminus.trantor2.scene.runtime.service;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.model.ModelApiWrapper;
import io.terminus.trantor2.scene.runtime.convert.LightRuntimeSceneMapper;
import io.terminus.trantor2.scene.runtime.convert.RuntimeSceneMapper;
import io.terminus.trantor2.scene.runtime.repo.ViewRuntimeRepo;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import io.terminus.trantor2.scene.service.SceneQueryService;
import io.terminus.trantor2.scene.service.SceneRuntimeQueryService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DataManagerRuntimeServiceImpl implements DataManagerRuntimeService {
    private final SceneRuntimeQueryService sceneQueryService;
    private final ModelApiWrapper modelApiWrapper;
    private final RuntimeSceneMapper mapper;
    private final ViewRuntimeRepo viewRepo;
    private final LightRuntimeSceneMapper lightMapper;

    @Override
    public DataManagerSceneVO findSceneVoByKey(@NotNull String key) {
        SceneMeta dbScene = sceneQueryService.findByKey(key)
                .orElseThrow(() -> new SceneNotFoundException(key));
        if (dbScene.getType() != SceneType.DATA) {
            throw new ValidationException("expected the type of scene: DATA but it was: " + dbScene.getType());
        }
        DataManagerSceneVO vo = mapper.toVo(dbScene);
        DataManagerSceneConfig dbSceneSceneConfig = (DataManagerSceneConfig) dbScene.getSceneConfig();
        Collection<DataStructNode> usedModes =
                modelApiWrapper.findByAliasLoosely(dbSceneSceneConfig.getUsedModelAlias(), dbScene.getTeamId()).values();
        vo.setUsedModels(usedModes);
        return vo;
    }

    @Override
    public LightDataManagerSceneVO findLightSceneVoByKey(@NotNull String key, @Nullable String view) {
        SceneMeta dbScene = sceneQueryService.findLightByKey(key, view)
                .orElseThrow(() -> new SceneNotFoundException(key));
        if (dbScene.getType() != SceneType.DATA) {
            throw new ValidationException("expected the type of scene: DATA but it was: " + dbScene.getType());
        }
        LightDataManagerSceneVO vo = lightMapper.toVo(dbScene);
        DataManagerSceneConfig dbSceneSceneConfig = (DataManagerSceneConfig) dbScene.getSceneConfig();
        Collection<DataStructNode> usedModes =
                modelApiWrapper.findByAliasLoosely(dbSceneSceneConfig.getUsedModelAlias(), dbScene.getTeamId()).values();
        vo.setUsedModels(usedModes);
        return vo;
    }

    @Override
    public SceneMeta findSceneMetaByKey(@NotNull String key) {
        return sceneQueryService.findByKey(key).orElseThrow(() -> new SceneNotFoundException(key));
    }

    @Override
    public DataManagerView findViewByKey(@NotNull String viewKey) {
        return Optional.ofNullable(viewRepo.findOneByKey(viewKey)).map(ExtModuleBaseMeta::getResourceProps)
                .orElse(null);
    }

    @Override
    public DataManagerSceneVO.RuntimeDataSceneView findRuntimeViewByKey(@NotNull String viewKey) {
        DataManagerViewMeta viewMeta = viewRepo.findOneByKey(viewKey);
        if (viewMeta == null) {
            return null;
        }
        DataManagerSceneVO.RuntimeDataSceneView runtimeDataSceneView = mapper.mapView(viewMeta.getResourceProps());
        // 兼容历史数据
        if (runtimeDataSceneView != null && runtimeDataSceneView.getI18nConfig() == null && TrantorContext.i18nEnabled()) {
            String sceneKey = viewMeta.getParentKey();
            sceneQueryService.findByKey(sceneKey, EditUtil.ctxFromThreadLocal(), null).ifPresent(scene -> {
                SceneConfig sceneConfig = scene.getSceneConfig();
                if (sceneConfig instanceof DataManagerSceneConfig) {
                    runtimeDataSceneView.setI18nConfig(((DataManagerSceneConfig) scene.getSceneConfig()).getI18nConfig());
                }
            });
        }
        return runtimeDataSceneView;
    }
}
