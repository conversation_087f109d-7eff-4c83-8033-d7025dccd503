package io.terminus.trantor2.scene.runtime.aspect;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldProperties;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import io.terminus.trantor2.scene.config.i18n.ViewI18nConfig;
import io.terminus.trantor2.scene.runtime.vo.BaseRuntimeSceneConfig;
import io.terminus.trantor2.scene.runtime.vo.BaseSceneVO;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static io.terminus.trantor2.module.constants.I18nConst.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class SceneVOAspect {
    private final I18nRuntimeTarget i18nRuntimeTarget;

    @Pointcut("execution(io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService.*(..))")
    public void dataManagerSceneVOMethods() {
    }

    @Pointcut("execution(io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService.*(..))")
    public void dataManagerLightSceneVOMethods() {
    }

    @Pointcut("execution(io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO.RuntimeDataSceneView io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService.*(..))")
    public void dataManagerViewMethods() {
    }

    @AfterReturning(pointcut = "dataManagerSceneVOMethods()", returning = "result")
    public void enhanceI18nDataManagerSceneVO(DataManagerSceneVO result) {
        enhanceI18nSceneVO(result, result.getUsedModels());
    }

    @AfterReturning(pointcut = "dataManagerLightSceneVOMethods()", returning = "result")
    public void enhanceI18nLightDataManagerSceneVO(LightDataManagerSceneVO result) {
        enhanceI18nSceneVO(result, result.getUsedModels());
    }

    @AfterReturning(pointcut = "dataManagerViewMethods()", returning = "result")
    public void enhanceI18nView(DataManagerSceneVO.RuntimeDataSceneView result) {
        if (result == null) {
            return;
        }
        if (!TrantorContext.i18nEnabled()) {
            result.setI18nConfig(null);
            return;
        }
        Set<String> i18nKeySet = Optional.ofNullable(result.getI18nConfig())
                .map(ViewI18nConfig::getI18nKeySet)
                .orElse(null);
        if (CollectionUtils.isEmpty(i18nKeySet)) {
            return;
        }
        String title = result.getTitle();
        i18nKeySet.add(title);
        Map<String, String> i18nResources = getViewI18nResourceMap(true, i18nKeySet);
        result.getI18nConfig().setI18nResources(i18nResources);
        result.getI18nConfig().setI18nKeySet(null);
        result.setTitle(i18nResources.getOrDefault(title, title));
    }

    private void enhanceI18nSceneVO(BaseSceneVO<?> result, Collection<DataStructNode> usedModels) {
        try {
            if (!TrantorContext.i18nEnabled()) {
                result.getSceneConfig().setI18nConfig(null);
                return;
            }
            Set<String> keys = new HashSet<>();
            BaseRuntimeSceneConfig sceneConfig = result.getSceneConfig();
            ViewI18nConfig sceneI18nConfig = result.getSceneConfig().getI18nConfig();
            result.getSceneConfig().setI18nConfig(null);
            List<DataManagerSceneVO.RuntimeDataSceneView> views = sceneConfig.getViews();
            if (!CollectionUtils.isEmpty(views)) {
                for (DataManagerSceneVO.RuntimeDataSceneView view : views) {
                    keys.add(view.getTitle());
                    // light view content is null
                    if (view.getContent() == null) {
                        continue;
                    }
                    if (view.getI18nConfig() == null) {
                        // 兼容历史数据
                        view.setI18nConfig(sceneI18nConfig);
                    }
                    if (view.getI18nConfig() == null || view.getI18nConfig().getI18nKeySet() == null) {
                        continue;
                    }
                    keys.addAll(view.getI18nConfig().getI18nKeySet());
                }
                sceneConfig.setI18nConfig(null);
            }
            String sceneName = result.getName();
            keys.add(sceneName);
            Map<String, String> i18nResources = getViewI18nResourceMap(keys);
            Map<String, String> dorsI18nResources = i18nResources.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith(DORS_PREFIX))
                    .collect(Collectors.toMap(it -> it.getKey().substring(DORS_PREFIX.length()), Map.Entry::getValue));

            result.setName(i18nResources.getOrDefault(sceneName, sceneName));
            result.getSceneConfig().getViews().forEach(view -> {
                view.setTitle(i18nResources.getOrDefault(view.getTitle(), view.getTitle()));
                if (view.getI18nConfig() != null && view.getI18nConfig().getI18nKeySet() != null) {
                    Set<String> i18nKeySet = view.getI18nConfig().getI18nKeySet();
                    view.getI18nConfig().setI18nResources(i18nKeySet.stream()
                            .filter(i18nResources::containsKey)
                            .collect(Collectors.toMap(key -> key, i18nResources::get)));
                    view.getI18nConfig().getI18nResources().putAll(dorsI18nResources);
                    view.getI18nConfig().setI18nKeySet(null);
                }
            });
            enhanceModels(usedModels);
        } catch (Exception e) {
            log.error("enhanceDataManagerSceneVO error", e);
        }
    }

    private Map<String, String> getViewI18nResourceMap(Set<String> keys) {
        return getViewI18nResourceMap(false, keys);
    }
    @NotNull
    private Map<String, String> getViewI18nResourceMap(boolean subDorsI18nKey, Set<String> keys) {
        Set<String> allKeys = new HashSet<>(keys);
        allKeys.addAll(keys.stream().map(VIEW_PREFIX::concat).collect(Collectors.toSet()));
        Map<String, String> allResources = i18nRuntimeTarget.findI18nResources(allKeys);
        Map<String, String> dorsI18nResource = i18nRuntimeTarget.findI18nResourcesByPrefix(DORS_PREFIX);
        allResources.putAll(dorsI18nResource);

        Map<String, String> i18nResources = new HashMap<>(keys.size());
        allResources.entrySet().stream().sorted(Map.Entry.comparingByKey(this::compareKeys))
                .forEachOrdered(entry -> {
                    String key = entry.getKey();
                    if (key.startsWith(VIEW_PREFIX)) {
                        key = key.substring(VIEW_PREFIX.length());
                    } else if (subDorsI18nKey && key.startsWith(DORS_PREFIX)) {
                        key = key.substring(DORS_PREFIX.length());
                    }
                    i18nResources.putIfAbsent(key, entry.getValue());
                });
        return i18nResources;
    }

    /**
     * 有前缀的key优先
     */
    private int compareKeys(String key1, String key2) {
        boolean key1StartsWithPrefix = key1.startsWith(VIEW_PREFIX) || key1.startsWith(DORS_PREFIX);
        boolean key2StartsWithPrefix = key2.startsWith(VIEW_PREFIX) || key2.startsWith(DORS_PREFIX);
        if (key1StartsWithPrefix && key2StartsWithPrefix) {
            return key1.compareTo(key2);
        } else if (key1StartsWithPrefix) {
            return -1;
        } else if (key2StartsWithPrefix) {
            return 1;
        } else {
            return key1.compareTo(key2);
        }
    }

    private void enhanceModels(Collection<DataStructNode> usedModels) {
        Map<String, String> modelI18nResources = i18nRuntimeTarget.findI18nResourcesByPrefix(MODEL_PREFIX);
        Set<String> modelI18nKey = new HashSet<>();
        usedModels.forEach(m -> {
            modelI18nKey.add(m.getName());
            Optional.ofNullable(m.getChildren()).ifPresent(c -> c.forEach(f -> modelI18nKey.add(f.getName())));
        });

        Map<String, String> i18nResources = i18nRuntimeTarget.findI18nResources(modelI18nKey);

        Map<String, String> allI18nResources = new HashMap<>(modelI18nResources.size() + i18nResources.size());
        modelI18nResources.forEach((key, value) -> allI18nResources.put(key.substring(MODEL_PREFIX.length()), value));
        i18nResources.forEach(allI18nResources::putIfAbsent);

        usedModels.forEach(model -> {
            model.setName(allI18nResources.getOrDefault(model.getName(), model.getName()));
            List<DataStructFieldNode> fields = model.getChildren();
            if (CollectionUtils.isEmpty(fields)) {
                return;
            }
            fields.forEach(f -> {
                f.setName(allI18nResources.getOrDefault(f.getName(), f.getName()));
                DataStructFieldProperties props = f.getProps();
                if (props != null && FieldType.ENUM.equals(props.getFieldType())
                        && props.getDictPros() != null && !CollectionUtils.isEmpty(props.getDictPros().getDictValues())) {
                    props.getDictPros().getDictValues().forEach(d -> {
                        d.computeIfPresent("label", (k, v) -> allI18nResources.getOrDefault(v, (String) v));
                    });
                }
            });
        });
    }
}