package io.terminus.trantor2.scene.runtime.service;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.TreeBaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.dto.LightParam;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.scene.runtime.repo.SceneRuntimeRepo;
import io.terminus.trantor2.scene.service.SceneRuntimeQueryService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
public class SceneRuntimeQueryServiceImpl implements SceneRuntimeQueryService {
    private final LoadingCache<ParentCacheKey, List<DataManagerViewMeta>> parentCache; //可能需要内存优化

    private final SceneRuntimeRepo sceneRuntimeRepo;
    private final ViewRepo viewRepo;

    public SceneRuntimeQueryServiceImpl(SceneRuntimeRepo sceneRuntimeRepo, ViewRepo viewRepo) {
        this.sceneRuntimeRepo = sceneRuntimeRepo;
        this.viewRepo = viewRepo;
        this.parentCache = Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .build(key -> {
                    ResourceContext ctx = ResourceContext.newResourceCtx(key.getTeamCode(), TrantorContext.getCurrentUserId());
                    return viewRepo.findAll(Field.parentKey().equal(key.getParentKey()), ctx);
                });
    }

    /**
     * 根据 key 集合查找场景
     *
     * @param keys 集合
     * @return 查询结果
     */
    @Override
    public Collection<SceneMeta> findAllByKeys(@NonNull MetaEditAndQueryContext ctx, @NonNull Collection<String> keys) {
        return findAllSceneByKeys(ctx, keys, null);
    }

    @Override
    public Collection<SceneMeta> findAllByKeysWithThinViews(@NotNull MetaEditAndQueryContext ctx, @NotNull Collection<String> keys) {
        return findAllSceneByKeys(ctx, keys, metaTreeNodeExt -> {
            DataManagerView dataManagerView = DataManagerView.convertViewOnlyResources(metaTreeNodeExt);
            DataManagerViewMeta dataManagerViewMeta = new DataManagerViewMeta();
            dataManagerViewMeta.setParentKey(metaTreeNodeExt.getParentKey());
            dataManagerViewMeta.setKey(metaTreeNodeExt.getKey());
            dataManagerViewMeta.setResourceProps(dataManagerView);
            return dataManagerViewMeta;
        });
    }
    /**
     * 场景标识标识查找场景
     */
    @Override
    public Optional<SceneMeta> findByKey(@NonNull String key, @NonNull MetaEditAndQueryContext ctx,
                                         @Nullable Collection<String> typesIn, @NonNull LightParam lightParam) {
        TrantorContext.setTeamIfNotNull(ctx.getTeamCode());
        SceneMeta scene = sceneRuntimeRepo.findOneByKey(key);
        if (scene == null) {
            return Optional.empty();
        }
        List<DataManagerViewMeta> children = parentCache.get(new ParentCacheKey(ctx.getTeamCode(), key));
        if (CollectionUtils.isEmpty(children) || CollectionUtils.isEmpty(typesIn)) {
            return Optional.of(scene);
        }
        if (scene.getType().equals(SceneType.DATA) && typesIn.contains(MetaType.View.name())) {
            DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) scene.getSceneConfig();
            List<String> viewKeys = children.stream()
                    .map(DataManagerViewMeta::getKey)
                    .collect(Collectors.toList());
            List<DataManagerView> views = new ArrayList<>(viewKeys.size());
            if (lightParam.getLight()) {
                if (!CollectionUtils.isEmpty(children)) {
                    AtomicInteger viewIndex = new AtomicInteger(Optional.ofNullable(lightParam.getView()).map(view -> {
                        if (viewKeys.contains(view)) {
                            return viewKeys.indexOf(view);
                        } else {
                            List<String> viewNames = children.stream()
                                    .map(DataManagerViewMeta::getName)
                                    .collect(Collectors.toList());
                            if (viewNames.contains(view)) {
                                return viewNames.indexOf(view);
                            } else {
                                return 0;
                            }
                        }
                    }).orElse(0));
                    viewIndex.set(Math.max(viewIndex.get(), 0));
                    views = IntStream.range(0, children.size())
                            .mapToObj(i -> i == viewIndex.get()
                                    ? children.get(i).getResourceProps()
                                    : DataManagerView.light(children.get(i).getResourceProps()))
                            .collect(Collectors.toList());
                }
            } else {
                views = children.stream()
                        .map(ExtModuleBaseMeta::getResourceProps)
                        .collect(Collectors.toList());
            }
            sceneConfig.setViews(views);
        }
        return Optional.of(scene);
    }

    private Collection<SceneMeta> findAllSceneByKeys(@NotNull MetaEditAndQueryContext ctx, @NotNull Collection<String> keys, Function<MetaTreeNodeExt, DataManagerViewMeta> customViewConvert) {
        TrantorContext.setTeamIfNotNull(ctx.getTeamCode());

        int pageSize = 100;
        int keyBatchSize = 30;
        List<SceneMeta> res = new ArrayList<>(keys.size());
        List<List<String>> keyPartitions = Lists.partition(new ArrayList<>(keys), keyBatchSize);

        for (List<String> keyBatch : keyPartitions) {
            int pageNumber = 0;
            List<DataManagerViewMeta> views = new ArrayList<>();
            while (true) {
                PageReq pageReq = PageReq.of(pageNumber, pageSize);
                // parentKey 没有索引会慢一点
                Paging<DataManagerViewMeta> page = customViewConvert == null ?
                        viewRepo.findAll(Field.parentKey().in(keyBatch), pageReq, ResourceContext.ctxFromThreadLocal()) :
                        viewRepo.findAll(Field.parentKey().in(keyBatch), pageReq, ResourceContext.ctxFromThreadLocal(), customViewConvert);
                if (page.getData().isEmpty()) {
                    break;
                }
                views.addAll(page.getData());
                if ((long) (pageNumber + 1) * pageSize >= page.getTotal()) {
                    break;
                }
                pageNumber++;
            }
            Map<String, List<DataManagerViewMeta>> parentMap = views.stream()
                    .filter(it -> keyBatch.contains(it.getParentKey()))
                    .collect(Collectors.groupingBy(TreeBaseMeta::getParentKey));

            List<SceneMeta> scenes = sceneRuntimeRepo.findAll(parentMap.keySet()).stream()
                    .filter(it -> !ObjectUtils.isEmpty(it) && it.getSceneConfig() instanceof DataManagerSceneConfig && parentMap.containsKey(it.getKey()))
                    .peek(scene -> {
                        List<DataManagerViewMeta> children = parentMap.get(scene.getKey());
                        if (!CollectionUtils.isEmpty(children)) {
                            ((DataManagerSceneConfig) scene.getSceneConfig()).setViews(
                                    children.stream().map(ExtModuleBaseMeta::getResourceProps).collect(Collectors.toList())
                            );
                        }
                    }).collect(Collectors.toList());

            res.addAll(scenes);
        }
        return res;
    }

    @Data
    @EqualsAndHashCode
    @AllArgsConstructor
    static class ParentCacheKey {
        /**
         * 所属团队
         */
        private String teamCode;
        /**
         * 父节点元数据标识
         */
        private String parentKey;
    }
}
