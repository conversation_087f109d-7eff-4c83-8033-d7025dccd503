package io.terminus.trantor2.scene.runtime;

import io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService;
import io.terminus.trantor2.scene.runtime.service.SceneSupplyServiceQuery;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class SceneRuntimeConfiguration {
    @Bean
    public SceneSupplyServiceQuery sceneSupplyServiceQuery(DataManagerRuntimeService service) {
        return new SceneSupplyServiceQuery(service);
    }
}
