package io.terminus.trantor2.scene.runtime.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.index.*;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuRuntimeQueryService;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.scene.runtime.repo.SceneRuntimeRepo;
import io.terminus.trantor2.scene.runtime.request.SceneBatchRequest;
import io.terminus.trantor2.scene.runtime.request.ViewSummaryRequest;
import io.terminus.trantor2.scene.runtime.vo.SceneInfo;
import io.terminus.trantor2.scene.runtime.vo.ViewSummary;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SceneRuntimeServiceImpl implements SceneRuntimeService {
    private final SceneRuntimeRepo sceneRuntimeRepo;
    // FIXME runtime repo 目前不支持条件查询
    private final ViewRepo viewRepo;
    private final MetaIndexRefRepo metaIndexRefRepo;
    private final MetaIndexAssetRepo metaIndexAssetRepo;
    private final MetaQueryService metaQueryService;
    private final MenuRuntimeQueryService menuQueryService;
    public static final List<String> systemFieldAlias = Arrays.stream(SystemFieldGeneratorV2.values()).map(SystemFieldGeneratorV2::getFieldAlias).collect(Collectors.toList());

    /**
     * 获取当前门户使用的场景
     */
    @Override
    public Collection<SceneInfo> findUsedSceneByModel(String modelKey, SceneBatchRequest request) {
        Set<String> usedSceneSet = menuQueryService.getMenuTreeMeta(TrantorContext.getPortalCode())
                .flatMenuAndChildrenRecursively().stream()
                .filter(MenuMeta::routeScene)
                .map(it -> it.getRouteConfig("sceneKey"))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toSet());

        request.setModelKey(modelKey);
        Long teamId = TrantorContext.getTeamId();

        Set<String> sourceKeys = metaIndexRefRepo.find(teamId, RefCond.builder()
                        .targetKey(modelKey).build())
                .stream()
                .map(MetaIndexRef::getSourceKey)
                .collect(Collectors.toSet());

        List<MetaIndexAsset.Lite> assets = metaIndexAssetRepo.find(
                teamId,
                MetaIndexAsset.Lite.class,
                Field.key().in(sourceKeys).and(
                        Field.type().in(MetaType.View.name(), MetaType.Scene.name()))
        );

        Map<String, SceneInfo> sceneInfoMap = new HashMap<>();
        List<SceneInfo.ViewInfo> viewInfos = new ArrayList<>();
        buildSceneInfoMap(request, usedSceneSet, assets, sceneInfoMap, viewInfos);

        return assembleSceneInfos(sceneInfoMap, viewInfos, request.getKeyword());
    }

    @Override
    public ViewSummary summaryView(ViewSummaryRequest request) {
        DataManagerViewMeta dataManagerViewMeta = viewRepo.findOneByKey(request.getViewKey(), ResourceContext.ctxFromThreadLocal())
                .orElseThrow(() -> new IllegalArgumentException("视图不存在"));
        ObjectNode content = dataManagerViewMeta.getResourceProps().getContent();
        if (content == null) {
            return null;
        }
        ViewSummary viewSummary = new ViewSummary();
        List<ViewSummary.Node> nodes = new ArrayList<>();
        traverse(content, nodes, request);
        if (dataManagerViewMeta.getResourceProps().getType().equals(DataManagerView.ViewType.LIST)) {
            nodes.removeIf(it -> it.getType().equals("Button"));
        }
        viewSummary.setNodes(nodes);
        return viewSummary;
    }

    private void buildSceneInfoMap(SceneBatchRequest request, Set<String> usedSceneSet,
                                   List<MetaIndexAsset.Lite> assets, Map<String, SceneInfo> sceneInfoMap,
                                   List<SceneInfo.ViewInfo> viewInfos) {
        for (MetaIndexAsset.Lite asset : assets) {
            String key = asset.getKey();
            if (asset.getType().equals(MetaType.Scene) && usedSceneSet.contains(key)) {
                SceneInfo sceneInfo = new SceneInfo();
                sceneInfo.setKey(key);
                sceneInfo.setName(asset.getName());
                sceneInfo.setType(SceneType.DATA);
                sceneInfo.setEndpointType(EndpointType.valueOf(asset.getSubType()));
                sceneInfoMap.put(key, sceneInfo);
            } else if (request.getLoadView() && asset.getType().equals(MetaType.View)) {
                SceneBatchRequest.ViewFilter viewFilter = request.getViewFilter();
                boolean filterViewTypes = viewFilter != null && !CollectionUtils.isEmpty(viewFilter.getTypes());

                DataManagerView.ViewType viewType = DataManagerView.ViewType.valueOf(
                        asset.getLiteProps().get("type").asText()
                );
                if (filterViewTypes && !viewFilter.getTypes().contains(viewType)) {
                    continue;
                }

                SceneInfo.ViewInfo viewInfo = new SceneInfo.ViewInfo();
                viewInfo.setKey(key);
                viewInfo.setName(asset.getName());
                viewInfo.setType(viewType);
                String parentKey = Optional.ofNullable(asset.getParentKey())
                        .orElseGet(() -> {
                            String[] parts = asset.getPath().split(":");
                            return parts.length > 1 ? parts[0] : null;
                        });
                viewInfo.setParentKey(parentKey);
                viewInfos.add(viewInfo);
            }
        }
    }

    private Set<SceneInfo> assembleSceneInfos(Map<String, SceneInfo> sceneInfoMap,
                                              List<SceneInfo.ViewInfo> viewInfos, String keyword) {
        boolean keywordFilter = !ObjectUtils.isEmpty(keyword);

        Map<String, List<SceneInfo.ViewInfo>> sceneKey2views = viewInfos.stream()
                .collect(Collectors.groupingBy(SceneInfo.ViewInfo::getParentKey));

        return Optional.ofNullable(sceneInfoMap)
                .map(Map::values)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(sceneInfo -> !keywordFilter || safeContainsKeyword(sceneInfo, keyword))
                .map(sceneInfo -> {
                    List<SceneInfo.ViewInfo> views = Optional.ofNullable(sceneKey2views)
                            .map(m -> m.get(sceneInfo.getKey()))
                            .orElse(Collections.emptyList());

                    if (!views.isEmpty()) {
                        sceneInfo.setViewInfos(views);
                        return sceneInfo;
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private boolean safeContainsKeyword(SceneInfo sceneInfo, String keyword) {
        if (sceneInfo == null || keyword == null) return false;
        return (sceneInfo.getName() != null && sceneInfo.getName().contains(keyword));
    }

    private void traverse(JsonNode node, List<ViewSummary.Node> result, ViewSummaryRequest request) {
        if (node == null || node.isNull()) {
            return;
        }
        String name = node.has("name") ? node.get("name").asText() : null;
        List<String> keys = request.getKeys();
        if (request.isFieldOnly()) {
            ArrayNode fieldsValueNode = ObjectJsonUtil.MAPPER.createArrayNode();
            if ("FormField".equals(name)) {
                fieldsValueNode.add(TextNode.valueOf(node.path("props").path("label").asText("")));
            } else if ("TableForm".equals(name)) {
                node.path("props").path("fields").forEach(field -> {
                    if (field.isObject()) {
                        ObjectNode fieldNode = (ObjectNode) field;
                        String fieldName = fieldNode.path("props").path("label").asText("");
                        fieldsValueNode.add(TextNode.valueOf(fieldName));
                    }
                });
            }
            if (CollectionUtils.isEmpty(result)) {
                ViewSummary.Node n = new ViewSummary.Node();
                n.setType("Field");
                n.setProps(fieldsValueNode);
                result.add(n);
            } else {
                JsonNode props = result.get(0).getProps();
                if (props instanceof ArrayNode) {
                    ((ArrayNode) props).addAll(fieldsValueNode);
                }
            }
        } else {
            boolean filter = keys != null && !keys.isEmpty();
            if ("FormField".equals(name)) {
                ViewSummary.Node n = buildNode(node, !filter);
                if (n != null && (!filter || (keys.contains(node.get("key").asText(""))))) {
                    result.add(n);
                }
            } else if ("Button".equals(name) && (!filter || (keys.contains(node.get("key").asText(""))))) {
                JsonNode props = node.get("props");
                if (props != null && "primary".equals(props.path("type").asText(""))) {
                    result.add(buildNode(node, !filter));
                }
            } else if ("PageTitle".equals(name)) {
                result.add(buildNode(node, !filter));
            } else if ("Table".equals(name)) {
                ViewSummary.Node n = buildNode(node, !filter);
                if (n != null) {
                    result.add(n);
                }
            } else if ("TableForm".equals(name)) {
                ViewSummary.Node n = buildNode(node, !filter);
                if (n != null && (!filter || (keys.contains(node.get("key").asText(""))))) {
                    result.add(n);
                }
            } else if ("ChildViewBody".equals(name)) {
                return;
            }
        }

        JsonNode children = node.get("children");
        if (children != null && children.isArray()) {
            for (JsonNode child : children) {
                traverse(child, result, request);
            }
        }
    }

    private ViewSummary.Node buildNode(JsonNode node, boolean trim) {
        ObjectNode props = (ObjectNode) node.path("props");
        String name = props.has("name") ? props.get("name").asText("") : null;
        if (systemFieldAlias.contains(name)) {
            return null;
        }

        ViewSummary.Node n = new ViewSummary.Node();
        n.setKey(node.has("key") ? node.get("key").asText() : null);
        n.setType(node.has("name") ? node.get("name").asText() : null);
        n.setLabel(props.path("label").asText(null));

        if (n.getType().contains("Table")) {
            if (!processTableNode(node, props)) {
                return null;
            }
        }
        cleanEditComponentProps(props);
        cleanActionConfig(props);
        boolean hasRequired = cleanLookup(props);

        if (trim && "FormField".equals(n.getType())) {
            n.setKey(props.path("name").asText(null));
            props.remove("name");

            n.setType("Field");
            String type = props.get("type").asText(null);
            if ("SELECT".equals(type)) {
                props.set("type", TextNode.valueOf("Enum"));
            }else if ("MULTISELECT".equals(type)) {
                props.set("type", TextNode.valueOf("MultiEnum"));
            }
            List<String> keysToRemove = Arrays.asList(
                    "rules", "label", "hidden",
                    "editComponentType", "initialValue",
                    "componentProps", "displayComponentProps", "displayComponentType"
            );
            keysToRemove.forEach(props::remove);
            if (props.has("modelAlias")) {
                props.set("model", props.get("modelAlias"));
                props.remove("modelAlias");
            }
            if (props.path("required").isBoolean() && !props.get("required").asBoolean()) {
                props.remove("required");
            }
        }

        if (hasRequired) {
            props.set("required", BooleanNode.valueOf(true));
        }
        List<String> keysToRemove = Arrays.asList(
                "subTableEnabled", "maxHeight", "disabled", "colSize", "tagConfig",
                "width", "permissionKey", "showCondition", "pagination",
                "colorConfig", "colGroup", "confirmOn"
        );

        keysToRemove.forEach(props::remove);
        n.setProps(props);
        return n;
    }

    private void cleanEditComponentProps(ObjectNode props) {
        JsonNode editNode = props.get("editComponentProps");
        if (!(editNode instanceof ObjectNode)) return;

        ObjectNode editProps = (ObjectNode) editNode;

        List<String> keysToRemove = Arrays.asList(
                "fields", "labelField", "modalProps", "showScope", "showType", "showFilterFields", "isProFilter",
                "tableCondition", "tableConditionContext", "pagination",
                "searchable", "separator", "findFlow", "rows", "expression", "enableExpression",
                "buttonConfig", "label", "mainField", "tableConditionContext$"
        );
        for (String key : keysToRemove) {
            editProps.remove(key);
        }

        simplifyFilterFields(editProps);
        processFlowObject(editProps);
        flattenEditProps(editProps, props);

        props.remove("editComponentProps");
    }

    private void simplifyFilterFields(ObjectNode editProps) {
        JsonNode filterFields = editProps.get("filterFields");
        if (filterFields instanceof ArrayNode) {
            ArrayNode array = (ArrayNode) filterFields;
            for (int i = array.size() - 1; i >= 0; i--) {
                JsonNode ele = array.get(i);
                if (ele instanceof ObjectNode) {
                    ObjectNode obj = (ObjectNode) ele;
                    JsonNode alias = obj.get("name");
                    JsonNode label = obj.get("label");
                    obj.removeAll();
                    if (alias != null) obj.set("alias", alias);
                    if (label != null) obj.set("label", label);
                }
            }
        }
    }

    private void processFlowObject(ObjectNode editProps) {
        JsonNode flow = editProps.get("flow");
        if (!(flow instanceof ObjectNode)) return;

        ObjectNode flowObj = (ObjectNode) flow;
        flowObj.remove("context$");
        flowObj.remove("type");
        flowObj.remove("modelAlias");

        if (flowObj.has("containerKey") && flowObj.get("containerKey").isEmpty()) {
            flowObj.remove("containerKey");
        }

        JsonNode params = flowObj.get("params");
        if (params instanceof ArrayNode) {
            ArrayNode array = (ArrayNode) params;
            for (JsonNode ele : array) {
                if (ele instanceof ObjectNode) {
                    ObjectNode obj = (ObjectNode) ele;
                    obj.remove("valueConfig");
                    obj.remove("fieldName");
                    obj.set("type", obj.path("fieldType"));
                    obj.remove("fieldType");
                }
            }
        }
    }

    private void flattenEditProps(ObjectNode editProps, ObjectNode props) {
        Iterator<Map.Entry<String, JsonNode>> fields = editProps.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            props.set(entry.getKey(), entry.getValue());
        }
    }
    private void cleanActionConfig(ObjectNode props) {
        JsonNode actionConfig = props.get("actionConfig");
        if (actionConfig instanceof ObjectNode) {
            ObjectNode obj = (ObjectNode) actionConfig;
            obj.remove("endLogicOtherConfig");
            obj.remove("executeLogic");
        }
    }

    private boolean cleanLookup(ObjectNode props) {
        JsonNode lookup = props.get("lookup");
        boolean hasRequired = false;
        if (lookup instanceof ArrayNode) {
            ArrayNode lookupArray = (ArrayNode) lookup;
            for (int i = lookupArray.size() - 1; i >= 0; i--) {
                JsonNode ele = lookupArray.get(i);
                if (ele.path("fieldRules").path("required").asBoolean(false)) {
                    hasRequired = true;
                }
                ((ObjectNode) ele).remove("fieldRules");
                String operator = ele.path("operator").asText("");
                if (!"SERVICE".equals(operator)) {
                    lookupArray.remove(i);
                    continue;
                }
                ((ObjectNode) ele).remove("conditionGroup");
                JsonNode triggerNode = ele.get("trigger");
                if (triggerNode != null) {
                    if (!"auto".equals(triggerNode.asText())) {
                        lookupArray.remove(i);
                    }
                } else {
                    JsonNode valueRules = ele.get("valueRules");
                    if (valueRules == null || (valueRules.isArray() && valueRules.isEmpty())) {
                        lookupArray.remove(i);
                    } else if (valueRules instanceof ObjectNode) {
                        ((ObjectNode) valueRules).remove("scope");
                        ((ObjectNode) valueRules).remove("val");
                        ObjectNode serviceParams = (ObjectNode) valueRules.path("serviceParams");
                        serviceParams.remove("outputParams");
                        JsonNode entryNewParamsNode = serviceParams.path("entryNewParams");
                        if (entryNewParamsNode instanceof ArrayNode) {
                            ArrayNode entryNewParams = (ArrayNode) entryNewParamsNode;

                            for (int j = entryNewParams.size() - 1; j >= 0; j--) {
                                JsonNode paramNode = entryNewParams.get(j);
                                if (paramNode instanceof ObjectNode) {
                                    ObjectNode param = (ObjectNode) paramNode;
                                    if ("action".equals(param.path("type").asText())) {
                                        entryNewParams.remove(j);
                                        continue;
                                    }
                                    param.remove("valueConfig");
                                    param.remove("fieldType");
                                    JsonNode elements = param.get("elements");
                                    if (elements instanceof ArrayNode) {
                                        for (JsonNode elementNode : elements) {
                                            if (elementNode instanceof ObjectNode) {
                                                ObjectNode element = (ObjectNode) elementNode;
                                                element.remove("valueConfig");
                                                element.remove("fieldType");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (lookupArray.isEmpty()) {
                props.remove("lookup");
            }
        }
        return hasRequired;
    }

    private boolean containsKeyword(SceneInfo sceneInfo, String keyword) {
        return sceneInfo.getName().contains(keyword) ||
                sceneInfo.getKey().contains(keyword);
    }

    private boolean processTableNode(JsonNode node, ObjectNode props) {
        JsonNode children = node.get("children");
        if (children == null || !children.isArray()) {
            return false;
        }

        ObjectNode tableProps = (ObjectNode) node.get("props");
        List<String> keysToRemove = Arrays.asList(
                "filterFields", "categoryFilter", "summarySetting", "selectType",
                "showScope", "showFilterFields", "isProFilter", "fixedColNumber",
                "showConfigure", "tableConditionContext$", "acceptFilterQuery",
                "allowClickRowSelect", "allowRowSelect", "enableSolution"
        );
        keysToRemove.forEach(tableProps::remove);

        String modelAlias = tableProps.path("modelAlias").asText("");

        for (JsonNode child : children) {
            if (child == null || !child.isObject()) continue;

            ObjectNode childObj = (ObjectNode) child;
            if (!"Fields".equals(childObj.path("name").asText())) continue;

            JsonNode fieldsChildren = childObj.get("children");
            if (fieldsChildren == null || !fieldsChildren.isArray()) continue;

            ArrayNode newFields = ObjectJsonUtil.MAPPER.createArrayNode();
            String nodeName = node.path("name").asText("");

            for (JsonNode field : fieldsChildren) {
                if (!field.isObject()) continue;

                ObjectNode fieldProps = (ObjectNode) field.get("props");
                ObjectNode newField = ObjectJsonUtil.MAPPER.createObjectNode();

                copyIfPresent(fieldProps, newField, "initialValue", "init");
                copyIfPresent(fieldProps, newField, "label", "label");

                String fieldModelAlias = fieldProps.path("modelAlias").asText();
                if (!fieldModelAlias.isEmpty() && !fieldModelAlias.equals(modelAlias)) {
                    newField.set("model", fieldProps.get("modelAlias"));
                }

                if (fieldProps.path("required").asBoolean(true)) {
                    newField.set("required", fieldProps.get("required"));
                }

                if ("TableForm".equals(nodeName)) {
                    JsonNode editProps = fieldProps.path("editComponentProps");
                    if (!editProps.isMissingNode()) {
                        newField.set("editComponentProps", editProps);
                        cleanEditComponentProps(newField);
                    }
                }

                // Fallback required from lookup
                if (!newField.path("required").asBoolean(false)) {
                    JsonNode lookupArray = fieldProps.path("lookup");
                    if (lookupArray.isArray()) {
                        for (JsonNode lookupItem : lookupArray) {
                            JsonNode fieldRules = lookupItem.path("fieldRules");
                            if (fieldRules.path("required").asBoolean(false)) {
                                newField.put("required", true);
                                break;
                            }
                        }
                    }
                }

                newField.set("alias", fieldProps.get("name"));
                newFields.add(newField);
            }

            props.set("fields", newFields);
            return true; // Only process first "Fields" node
        }

        return false;
    }

    private void copyIfPresent(ObjectNode from, ObjectNode to, String fromKey, String toKey) {
        if (from.hasNonNull(fromKey)) {
            to.set(toKey, from.get(fromKey));
        }
    }
}
