package io.terminus.trantor2.scene.runtime.util;

import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.properties.Condition;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.dsl.properties.ConditionLeaf;
import io.terminus.trantor2.service.dsl.properties.Value;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ConditionProcessor {
    /**
     * 从视图配置构建默认的 conditionGroup
     *
     * @param rightValues    前端重构过的默认条件右值
     * @param conditionGroup 视图配置中的对应的条件组
     */
    public static void rebuildConditionGroup(Map<String, List<Value>> rightValues, ConditionGroup conditionGroup) {
        if (conditionGroup == null || CollectionUtils.isEmpty(conditionGroup.getConditions()) || MapUtils.isEmpty(rightValues)) {
            return;
        }
        processConditions(rightValues, conditionGroup);
    }

    /**
     * 递归处理条件组中的条件
     *
     * @param rightValues    条件值映射表
     * @param conditionGroup 条件组
     */
    private static void processConditions(Map<String, List<Value>> rightValues, ConditionGroup conditionGroup) {
        for (Condition condition : conditionGroup.getConditions()) {
            if (condition instanceof ConditionGroup) {
                processConditions(rightValues, (ConditionGroup) condition);
            } else if (condition instanceof ConditionLeaf) {
                processConditionLeaf(rightValues, (ConditionLeaf) condition);
            }
        }
    }

    /**
     * 处理条件叶子节点
     *
     * @param rightValues   条件值映射表
     * @param conditionLeaf 条件叶子节点
     */
    private static void processConditionLeaf(Map<String, List<Value>> rightValues, ConditionLeaf conditionLeaf) {
        String key = conditionLeaf.getId();
        key = !StringUtils.isBlank(key) ? key : conditionLeaf.getKey();
        if (!rightValues.containsKey(key)) {
            return;
        }
        List<Value> values = rightValues.get(key);
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        if (conditionLeaf.getOperator() == Operator.IN || conditionLeaf.getOperator() == Operator.NOT_IN) {
            conditionLeaf.setRightValues(values);
            conditionLeaf.setRightValue(null);
            return;
        }
        // 只有一个值时，直接设置为右值，否则设置为右值集合
        if (values.size() == 1) {
            conditionLeaf.setRightValue(values.get(0));
            conditionLeaf.setRightValues(null);
        } else {
            conditionLeaf.setRightValues(values);
            conditionLeaf.setRightValue(null);

            // 等于条件变成 IN，不等于条件变成 NOT IN
            if (conditionLeaf.getOperator() == Operator.EQ) {
                conditionLeaf.setOperator(Operator.IN);
            } else if (conditionLeaf.getOperator() == Operator.NEQ) {
                conditionLeaf.setOperator(Operator.NOT_IN);
            }
        }
    }
}
