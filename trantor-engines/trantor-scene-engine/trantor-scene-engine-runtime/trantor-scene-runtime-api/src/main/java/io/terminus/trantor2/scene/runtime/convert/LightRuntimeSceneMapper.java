package io.terminus.trantor2.scene.runtime.convert;

import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface LightRuntimeSceneMapper {
    @Mapping(target = "sceneConfig", source = "sceneConfig", qualifiedByName = "mapSceneConfig")
    LightDataManagerSceneVO toVo(SceneMeta meta);

    @Named("mapSceneConfig")
    default LightDataManagerSceneVO.LightRuntimeDataSceneConfig mapSceneConfig(SceneConfig config) {
        if (config instanceof DataManagerSceneConfig) {
            return mapDataSceneConfig((DataManagerSceneConfig) config);
        }
        return null;
    }

    @Mapping(target = "views", source = "views", qualifiedByName = "mapViews")
    @Named("mapDataSceneConfig")
    LightDataManagerSceneVO.LightRuntimeDataSceneConfig mapDataSceneConfig(DataManagerSceneConfig config);

    @Named("mapViews")
    default List<DataManagerSceneVO.RuntimeDataSceneView> mapViews(List<DataManagerView> originalViews) {
        return originalViews.stream().map(this::mapView).collect(Collectors.toList());
    }

    DataManagerSceneVO.RuntimeDataSceneView mapView(DataManagerView originalViews);
}