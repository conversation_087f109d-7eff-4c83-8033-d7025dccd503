package io.terminus.trantor2.scene.runtime.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.scene.config.FrontendConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.config.datamanager.ViewResourceConfig;
import io.terminus.trantor2.scene.config.i18n.ViewI18nConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "DataManagerSceneRuntimeVO")
public class DataManagerSceneVO extends BaseSceneVO<DataManagerSceneVO.RuntimeDataSceneConfig> {
    private static final long serialVersionUID = -759823072948971801L;

    private RuntimeDataSceneConfig sceneConfig;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Schema(description = "运行态场景配置")
    public static class RuntimeDataSceneConfig extends BaseRuntimeSceneConfig {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Schema(description = "运行态视图")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class RuntimeDataSceneView extends FrontendConfig {
        private static final long serialVersionUID = 8809255824399172061L;
        @Schema(description = "标题")
        private String title;
        @Schema(description = "标识(随机生成)")
        private String key;
        @Schema(description = "页面内容 string", example = "{\"type\": \"Container\"}")
        private ObjectNode content;
        @Schema(description = "视图类型 (LIST, DETAIL, FORM, CUSTOM)", example = "LIST")
        private DataManagerView.ViewType type;
        @Schema(description = "视图资源配置")
        private List<RuntimeViewResourceConfig> resources;
        @Schema(description = "国际化资源")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private ViewI18nConfig i18nConfig;
    }
    @Data
    @Schema(description = "运行态视图资源配置")
    public static class RuntimeViewResourceConfig {
        @Schema(description = "视图资源类型")
        private ViewResourceConfig.ViewResourceType type;

        @Schema(description = "key")
        private String key;

        @Schema(description = "用于展示的资源label")
        private String label;
    }
}
