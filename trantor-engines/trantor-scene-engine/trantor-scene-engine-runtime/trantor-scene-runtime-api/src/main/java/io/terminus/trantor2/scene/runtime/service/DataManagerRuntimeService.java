package io.terminus.trantor2.scene.runtime.service;

import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import org.jetbrains.annotations.NotNull;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

/**
 * <AUTHOR>
 */
public interface DataManagerRuntimeService {
    DataManagerSceneVO findSceneVoByKey(@Nonnull String key);

    LightDataManagerSceneVO findLightSceneVoByKey(@Nonnull String key, @Nullable String view);

    SceneMeta findSceneMetaByKey(@NotNull String key);

    DataManagerSceneVO.RuntimeDataSceneView findRuntimeViewByKey(@NotNull String viewKey);

    DataManagerView findViewByKey(@NotNull String viewKey);
}
