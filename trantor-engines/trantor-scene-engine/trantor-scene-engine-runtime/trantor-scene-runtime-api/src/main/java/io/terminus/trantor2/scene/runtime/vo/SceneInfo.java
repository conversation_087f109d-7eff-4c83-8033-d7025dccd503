package io.terminus.trantor2.scene.runtime.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import lombok.Data;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SceneInfo {

    @Schema(description = "唯一标识")
    private String key;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "场景类型")
    private SceneType type;

    @Schema(description = "终端类型")
    private EndpointType endpointType;

    @Schema(description = "视图信息")
    private Collection<ViewInfo> viewInfos;

    @Data
    public static class ScenePropsInfo {
        private SceneType type;
        private EndpointType endpointType;
    }

    @Data
    public static class ViewInfo {
        private String key;
        private String name;
        @JsonIgnore
        private String parentKey;
        private DataManagerView.ViewType type;

        public static ViewInfo of(DataManagerViewMeta object) {
            if (object == null) {
                return null;
            }
            ViewInfo viewInfo = new ViewInfo();
            viewInfo.setKey(object.getKey());
            viewInfo.setName(object.getName());
            viewInfo.setType(object.getResourceProps().getType());
            return viewInfo;
        }
    }

    public static SceneInfo of(SceneMeta object) {
        if (object == null) {
            return null;
        }
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setKey(object.getKey());
        sceneInfo.setName(object.getName());
        sceneInfo.setType(object.getType());
        sceneInfo.setEndpointType(object.getEndpointType());
        return sceneInfo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SceneInfo)) return false;
        SceneInfo that = (SceneInfo) o;
        return Objects.equals(key, that.key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key);
    }
}
