package io.terminus.trantor2.scene.runtime.service;

import io.terminus.trantor2.scene.runtime.request.SceneBatchRequest;
import io.terminus.trantor2.scene.runtime.request.ViewSummaryRequest;
import io.terminus.trantor2.scene.runtime.vo.SceneInfo;
import io.terminus.trantor2.scene.runtime.vo.ViewSummary;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface SceneRuntimeService {

    Collection<SceneInfo> findUsedSceneByModel(String modelKey, SceneBatchRequest request);

    ViewSummary summaryView(ViewSummaryRequest request);
}
