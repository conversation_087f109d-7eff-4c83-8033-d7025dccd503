package io.terminus.trantor2.scene.runtime.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.FieldAccessType;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.trantor2.lang.meta.ViewContainer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视图权限VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "视图权限对象")
public class ViewPermissionVO {

    @Schema(description = "当前用户是否是管理员")
    private Boolean admin;

    @Schema(description = "功能权限开关")
    private Boolean functionPermissionEnabled;

    @Schema(description = "功能权限执行策略")
    private PolicyEnforcementMode functionPermissionEnforcementMode;

    @Schema(description = "视图权限项")
    private Map<String, Boolean> viewFunctionItems = new HashMap<>();

    @Schema(description = "组件权限项")
    private List<ComponentFunctionItems> componentFunctionItems = new ArrayList<>();

    @Schema(description = "字段权限开关")
    private Boolean fieldPermissionEnabled;

    @Schema(description = "字段权限执行策略")
    private PolicyEnforcementMode fieldPermissionEnforcementMode;

    @Schema(description = "组件字段权限")
    private List<ComponentFieldRules> componentFieldRules = new ArrayList<>();

    @Data
    public static class ComponentFunctionItems {
        private String componentKey;
        private String resourceType;
        private Map<String, Boolean> functionItems;
        private List<VirtualComponentFunctionItems> virtualComponents;
    }

    @Data
    public static class VirtualComponentFunctionItems {
        private String virtualComponentKey;
        private String resourceType;
        private Map<String, Boolean> functionItems;
    }

    @Data
    public static class ComponentFieldRules {
        private String componentKey;
        private String modelKey;
        private List<FieldAccess> fields;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class FieldAccess extends ViewContainer.Field {
        private List<FieldAccessType> accessList;
    }
}
