package io.terminus.trantor2.scene.runtime.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.dto.IdDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseSceneVO<T extends BaseRuntimeSceneConfig> extends IdDTO<Long> {
    private static final long serialVersionUID = 6256649640743183546L;
    @Schema(description = "唯一标识")
    private String key;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属应用ID")
    @Deprecated
    private Long appId;

    @Schema(description = "所属团队ID")
    private Long teamId;

    private SceneType type;

    private T sceneConfig;

    @Schema(description = "所有使用到的模型")
    private Collection<DataStructNode> usedModels;

    /**
     * 前端代码还有用到不返回会报错，兼容用
     */
    @Deprecated
    private Models models;

    @Data
    @Deprecated
    public static class Models {
        private List<DataStructNode> relations = new ArrayList<>();
    }

}