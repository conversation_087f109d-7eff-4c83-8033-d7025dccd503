package io.terminus.trantor2.scene.runtime.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.scene.config.i18n.ViewI18nConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BaseRuntimeSceneConfig {
    @Schema(description = "国际化资源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ViewI18nConfig i18nConfig;
    @Schema(description = "视图配置")
    private List<DataManagerSceneVO.RuntimeDataSceneView> views;
}
