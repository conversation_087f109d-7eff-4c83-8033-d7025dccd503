package io.terminus.trantor2.scene.runtime.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Schema(description = "portal 场景批量请求")
@Data
public class SceneBatchRequest {
    @Schema(description = "是否加载视图节点信息，默认为 true")
    private Boolean loadView = true;
    @Schema(description = "视图过滤器，为空返回所有视图节点信息")
    private ViewFilter viewFilter;
    private String keyword;
    private String modelKey;

    @Data
    public static class ViewFilter {
        private Set<DataManagerView.ViewType> types;
    }
}
