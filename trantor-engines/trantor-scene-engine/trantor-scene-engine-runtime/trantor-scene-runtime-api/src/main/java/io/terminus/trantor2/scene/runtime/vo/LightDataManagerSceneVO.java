package io.terminus.trantor2.scene.runtime.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "DataManagerLightSceneVO")
public class LightDataManagerSceneVO extends BaseSceneVO<LightDataManagerSceneVO.LightRuntimeDataSceneConfig> {
    private static final long serialVersionUID = 3035632993806465650L;

    private LightRuntimeDataSceneConfig sceneConfig;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Schema(description = "运行态场景配置")
    public static class LightRuntimeDataSceneConfig extends BaseRuntimeSceneConfig {
    }
}
