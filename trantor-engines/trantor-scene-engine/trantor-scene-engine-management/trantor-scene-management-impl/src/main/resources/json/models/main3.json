{"name": "主模型(供应商主数据)", "config": {"modelKey": "TERP_MIGRATE$gen_vend_info_md", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_vend_level_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$org_pur_org_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$gen_vend_type_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_partner_procedure_head_cf", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$user", "relations": {"LINK": []}}, {"modelKey": "TERP_MIGRATE$org_inv_org_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_language_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_timezone_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_com_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$org_pur_org_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_vend_info_md", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$org_sls_dc_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_addr_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$org_com_org_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$org_sls_org_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_cust_info_md", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$gen_com_type_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_timezone_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_coun_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_addr_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_curr_type_cf", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$gen_vend_info_md", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_com_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$org_inv_org_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_vend_info_md", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_vend_level_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_vend_type_cf", "relations": {}}]}}], "PARENT_CHILD": []}}, "models": [{"key": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "name": "附件过程定义抬头表", "id": 38674, "alias": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "desc": "附件过程定义抬头表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "gen_attachment_procedure_item", "name": "过程定义项目行表", "alias": "genAttachmentProcedureItem", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "过程定义项目行表", "columnName": "gen_attachment_procedure_item", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "currentModelFieldAlias": "genAttachmentProcedureItem", "relationModelAlias": "TERP_MIGRATE$gen_attachment_procedure_item_cf", "linkModelFieldAlias": "genAttachmentProcedureHeadCfId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_attachment_procedure_item_cf", "linkModelAlias": "TERP_MIGRATE$gen_attachment_procedure_item_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "gen_attachment_procedure_head_name", "name": "附件组名称", "alias": "genAttachmentProcedureHeadName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "附件组名称", "columnName": "gen_attachment_procedure_head_name", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_attachment_procedure_head_cf", "mainField": "gen_attachment_procedure_item", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "genAttachmentProcedureItem"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_partner_procedure_head_cf", "name": "相关方过程定义表", "id": 38661, "alias": "TERP_MIGRATE$gen_partner_procedure_head_cf", "desc": "相关方过程定义表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_partner_procedure_head_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_partner_procedure_head_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "partner_procedure_item", "name": "过程定义项目行表", "alias": "partnerProcedureItem", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "过程定义项目行表", "columnName": "partner_procedure_item", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_partner_procedure_head_cf", "currentModelFieldAlias": "partnerProcedureItem", "relationModelAlias": "TERP_MIGRATE$gen_partner_procedure_item_cf", "linkModelFieldAlias": "genPartnerProcedureHeadCfId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_partner_procedure_item_cf", "linkModelAlias": "TERP_MIGRATE$gen_partner_procedure_item_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_partner_procedure_head_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_addr_type_cf", "name": "地址库表", "id": 32417, "alias": "TERP_MIGRATE$gen_addr_type_cf", "desc": "地址库表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "addr_code", "name": "地址库编码", "alias": "addrCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址库编码", "columnName": "addr_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "addr_name", "name": "地址库名称", "alias": "addrName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址库名称", "columnName": "addr_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "post_code", "name": "邮政编码", "alias": "postCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮政编码", "columnName": "post_code", "length": 16, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_id", "name": "国家", "alias": "counId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家", "columnName": "coun_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "currentModelFieldAlias": "counId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_leaf", "name": "是否叶子节点", "alias": "<PERSON><PERSON><PERSON><PERSON>", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否叶子节点", "columnName": "is_leaf", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "addr_parent_id", "name": "父节点", "alias": "addrParentId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "父节点", "columnName": "addr_parent_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "currentModelFieldAlias": "addrParentId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "pln_soa_regcons_md_id", "name": "plnSoaRegconsMdId", "alias": "plnSoaRegconsMdId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "comment": "plnSoaRegconsMdId", "columnName": "pln_soa_regcons_md_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "currentModelFieldAlias": "plnSoaRegconsMdId", "relationModelAlias": "TERP_MIGRATE$pln_soa_regcons_md", "sync": false, "relationModelKey": "TERP_MIGRATE$pln_soa_regcons_md"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": true, "selfRelationFieldAlias": "addrParentId"}, "tableName": "gen_addr_type_cf", "mainField": "addr_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "addrName"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$org_com_org_cf", "name": "公司组织配置表", "id": 32258, "alias": "TERP_MIGRATE$org_com_org_cf", "desc": "公司组织配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "公司组织编码", "alias": "code", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司组织编码", "columnName": "code", "length": 30, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "公司组织名称", "alias": "name", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司组织名称", "columnName": "name", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_id", "name": "币种", "alias": "currId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "币种", "columnName": "curr_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "currId", "relationModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_curr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_id", "name": "关联公司", "alias": "comId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联公司", "columnName": "com_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "comId", "relationModelAlias": "TERP_MIGRATE$gen_com_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_com_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "chacc_id", "name": "公司后续会计科目表", "alias": "chaccId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司后续会计科目表", "columnName": "chacc_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "chaccId", "relationModelAlias": "TERP_MIGRATE$gen_chart_acc_head_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_chart_acc_head_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_period_id", "name": "公司后续记账期间", "alias": "comPeriodId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司后续记账期间", "columnName": "com_period_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "comPeriodId", "relationModelAlias": "TERP_MIGRATE$gen_com_period_head_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_com_period_head_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "DELETED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_id", "name": "国家", "alias": "counId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家", "columnName": "coun_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "counId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_ncc_code", "name": "NCC组织编码", "alias": "extWqNccCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NCC组织编码", "columnName": "ext_wq_ncc_code", "length": 30, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_sap_code_wq", "name": "SAP组织编码", "alias": "extWqSapCodeWq", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "SAP组织编码", "columnName": "ext_wq_sap_code_wq", "length": 30, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_nc_code_wq", "name": "NC组织编码", "alias": "extWqNcCodeWq", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NC组织编码", "columnName": "ext_wq_nc_code_wq", "length": 30, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_k3_code_wq", "name": "K3组织编码", "alias": "extWqK3CodeWq", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "K3组织编码", "columnName": "ext_wq_k3_code_wq", "length": 30, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_com_type", "name": "公司类型", "alias": "extWqComType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司类型", "columnName": "ext_wq_com_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "内部公司", "value": "INTERNAL"}, {"label": "外部公司", "value": "EXTERNAL"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_abbr", "name": "公司英文缩写", "alias": "extAbbr", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司英文缩写", "columnName": "ext_abbr", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_est_bank", "name": "开户银行", "alias": "extEstBank", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "开户银行", "columnName": "ext_est_bank", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_est_name", "name": "开户名称", "alias": "extEstName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "开户名称", "columnName": "ext_est_name", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_bank_acct", "name": "银行账号", "alias": "extBankAcct", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "银行账号", "columnName": "ext_bank_acct", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "gen_wc_head_id", "name": "关联工作日日历", "alias": "genWcHeadId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联工作日日历", "columnName": "gen_wc_head_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "genWcHeadId", "relationModelAlias": "TERP_MIGRATE$gen_wc_head_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_wc_head_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "comm_uscc", "name": "统一社会信用代码", "alias": "commUscc", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "统一社会信用代码", "columnName": "comm_uscc", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "lgl_repr", "name": "法定代表人", "alias": "lglRepr", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法定代表人", "columnName": "lgl_repr", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "addr_id", "name": "行政区划", "alias": "addrId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "行政区划", "columnName": "addr_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "addrId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "addr_desc", "name": "详细地址", "alias": "addrDesc", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "详细地址", "columnName": "addr_desc", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "fin_glm_as_sys_cf_id", "name": "finGlmAsSysCfId", "alias": "finGlmAsSysCfId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "columnName": "fin_glm_as_sys_cf_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_com_org_cf", "currentModelFieldAlias": "finGlmAsSysCfId", "relationModelAlias": "TERP_MIGRATE$fin_glm_as_sys_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$fin_glm_as_sys_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "org_com_org_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_com_type_cf", "name": "公司配置表", "id": 32404, "alias": "TERP_MIGRATE$gen_com_type_cf", "desc": "公司配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "公司编码", "alias": "code", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司编码", "columnName": "code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "公司名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司名称", "columnName": "name", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "公司状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删", "value": "DELETED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ver", "name": "版本号", "alias": "ver", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "版本号", "columnName": "ver", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "is_latest_version", "name": "是否最新版本", "alias": "isLatestVersion", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否最新版本", "columnName": "is_latest_version", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "country_id", "name": "国家", "alias": "countryId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家", "columnName": "country_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "countryId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "postcode", "name": "邮编", "alias": "postcode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮编", "columnName": "postcode", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "coordinate", "name": "经纬度坐标", "alias": "coordinate", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经纬度坐标", "columnName": "coordinate", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "area", "name": "区号", "alias": "area", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "区号", "columnName": "area", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "phone", "name": "手机", "alias": "phone", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "手机", "columnName": "phone", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "bankaccount", "name": "银行账户", "alias": "bankaccount", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "银行账户", "columnName": "bankaccount", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "bankaccount_code", "name": "银行编号", "alias": "bankaccountCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "银行编号", "columnName": "bankaccount_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "bank_name", "name": "银行名称", "alias": "bankName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "银行名称", "columnName": "bank_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "com_corporation", "name": "法定代表人", "alias": "comCorporation", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法定代表人", "columnName": "com_corporation", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "open_date", "name": "营业期限开始日期", "alias": "openDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "营业期限开始日期", "columnName": "open_date", "encrypted": false}, "type": "DataStructField"}, {"key": "close_date", "name": "营业期限结束日期", "alias": "closeDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "营业期限结束日期", "columnName": "close_date", "encrypted": false}, "type": "DataStructField"}, {"key": "enterprise_type", "name": "企业类型", "alias": "enterpriseType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "企业类型", "columnName": "enterprise_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "内部公司", "value": "INTERNAL"}, {"label": "外部公司", "value": "EXTERNAL"}, {"label": "供应商公司", "value": "VENDOR"}, {"label": "生产", "value": "PRODUCE"}, {"label": "贸易", "value": "TRADE"}, {"label": "物流", "value": "LOGISTICS"}, {"label": "其他", "value": "OTHERS"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "assurance_cnt", "name": "参保人数", "alias": "assuranceCnt", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "参保人数", "columnName": "assurance_cnt", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "old_name", "name": "曾用名", "alias": "old<PERSON>ame", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "曾用名", "columnName": "old_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "socialcredit_code", "name": "统一社会信用代码", "alias": "socialcreditCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "统一社会信用代码", "columnName": "socialcredit_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "address_id", "name": "注册地址", "alias": "addressId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "注册地址", "columnName": "address_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "addressId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "address_detail", "name": "注册详细地址", "alias": "addressDetail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "注册详细地址", "columnName": "address_detail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "biz_scope", "name": "经营范围", "alias": "bizScope", "props": {"fieldType": "MULTI_TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经营范围", "columnName": "biz_scope", "encrypted": false}, "type": "DataStructField"}, {"key": "biz_status", "name": "经营状态", "alias": "bizStatus", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经营状态", "columnName": "biz_status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "存续", "value": "EXISTENCE"}, {"label": "在业", "value": "RUNNING"}, {"label": "吊销", "value": "DEACTIVE"}, {"label": "注销", "value": "LOGOUT"}, {"label": "迁入", "value": "IMMIGRATION"}, {"label": "迁出", "value": "EMIGRATION"}, {"label": "停业", "value": "CLOSED"}, {"label": "清算", "value": "CLEAR"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "registered_capital", "name": "注册资本", "alias": "registeredCapital", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "注册资本", "columnName": "registered_capital", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "paidin_capital", "name": "实缴资本", "alias": "paidinCapital", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "实缴资本", "columnName": "paidin_capital", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_unified_code", "name": "是否三证合一", "alias": "isUnifiedCode", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否三证合一", "columnName": "is_unified_code", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "biz_license_no", "name": "营业执照号", "alias": "bizLicenseNo", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "营业执照号", "columnName": "biz_license_no", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "biz_license_scan", "name": "营业执照扫描件", "alias": "bizLicenseScan", "props": {"fieldType": "ATTACHMENT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "营业执照扫描件", "columnName": "biz_license_scan", "length": 256, "attachmentProps": {"multi": false}, "encrypted": false}, "type": "DataStructField"}, {"key": "org_code", "name": "组织机构代码", "alias": "orgCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织机构代码", "columnName": "org_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "found_date", "name": "成立日期", "alias": "foundDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "成立日期", "columnName": "found_date", "encrypted": false}, "type": "DataStructField"}, {"key": "taxpayers_num", "name": "纳税人识别号", "alias": "taxpayersNum", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "纳税人识别号", "columnName": "taxpayers_num", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "taxpayers_type", "name": "纳税人类别", "alias": "taxpayersType", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "纳税人类别", "columnName": "taxpayers_type", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "biz_sector", "name": "行业", "alias": "bizSector", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "行业", "columnName": "biz_sector", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "registration_authority", "name": "登记机关", "alias": "registrationAuthority", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "登记机关", "columnName": "registration_authority", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name_in_en", "name": "英文名称", "alias": "nameInEn", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "英文名称", "columnName": "name_in_en", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "business_registration_no", "name": "工商注册号", "alias": "businessRegistrationNo", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "工商注册号", "columnName": "business_registration_no", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "issue_date", "name": "核准日期", "alias": "issueDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "核准日期", "columnName": "issue_date", "encrypted": false}, "type": "DataStructField"}, {"key": "staff_size", "name": "人员规模", "alias": "staffSize", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "人员规模", "columnName": "staff_size", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "offical_website", "name": "官网", "alias": "officalWebsite", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "官网", "columnName": "offical_website", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "mail", "name": "邮箱", "alias": "mail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮箱", "columnName": "mail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "intro", "name": "简介", "alias": "intro", "props": {"fieldType": "MULTI_TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "简介", "columnName": "intro", "encrypted": false}, "type": "DataStructField"}, {"key": "capital_currency", "name": "注册资本币种", "alias": "capitalCurrency", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "注册资本币种", "columnName": "capital_currency", "length": 256, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "capitalCurrency", "relationModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_curr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_is_signature", "name": "是否开通签章", "alias": "extWqIsSignature", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否开通签章", "columnName": "ext_wq_is_signature", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_graphic_seal", "name": "公司图形章", "alias": "extWqGraphicSeal", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司图形章", "columnName": "ext_wq_graphic_seal", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "extWqGraphicSeal", "relationModelAlias": "TERP_MIGRATE$ext_wq_gen_com_graphic_seal_cf", "linkModelFieldAlias": "genComTypeCfId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_wq_gen_com_graphic_seal_cf", "linkModelAlias": "TERP_MIGRATE$ext_wq_gen_com_graphic_seal_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_payer_type", "name": "纳税人资质", "alias": "taxPayerType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "纳税人资质", "columnName": "tax_payer_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "增值税一般纳税人", "value": "VAT_PAYER"}, {"label": "增值税小规模纳税人", "value": "VAT_SPAYER"}, {"label": "非增值税纳税人", "value": "NOT_VAT"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "corporation_credential_type", "name": "法人证件类型", "alias": "corporationCredentialType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法人证件类型", "columnName": "corporation_credential_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "身份证", "value": "ID_CARD"}, {"label": "护照", "value": "PASSPORT"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "corporation_credential_code", "name": "法人证件号", "alias": "corporationCredentialCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法人证件号", "columnName": "corporation_credential_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "corporation_credential_copy", "name": "法定代表人证件复印件", "alias": "corporationCredentialCopy", "props": {"fieldType": "ATTACHMENT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法定代表人证件复印件", "columnName": "corporation_credential_copy", "length": 4000, "attachmentProps": {"multi": true}, "encrypted": false}, "type": "DataStructField"}, {"key": "corporation_credential_phone", "name": "法定代表人联系电话", "alias": "corporationCredentialPhone", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法定代表人联系电话", "columnName": "corporation_credential_phone", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "actual_location", "name": "实际所在地区", "alias": "actualLocation", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "实际所在地区", "columnName": "actual_location", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_com_type_cf", "currentModelFieldAlias": "actualLocation", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "actual_address", "name": "实际办公地址", "alias": "actual<PERSON>ddress", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "实际办公地址", "columnName": "actual_address", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "com_type", "name": "公司类型", "alias": "comType", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司类型", "columnName": "com_type", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_com_type_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "user", "name": "用户", "id": -100, "alias": "TERP_MIGRATE$user", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "nickname", "name": "昵称", "alias": "nickname", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "nickname", "encrypted": false}, "type": "DataStructField"}, {"key": "username", "name": "用户名", "alias": "username", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "username", "encrypted": false}, "type": "DataStructField"}, {"key": "email", "name": "用户邮箱", "alias": "email", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "email", "encrypted": false}, "type": "DataStructField"}, {"key": "mobile", "name": "用户手机", "alias": "mobile", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "mobile", "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": true, "self": false}, "mainField": "username", "type": "SYSTEM", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "username"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_vend_type_cf", "name": "供应商类型", "id": 31756, "alias": "TERP_MIGRATE$gen_vend_type_cf", "desc": "供应商类型", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_type", "name": "类型编码", "alias": "vendType", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类型编码", "columnName": "vend_type", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_type_name", "name": "类型名称", "alias": "vendTypeName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类型名称", "columnName": "vend_type_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "remark", "name": "描述", "alias": "remark", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "描述", "columnName": "remark", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_onetime", "name": "是否一次性类型", "alias": "isOnetime", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否一次性类型", "columnName": "is_onetime", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "未启用", "value": "inactive"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "deleted"}, {"label": "草稿", "value": "draft"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_internal", "name": "是否为内部公司", "alias": "isInternal", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否为内部公司", "columnName": "is_internal", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "partner_procedure_ref", "name": "相关方过程定义", "alias": "partnerProcedureRef", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "相关方过程定义", "columnName": "partner_procedure_ref", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_type_cf", "currentModelFieldAlias": "partnerProcedureRef", "relationModelAlias": "TERP_MIGRATE$gen_partner_procedure_head_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_partner_procedure_head_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "attachment_procedure_ref", "name": "附件过程定义", "alias": "attachmentProcedureRef", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "附件过程定义", "columnName": "attachment_procedure_ref", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_type_cf", "currentModelFieldAlias": "attachmentProcedureRef", "relationModelAlias": "TERP_MIGRATE$gen_attachment_procedure_head_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_attachment_procedure_head_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_vend_type_cf", "mainField": "vend_type_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "vendTypeName"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$org_pur_org_cf", "name": "采购组织配置表", "id": 32260, "alias": "TERP_MIGRATE$org_pur_org_cf", "desc": "采购组织配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "组织编码", "alias": "code", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织编码", "columnName": "code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "组织名称", "alias": "name", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "com_org_code", "name": "所属公司组织", "alias": "comOrgCode", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "所属公司组织", "columnName": "com_org_code", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "comOrgCode", "relationModelAlias": "TERP_MIGRATE$org_com_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_com_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "language_id", "name": "语言", "alias": "languageId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言", "columnName": "language_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "languageId", "relationModelAlias": "TERP_MIGRATE$gen_language_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_language_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "area", "name": "区号", "alias": "area", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "区号", "columnName": "area", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "tele", "name": "电话/分机号", "alias": "tele", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "电话/分机号", "columnName": "tele", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "fax", "name": "传真/分机号", "alias": "fax", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "传真/分机号", "columnName": "fax", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "mail", "name": "邮箱", "alias": "mail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮箱", "columnName": "mail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "phone", "name": "手机", "alias": "phone", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "手机", "columnName": "phone", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "address_id", "name": "地址", "alias": "addressId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址", "columnName": "address_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "addressId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "address_detail", "name": "街道", "alias": "addressDetail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "街道", "columnName": "address_detail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "postcode", "name": "邮编", "alias": "postcode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮编", "columnName": "postcode", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "coordinate", "name": "经纬度坐标", "alias": "coordinate", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经纬度坐标", "columnName": "coordinate", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_code", "name": "公司间开票的供应商编号", "alias": "vendCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司间开票的供应商编号", "columnName": "vend_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_code", "name": "公司间开票的客户编号", "alias": "custCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司间开票的客户编号", "columnName": "cust_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "DELETED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_org_id", "name": "库存组织", "alias": "invOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "库存组织", "columnName": "inv_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "invOrgId", "relationModelAlias": "TERP_MIGRATE$org_inv_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_inv_org_list", "name": "服务组织", "alias": "extWqInvOrgList", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "服务组织", "columnName": "ext_wq_inv_org_list", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "extWqInvOrgList", "relationModelAlias": "TERP_MIGRATE$ext_wq_pur_inv_org_cf", "linkModelFieldAlias": "orgPurOrgCfId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_wq_pur_inv_org_cf", "linkModelAlias": "TERP_MIGRATE$ext_wq_pur_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_vend_warehouse", "name": "供应商库配置", "alias": "extWqVendWarehouse", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "comment": "供应商库配置", "columnName": "ext_wq_vend_warehouse", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_pur_org_cf", "currentModelFieldAlias": "extWqVendWarehouse", "relationModelAlias": "TERP_MIGRATE$gen_vend_pur_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_pur_type_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "org_pur_org_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_timezone_type_cf", "name": "时区配置表", "id": 32395, "alias": "TERP_MIGRATE$gen_timezone_type_cf", "desc": "时区配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_code", "name": "时区编码", "alias": "timezoneCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区编码", "columnName": "timezone_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_desc", "name": "时区描述", "alias": "timezoneDesc", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区描述", "columnName": "timezone_desc", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_id", "name": "时区国家", "alias": "counId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区国家", "columnName": "coun_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "currentModelFieldAlias": "counId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_district", "name": "时区区划", "alias": "timezoneDistrict", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区区划", "columnName": "timezone_district", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "currentModelFieldAlias": "timezoneDistrict", "relationModelAlias": "TERP_MIGRATE$gen_addr_acc_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_acc_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_format", "name": "时区格式", "alias": "timezoneFormat", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区格式", "columnName": "timezone_format", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "UTC", "value": "UTC"}, {"label": "GMT", "value": "GMT"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_summer_time", "name": "启用夏令时", "alias": "isSummerTime", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "启用夏令时", "columnName": "is_summer_time", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "summer_time_start", "name": "夏令时开始时间", "alias": "summerTimeStart", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "夏令时开始时间", "columnName": "summer_time_start", "encrypted": false}, "type": "DataStructField"}, {"key": "summer_time_end", "name": "夏令时结束时间", "alias": "summerTimeEnd", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "夏令时结束时间", "columnName": "summer_time_end", "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "已启用", "value": "ENABLED"}, {"label": "已停", "value": "DISABLED"}]}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_timezone_type_cf", "mainField": "timezone_desc", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "timezoneDesc"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_vend_level_cf", "name": "供应商等级", "id": 50462, "alias": "TERP_MIGRATE$gen_vend_level_cf", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_level_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_level_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_level_code", "name": "等级编码", "alias": "vendLevelCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "等级编码", "columnName": "vend_level_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_level_name", "name": "等级名称", "alias": "vendLevelName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "等级名称", "columnName": "vend_level_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "remark", "name": "备注", "alias": "remark", "props": {"fieldType": "MULTI_TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "备注", "columnName": "remark", "encrypted": false}, "type": "DataStructField"}, {"key": "pur_org", "name": "采购组织", "alias": "purOrg", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "采购组织", "columnName": "pur_org", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_level_cf", "currentModelFieldAlias": "purOrg", "relationModelAlias": "TERP_MIGRATE$org_pur_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_pur_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "score_range", "name": "分数区间", "alias": "scoreRange", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "分数区间", "columnName": "score_range", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "left_range_include", "name": "左区间开闭", "alias": "leftRangeInclude", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "左区间开闭", "columnName": "left_range_include", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "[", "value": "INCLUDE"}, {"label": "(", "value": "EXCLUDE"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "left_range", "name": "左区间", "alias": "leftRange", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "左区间", "columnName": "left_range", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "right_range_include", "name": "右区间开闭", "alias": "rightRangeInclude", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "右区间开闭", "columnName": "right_range_include", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "]", "value": "INCLUDE"}, {"label": ")", "value": "EXCLUDE"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "right_range", "name": "右区间", "alias": "rightRange", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "右区间", "columnName": "right_range", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "free", "name": "是否免保证金", "alias": "free", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否免保证金", "columnName": "free", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "freezed", "name": "是否冻结", "alias": "freezed", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否冻结", "columnName": "freezed", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "blocked", "name": "是否拉黑", "alias": "blocked", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否拉黑", "columnName": "blocked", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "expire_date", "name": "失效日期", "alias": "expireDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "失效日期", "columnName": "expire_date", "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}]}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_vend_level_cf", "mainField": "vend_level_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "vendLevelName"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$org_sls_dc_cf", "name": "销售渠道配置表", "id": 32399, "alias": "TERP_MIGRATE$org_sls_dc_cf", "desc": "销售渠道配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_dc_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_dc_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "渠道编码", "alias": "code", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "渠道编码", "columnName": "code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "渠道名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "渠道名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "org_sls_dc_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$org_inv_org_cf", "name": "库存组织配置表", "id": 32259, "alias": "TERP_MIGRATE$org_inv_org_cf", "desc": "库存组织配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "组织编码", "alias": "code", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织编码", "columnName": "code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "组织名称", "alias": "name", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "com_org_id", "name": "所属公司组织", "alias": "comOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "所属公司组织", "columnName": "com_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "comOrgId", "relationModelAlias": "TERP_MIGRATE$org_com_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_com_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "language_id", "name": "语言", "alias": "languageId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言", "columnName": "language_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "languageId", "relationModelAlias": "TERP_MIGRATE$gen_language_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_language_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "area", "name": "区号", "alias": "area", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "区号", "columnName": "area", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "tele", "name": "电话/分机号", "alias": "tele", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "电话/分机号", "columnName": "tele", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "fax", "name": "传真/分机号", "alias": "fax", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "传真/分机号", "columnName": "fax", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "mail", "name": "邮箱", "alias": "mail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮箱", "columnName": "mail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "phone", "name": "手机", "alias": "phone", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "手机", "columnName": "phone", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "address_id", "name": "地址", "alias": "addressId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址", "columnName": "address_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "addressId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "address_detail", "name": "详细地址", "alias": "addressDetail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "详细地址", "columnName": "address_detail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "postcode", "name": "邮编", "alias": "postcode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮编", "columnName": "postcode", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "coordinate", "name": "经纬度坐标", "alias": "coordinate", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经纬度坐标", "columnName": "coordinate", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_code", "name": "公司间开票的供应商编号", "alias": "vendCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司间开票的供应商编号", "columnName": "vend_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "DELETED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "pur_org_id", "name": "采购组织", "alias": "purOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "采购组织", "columnName": "pur_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "purOrgId", "relationModelAlias": "TERP_MIGRATE$org_pur_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_pur_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "sls_org_id", "name": "销售组织", "alias": "slsOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售组织", "columnName": "sls_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "slsOrgId", "relationModelAlias": "TERP_MIGRATE$org_sls_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_sls_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "sls_cust_info_id", "name": "客户", "alias": "slsCustInfoId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户", "columnName": "sls_cust_info_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "slsCustInfoId", "relationModelAlias": "TERP_MIGRATE$gen_cust_info_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_cust_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "pur_vend_info_id", "name": "供应商（内采使用）", "alias": "purVendInfoId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商（内采使用）", "columnName": "pur_vend_info_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "purVendInfoId", "relationModelAlias": "TERP_MIGRATE$gen_vend_info_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "org_sls_dc_id", "name": "销售渠道", "alias": "orgSlsDcId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售渠道", "columnName": "org_sls_dc_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "orgSlsDcId", "relationModelAlias": "TERP_MIGRATE$org_sls_dc_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_sls_dc_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_belong_type", "name": "仓库归属类型", "alias": "extBelongType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": true, "isSystemField": false, "autoGenerated": false, "comment": "仓库归属类型", "columnName": "ext_belong_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "元亨仓", "value": "YHC"}, {"label": "外仓", "value": "WC"}, {"label": "浮仓", "value": "FC"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "testbom_header1_id", "name": "testbom_header1_id", "alias": "testbomHeader1Id", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "testbom_header1_id", "columnName": "testbom_header1_id", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_ncc_code", "name": "NCC组织编码", "alias": "extWqNccCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NCC组织编码", "columnName": "ext_wq_ncc_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_nc_code", "name": "NC组织编码", "alias": "extWqNcCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NC组织编码", "columnName": "ext_wq_nc_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_sap_code", "name": "SAP组织编码", "alias": "extWqSapCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "SAP组织编码", "columnName": "ext_wq_sap_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_k3_code", "name": "K3组织编码", "alias": "extWqK3Code", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "K3组织编码", "columnName": "ext_wq_k3_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_com_info", "name": "公司信息", "alias": "extWqComInfo", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司信息", "columnName": "ext_wq_com_info", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_inv_org_cf", "currentModelFieldAlias": "extWqComInfo", "relationModelAlias": "TERP_MIGRATE$gen_com_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_com_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_erp_type", "name": "集成ERP", "alias": "extWqErpType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "集成ERP", "columnName": "ext_wq_erp_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "无", "value": "EMPTY"}, {"label": "SAP", "value": "SAP"}, {"label": "NCC", "value": "NCC"}, {"label": "金蝶K3", "value": "K3"}, {"label": "NC502", "value": "NC"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_erp_pur_org_code", "name": "ERP采购组织编码", "alias": "extWqErpPurOrgCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "ERP采购组织编码", "columnName": "ext_wq_erp_pur_org_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_wq_erp_pur_org_name", "name": "ERP采购组织名称", "alias": "extWqErpPurOrgName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "ERP采购组织名称", "columnName": "ext_wq_erp_pur_org_name", "length": 128, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "org_inv_org_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_cust_info_md", "name": "客户基础信息", "id": 31795, "alias": "TERP_MIGRATE$gen_cust_info_md", "desc": "客户基础信息", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_code", "name": "客户编码", "alias": "custCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户编码", "columnName": "cust_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_name", "name": "客户名称", "alias": "custName", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户名称", "columnName": "cust_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_short_name", "name": "客户简称", "alias": "custShortName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户简称", "columnName": "cust_short_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "is_person", "name": "机构类型", "alias": "<PERSON><PERSON><PERSON>", "props": {"fieldType": "ENUM", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "机构类型", "columnName": "is_person", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "企业", "value": "COMPANY"}, {"label": " 个人", "value": "PERSON"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_id", "name": "公司编号", "alias": "comId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司编号", "columnName": "com_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "comId", "relationModelAlias": "TERP_MIGRATE$gen_com_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_com_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_role", "name": "客户角色", "alias": "custRole", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户角色", "columnName": "cust_role", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "经销商", "value": "DEALER"}, {"label": "代理商", "value": "AGENT"}, {"label": "代销商", "value": "COM_AGENT"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_vend", "name": "是否同时为供应商", "alias": "isVend", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否同时为供应商", "columnName": "is_vend", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_status", "name": "状态", "alias": "custStatus", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "cust_status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "DELETED"}, {"label": "待审批", "value": "WAIT_APPROVE"}, {"label": "审批不通过", "value": "APPROVE_FAIL"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "remark", "name": "备注", "alias": "remark", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "备注", "columnName": "remark", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_type_id", "name": "客户类型", "alias": "custTypeId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户类型", "columnName": "cust_type_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custTypeId", "relationModelAlias": "TERP_MIGRATE$gen_cust_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_cust_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_cate_id", "name": "客户类别", "alias": "custCateId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户类别", "columnName": "cust_cate_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custCateId", "relationModelAlias": "TERP_MIGRATE$gen_cust_cate_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_cust_cate_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "person_id", "name": "个人编号", "alias": "personId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "个人编号", "columnName": "person_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "personId", "relationModelAlias": "TERP_MIGRATE$gen_person_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_person_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "industry_id", "name": "所属行业", "alias": "industryId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "所属行业", "columnName": "industry_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "industryId", "relationModelAlias": "TERP_MIGRATE$gen_industry_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_industry_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_level_id", "name": "客户等级", "alias": "custLevelId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户等级", "columnName": "cust_level_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custLevelId", "relationModelAlias": "TERP_MIGRATE$gen_cust_level_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_cust_level_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_id", "name": "供应商编码", "alias": "vendId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商编码", "columnName": "vend_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "vendId", "relationModelAlias": "TERP_MIGRATE$gen_vend_info_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_pre_id", "name": "上级客户", "alias": "custPreId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "上级客户", "columnName": "cust_pre_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custPreId", "relationModelAlias": "TERP_MIGRATE$gen_cust_info_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_cust_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_id", "name": "国家", "alias": "counId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家", "columnName": "coun_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "counId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_addresses", "name": "客户地址", "alias": "custAddresses", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户地址", "columnName": "cust_addresses", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custAddresses", "relationModelAlias": "TERP_MIGRATE$gen_cust_addr_cf", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_addr_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_addr_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_persons", "name": "客户联系人", "alias": "cust<PERSON><PERSON><PERSON>", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户联系人", "columnName": "cust_persons", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "cust<PERSON><PERSON><PERSON>", "relationModelAlias": "TERP_MIGRATE$gen_cust_person_link_cf", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_person_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_person_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_banks", "name": "客户银行信息", "alias": "custBanks", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户银行信息", "columnName": "cust_banks", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custBanks", "relationModelAlias": "TERP_MIGRATE$gen_cust_bank_link_cf", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_bank_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_bank_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_sls_infos", "name": "客户销售信息", "alias": "custSlsInfos", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户销售信息", "columnName": "cust_sls_infos", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custSlsInfos", "relationModelAlias": "TERP_MIGRATE$gen_cust_sls_md", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_sls_md", "linkModelAlias": "TERP_MIGRATE$gen_cust_sls_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_fin_infos", "name": "客户财务信息", "alias": "custFinInfos", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户财务信息", "columnName": "cust_fin_infos", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custFinInfos", "relationModelAlias": "TERP_MIGRATE$gen_cust_fin_md", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_fin_md", "linkModelAlias": "TERP_MIGRATE$gen_cust_fin_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_cm_infos", "name": "客户信用管理", "alias": "custCmInfos", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户信用管理", "columnName": "cust_cm_infos", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custCmInfos", "relationModelAlias": "TERP_MIGRATE$gen_cust_credit_link_cf", "linkModelFieldAlias": "custRef", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_credit_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_credit_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "credit_check", "name": "是否开启信用管理", "alias": "creditCheck", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否开启信用管理", "columnName": "credit_check", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_org_id", "name": "库存组织", "alias": "invOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "库存组织", "columnName": "inv_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "invOrgId", "relationModelAlias": "TERP_MIGRATE$org_inv_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_partener_id", "name": "客户相关方", "alias": "custPartenerId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户相关方", "columnName": "cust_partener_id", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custPartenerId", "relationModelAlias": "TERP_MIGRATE$gen_cust_partner_link_md", "linkModelFieldAlias": "custRef", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_partner_link_md", "linkModelAlias": "TERP_MIGRATE$gen_cust_partner_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_attachment", "name": "客户附件", "alias": "custAttachment", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户附件", "columnName": "cust_attachment", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custAttachment", "relationModelAlias": "TERP_MIGRATE$gen_cust_attachment_link_md", "linkModelFieldAlias": "genCustInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_attachment_link_md", "linkModelAlias": "TERP_MIGRATE$gen_cust_attachment_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_unpaid_day", "name": "未支付天数", "alias": "custUnpaidDay", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": 0, "comment": "未支付天数", "columnName": "cust_unpaid_day", "intLength": 10, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_qualifications", "name": "客户资质", "alias": "custQualifications", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户资质", "columnName": "cust_qualifications", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custQualifications", "relationModelAlias": "TERP_MIGRATE$gen_cust_qualifications_link", "linkModelFieldAlias": "genCustInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_qualifications_link", "linkModelAlias": "TERP_MIGRATE$gen_cust_qualifications_link"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_cate", "name": "客户合作类目", "alias": "custCate", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户合作类目", "columnName": "cust_cate", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custCate", "relationModelAlias": "TERP_MIGRATE$gen_cust_cate_link_cf", "linkModelFieldAlias": "genCustInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_cate_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_cate_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_bus", "name": "客户商务视图", "alias": "custBus", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户商务视图", "columnName": "cust_bus", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custBus", "relationModelAlias": "TERP_MIGRATE$ext_sls_cust_bus_md", "linkModelFieldAlias": "genCustInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_sls_cust_bus_md", "linkModelAlias": "TERP_MIGRATE$ext_sls_cust_bus_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_name", "name": "公司名称", "alias": "comName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司名称", "columnName": "com_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "com_corporation", "name": "法定代表人", "alias": "comCorporation", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "法定代表人", "columnName": "com_corporation", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "socialcredit_code", "name": "统一社会信用代码", "alias": "socialcreditCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "统一社会信用代码", "columnName": "socialcredit_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "registered_capital", "name": "注册资本（万元）", "alias": "registeredCapital", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "注册资本（万元）", "columnName": "registered_capital", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "paidin_capital", "name": "实缴资本（万元）", "alias": "paidinCapital", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "实缴资本（万元）", "columnName": "paidin_capital", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "found_date", "name": "成立日期", "alias": "foundDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "成立日期", "columnName": "found_date", "encrypted": false}, "type": "DataStructField"}, {"key": "biz_scope", "name": "经营范围", "alias": "bizScope", "props": {"fieldType": "MULTI_TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经营范围", "columnName": "biz_scope", "encrypted": false}, "type": "DataStructField"}, {"key": "intro", "name": "简介", "alias": "intro", "props": {"fieldType": "MULTI_TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "简介", "columnName": "intro", "encrypted": false}, "type": "DataStructField"}, {"key": "person_name", "name": "个人姓名", "alias": "personName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "个人姓名", "columnName": "person_name", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "person_mail", "name": "个人邮箱", "alias": "personMail", "props": {"fieldType": "EMAIL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "个人邮箱", "columnName": "person_mail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "person_tel", "name": "个人电话", "alias": "personTel", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "个人电话", "columnName": "person_tel", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_adv_acc", "name": "扩展-客户账户关联", "alias": "extCustAdvAcc", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "扩展-客户账户关联", "columnName": "ext_cust_adv_acc", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extCustAdvAcc", "relationModelAlias": "TERP_MIGRATE$ext_sls_cust_adv_acc_link", "linkModelFieldAlias": "genCustInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_sls_cust_adv_acc_link", "linkModelAlias": "TERP_MIGRATE$ext_sls_cust_adv_acc_link"}, "encrypted": false}, "type": "DataStructField"}, {"key": "frozen_relv", "name": "冻结标识", "alias": "frozenRelv", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "冻结标识", "columnName": "frozen_relv", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_credit", "name": "客户信用", "alias": "extCustCredit", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户信用", "columnName": "ext_cust_credit", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extCustCredit", "relationModelAlias": "TERP_MIGRATE$ext_sls_cust_adv_acc_link", "sync": false, "relationModelKey": "TERP_MIGRATE$ext_sls_cust_adv_acc_link"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_balance", "name": "客户余额", "alias": "extCustBalance", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户余额", "columnName": "ext_cust_balance", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extCustBalance", "relationModelAlias": "TERP_MIGRATE$ext_sls_cust_adv_acc_link", "sync": false, "relationModelKey": "TERP_MIGRATE$ext_sls_cust_adv_acc_link"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_child", "name": "下级客户", "alias": "extCustChild", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "下级客户", "columnName": "ext_cust_child", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extCustChild", "relationModelAlias": "TERP_MIGRATE$gen_cust_info_md", "linkModelFieldAlias": "custPreId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_info_md", "linkModelAlias": "TERP_MIGRATE$gen_cust_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "approve_code", "name": "授信审批号", "alias": "approveCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "授信审批号", "columnName": "approve_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_child_cate", "name": "下级客户类目", "alias": "extChildCate", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "下级客户类目", "columnName": "ext_child_cate", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extChildCate", "relationModelAlias": "TERP_MIGRATE$ext_gen_child_cust_cate", "linkModelFieldAlias": "custRef", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_gen_child_cust_cate", "linkModelAlias": "TERP_MIGRATE$ext_gen_child_cust_cate"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_child_bus", "name": "下级客户商务视图", "alias": "extChildBus", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "下级客户商务视图", "columnName": "ext_child_bus", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extChildBus", "relationModelAlias": "TERP_MIGRATE$ext_gen_child_bus", "linkModelFieldAlias": "custRef", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_gen_child_bus", "linkModelAlias": "TERP_MIGRATE$ext_gen_child_bus"}, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_user_link", "name": "用户-客户关联关系", "alias": "custUserLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "用户-客户关联关系", "columnName": "cust_user_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "custUserLink", "relationModelAlias": "TERP_MIGRATE$gen_cust_user_link_cf", "linkModelFieldAlias": "custId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_cust_user_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_cust_user_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_contract_people", "name": "企业联系人", "alias": "extCustContractPeople", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "企业联系人", "columnName": "ext_cust_contract_people", "length": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_contract_tel", "name": "联系电话", "alias": "extCustContractTel", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "联系电话", "columnName": "ext_cust_contract_tel", "length": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_contract_mail", "name": "联系邮箱", "alias": "extCustContractMail", "props": {"fieldType": "EMAIL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "联系邮箱", "columnName": "ext_cust_contract_mail", "length": 40, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_enterprise_type", "name": "企业类型", "alias": "extEnterpriseType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "企业类型", "columnName": "ext_enterprise_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "加油站", "value": "enum1"}, {"label": "工程建设或生产实体", "value": "enum2"}, {"label": "批发商", "value": "enum3"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_ncc_code", "name": "NCC编码", "alias": "extNccCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NCC编码", "columnName": "ext_ncc_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_contract_people2", "name": "企业联系人", "alias": "extCustContractPeople2", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "企业联系人", "columnName": "ext_cust_contract_people2", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_cust_info_md", "currentModelFieldAlias": "extCustContractPeople2", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_cust_contract_mail2", "name": "联系邮箱", "alias": "extCustContractMail2", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "联系邮箱", "columnName": "ext_cust_contract_mail2", "length": 40, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": true, "selfRelationFieldAlias": "custPreId"}, "tableName": "gen_cust_info_md", "mainField": "cust_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "custName"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_vend_info_md", "name": "供应商主数据", "id": 31755, "alias": "TERP_MIGRATE$gen_vend_info_md", "desc": "供应商主数据", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_code", "name": "供应商编码", "alias": "vendCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": true, "isSystemField": false, "autoGenerated": false, "comment": "供应商编码", "columnName": "vend_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_name_short", "name": "简称", "alias": "vendNameShort", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "简称", "columnName": "vend_name_short", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_person", "name": "机构类型", "alias": "<PERSON><PERSON><PERSON>", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "机构类型", "columnName": "is_person", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "公司", "value": "COMPANY"}, {"label": "个人", "value": "PERSON"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_id", "name": "关联公司编码", "alias": "comId", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联公司编码", "columnName": "com_id", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "person_id", "name": "关联个人编码", "alias": "personId", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联个人编码", "columnName": "person_id", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_source", "name": "来源", "alias": "vendSource", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "来源", "columnName": "vend_source", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "常规", "value": "EXTERNAL"}, {"label": "内部", "value": "INNER"}, {"label": "特殊", "value": "STRONG"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿", "value": "draft"}, {"label": "已提交", "value": "submitted"}, {"label": "已启用", "value": "enabled"}, {"label": "已停用", "value": "disabled"}, {"label": "已删除", "value": "deleted"}, {"label": "未启用", "value": "inactive"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_cust", "name": "是否同时为客户", "alias": "isCust", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否同时为客户", "columnName": "is_cust", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_id", "name": "客户编码", "alias": "custId", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "客户编码", "columnName": "cust_id", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "remark", "name": "备注", "alias": "remark", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "备注", "columnName": "remark", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_type", "name": "供应商类型", "alias": "vendType", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商类型", "columnName": "vend_type", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendType", "relationModelAlias": "TERP_MIGRATE$gen_vend_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_cate_id", "name": "供应商类目", "alias": "vendCateId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商类目", "columnName": "vend_cate_id", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendCateId", "relationModelAlias": "TERP_MIGRATE$gen_vend_cate_link_cf", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_cate_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_vend_cate_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_fin", "name": "财务视图", "alias": "vendFin", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "财务视图", "columnName": "vend_fin", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendFin", "relationModelAlias": "TERP_MIGRATE$gen_vend_fin_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_fin_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_fin_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_pur", "name": "采购视图", "alias": "vendPur", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "采购视图", "columnName": "vend_pur", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendPur", "relationModelAlias": "TERP_MIGRATE$gen_vend_pur_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_pur_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_pur_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_person_link", "name": "联系人", "alias": "vendPersonLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "联系人", "columnName": "vend_person_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendPersonLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_person_link_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_person_link_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_person_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_addr_link", "name": "地址", "alias": "vendAddrLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址", "columnName": "vend_addr_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendAddrLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_addr_link_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_addr_link_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_addr_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_bank_link", "name": "银行", "alias": "vendBankLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "银行", "columnName": "vend_bank_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendBankLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_bank_link_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_bank_link_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_bank_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_org_id", "name": "内部供应商库存组织", "alias": "invOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "内部供应商库存组织", "columnName": "inv_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "invOrgId", "relationModelAlias": "TERP_MIGRATE$org_inv_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_pur_id", "name": "供应商异常视图", "alias": "vendPurId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商异常视图", "columnName": "vend_pur_id", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendPurId", "relationModelAlias": "TERP_MIGRATE$gen_vend_punishment_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_punishment_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_punishment_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "pur_com_id", "name": "关联公司", "alias": "purComId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联公司", "columnName": "pur_com_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "purComId", "relationModelAlias": "TERP_MIGRATE$gen_com_type_cf", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_com_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "approve_status", "name": "审核状态", "alias": "approveStatus", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "审核状态", "columnName": "approve_status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "审核中", "value": "inprocess"}, {"label": "已通过", "value": "approved"}, {"label": "已拒绝", "value": "rejected"}, {"label": "已撤回", "value": "revoked"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "qualifications_link", "name": "关联资质", "alias": "qualificationsLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联资质", "columnName": "qualifications_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "qualificationsLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_qualifications_link_cf", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_qualifications_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_vend_qualifications_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "user_link", "name": "关联用户", "alias": "userLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联用户", "columnName": "user_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "userLink", "relationModelAlias": "TERP_MIGRATE$gen_user_vend_link_cf", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_user_vend_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_user_vend_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "attachment_link", "name": "关联附件", "alias": "attachmentLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联附件", "columnName": "attachment_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "attachmentLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_attachment_link_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_attachment_link_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_attachment_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "partner_link", "name": "关联相关方", "alias": "partnerLink", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联相关方", "columnName": "partner_link", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "partnerLink", "relationModelAlias": "TERP_MIGRATE$gen_vend_partner_link_md", "linkModelFieldAlias": "vendId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_partner_link_md", "linkModelAlias": "TERP_MIGRATE$gen_vend_partner_link_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_level", "name": "供应商等级", "alias": "vendLevel", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "供应商等级", "columnName": "vend_level", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendLevel", "relationModelAlias": "TERP_MIGRATE$gen_vend_level_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_level_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_vend_bus", "name": "商务视图", "alias": "extVendBus", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "商务视图", "columnName": "ext_vend_bus", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "extVendBus", "relationModelAlias": "TERP_MIGRATE$ext_pur_vend_bus_md", "linkModelFieldAlias": "genVendInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_pur_vend_bus_md", "linkModelAlias": "TERP_MIGRATE$ext_pur_vend_bus_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "vend_credit", "name": "信用账户", "alias": "vendCredit", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "信用账户", "columnName": "vend_credit", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "vendCredit", "relationModelAlias": "TERP_MIGRATE$gen_vend_credit_link_cf", "linkModelFieldAlias": "vendRef", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_vend_credit_link_cf", "linkModelAlias": "TERP_MIGRATE$gen_vend_credit_link_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_credit_limit", "name": "授信额度", "alias": "extCreditLimit", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "授信额度", "columnName": "ext_credit_limit", "intLength": 20, "scale": 6, "encrypted": false}, "type": "DataStructField"}, {"key": "investigation_result", "name": "考察结果", "alias": "investigationResult", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "考察结果", "columnName": "investigation_result", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "通过", "value": "PASSED"}, {"label": "不通过", "value": "REJECTED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_pur_vend_acc_", "name": "账户视图", "alias": "extPurVendAcc", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "账户视图", "columnName": "ext_pur_vend_acc_", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "extPurVendAcc", "relationModelAlias": "TERP_MIGRATE$ext_pur_vend_acc_md", "linkModelFieldAlias": "genVendInfoMdId", "sync": true, "relationModelKey": "TERP_MIGRATE$ext_pur_vend_acc_md", "linkModelAlias": "TERP_MIGRATE$ext_pur_vend_acc_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_upstream_supplier", "name": "上级供应商", "alias": "extUpstreamSupplier", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "上级供应商", "columnName": "ext_upstream_supplier", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_vend_info_md", "currentModelFieldAlias": "extUpstreamSupplier", "relationModelAlias": "TERP_MIGRATE$gen_vend_info_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_vend_info_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_vend_nature", "name": "企业性质", "alias": "extVendNature", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "企业性质", "columnName": "ext_vend_nature", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "国企或国有控股企业", "value": "SOE"}, {"label": "民营企业", "value": "PE"}, {"label": "央企", "value": "CE"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_vend_abbr", "name": "英文缩写", "alias": "extVendAbbr", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "英文缩写", "columnName": "ext_vend_abbr", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "context_flag", "name": "上下文bool", "alias": "contextFlag", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "上下文bool", "columnName": "context_flag", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "active", "name": "是否生效", "alias": "active", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否生效", "columnName": "active", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "overseas", "name": "境外供应商", "alias": "overseas", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "境外供应商", "columnName": "overseas", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "create_from", "name": "创建方式", "alias": "createFrom", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "创建方式", "columnName": "create_from", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "自主发起", "value": "VENDOR"}, {"label": "后台发起", "value": "PURCHASER"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_ncc_code", "name": "NCC编码", "alias": "extNccCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "NCC编码", "columnName": "ext_ncc_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "ext_credit_begin_date", "name": "授信生效开始日期", "alias": "extCreditBeginDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "授信生效开始日期", "columnName": "ext_credit_begin_date", "encrypted": false}, "type": "DataStructField"}, {"key": "ext_credit_end_date", "name": "授信生效结束日期", "alias": "extCreditEndDate", "props": {"fieldType": "DATE", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "授信生效结束日期", "columnName": "ext_credit_end_date", "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": true, "selfRelationFieldAlias": "extUpstreamSupplier"}, "tableName": "gen_vend_info_md", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_coun_type_cf", "name": "国家配置表", "id": 32388, "alias": "TERP_MIGRATE$gen_coun_type_cf", "desc": "国家配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_code", "name": "国家代码", "alias": "counCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家代码", "columnName": "coun_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_name", "name": "名称", "alias": "coun<PERSON><PERSON>", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "coun_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "language_id", "name": "语言", "alias": "languageId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言", "columnName": "language_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "languageId", "relationModelAlias": "TERP_MIGRATE$gen_language_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_language_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_id", "name": "币种", "alias": "currId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "币种", "columnName": "curr_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "currId", "relationModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_curr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "length": 256, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "default_relv", "name": "是否默认", "alias": "defaultRelv", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否默认", "columnName": "default_relv", "length": 1, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_coun_type_cf", "mainField": "coun_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "counCode"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$org_sls_org_cf", "name": "销售组织配置表", "id": 32257, "alias": "TERP_MIGRATE$org_sls_org_cf", "desc": "销售组织配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "code", "name": "组织编码", "alias": "code", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织编码", "columnName": "code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "组织名称", "alias": "name", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "组织名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_type_id", "name": "统计货币", "alias": "currTypeId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "统计货币", "columnName": "curr_type_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "currTypeId", "relationModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_curr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "com_org_id", "name": "所属公司组织", "alias": "comOrgId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "所属公司组织", "columnName": "com_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "comOrgId", "relationModelAlias": "TERP_MIGRATE$org_com_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_com_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "language_id", "name": "语言", "alias": "languageId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言", "columnName": "language_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "languageId", "relationModelAlias": "TERP_MIGRATE$gen_language_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_language_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "area_", "name": "区号", "alias": "area", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "区号", "columnName": "area_", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "tele", "name": "电话/分机号", "alias": "tele", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "电话/分机号", "columnName": "tele", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "fax", "name": "传真/分机号", "alias": "fax", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "传真/分机号", "columnName": "fax", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "mail", "name": "邮箱", "alias": "mail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮箱", "columnName": "mail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "phone", "name": "手机", "alias": "phone", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "手机", "columnName": "phone", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "address_id", "name": "地址", "alias": "addressId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "地址", "columnName": "address_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "addressId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "address_detail", "name": "街道", "alias": "addressDetail", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "街道", "columnName": "address_detail", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "postcode", "name": "邮编", "alias": "postcode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "邮编", "columnName": "postcode", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "coordinate", "name": "经纬度坐标", "alias": "coordinate", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "经纬度坐标", "columnName": "coordinate", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "cust_code", "name": "公司间开票的客户编号", "alias": "custCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "公司间开票的客户编号", "columnName": "cust_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删除", "value": "DELETED"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_org_id", "name": "库存组织", "alias": "invOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "库存组织", "columnName": "inv_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "invOrgId", "relationModelAlias": "TERP_MIGRATE$org_inv_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "fin_glm_as_sys_cf_id", "name": "finGlmAsSysCfId", "alias": "finGlmAsSysCfId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "columnName": "fin_glm_as_sys_cf_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$org_sls_org_cf", "currentModelFieldAlias": "finGlmAsSysCfId", "relationModelAlias": "TERP_MIGRATE$fin_glm_as_sys_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$fin_glm_as_sys_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "org_sls_org_cf", "mainField": "name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "name"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_language_type_cf", "name": "语言", "id": 32387, "alias": "TERP_MIGRATE$gen_language_type_cf", "desc": "语言", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_language_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_language_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "language_code", "name": "语言代码", "alias": "languageCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言代码", "columnName": "language_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "language_name", "name": "语言名称", "alias": "languageName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言名称", "columnName": "language_name", "length": 32, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_language_type_cf", "mainField": "language_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "languageName"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_curr_type_cf", "name": "币种配置表", "id": 32396, "alias": "TERP_MIGRATE$gen_curr_type_cf", "desc": "币种配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_code", "name": "币别编码", "alias": "currCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "币别编码", "columnName": "curr_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_name", "name": "名称", "alias": "currName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "curr_name", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_iso_name", "name": "ISO名称", "alias": "currIsoName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "ISO名称", "columnName": "curr_iso_name", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "decimal_place", "name": "小数位", "alias": "decimalPlace", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "小数位", "columnName": "decimal_place", "intLength": 8, "encrypted": false}, "type": "DataStructField"}, {"key": "symbol", "name": "币种符号", "alias": "symbol", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "币种符号", "columnName": "symbol", "length": 16, "encrypted": false}, "type": "DataStructField"}, {"key": "remark", "name": "备注", "alias": "remark", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "备注", "columnName": "remark", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_curr_type_cf", "mainField": "curr_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "currName"}, "searchModel": false, "type": "DataStruct"}]}