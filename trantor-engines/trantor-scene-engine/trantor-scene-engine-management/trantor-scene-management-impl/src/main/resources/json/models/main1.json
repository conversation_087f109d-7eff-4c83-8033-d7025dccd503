{"name": "主模型(应付单类型配置表)", "config": {"modelKey": "TERP_MIGRATE$fin_apm_ap_type_md", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$fin_apm_ap_type_md", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$fin_cm_pr_type_md", "relations": {}}, {"modelKey": "TERP_MIGRATE$fin_apm_ap_type_md", "relations": {}}, {"modelKey": "user", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$fin_cm_pr_type_md", "relations": {"LINK": [{"modelKey": "user", "relations": {}}, {"modelKey": "TERP_MIGRATE$fin_cm_pn_type_md", "relations": {}}]}}, {"modelKey": "user", "relations": {"LINK": []}}], "PARENT_CHILD": []}}, "models": [{"key": "TERP_MIGRATE$fin_cm_pr_type_md", "name": "付款申请单类型配置表", "alias": "TERP_MIGRATE$fin_cm_pr_type_md", "desc": "付款申请单类型配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_cm_pr_type_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_cm_pr_type_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "pr_type_code", "name": "类型编码", "alias": "prTypeCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类型编码", "columnName": "pr_type_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "单据名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "单据名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_acc_doc_relv", "name": "是否生成总账凭证", "alias": "isAccDocRelv", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否生成总账凭证", "columnName": "is_acc_doc_relv", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "是", "value": "YES"}, {"label": "否", "value": "NO"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "rel_pn_type_id", "name": "关联收付款单单据类型", "alias": "relPnTypeId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联收付款单单据类型", "columnName": "rel_pn_type_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_cm_pr_type_md", "currentModelFieldAlias": "relPnTypeId", "relationModelAlias": "TERP_MIGRATE$fin_cm_pn_type_md", "sync": false, "relationModelKey": "TERP_MIGRATE$fin_cm_pn_type_md"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "fin_cm_pr_type_md", "mainField": "pr_type_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "prTypeCode"}, "type": "DataStruct", "searchModel": false}, {"key": "TERP_MIGRATE$fin_apm_ap_type_md", "name": "应付单类型配置表", "alias": "TERP_MIGRATE$fin_apm_ap_type_md", "desc": "应付单类型配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_apm_ap_type_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_apm_ap_type_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "ap_type_code", "name": "类型编码", "alias": "apTypeCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类型编码", "columnName": "ap_type_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "单据名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "单据名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_acc_doc_relv", "name": "是否生成总账凭证", "alias": "isAccDocRelv", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否生成总账凭证", "columnName": "is_acc_doc_relv", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "是", "value": "YES"}, {"label": "否", "value": "NO"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "account_type", "name": "入账方式", "alias": "accountType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "入账方式", "columnName": "account_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "暂估应付", "value": "EST"}, {"label": "财务应付", "value": "FIN"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "fin_doc_type_id", "name": "暂估对应财务应付单据类型", "alias": "finDocTypeId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "暂估对应财务应付单据类型", "columnName": "fin_doc_type_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_apm_ap_type_md", "currentModelFieldAlias": "finDocTypeId", "relationModelAlias": "TERP_MIGRATE$fin_apm_ap_type_md", "sync": false, "relationModelKey": "TERP_MIGRATE$fin_apm_ap_type_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "rel_pr_type_id", "name": "关联付款申请单单据类型", "alias": "relPrTypeId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联付款申请单单据类型", "columnName": "rel_pr_type_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_apm_ap_type_md", "currentModelFieldAlias": "relPrTypeId", "relationModelAlias": "TERP_MIGRATE$fin_cm_pr_type_md", "sync": false, "relationModelKey": "TERP_MIGRATE$fin_cm_pr_type_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_adjust_relv", "name": "是否调整类型", "alias": "isAdjustRelv", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": false, "comment": "是否调整类型", "columnName": "is_adjust_relv", "length": 1, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": true, "selfRelationFieldAlias": "finDocTypeId"}, "tableName": "fin_apm_ap_type_md", "mainField": "ap_type_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "apTypeCode"}, "type": "DataStruct", "searchModel": false}, {"key": "TERP_MIGRATE$fin_cm_pn_type_md", "name": "收付款单类型配置表", "alias": "TERP_MIGRATE$fin_cm_pn_type_md", "desc": "收付款单类型配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_cm_pn_type_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$fin_cm_pn_type_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "user", "sync": false, "relationModelKey": "user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "pn_type_code", "name": "类型编码", "alias": "pnTypeCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类型编码", "columnName": "pn_type_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "pn_class", "name": "单据分类", "alias": "pnClass", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "单据分类", "columnName": "pn_class", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "收款", "value": "COLLECTION"}, {"label": "付款", "value": "PAYMENT"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "name", "name": "单据名称", "alias": "name", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "单据名称", "columnName": "name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "is_acc_doc_relv", "name": "是否生成总账凭证", "alias": "isAccDocRelv", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否生成总账凭证", "columnName": "is_acc_doc_relv", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "是", "value": "YES"}, {"label": "否", "value": "NO"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "business_type", "name": "关联业务类型", "alias": "businessType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联业务类型", "columnName": "business_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "采销业务", "value": "PUR_SLS"}, {"label": "充值业务", "value": "RECHARGE"}, {"label": "其他业务", "value": "OTHER"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "create_default_status", "name": "创建后默认状态", "alias": "createDefaultStatus", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "创建后默认状态", "columnName": "create_default_status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "草稿", "value": "DRAFT"}, {"label": "提交", "value": "CONFIRM"}, {"label": "完成", "value": "DONE"}, {"label": "删除", "value": "DELETE"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_advance_relv", "name": "是否预收预付", "alias": "isAdvanceRelv", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否预收预付", "columnName": "is_advance_relv", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "finance_direction_type", "name": "资金流向", "alias": "financeDirectionType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "资金流向", "columnName": "finance_direction_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "流出", "value": "OUTFLOW"}, {"label": "流入", "value": "INFLOW"}]}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "fin_cm_pn_type_md", "mainField": "pn_type_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "pnTypeCode"}, "type": "DataStruct", "searchModel": false}, {"key": "user", "name": "用户", "alias": "user", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "nickname", "name": "昵称", "alias": "nickname", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "nickname", "encrypted": false}, "type": "DataStructField"}, {"key": "username", "name": "用户名", "alias": "username", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "username", "encrypted": false}, "type": "DataStructField"}, {"key": "email", "name": "用户邮箱", "alias": "email", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "email", "encrypted": false}, "type": "DataStructField"}, {"key": "mobile", "name": "用户手机", "alias": "mobile", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "mobile", "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": true, "self": false}, "mainField": "username", "type": "SYSTEM", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "username"}, "type": "DataStruct", "searchModel": false}]}