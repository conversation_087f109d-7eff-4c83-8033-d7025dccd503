{"name": "主模型(类目配置表)", "config": {"modelKey": "TERP_MIGRATE$gen_mat_cate_md", "relations": {"LINK": [{"modelKey": "VEND_2B$test", "relations": {"LINK": [{"modelKey": "VEND_2B$user", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$gen_qualifications_head_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$user", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$user", "relations": {"LINK": []}}, {"modelKey": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_mat_sls_md", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_mat_tax_type_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_coun_type_cf", "relations": {}}]}}, {"modelKey": "TERP_MIGRATE$gen_mat_cate_md", "relations": {"LINK": [{"modelKey": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "relations": {}}, {"modelKey": "VEND_2B$test", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_qualifications_head_cf", "relations": {}}, {"modelKey": "TERP_MIGRATE$user", "relations": {}}, {"modelKey": "TERP_MIGRATE$gen_mat_cate_md", "relations": {}}]}}], "PARENT_CHILD": []}}, "models": [{"key": "VEND_2B$test", "name": "blacklist_test", "id": 182411, "alias": "VEND_2B$test", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "VEND_2B$test", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "VEND_2B$user", "sync": false, "relationModelKey": "VEND_2B$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "VEND_2B$test", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "VEND_2B$user", "sync": false, "relationModelKey": "VEND_2B$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "test_id", "name": "测试编码", "alias": "testId", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "测试编码", "columnName": "test_id", "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "<PERSON><PERSON><PERSON>", "name": "测试字段", "alias": "<PERSON><PERSON><PERSON>", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "测试字段", "columnName": "<PERSON><PERSON><PERSON>", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "VEND_2B$test", "currentModelFieldAlias": "<PERSON><PERSON><PERSON>", "relationModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "linkModelFieldAlias": "testId", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_mat_cate_md", "linkModelAlias": "TERP_MIGRATE$gen_mat_cate_md"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "test", "mainField": "test_id", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "testId"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_mat_tax_type_cf", "name": "物料税分类", "id": 32301, "alias": "TERP_MIGRATE$gen_mat_tax_type_cf", "desc": "物料税分类", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_tax_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_tax_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_type", "name": "税收类别", "alias": "taxType", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "税收类别", "columnName": "tax_type", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_tax_type_cf", "currentModelFieldAlias": "taxType", "relationModelAlias": "TERP_MIGRATE$gen_coun_tax_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_tax_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_class_code", "name": "税分类编码", "alias": "taxClassCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "税分类编码", "columnName": "tax_class_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_class_desc", "name": "分类描述", "alias": "taxClassDesc", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "分类描述", "columnName": "tax_class_desc", "length": 256, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_mat_tax_type_cf", "mainField": "tax_class_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "taxClassCode"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_mat_sls_md", "name": "物料销售视图", "id": 32306, "alias": "TERP_MIGRATE$gen_mat_sls_md", "desc": "物料销售视图", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_code", "name": "物料编码", "alias": "matCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "物料编码", "columnName": "mat_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_name", "name": "物料名称", "alias": "<PERSON><PERSON><PERSON>", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "物料名称", "columnName": "mat_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "sls_org_id", "name": "销售组织", "alias": "slsOrgId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售组织", "columnName": "sls_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "slsOrgId", "relationModelAlias": "TERP_MIGRATE$org_sls_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_sls_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "sls_dc_id", "name": "销售渠道", "alias": "slsDcId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售渠道", "columnName": "sls_dc_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "slsDcId", "relationModelAlias": "TERP_MIGRATE$org_sls_dc_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_sls_dc_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "so_qty_min", "name": "最小订货量", "alias": "soQtyMin", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "最小订货量", "columnName": "so_qty_min", "encrypted": false}, "type": "DataStructField"}, {"key": "dn_qty_min", "name": "最小交货量", "alias": "dnQtyMin", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "最小交货量", "columnName": "dn_qty_min", "encrypted": false}, "type": "DataStructField"}, {"key": "sls_uom_id", "name": "销售单位", "alias": "slsUomId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售单位", "columnName": "sls_uom_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "slsUomId", "relationModelAlias": "TERP_MIGRATE$gen_uom_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_uom_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "allow_shortage_shortage_percent", "name": "允许缺发比例", "alias": "allowShortageShortagePercent", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "允许缺发比例", "columnName": "allow_shortage_shortage_percent", "encrypted": false}, "type": "DataStructField"}, {"key": "allow_surplus_surplus_percent", "name": "允许超发比例", "alias": "allowSurplusSurplusPercent", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "允许超发比例", "columnName": "allow_surplus_surplus_percent", "encrypted": false}, "type": "DataStructField"}, {"key": "processing_time", "name": "发货处理时间", "alias": "processingTime", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "defaultValue": 0, "comment": "发货处理时间", "columnName": "processing_time", "encrypted": false}, "type": "DataStructField"}, {"key": "sls_range_id", "name": "销售区域", "alias": "slsRangeId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售区域", "columnName": "sls_range_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "slsRangeId", "relationModelAlias": "TERP_MIGRATE$gen_addr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_addr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "gen_mat_md_id", "name": "gen_mat_md_id", "alias": "genMatMdId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "comment": "gen_mat_md_id", "columnName": "gen_mat_md_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "genMatMdId", "relationModelAlias": "TERP_MIGRATE$gen_mat_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_mat_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_classes", "name": "销售税分类", "alias": "taxClasses", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售税分类", "columnName": "tax_classes", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "taxClasses", "relationModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "linkModelFieldAlias": "matSlsId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "linkModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_org_id", "name": "默认库存组织", "alias": "invOrgId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "默认库存组织", "columnName": "inv_org_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "invOrgId", "relationModelAlias": "TERP_MIGRATE$org_inv_org_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_org_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "inv_lov_id", "name": "默认库存地点", "alias": "invLovId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "默认库存地点", "columnName": "inv_lov_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "currentModelFieldAlias": "invLovId", "relationModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$org_inv_loc_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_mat_sls_md", "mainField": "mat_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "matCode"}, "searchModel": false, "type": "DataStruct"}, {"key": "user", "name": "用户", "id": -100, "alias": "TERP_MIGRATE$user", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "nickname", "name": "昵称", "alias": "nickname", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "nickname", "encrypted": false}, "type": "DataStructField"}, {"key": "username", "name": "用户名", "alias": "username", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "username", "encrypted": false}, "type": "DataStructField"}, {"key": "email", "name": "用户邮箱", "alias": "email", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "email", "encrypted": false}, "type": "DataStructField"}, {"key": "mobile", "name": "用户手机", "alias": "mobile", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "mobile", "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": true, "self": false}, "mainField": "username", "type": "SYSTEM", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "username"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "name": "国家物料税分类行表", "id": 32275, "alias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "desc": "国家物料税分类行表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_id", "name": "国家", "alias": "counId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家", "columnName": "coun_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "currentModelFieldAlias": "counId", "relationModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_type", "name": "税类别", "alias": "taxType", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "税类别", "columnName": "tax_type", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_class_id", "name": "税分类", "alias": "taxClassId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "税分类", "columnName": "tax_class_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "currentModelFieldAlias": "taxClassId", "relationModelAlias": "TERP_MIGRATE$gen_mat_tax_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_mat_tax_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "tax_class_desc", "name": "分类描述", "alias": "taxClassDesc", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "分类描述", "columnName": "tax_class_desc", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_sls_id", "name": "物料销售视图信息", "alias": "matSlsId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "comment": "物料销售视图信息", "columnName": "mat_sls_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "currentModelFieldAlias": "matSlsId", "relationModelAlias": "TERP_MIGRATE$gen_mat_sls_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_mat_sls_md"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_coun_class_mat_tax_item_cf", "mainField": "tax_type", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "taxType"}, "searchModel": false, "type": "DataStruct"}, {"key": "user", "name": "用户", "id": -100, "alias": "VEND_2B$user", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": false, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "nickname", "name": "昵称", "alias": "nickname", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "nickname", "encrypted": false}, "type": "DataStructField"}, {"key": "username", "name": "用户名", "alias": "username", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "username", "encrypted": false}, "type": "DataStructField"}, {"key": "email", "name": "用户邮箱", "alias": "email", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "email", "encrypted": false}, "type": "DataStructField"}, {"key": "mobile", "name": "用户手机", "alias": "mobile", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "columnName": "mobile", "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": true, "self": false}, "mainField": "username", "type": "SYSTEM", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "username"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_coun_type_cf", "name": "国家配置表", "id": 32388, "alias": "TERP_MIGRATE$gen_coun_type_cf", "desc": "国家配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_code", "name": "国家代码", "alias": "counCode", "props": {"fieldType": "TEXT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "国家代码", "columnName": "coun_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "coun_name", "name": "名称", "alias": "coun<PERSON><PERSON>", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "名称", "columnName": "coun_name", "length": 64, "encrypted": false}, "type": "DataStructField"}, {"key": "language_id", "name": "语言", "alias": "languageId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "语言", "columnName": "language_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "languageId", "relationModelAlias": "TERP_MIGRATE$gen_language_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_language_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "curr_id", "name": "币种", "alias": "currId", "props": {"fieldType": "OBJECT", "required": true, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "币种", "columnName": "curr_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "currId", "relationModelAlias": "TERP_MIGRATE$gen_curr_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_curr_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "timezone_id", "name": "时区", "alias": "timezoneId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "时区", "columnName": "timezone_id", "length": 256, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_coun_type_cf", "currentModelFieldAlias": "timezoneId", "relationModelAlias": "TERP_MIGRATE$gen_timezone_type_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_timezone_type_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "default_relv", "name": "是否默认", "alias": "defaultRelv", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否默认", "columnName": "default_relv", "length": 1, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_coun_type_cf", "mainField": "coun_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "counCode"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_qualifications_head_cf", "name": "资质组", "id": 48340, "alias": "TERP_MIGRATE$gen_qualifications_head_cf", "desc": "资质组", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_qualifications_head_cf", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_qualifications_head_cf", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "qualifications_head_code", "name": "资质组编码", "alias": "qualificationsHeadCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "资质组编码", "columnName": "qualifications_head_code", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "qualifications_head_name", "name": "资质组名称", "alias": "qualificationsHeadName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": true, "isSystemField": false, "autoGenerated": false, "comment": "资质组名称", "columnName": "qualifications_head_name", "length": 256, "encrypted": false}, "type": "DataStructField"}, {"key": "qualifications_item", "name": "资质行", "alias": "qualificationsItem", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "资质行", "columnName": "qualifications_item", "relationMeta": {"relationType": "PARENT_CHILD", "currentModelAlias": "TERP_MIGRATE$gen_qualifications_head_cf", "currentModelFieldAlias": "qualificationsItem", "relationModelAlias": "TERP_MIGRATE$gen_qualifications_item_cf", "linkModelFieldAlias": "qualificationsHeadCfId", "sync": true, "relationModelKey": "TERP_MIGRATE$gen_qualifications_item_cf", "linkModelAlias": "TERP_MIGRATE$gen_qualifications_item_cf"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": false}, "tableName": "gen_qualifications_head_cf", "mainField": "qualifications_head_code", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "qualificationsHeadCode"}, "searchModel": false, "type": "DataStruct"}, {"key": "TERP_MIGRATE$gen_mat_cate_md", "name": "类目配置表", "id": 32414, "alias": "TERP_MIGRATE$gen_mat_cate_md", "desc": "类目配置表", "children": [{"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "intLength": 20, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "TERP_MIGRATE$user", "sync": false, "relationModelKey": "TERP_MIGRATE$user"}, "encrypted": false}, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_cate_code", "name": "类目编码", "alias": "matCateCode", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类目编码", "columnName": "mat_cate_code", "length": 32, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_cate_name", "name": "类目名称", "alias": "matCateName", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类目名称", "columnName": "mat_cate_name", "length": 128, "encrypted": false}, "type": "DataStructField"}, {"key": "is_leaf", "name": "是否叶子节点", "alias": "<PERSON><PERSON><PERSON><PERSON>", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "是否叶子节点", "columnName": "is_leaf", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "status", "name": "状态", "alias": "status", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "状态", "columnName": "status", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "启用", "value": "ENABLED"}, {"label": "停用", "value": "INACTIVE"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "mat_cate_parent", "name": "父类目", "alias": "mat<PERSON>ate<PERSON><PERSON>nt", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "父类目", "columnName": "mat_cate_parent", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "relationModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_mat_cate_md"}, "encrypted": false}, "type": "DataStructField"}, {"key": "gen_coun_class_mat_tax_item_cf_id", "name": "物料税", "alias": "genCounClassMatTaxItemCfId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "物料税", "columnName": "gen_coun_class_mat_tax_item_cf_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "genCounClassMatTaxItemCfId", "relationModelAlias": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_coun_class_mat_tax_item_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "qualifications_group_id", "name": "关联资质组", "alias": "qualificationsGroupId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "关联资质组", "columnName": "qualifications_group_id", "length": 256, "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "qualificationsGroupId", "relationModelAlias": "TERP_MIGRATE$gen_qualifications_head_cf", "sync": false, "relationModelKey": "TERP_MIGRATE$gen_qualifications_head_cf"}, "encrypted": false}, "type": "DataStructField"}, {"key": "is_limit_po_qualifications", "name": "采购订单是否限制资质", "alias": "isLimitPoQualifications", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "采购订单是否限制资质", "columnName": "is_limit_po_qualifications", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "is_limit_so_qualifications", "name": "销售订单是否限制资质", "alias": "isLimitSoQualifications", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "销售订单是否限制资质", "columnName": "is_limit_so_qualifications", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "is_limit_ct_qualifications", "name": "合同是否限制资质", "alias": "isLimitCtQualifications", "props": {"fieldType": "BOOL", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "合同是否限制资质", "columnName": "is_limit_ct_qualifications", "length": 1, "encrypted": false}, "type": "DataStructField"}, {"key": "path", "name": "类目路径", "alias": "path", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "类目路径", "columnName": "path", "length": 512, "encrypted": false}, "type": "DataStructField"}, {"key": "wq_mat_type", "name": "物料类型", "alias": "wqMatType", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "物料类型", "columnName": "wq_mat_type", "length": 256, "dictPros": {"multiSelect": false, "dictValues": [{"label": "经营物料", "value": "JYWL"}, {"label": "配件", "value": "PJ"}, {"label": "基建物资", "value": "JJWZ"}, {"label": "非生产物料", "value": "FSCWL"}, {"label": "非经营性废料", "value": "FJYXFL"}, {"label": "受委托加工料", "value": "SWTJGL"}]}, "encrypted": false}, "type": "DataStructField"}, {"key": "test_id", "name": "testId", "alias": "testId", "props": {"fieldType": "OBJECT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": true, "columnName": "test_id", "relationMeta": {"relationType": "LINK", "currentModelAlias": "TERP_MIGRATE$gen_mat_cate_md", "currentModelFieldAlias": "testId", "relationModelAlias": "VEND_2B$test", "sync": false, "relationModelKey": "VEND_2B$test"}, "encrypted": false}, "type": "DataStructField"}], "props": {"config": {"persist": false, "system": false, "self": true, "selfRelationFieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt"}, "tableName": "gen_mat_cate_md", "mainField": "mat_cate_name", "type": "PERSIST", "physicalDelete": false, "searchModel": false, "mainFieldAlias": "matCateName"}, "searchModel": false, "type": "DataStruct"}]}