import sys
import json
import os

def clean_json(json_data):
  if isinstance(json_data, dict):
    # 删除不需要的字段
    fields_to_delete = ["teamId", "teamCode", "appId", "parentKey", "access", "description"]
    for field in fields_to_delete:
      json_data.pop(field, None)

    # 删除值为 null 的字段
    keys_to_delete = [key for key, value in json_data.items() if value is None]
    for key in keys_to_delete:
      del json_data[key]

    # 递归处理子结构
    for value in json_data.values():
      if isinstance(value, (dict, list)):
        clean_json(value)
  elif isinstance(json_data, list):
    # 递归处理列表中的元素
    for item in json_data:
      clean_json(item)

# 检查命令行参数
if len(sys.argv) < 2:
  print("请提供要处理的 JSON 文件路径作为命令行参数")
  sys.exit(1)

# 获取命令行参数
path = sys.argv[1]

processed_files = []  # 记录已处理的文件路径

# 处理文件或文件夹
if os.path.isfile(path):
  # 处理单个文件
  try:
    with open(path, 'r', encoding='utf-8') as file:
      json_data = json.load(file)
  except FileNotFoundError:
    print(f"找不到文件: {path}")
    sys.exit(1)
  except json.JSONDecodeError:
    print(f"无法解析 JSON 文件: {path}")
    sys.exit(1)

  # 清理 JSON 数据
  clean_json(json_data)

  # 将清理后的数据写回原始 JSON 文件
  with open(path, 'w', encoding='utf-8') as file:
    json.dump(json_data, file, indent=2, ensure_ascii=False)

  processed_files.append(path)
  print("处理完成！")
elif os.path.isdir(path):
  # 处理文件夹
  for root, dirs, files in os.walk(path):
    for file in files:
      if file.endswith(".json"):
        file_path = os.path.join(root, file)
        try:
          with open(file_path, 'r', encoding='utf-8') as file:
            json_data = json.load(file)
        except FileNotFoundError:
          print(f"找不到文件: {file_path}")
          continue
        except json.JSONDecodeError:
          print(f"无法解析 JSON 文件: {file_path}")
          continue

        # 清理 JSON 数据
        clean_json(json_data)

        # 将清理后的数据写回原始 JSON 文件
        with open(file_path, 'w', encoding='utf-8') as file:
          json.dump(json_data, file, indent=2, ensure_ascii=False)

        processed_files.append(file_path)

  print("处理完成！")
else:
  print(f"无效的路径: {path}")

# 打印已处理的文件列表
print("已处理的文件：")
for file_path in processed_files:
  print(file_path)
