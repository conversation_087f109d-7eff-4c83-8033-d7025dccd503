package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.event.TrantorEventPublisher;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.management.dto.SceneBatchRequest;
import io.terminus.trantor2.scene.management.dto.SceneProfile;
import io.terminus.trantor2.scene.management.utils.SceneJsonUtil;
import io.terminus.trantor2.scene.management.vo.SceneMetaVO;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 场景元数据存储
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SceneManagerService {
    protected final SceneManagerQueryService sceneQueryService;
    protected final MenuConsoleQueryService menuQueryService;
    protected final SceneRepo sceneRepo;
    protected final ExtMetaService extMetaService;
    protected final TrantorIAMUserService trantorIAMUserService;
    protected final TrantorEventPublisher publisher;

    public Optional<SceneMeta> findByKeyWithViews(@NonNull String key) {
        return sceneQueryService.findByKey(key,EditUtil.ctxFromThreadLocal(), Collections.singletonList(MetaType.View.name()));
    }

    public Optional<SceneMeta> findByKeyWithAllChildren(@NonNull String key) {
        return sceneQueryService.findByKey(key, EditUtil.ctxFromThreadLocal(),
                Collections.singletonList(MetaType.View.name()));
    }

    public Optional<SceneMeta> findByKey(@NonNull String key, @Nullable Collection<String> typesIn) {
        return sceneQueryService.findByKey(key, EditUtil.ctxFromThreadLocal(), typesIn);
    }

    public Collection<SceneMeta> findByKeyIn(@NonNull Collection<String> keys) {
        return sceneQueryService.findAllByKeys(EditUtil.ctxFromThreadLocal(), keys);
    }

    public Paging<SceneMetaVO> pagingScenes(SceneBatchRequest request, PageReq pageRequest) {
        if (ObjectUtils.isEmpty(request)) {
            request = new SceneBatchRequest();
        }
        Paging<SceneMeta> pagingRes
                = sceneQueryService.pagingScenes(request.getParentKey(), request.getFuzzyValue(), pageRequest);
        Paging<SceneMetaVO> voPaging = new Paging<>();
        List<SceneMetaVO> voList = pagingRes.getData().stream().map(SceneMetaVO::convertFrom).collect(Collectors.toList());

        Map<Long, String> id2name = trantorIAMUserService.getNamesByIds(voList.stream().map(SceneMetaVO::getUpdatedBy).collect(Collectors.toSet()));
        voList.forEach(vo -> vo.setUpdatedByName(id2name.computeIfAbsent(vo.getUpdatedBy(), u -> User.UNKNOWN_USERNAME)));
        voPaging.setData(voList);
        voPaging.setTotal(pagingRes.getTotal());
        return voPaging;
    }

    public List<SceneMetaVO> listScenes(SceneBatchRequest request) {
        if (ObjectUtils.isEmpty(request)) {
            request = new SceneBatchRequest();
        }
        return sceneQueryService.listScenes(request.getParentKey(), request.getFuzzyValue())
                .stream().map(SceneMetaVO::convertFrom)
                .collect(Collectors.toList());
    }

    /**
     * 创建场景
     */
    public SceneMeta create(@NonNull SceneMeta meta, @NonNull String parentKey) {
        meta.setAccess(MetaNodeAccessLevel.Public);
        meta.setParentKey(parentKey);
        sceneRepo.create(meta, ResourceContext.ctxFromThreadLocal());
        return meta;
    }

    /**
     * 更新场景基础信息
     *
     * @param profile 场景基础信息
     */
    @Transactional
    public void updateProfile(@NonNull SceneProfile profile) {
        if (StringUtils.isBlank(profile.getKey())) {
            throw new ValidationException("key must not be empty or null");
        }
        SceneMeta scene = findByKey(profile.getKey(), null).orElseThrow(
            () -> new SceneNotFoundException(profile.getKey())
        );
        // 临时修复 sceneConfig 存在 views 的情况
        SceneConfig sceneConfig = scene.getSceneConfig();
        if (sceneConfig instanceof DataManagerSceneConfig) {
            ((DataManagerSceneConfig) sceneConfig).setViews(null);
        }
        scene.setName(profile.getName());
        scene.setDescription(profile.getDescription());
        sceneRepo.update(scene, ResourceContext.ctxFromThreadLocal());
    }

    /**
     * 删除场景
     */
    @Transactional
    public void delete(@NonNull String key) {
        sceneRepo.deleteByKey(key, true, false, true, ResourceContext.ctxFromThreadLocal());
    }

    /**
     * TODO 后续根据场景类型拆分
     */
    @Transactional
    public String copy(@NonNull String key, @NonNull String newKey, @NonNull String name, @Nullable String parentKey) {
        SceneMeta scene = findByKeyWithAllChildren(key)
            .orElseThrow(() -> new SceneNotFoundException(key));
        parentKey = Optional.ofNullable(parentKey).orElse(scene.getParentKey());
        if (SceneType.DATA.equals(scene.getType())) {
            List<ResourceBaseMeta<?>> children = new ArrayList<>(4);
            DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) scene.getSceneConfig();
            List<DataManagerViewMeta> dataManagerViewMetas = SceneJsonUtil.updateViews(newKey, sceneConfig.getViews(), getReplaceFunction(key, newKey), getSkipFieldTraversePredicate());
            children.addAll(dataManagerViewMetas);
            scene.setSubMetas(children);
            sceneConfig.setViews(null);
        }
        scene.setKey(newKey);
        scene.setName(name);
        scene.setId(null);
        return create(scene, parentKey).getKey();
    }

    @Transactional
    public void reset(@NonNull String key, @Nullable String viewKey) {
        if (!ObjectUtils.isEmpty(viewKey)) {
            extMetaService.delete(viewKey, ResourceContext.ctxFromThreadLocal());
            return;
        }
        SceneMeta scene = findByKeyWithViews(key).orElseThrow(() -> new SceneNotFoundException(key));
        if (!scene.getExtended()) {
            return;
        }
        if (scene.getSceneConfig() instanceof DataManagerSceneConfig) {
            DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) scene.getSceneConfig();
            Set<String> extendedViewKeys = sceneConfig.getViews().stream()
                    .filter(DataManagerView::getExtended)
                    .map(DataManagerView::getKey).collect(Collectors.toSet());
            extMetaService.deleteAllByKeys(extendedViewKeys, ResourceContext.ctxFromThreadLocal());
        }
    }

    /**
     * 场景元信息转为元数据节点
     *
     * @return 元数据实体
     */
    private UnaryOperator<String> getReplaceFunction(String key, String newKey) {
        return it -> it.startsWith(key) ? newKey + it.substring(key.length()) : newKey + "-" + it;
    }

    private BiPredicate<JsonNode, String> getSkipFieldTraversePredicate() {
        return (node, key) -> {
            if (node == null || node.isNull()) {
                return true;
            }
            if (Objects.equals(key, "props")) {
                return true;
            }
            return node.isObject() && key.equals("relations");
        };
    }
}
