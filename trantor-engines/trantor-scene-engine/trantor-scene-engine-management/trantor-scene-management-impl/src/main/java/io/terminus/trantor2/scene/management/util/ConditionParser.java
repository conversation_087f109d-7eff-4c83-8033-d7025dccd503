package io.terminus.trantor2.scene.management.util;

import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Nonnull;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ConditionParser {
    private static final String TABLE_CONDITION = "tableCondition";
    private static final String KEY = "key";

    /**
     * 递归解析条件组, json key 为 "tableCondition"
     */
    public static void recursiveParseConditionGroup(@Nonnull JsonNode node, @Nonnull Map<String, JsonNode> conditionGroupMap, boolean throwException) {
        if (node.isNull()) {
            return;
        }
        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                if (entry == null) {
                    return;
                }
                JsonNode value = entry.getValue();
                if (value.isObject()) {
                    if (TABLE_CONDITION.equals(entry.getKey())) {
                        JsonNode keyNode = value.get(KEY);
                        if (keyNode == null || keyNode.isNull() || keyNode.asText().isEmpty()) {
                            return;
                        }
                        String key = keyNode.asText();
                        try {
                            ConditionGroup conditionGroup = JsonUtil.NON_INDENT.getObjectMapper().convertValue(value, ConditionGroup.class);
                            if (conditionGroup != null) {
                                conditionGroupMap.put(key, value);
                            }
                        } catch (Exception e) {
                            if (throwException) {
                                throw new TrantorRuntimeException("parse conditionGroup error, key: " + key + ", value: " + value, e);
                            }
                            log.error("parse conditionGroup error, key: {}, value: {}", key, value, e);
                        }
                    } else {
                        recursiveParseConditionGroup(value, conditionGroupMap, throwException);
                    }
                } else if (value.isArray()) {
                    value.elements().forEachRemaining(element -> recursiveParseConditionGroup(element, conditionGroupMap, throwException));
                }
            });
        } else if (node.isArray()) {
            node.elements().forEachRemaining(element -> recursiveParseConditionGroup(element, conditionGroupMap, throwException));
        }
    }
}
