package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.management.dto.SceneBatchRequest;
import io.terminus.trantor2.scene.management.vo.SceneMetaVO;
import io.terminus.trantor2.scene.meta.SceneMeta;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 修复场景元数据数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RejudgeSceneService {

    private final SceneManagerService sceneManagerService;
    private final TeamService teamService;
    private final DataManagerSceneService dataManagerSceneService;

    private final String CHILDREN = "children";
    private final String NAME = "name";
    private final String TABLE = "Table";
    private final String FILTER_FIELDS = "filterFields";
    private final List<String> FIELDS = Lists.newArrayList("Field", "FormField", "DetailField");
    private final String PROPS = "props";
    private final String EXT = "ext";

    public void rejudgeSceneFields(Long teamId, Long appId, String parentKey, String key) {
        SceneBatchRequest request = new SceneBatchRequest();
        request.setParentKey(parentKey);
        request.setFuzzyValue(key);

        // 设置上下文项目属性
        String teamCode = teamService.getTeamCode(teamId);
        TrantorContext.setTeamId(teamId);
        TrantorContext.setTeamCode(teamCode);

        int pageNo = 1;
        while (true) {
            PageReq pageRequest = PageReq.of(pageNo - 1, 20, Order.byModifiedAt().desc());
            Paging<SceneMetaVO> scenes = sceneManagerService.pagingScenes(request, pageRequest);
            if (CollectionUtils.isEmpty(scenes.getData())) {
                return;
            }
            scenes.getData().forEach(scene -> {
                // 修改成非原生模块
                ModuleInfo module = ModuleInfo.of(teamCode, KeyUtil.moduleKey(scene.getKey()));
                ModuleInfo moduleInfo = MetaContext.getCurrentDeployModules().stream()
                    .filter(m -> Objects.equals(m, module)).findAny().get();
                moduleInfo.setNativeModule(false);

                SceneMeta dbMeta = sceneManagerService.findByKeyWithViews(scene.getKey())
                    .orElseThrow(() -> new SceneNotFoundException(scene.getKey()));
                DataManagerSceneConfig dataManagerSceneConfig = (DataManagerSceneConfig) dbMeta.getSceneConfig();
                dataManagerSceneConfig.getViews().forEach(view -> rejudgeView(view.getContent(), false));

                // 更新元数据
                dataManagerSceneService.update(dbMeta.getKey(), dataManagerSceneConfig);

                // 删除自定义字段中ext
                dbMeta = sceneManagerService.findByKeyWithViews(scene.getKey()).get();
                deleteField(dbMeta, moduleInfo);
            });
            pageNo++;
        }
    }

    public void deleteSceneFields(Long teamId, Long appId, String parentKey, String key) {
        SceneBatchRequest request = new SceneBatchRequest();
        request.setParentKey(parentKey);
        request.setFuzzyValue(key);

        // 设置上下文项目属性
        String teamCode = teamService.getTeamCode(teamId);
        TrantorContext.setTeamId(teamId);
        TrantorContext.setTeamCode(teamCode);

        int pageNo = 1;
        while (true) {
            PageReq pageRequest = PageReq.of(pageNo - 1, 20, Order.byModifiedAt().desc());
            Paging<SceneMetaVO> scenes = sceneManagerService.pagingScenes(request, pageRequest);
            if (CollectionUtils.isEmpty(scenes.getData())) {
                return;
            }
            scenes.getData().forEach(scene -> {
                SceneMeta dbMeta = sceneManagerService.findByKeyWithViews(scene.getKey())
                    .orElseThrow(() -> new SceneNotFoundException(scene.getKey()));

                // 更新元数据
                deleteField(dbMeta, null);
            });
            pageNo++;
        }
    }

    private void rejudgeView(ObjectNode objectNode, boolean delete) {
        ArrayNode parent = (ArrayNode) objectNode.get(CHILDREN);
        boolean haveField = false;
        for (JsonNode jsonNode : parent) {
            if (jsonNode != null) {
                String name = jsonNode.get(NAME).textValue();
                if (FIELDS.contains(name)) {
                    haveField = true;
                    continue;
                }
                if (StringUtils.equals(name, TABLE)) {
                    // Table, 重新调整filter数据
                    rejudgeTableFilter(objectNode, delete);
                }
                rejudgeView((ObjectNode) jsonNode, delete);
            }
        }
        if (haveField) {
            List<JsonNode> newFields = fields(parent, delete);
            objectNode.set(CHILDREN, parent.removeAll().addAll(newFields));
        }
    }

    /**
     * TableFilter重新计算二开逻辑并且重新排序
     */
    private void rejudgeTableFilter(ObjectNode table, boolean delete) {
        ObjectNode props = (ObjectNode) table.get(PROPS);
        if (props != null) {
            ArrayNode filterFields = (ArrayNode) props.get(FILTER_FIELDS);
            if (filterFields != null) {
                List<JsonNode> normalFields = Lists.newArrayListWithCapacity(filterFields.size());
                List<JsonNode> extFields = Lists.newArrayListWithCapacity(filterFields.size());
                for (JsonNode jsonNode : filterFields) {
                    ObjectNode field = (ObjectNode) jsonNode;
                    if (judge(field.get(NAME).textValue())) {
                        if (!delete) {
                            field.putIfAbsent(EXT, BooleanNode.TRUE);
                            extFields.add(field);
                        }
                    } else {
                        normalFields.add(field);
                    }
                }
                normalFields.addAll(extFields);
                props.set(FILTER_FIELDS, filterFields.removeAll().addAll(normalFields));
            }
        }
    }

    /**
     * Field、FormField、DetailField 重新计算二开逻辑
     */
    private List<JsonNode> fields(ArrayNode fields, boolean delete) {
        List<JsonNode> normalFields = Lists.newArrayListWithCapacity(fields.size());
        List<JsonNode> extFields = Lists.newArrayListWithCapacity(fields.size());

        for (JsonNode jsonNode : fields) {
            ObjectNode field = (ObjectNode) jsonNode;
            if (field.get(PROPS) != null && field.get(PROPS).get(NAME) != null && judge(field.get(PROPS).get(NAME).textValue())) {
                if (!delete) {
                    field.putIfAbsent(EXT, BooleanNode.TRUE);
                    extFields.add(field);
                }
            } else {
                normalFields.add(field);
            }
        }

        normalFields.addAll(extFields);
        return normalFields;
    }

    /**
     * 删除普通字段中的ext字段
     */
    private void deleteField(SceneMeta dbMeta, ModuleInfo moduleInfo) {
        // 二开的模块不会更新原始字段
        if (moduleInfo != null) {
            moduleInfo.setNativeModule(true);
        }

        DataManagerSceneConfig dataManagerSceneConfig = (DataManagerSceneConfig) dbMeta.getSceneConfig();
        dataManagerSceneConfig.getViews().forEach(view -> rejudgeView(view.getContent(), true));
        dataManagerSceneService.update(dbMeta.getKey(), dataManagerSceneConfig);
    }

    /**
     * 根据字段名称重新判断字段
     *
     * @param columnName 字段名称
     */
    private boolean judge(String columnName) {
        return columnName.startsWith("ext");
    }

}
