package io.terminus.trantor2.scene.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.internal.EnableDesignMode;
import io.terminus.trantor2.scene.management.dto.datamanager.DataManagerSceneDTO;
import io.terminus.trantor2.scene.management.dto.datamanager.UpdateSceneConfigRequest;
import io.terminus.trantor2.scene.management.service.DataManagerSceneService;
import io.terminus.trantor2.scene.management.vo.DataManagerSceneVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
@Tag(name = "数据管理场景管理", description = "console")
@RestController
@RequestMapping("/api/trantor/console/scenes/data-manager")
@RequiredArgsConstructor
public class DataManagerController {
    private final DataManagerSceneService dataManagerSceneService;

    @PostMapping("/create")
    @Operation(summary = "新建数据管理场景")
    public String create(@Valid @Parameter(description = "场景实例") @RequestBody DataManagerSceneDTO sceneDTO) {
        return dataManagerSceneService.create(sceneDTO).getKey();
    }

    @EnableDesignMode
    @GetMapping
    @Operation(summary = "查询数据管理场景", description = "通过场景标识查询数据管理场景详情")
    public DataManagerSceneVO get(@Parameter(description = "场景标识") @RequestParam String key) {
        return dataManagerSceneService.findSceneVoByKey(key);
    }

    @PostMapping("/update")
    @Operation(summary = "保存数据管理场景配置")
    public void update(@Valid @RequestBody UpdateSceneConfigRequest request) {
        dataManagerSceneService.update(request.getKey(), request.getSceneConfig());
    }
}
