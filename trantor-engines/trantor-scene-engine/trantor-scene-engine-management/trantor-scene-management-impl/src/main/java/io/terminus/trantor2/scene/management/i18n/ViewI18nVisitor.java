package io.terminus.trantor2.scene.management.i18n;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.i18n.I18nVisitor;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.config.i18n.ViewI18nConfig;
import io.terminus.trantor2.scene.management.dors.DorsClient;
import io.terminus.trantor2.scene.management.utils.SceneJsonUtil;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.repo.ViewRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Nullable;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static io.terminus.trantor2.module.constants.I18nConst.DORS_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ViewI18nVisitor implements I18nVisitor {
    private final ViewRepo viewRepo;
    private final DorsClient dorsClient;
    private final Function<MetaTreeNodeExt, DataManagerViewMeta> i18nViewConvert = getI18nViewConvert();

    @Override
    public MetaType getType() {
        return MetaType.View;
    }

    @Override
    public Set<String> visit(Cond cond, @Nullable Predicate<String> predicate) {
        int pageSize = 100;
        int pageNumber = 0;
        Set<String> finalResult = new HashSet<>();
        PageReq pageReq = PageReq.of(pageNumber, pageSize);
        while (true) {
            Paging<DataManagerViewMeta> page = viewRepo.findAll(cond, pageReq, ResourceContext.ctxFromThreadLocal(), i18nViewConvert);
            if (page.getData().isEmpty()) {
                break;
            }
            List<DataManagerViewMeta> views = page.getData()
                    .stream()
                    .filter(it -> predicate == null || predicate.test(KeyUtil.moduleKey(it.getKey())))
                    .collect(Collectors.toList());

            if (!views.isEmpty()) {
                finalResult.addAll(visitViews(views));
            }
            if ((long) (pageNumber + 1) * pageSize >= page.getTotal()) {
                break;
            }
            pageNumber++;
            pageReq = PageReq.of(pageNumber, pageSize);
        }
        return finalResult;
    }

    private Function<MetaTreeNodeExt, DataManagerViewMeta> getI18nViewConvert() {
        return node -> {
            if (node.getType().equals(MetaType.View.name())) {
                DataManagerViewMeta dataManagerViewMeta = new DataManagerViewMeta();
                DataManagerView dataManagerView = new DataManagerView();
                ObjectNode props = node.getProps();
                dataManagerViewMeta.setKey(node.getKey());
                JsonNode i18nConfigNode = props.get(DataManagerView.DataManagerViewFields.i18nConfig);
                if (i18nConfigNode != null) {
                    dataManagerView.setI18nConfig(ObjectJsonUtil.MAPPER.convertValue(i18nConfigNode, ViewI18nConfig.class));
                }
                JsonNode contentNode = node.getProps().get(DataManagerView.DataManagerViewFields.content);
                if (contentNode instanceof ObjectNode && contentNode.toString().contains("\"name\": \"DorsPage\"")) {
                    dataManagerView.setContent((ObjectNode) contentNode);
                }
                dataManagerViewMeta.setResourceProps(dataManagerView);
                return dataManagerViewMeta;
            }
            return null;
        };
    }
    private Set<String> visitViews(List<DataManagerViewMeta> views) {
        Set<String> res = new HashSet<>(views.size() * 32);
        if (CollectionUtils.isEmpty(views)) {
            return new HashSet<>();
        }
        Map<String, String> pageId2Workspace = new HashMap<>();
        for (DataManagerViewMeta view : views) {
            if (view.getResourceProps() == null) {
                continue;
            }
            ViewI18nConfig i18nConfig = view.getResourceProps().getI18nConfig();
            if (i18nConfig != null) {
                res.addAll(i18nConfig.getI18nKeySet());
            }
            ObjectNode content = view.getResourceProps().getContent();
            if (content != null) {
                visitDors(content, pageId2Workspace);
                // 处理完后释放 content
                view.getResourceProps().setContent(null);
            }
        }
        try {
            if (!CollectionUtils.isEmpty(pageId2Workspace)) {
                Set<String> dorsI18nKeySet = dorsClient.obtainDorsI18nKeySet(pageId2Workspace).stream()
                        .map(key -> DORS_PREFIX + key).collect(Collectors.toSet());
                res.addAll(dorsI18nKeySet);
            }
        } catch (Exception e) {
            // ignore
        }
        return res;
    }

    private void visitDors(ObjectNode content, Map<String, String> pageId2Workspace) {
        if (content == null) {
            return;
        }
        List<ObjectNode> dorsPages = SceneJsonUtil.findViewContentWidgets(content, "DorsPage");
        if (CollectionUtils.isEmpty(dorsPages)) {
            return;
        }

        for (ObjectNode dorsPage : dorsPages) {
            JsonNode props = dorsPage.get("props");
            if (props == null || !props.isObject()) {
                continue;
            }
            String workspace = Optional.ofNullable(props.get("workspace")).map(JsonNode::asText).orElse(null);
            String pageId = Optional.ofNullable(props.get("pageId")).map(JsonNode::asText).orElse(null);

            if (StringUtils.hasLength(workspace) && StringUtils.hasLength(pageId)) {
                pageId2Workspace.putIfAbsent(pageId, workspace);
            }
        }
    }
}
