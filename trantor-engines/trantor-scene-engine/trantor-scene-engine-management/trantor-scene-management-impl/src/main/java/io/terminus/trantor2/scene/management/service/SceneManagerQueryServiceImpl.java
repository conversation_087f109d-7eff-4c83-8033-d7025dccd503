package io.terminus.trantor2.scene.management.service;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.editor.repository.ext.ExtMetaRepo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.resource.TreeBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.dto.LightParam;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.repo.ViewRepo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SceneManagerQueryServiceImpl implements SceneManagerQueryService {
    private final SceneRepo sceneRepo;
    private final ViewRepo viewRepo;
    private final ExtMetaRepo extMetaRepo;

    /**
     * 根据 key 集合查找场景，包含视图
     *
     * @param keys 集合
     * @return 查询结果
     */
    @Override
    public Collection<SceneMeta> findAllByKeys(@NonNull MetaEditAndQueryContext ctx, @NonNull Collection<String> keys) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(ctx);
        Map<String, String> view2SceneKeyMap = new HashMap<>();
        List<DataManagerViewMeta> views = viewRepo.findAll(Field.parentKey().in(keys), resourceCtx);
        Map<String, List<DataManagerViewMeta>> parentMap = views.stream().collect(Collectors.groupingBy(TreeBaseMeta::getParentKey));

        Map<String, SceneMeta> sceneMap = sceneRepo.findAll(sceneSpec().and(Field.key().in(keys)), resourceCtx)
                .stream().filter(Objects::nonNull)
                .peek(scene -> {
                    if (!CollectionUtils.isEmpty(parentMap.get(scene.getKey())))  {
                        parentMap.get(scene.getKey())
                                .forEach(view -> view2SceneKeyMap.put(view.getKey(), scene.getKey()));
                    }
                })
                .collect(Collectors.toMap(SceneMeta::getKey, s -> s));

        if (CollectionUtils.isEmpty(sceneMap)) {
            return Collections.emptyList();
        }
        views.forEach(view -> Optional.ofNullable(sceneMap.get(view2SceneKeyMap.get(view.getKey())))
                    .ifPresent(scene -> {
                        if (scene.getSceneConfig() instanceof DataManagerSceneConfig) {
                            ((DataManagerSceneConfig) scene.getSceneConfig()).addView(view.getResourceProps());
                        }
                    }));
        return sceneMap.values();
    }

    /**
     * 根据场景标识查找场景，不包含子节点
     */
    @Override
    public Optional<SceneMeta> findByKeyWithOutChild(@NonNull String key, @Nullable String teamCode) {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        ctx.setTeamCode(teamCode);
        return findByKey(key, ctx, null);
    }

    /**
     * 场景标识标识查找场景
     *
     * @param key     标识
     * @param typesIn 子节点类型，为 null 时不加载子节点
     */
    @Override
    public Optional<SceneMeta> findByKey(@NonNull String key, @NonNull MetaEditAndQueryContext ctx, @Nullable Collection<String> typesIn) {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx(ctx);
        return sceneRepo.findOneByKey(key, resourceCtx).map(sceneMeta -> {
            if (SceneType.DATA.equals(sceneMeta.getType())) {
                if (CollectionUtils.isEmpty(typesIn)) {
                    return sceneMeta;
                }
                DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) sceneMeta.getSceneConfig();
                if (sceneConfig != null && typesIn.contains(MetaType.View.name())) {

                    sceneConfig.setViews(viewRepo.findAll(Field.parentKey().equal(sceneMeta.getKey()), resourceCtx).stream()
                        .map(ResourceBaseMeta::getResourceProps).collect(Collectors.toList()));
                    if (!sceneMeta.getCustomExt()) {
                        sceneMeta.setExtended(sceneConfig.getViews().stream().anyMatch(DataManagerView::getExtended));
                    }
                }
            }
            return sceneMeta;
        });
    }

    @Override
    public Optional<SceneMeta> findByKey(@NotNull String key, @NotNull MetaEditAndQueryContext ctx,
                                         Collection<String> typesIn, @NonNull LightParam lightParam) {
        return findByKey(key, ctx, typesIn);
    }

    @Override
    public Paging<SceneMeta> pagingScenes(@Nullable String parentKey, @Nullable String fuzzyValue, PageReq pageable) {
        Cond cond = generateBatchQuerySpec(parentKey, fuzzyValue);
        Paging<SceneMeta> all = sceneRepo.findAll(cond, pageable, ResourceContext.newResourceCtx(EditUtil.ctxFromThreadLocal()));
        List<SceneMeta> scenes = all.getData();

        setExtended(scenes);
        return new Paging<>(all.getTotal(), scenes);
    }

    @Override
    public List<SceneMeta> listScenes(@Nullable String parentKey, @Nullable String fuzzyValue) {
        Cond cond = generateBatchQuerySpec(parentKey, fuzzyValue);
        List<SceneMeta> scenes = sceneRepo.findAll(cond, ResourceContext.newResourceCtx(EditUtil.ctxFromThreadLocal()));
        setExtended(scenes);
        return scenes;
    }

    /**
     * @param parentKey  资源树节点标识
     * @param fuzzyValue 模糊匹配值
     * @return 查询 Specification
     */
    private Cond generateBatchQuerySpec(@Nullable String parentKey, @Nullable String fuzzyValue) {
        List<Cond> conds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(parentKey)) {
            conds.add(Field.parentKey().equal(parentKey));
        }
        if (!ObjectUtils.isEmpty(fuzzyValue)) {
            conds.add(Cond.or(
                Field.name().like("%" + fuzzyValue + "%"),
                Field.key().like("%" + fuzzyValue + "%")
            ));
        }
        return sceneSpec().and(Cond.and(conds.toArray(new Cond[0])));
    }

    private void setExtended(@Nullable List<SceneMeta> sceneMetas) {
        if (CollectionUtils.isEmpty(sceneMetas)) {
            return;
        }
        if (BooleanUtils.isNotTrue(MetaContext.isEnhanced(
                ModuleInfo.of(TrantorContext.getTeamCode(), TrantorContext.getModuleKey())))) {
            return;
        }
        ResourceContext ctx = ResourceContext.newResourceCtx(sceneMetas.get(0).getTeamCode(), TrantorContext.getCurrentUserId());
        List<DataManagerViewMeta> views = viewRepo.findAll(Field.parentKey().in(sceneMetas.stream().map(SceneMeta::getKey).collect(Collectors.toSet())), ctx);
        Map<String, Set<String>> scene2ViewKeys = views.stream()
                .collect(Collectors.groupingBy(DataManagerViewMeta::getParentKey, Collectors.mapping(DataManagerViewMeta::getKey, Collectors.toSet())));
        if (CollectionUtils.isEmpty(views)) {
            return;
        }

        Map<String, Boolean> existKeysMap = extMetaRepo.existKeys(views.stream().map(it -> KeyUtil.extKey(it.getKey())).collect(Collectors.toSet()), ctx);

        for (SceneMeta sceneMeta : sceneMetas) {
            if (BooleanUtils.isTrue(sceneMeta.getExtended())) {
                continue;
            }
            Set<String> viewKeys = scene2ViewKeys.getOrDefault(sceneMeta.getKey(), null);
            if (CollectionUtils.isEmpty(viewKeys)) {
                continue;
            }
            sceneMeta.setExtended(viewKeys.stream().anyMatch(v -> existKeysMap.getOrDefault(KeyUtil.extKey(v), false)));
        }
    }

    private Cond sceneSpec() {
        return Field.type().equal(MetaType.Scene.name());
    }
}
