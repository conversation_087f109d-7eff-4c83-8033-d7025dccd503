package io.terminus.trantor2.scene.management.service;


import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.exception.MetaLockedException;
import io.terminus.trantor2.meta.management.dlock.TeamBasedLockApi;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.event.AfterRouteResourceChangedEvent;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.scene.config.SceneTemplateConfig;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.exception.SceneTemplateNotFoundException;
import io.terminus.trantor2.scene.management.dto.datamanager.DataManagerSceneDTO;
import io.terminus.trantor2.scene.management.meta.SceneTemplateMeta;
import io.terminus.trantor2.scene.management.repo.SceneTemplateRepo;
import io.terminus.trantor2.scene.management.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.management.vo.SceneTemplateInfo;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DataManagerSceneService
    extends AbstractSceneService<DataManagerSceneDTO, DataManagerSceneVO, DataManagerSceneConfig> {

    private final SceneTemplateRepo sceneTemplateRepo;
    private final SceneTemplateService sceneTemplateService;

    protected DataManagerSceneService(SceneManagerService sceneManagerService, @Qualifier(value = "menuQueryConsoleService") MenuQueryService menuQueryService,
                                      MetaQueryService queryService, ServiceRepo serviceRepo, SceneRepo sceneRepo, PermissionRepo permissionRepo, ModuleRepo moduleRepo, TeamBasedLockApi dLockApi,
                                      SceneTemplateRepo sceneTemplateRepo, SceneTemplateService sceneTemplateService, PermissionKeyInitializer permissionKeyInitializer) {
        super(sceneManagerService, menuQueryService, queryService, serviceRepo, sceneRepo, permissionRepo, moduleRepo, dLockApi, permissionKeyInitializer);
        this.sceneTemplateRepo = sceneTemplateRepo;
        this.sceneTemplateService = sceneTemplateService;
    }

    public DataManagerSceneVO findSceneVoByKey(@Nonnull String key) {
        SceneMeta dbScene = sceneManagerService.findByKeyWithViews(key)
            .orElseThrow(() -> new SceneNotFoundException(key));

        DataManagerSceneVO vo = toVO(dbScene);
        Response<User> response = dLockApi.lockedBy(key, dbScene.getTeamCode());
        if (response.isSuccess()) {
            vo.setLockedBy(response.getData());
        }
        return vo;
    }

    /**
     * 更新场景
     */
    @Transactional
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void update(String key, DataManagerSceneConfig config) {
        SceneMeta dbMeta = sceneManagerService.findByKey(key, null).orElseThrow(() -> new SceneNotFoundException(key));
        Response<User> response = dLockApi.lockedBy(key, dbMeta.getTeamCode());
        if (response.isSuccess() && response.getData() != null
                && !Objects.equals(response.getData().getId(), TrantorContext.getCurrentUserId())) {
            throw new MetaLockedException(response.getData().getUsername());
        }
        // 修复历史数据
        if (config.getTemplateConfig() == null && config.getTemplateId() != null) {
            SceneTemplateConfig templateConfig = new SceneTemplateConfig();
            SceneTemplateInfo.findById(config.getTemplateId()).ifPresent(template -> {
                templateConfig.setTemplateKey(template.getKey());
                templateConfig.setVersion(template.getVersion());
                if (template.getKey().startsWith("sys_common$")) {
                    templateConfig.setTemplateKey(template.getKey().substring(11));
                }
                templateConfig.setTemplateType(template.getType());
                templateConfig.setLocal(false);
            });
            config.setTemplateConfig(templateConfig);
        }
        dbMeta.setSceneConfig(config);
        permissionKeyInitializer.initScenePermission(dbMeta); // 初始化视图权限项
        super.update(dbMeta);
    }

    @Override
    public DataManagerSceneConfig initSceneConfig(DataManagerSceneDTO dto) {
        DataManagerSceneConfig sceneConfig = dto.getSceneConfig();
        SceneTemplateMeta template = Optional.ofNullable(dto.getTemplateId())
                .map(id -> sceneTemplateRepo.findById(id)
                        .orElseThrow(() -> new SceneTemplateNotFoundException(id)))
                .orElseGet(() -> Optional.ofNullable(dto.getTemplateConfig())
                        .flatMap(config -> Optional.ofNullable(sceneTemplateService.findApp(config.getTemplateKey(), !config.getLocal())))
                        .orElseThrow(() -> new SceneTemplateNotFoundException("template not found")));

        SceneTemplateConfig sceneTemplateConfig = new SceneTemplateConfig();
        sceneTemplateConfig.setTemplateKey(template.getKey());
        sceneTemplateConfig.setTemplateType(template.getBaseInfo().getType());
        sceneTemplateConfig.setLocal(template.getLocal());
        sceneTemplateConfig.setVersion(template.getEnabledVersionedApp().get().getAppVersion());
        sceneConfig.setTemplateConfig(sceneTemplateConfig);
        return dto.getSceneConfig();
    }


    @Override
    protected void validateDTO(@Nonnull DataManagerSceneDTO dto) {
        super.validateDTO(dto);
        DataManagerSceneDTO.TemplateConfig templateConfig = dto.getTemplateConfig();
        if ((ObjectUtils.isEmpty(dto.getTemplateId()) && ObjectUtils.isEmpty(templateConfig)) ||
                (templateConfig != null && (templateConfig.getTemplateKey() == null || templateConfig.getLocal() == null))) {
            throw new ValidationException("template not be null");
        }
    }

    @Override
    protected void validateAndTidyConfig(String sceneKey, DataManagerSceneConfig config) {
        super.validateAndTidyConfig(sceneKey, config);
        if (!CollectionUtils.isEmpty(config.getViews())) {
            for (DataManagerView view : config.getViews()) {
                if (KeyUtil.isInvalidKey(view.getKey())
                        || !KeyUtil.moduleKey(sceneKey).equals(KeyUtil.moduleKey(view.getKey()))) {
                    throw new ValidationException("invalid view key: " + view.getKey());
                }
            }
        }
    }

    @Override
    protected DataManagerSceneVO toVO(SceneMeta meta) {
        if (meta.getType() != SceneType.DATA) {
            throw new ValidationException("expected the type of scene: DATA but it was: " + meta.getType());
        }
        DataManagerSceneVO vo = new DataManagerSceneVO();
        BeanUtils.copyProperties(meta, vo);
        vo.setSceneConfig((DataManagerSceneConfig) meta.getSceneConfig());
        DataManagerSceneConfig sceneConfig = vo.getSceneConfig();
        sceneConfig.getViews().forEach(view -> view.setI18nConfig(null));
        sceneConfig.setI18nConfig(null);
        return vo;
    }

    @Override
    protected void buildChildrenBeforeSave(SceneMeta sceneMeta, boolean create) {
        DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) sceneMeta.getSceneConfig();
        String sceneMetaKey = sceneMeta.getKey();
        List<ResourceBaseMeta<?>> children = new ArrayList<>(4);
        children.addAll(sceneConfig.getViews().stream()
                .map(view -> DataManagerViewMeta.fromView(view, sceneMetaKey))
                .collect(Collectors.toList()));
        sceneMeta.setSubMetas(children);
        sceneConfig.setViews(null);
    }

    @Override
    protected void afterUpdate(@NotNull SceneMeta meta) {
        super.afterUpdate(meta);
        List<MenuMeta> menuMetas = menuQueryService.findAllBySceneKeyAndTeam(meta.getKey(), meta.getTeamCode());
        if (CollectionUtils.isEmpty(menuMetas)) {
            return;
        }
        publishEvent(new AfterRouteResourceChangedEvent(menuMetas));
    }
}
