package io.terminus.trantor2.scene.management.service;

import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.management.dlock.TeamBasedLockApi;
import io.terminus.trantor2.meta.management.util.MigPermUtil;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.dto.IdDTO;
import io.terminus.trantor2.scene.dto.SceneDTO;
import io.terminus.trantor2.scene.exception.SceneExistException;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import jakarta.annotation.Nonnull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @param <T> 场景 DTO
 * @param <V> 场景 VO
 * @param <C> 场景配置
 * <AUTHOR>
 */
public abstract class AbstractSceneService<
    T extends SceneDTO<C>,
    V extends IdDTO<?>,
    C extends SceneConfig> implements ApplicationEventPublisherAware, SceneConsoleWriteService {

    protected final SceneManagerService sceneManagerService;
    protected final MenuQueryService menuQueryService;
    protected final MetaQueryService metaQueryService;
    protected final ServiceRepo serviceRepo;
    protected final SceneRepo sceneRepo;
    protected final PermissionRepo permissionRepo;
    protected final ModuleRepo moduleRepo;
    protected final TeamBasedLockApi dLockApi;
    protected final PermissionKeyInitializer permissionKeyInitializer;

    private ApplicationEventPublisher eventPublisher;

    protected AbstractSceneService(SceneManagerService sceneManagerService, MenuQueryService menuQueryService, MetaQueryService queryService, ServiceRepo serviceRepo, SceneRepo sceneRepo, PermissionRepo permissionRepo, ModuleRepo moduleRepo, TeamBasedLockApi dLockApi, PermissionKeyInitializer permissionKeyInitializer) {
        this.sceneManagerService = sceneManagerService;
        this.menuQueryService = menuQueryService;
        this.metaQueryService = queryService;
        this.serviceRepo = serviceRepo;
        this.sceneRepo = sceneRepo;
        this.permissionRepo = permissionRepo;
        this.moduleRepo = moduleRepo;
        this.dLockApi = dLockApi;
        this.permissionKeyInitializer = permissionKeyInitializer;
    }

    /**
     * 创建场景
     *
     * @param dto
     * @return 新建场景 ID
     */
    @Transactional
    public SceneMeta create(@Nonnull T dto) {
        validateDTO(dto);
        C config = initSceneConfig(dto);
        SceneMeta sceneMeta = toEntity(dto);
        sceneMeta.setSceneConfig(config);
        buildChildrenBeforeSave(sceneMeta, true);
        sceneMeta.setAccess(MetaNodeAccessLevel.Public);
        permissionKeyInitializer.initScenePermission(sceneMeta);  // 初始化视图权限项
        Long id = sceneRepo.create(sceneMeta, ResourceContext.ctxFromThreadLocal());
        sceneMeta.setId(id);
        afterCreate(sceneMeta);
        return sceneMeta;
    }

    /**
     * 初始化场景配置
     *
     * @param sceneDTO
     * @return 场景配置
     */
    public abstract C initSceneConfig(T sceneDTO);

    @Override
    public void update(SceneMeta meta) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        validateAndTidyConfig(meta.getKey(), (C) meta.getSceneConfig());
        buildChildrenBeforeSave(meta, false);
        sceneRepo.update(meta, ctx);
        afterUpdate(meta);
    }

    protected void buildChildrenBeforeSave(SceneMeta sceneMeta, boolean create) {
    }

    protected void afterCreate(@Nonnull SceneMeta meta) {
        initPermForSysService(meta);
    }

    protected void afterUpdate(@Nonnull SceneMeta meta) {
        initPermForSysService(meta);
        dLockApi.renewal(meta.getKey());
    }

    private void initPermForSysService(SceneMeta meta) {
        List<String> viewKeys = new ArrayList<>();
        if (meta.getSubMetas() != null) {
            meta.getSubMetas().stream().filter(it -> it.getMetaType() == MetaType.View).map(BaseMeta::getKey).forEach(viewKeys::add);
        }
        if (viewKeys.isEmpty()) {
            return;
        }
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        Map<String, Set<String>> sysServiceToModels = MigPermUtil.buildSysServiceToModels(metaQueryService, ctx, viewKeys);
        if (sysServiceToModels.isEmpty()) {
            return;
        }
        Set<String> allModelKeys = new HashSet<>();
        sysServiceToModels.forEach((s, ms) -> allModelKeys.addAll(ms));
        if (allModelKeys.isEmpty()) {
            return;
        }
        List<ModuleMeta> allModules = moduleRepo.findAll(Cond.all(), ResourceContext.newResourceCtx(ctx));
        Set<String> allowedModuleKeys = allModules.stream()
                .filter(ModuleMeta::isNativeModule)
                .map(it -> it.getKey())
                .collect(Collectors.toSet());
        if (allowedModuleKeys.isEmpty()) {
            return;
        }

        Map<String, String> modelNameMap = metaQueryService.findByKeyIn(ctx, allModelKeys).stream()
                .collect(Collectors.toMap(it -> it.getKey(), it -> it.getName()));

        List<MetaTreeNodeExt> sysServices = metaQueryService.findByKeyIn(ctx, sysServiceToModels.keySet());
        for (MetaTreeNodeExt sysService : sysServices) {
            if (sysService == null) {
                continue;
            }
            if (!allowedModuleKeys.contains(KeyUtil.moduleKey(sysService.getKey()))) {
                // sys service now support ext
                //continue;
            }
            handleSysService(sysService, sysServiceToModels, modelNameMap, ctx);
        }
    }

    private void handleSysService(MetaTreeNodeExt node, Map<String, Set<String>> sysServiceToModels, Map<String, String> modelNameMap, MetaEditAndQueryContext ctx) {
        if (!MetaType.ServiceDefinition.name().equals(node.getType())) {
            return;
        }
        if (!Objects.equals(node.getProps().path("serviceType").asText(), ServiceType.SYSTEM.name())) {
            return;
        }
        Set<String> modelKeys = sysServiceToModels.getOrDefault(node.getKey(), new HashSet<>());
        if (modelKeys.isEmpty()) {
            return;
        }
        Map<String, String> modelKeyToName = modelNameMap.entrySet().stream()
                .filter(entry -> modelKeys.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        ServiceMeta serviceMeta = BaseMeta.newInstanceFrom(node, ServiceMeta.class);
        permissionKeyInitializer.initSystemServicePermission(serviceMeta, modelKeyToName, PermissionKeyInitializer.GenerateStrategy.ABSENT);
        serviceRepo.update(serviceMeta, ResourceContext.newResourceCtx(ctx));
    }

    /**
     * 校验 DTO
     *
     * @param dto DTO
     */
    protected void validateDTO(@Nonnull T dto) {
        if (ObjectUtils.isEmpty(dto.getKey())) {
            throw new ValidationException("scene key must not be null");
        }
        if (dto.getEndpointType() == null) {
            throw new ValidationException("endpoint type must not be null");
        }
        QueryOp queryOp = metaQueryService.queryInTeam(dto.getTeamId());
        if (queryOp.existsByKey(dto.getKey())) {
            throw new SceneExistException(dto.getKey());
        }
        if (queryOp.existsByParentKeyAndNameAndType(dto.getParentKey(), dto.getName(), MetaType.Scene.name())) {
            throw new SceneExistException(dto.getName());
        }
        dto.getTeamIdOrElseThrow();
        Pattern pattern = Pattern.compile("^[a-zA-Z][\\w\\-$]{0,49}$");
        Matcher matcher = pattern.matcher(KeyUtil.shortKey(dto.getKey()));
        if (!matcher.matches()) {
            throw new ValidationException(
                "scene key must start with a letter and contain a maximum of 50 uppercase letters, "
                    + "lowercase letters, underscores (_), midline (-) and digits");
        }
        validateAndTidyConfig(dto.getKey(), dto.getSceneConfig());
    }

    /**
     * 校验并整理 config
     *
     * @param config 场景配置
     */
    protected void validateAndTidyConfig(String sceneKey, C config) {
        if (ObjectUtils.isEmpty(config)) {
            throw new ValidationException("scene config must not be null");
        }
    }

    /**
     * DTO 转为持久化场景对象
     *
     * @param dto DTO
     * @return 持久化场景对象
     */
    protected SceneMeta toEntity(T dto) {
        SceneMeta sceneMeta = new SceneMeta();
        BeanUtils.copyProperties(dto, sceneMeta);
        return sceneMeta;
    }

    /**
     * 实体转 VO
     *
     * @param meta 实体
     * @return VO
     */
    protected abstract V toVO(SceneMeta meta);

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    protected void publishEvent(Object event) {
        if (eventPublisher == null) {
            return;
        }
        eventPublisher.publishEvent(event);
    }
}
