package io.terminus.trantor2.scene.management.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.scene.management.dto.template.SceneTemplateBatchRequest;
import io.terminus.trantor2.scene.management.service.SceneTemplateService;
import io.terminus.trantor2.scene.management.vo.ExampleModel;
import io.terminus.trantor2.scene.management.vo.SceneTemplateInfo__;
import io.terminus.trantor2.scene.management.vo.TemplateTypeInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Tag(name = "场景模版管理", description = "console")
@RestController
@RequestMapping("/api/trantor/console/scene/template")
@RequiredArgsConstructor
public class SceneTemplateManagementController {
    private final SceneTemplateService templateService;
    @GetMapping("/example-models")
    @Operation(summary = "获取示例模型", responses = {
        @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExampleModel.class))))
    })
    public List<ObjectNode> listExampleModels() {
        return SceneTemplateService.listExampleModels();
    }

    @GetMapping("/types")
    @Operation(summary = "模版类型列表")
    public List<TemplateTypeInfo> getTemplateTypes() {
        return TemplateTypeInfo.ALL;
    }

    @GetMapping("/list")
    @Operation(summary = "根据条件查询应用下场景模版列表, 最多返回 1000 条", deprecated = true, description = "废弃，使用“应用市场”下的列表接口")
    @Deprecated
    public List<SceneTemplateInfo__> list(SceneTemplateBatchRequest request) {
        request.setFilterEnabled(!request.isDraft());
        return templateService.listItems(request).stream()
                .map(SceneTemplateInfo__::convertFrom)
                .collect(Collectors.toList());
    }

    @GetMapping("/{key}")
    @Operation(summary = "模版详情", deprecated = true, description = "废弃，使用“应用市场”下的详情接口")
    @Deprecated
    public SceneTemplateInfo__ detail(@PathVariable String key,
                                      @Parameter(description = "是否草稿, 默认为 false, 即查询已发布的模版")
                                    @RequestParam(required = false) boolean draft,
                                      @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
                                    @RequestParam(required = false, defaultValue = "true") boolean fromMarket) {
        return SceneTemplateInfo__.convertFrom(templateService.findVersion(key, null, !draft, fromMarket));
    }
}
