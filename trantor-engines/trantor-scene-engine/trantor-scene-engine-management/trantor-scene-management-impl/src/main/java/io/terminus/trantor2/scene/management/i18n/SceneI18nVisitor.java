package io.terminus.trantor2.scene.management.i18n;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.i18n.I18nVisitor;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nullable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SceneI18nVisitor implements I18nVisitor {
    private final SceneRepo sceneRepo;

    @Override
    public MetaType getType() {
        return MetaType.Scene;
    }

    @Override
    public Set<String> visit(Cond cond, @Nullable Predicate<String> predicate) {
        List<SceneMeta> scenes = sceneRepo.findAll(cond, ResourceContext.ctxFromThreadLocal())
                .stream()
                .filter(it -> predicate == null || predicate.test(KeyUtil.moduleKey(it.getKey())))
                .collect(Collectors.toList());
        Set<String> res = new HashSet<>(scenes.size() * 4);

        for (SceneMeta scene : scenes) {
            res.add(scene.getName());
            Set<String> i18nKeySet = scene.getSceneConfig().getI18nKeySet();
            if (!CollectionUtils.isEmpty(i18nKeySet)) {
                res.addAll(i18nKeySet);
            }
        }
        return res;
    }
}
