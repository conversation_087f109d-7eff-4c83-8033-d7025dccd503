package io.terminus.trantor2.scene.management.controller;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.scene.management.service.RejudgeSceneService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/trantor/struct-node/rejudge")
@RequiredArgsConstructor
public class RejudgeSceneController {

    private final RejudgeSceneService rejudgeSceneService;

    @GetMapping(value = "/scene")
    public Response<Boolean> rejudgeView(@RequestParam(required = true) Long teamId,
                                         @RequestParam Long appId,
                                         @RequestParam String parentKey,
                                         @RequestParam String key) {
        rejudgeSceneService.rejudgeSceneFields(teamId, appId, parentKey, key);
        return Response.ok(<PERSON><PERSON>an.TRUE);
    }

}
