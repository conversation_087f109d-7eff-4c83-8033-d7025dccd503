package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.dto.AppItemSaveRequest;
import io.terminus.trantor2.application.item.scene.SceneTemplateBaseInfo;
import io.terminus.trantor2.application.service.AbstractAppManagerService;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.common.utils.JsonReaderUtil;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.scene.constants.SceneConsts;
import io.terminus.trantor2.scene.management.meta.SceneTemplateMeta;
import io.terminus.trantor2.scene.management.meta.VersionedSceneTemplateMeta;
import io.terminus.trantor2.scene.management.repo.SceneTemplateRepo;
import io.terminus.trantor2.scene.management.repo.VersionedSceneTemplateRepo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SceneTemplateService extends AbstractAppManagerService<SceneTemplateMeta, VersionedSceneTemplateMeta> {
    private static final String EXAMPLE_MODEL_FOLDER = "/json/models";
    private static final List<ObjectNode> exampleModels;

    private final SceneTemplateRepo templateRepo;
    private final VersionedSceneTemplateRepo versionedTemplateRepo;

    static {
        exampleModels = JsonReaderUtil.readJsonFiles(EXAMPLE_MODEL_FOLDER, ObjectNode.class);
    }

    protected SceneTemplateService(ConsoleService consoleService,
                                   NexusApiClientFactory nexusApiClientFactory, NexusConfigProperties nexusConfigProperties,
                                   TrantorIAMUserService trantorIAMUserService, SceneTemplateRepo templateRepo,
                                   VersionedSceneTemplateRepo versionedTemplateRepo) {
        super(consoleService, nexusApiClientFactory, nexusConfigProperties, trantorIAMUserService);
        this.templateRepo = templateRepo;
        this.versionedTemplateRepo = versionedTemplateRepo;
    }

    public static List<ObjectNode> listExampleModels() {
        return exampleModels;
    }

    @Override
    public String getGroup() {
        return SceneConsts.SCENE_TEMPLATE_GROUP;
    }

    @Override
    public String getExtension() {
        return "json";
    }

    @Override
    public ApplicationType getApplicationType() {
        return ApplicationType.SCENE_TEMPLATE;
    }

    @Override
    public SceneTemplateRepo getRepo() {
        return templateRepo;
    }

    @Override
    protected void validateSaveAppRequest(AppItemSaveRequest request) {
        super.validateSaveAppRequest(request);
        SceneTemplateBaseInfo baseInfo = request.getBaseInfo();
        if (baseInfo == null) {
            throw new ValidationException("the basic info about the scene template cannot be empty");
        }
        Optional.ofNullable(baseInfo.getImages()).ifPresent(images -> images.forEach(image -> {
            if (!image.startsWith("trantor") && !image.startsWith("/trantor")) {
                throw new ValidationException("unexpected image url: " + image);
            }
        }));
    }

    @Override
    public VersionedSceneTemplateRepo getVersionedRepo() {
        return versionedTemplateRepo;
    }

    @NotNull
    @Override
    protected Set<String> getAllOssUrls(@NotNull SceneTemplateMeta appItem) {
        Set<String> allOssUrls = super.getAllOssUrls(appItem);
        if (appItem.getBaseInfo() != null) {
            List<String> images = appItem.getBaseInfo().getImages();
            if (!CollectionUtils.isEmpty(images)) {
                allOssUrls.addAll(images);
            }
        }
        return allOssUrls;
    }
}
