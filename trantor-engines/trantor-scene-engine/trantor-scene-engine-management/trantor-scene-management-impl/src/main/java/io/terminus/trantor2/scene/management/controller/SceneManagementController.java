package io.terminus.trantor2.scene.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.scene.management.dto.CopyRequest;
import io.terminus.trantor2.scene.management.dto.KeyRequest;
import io.terminus.trantor2.scene.management.dto.SceneBatchRequest;
import io.terminus.trantor2.scene.management.dto.SceneProfile;
import io.terminus.trantor2.scene.management.service.SceneManagerService;
import io.terminus.trantor2.scene.management.vo.SceneMetaVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "场景通用管理", description = "console")
@RestController
@RequestMapping("/api/trantor/console/scene")
@RequiredArgsConstructor
public class SceneManagementController {
    private final SceneManagerService sceneManagerService;

    @PostMapping("/profile/update")
    @Operation(summary = "保存场景基本信息")
    public void updateProfile(@Parameter(description = "场景基本配置")
                              @RequestBody SceneProfile profile) {
        sceneManagerService.updateProfile(profile);
    }

    @GetMapping("/paging")
    @Operation(summary = "根据条件分页查询场景")
    @InjectUserInfos
    public Paging<SceneMetaVO> paging(
        @Parameter(description = "页号") @RequestParam
        @Min(value = 1, message = "illegal page number") Integer pageNumber,
        @Parameter(description = "每页条数") @RequestParam
        @Min(value = 1, message = "illegal page size") Integer pageSize,
        SceneBatchRequest sceneBatchRequest) {
        PageReq pageRequest = PageReq.of(pageNumber - 1, pageSize, Order.byModifiedAt().desc());
        return sceneManagerService.pagingScenes(sceneBatchRequest, pageRequest);
    }

    @GetMapping("/list")
    @Operation(summary = "根据条件查询应用下场景列表, 最多返回 1000 条, 大数据集场景使用 paging")
    public List<SceneMetaVO> list(SceneBatchRequest sceneBatchRequest) {
        return sceneManagerService.listScenes(sceneBatchRequest);
    }

    @PostMapping("/copy")
    @Operation(summary = "场景复制")
    public String copy(@RequestBody @Valid CopyRequest request) {
        return sceneManagerService.copy(request.getKey(), request.getNewKey(), request.getName(), request.getFolderKey());
    }

    @PostMapping("/reset/{key}")
    @Operation(summary = "二开场景重置")
    public void reset(@Parameter(description = "场景标识", required = true)
                      @PathVariable String key,
                      @Parameter(description = "视图标识，为空时重置场景下所有扩展视图")
                      @RequestParam(required = false) String viewKey) {
        sceneManagerService.reset(key, viewKey);
    }
}
