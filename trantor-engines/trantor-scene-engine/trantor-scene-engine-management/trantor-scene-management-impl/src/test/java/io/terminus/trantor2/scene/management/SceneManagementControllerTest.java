package io.terminus.trantor2.scene.management;

import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.scene.management.controller.SceneManagementController;
import io.terminus.trantor2.scene.management.dto.CopyRequest;
import io.terminus.trantor2.scene.management.dto.KeyRequest;
import io.terminus.trantor2.scene.management.dto.SceneBatchRequest;
import io.terminus.trantor2.scene.management.dto.SceneProfile;
import io.terminus.trantor2.scene.management.service.SceneManagerService;
import io.terminus.trantor2.scene.management.service.SceneTemplateService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class SceneManagementControllerTest {
    @Mock
    SceneManagerService sceneService;
    @Mock
    SceneTemplateService templateService;
    @InjectMocks
    SceneManagementController controller;

    @Test
    void updateProfile() {
        SceneProfile sceneProfile = new SceneProfile();
        Assertions.assertDoesNotThrow(() -> controller.updateProfile(sceneProfile));
    }

    @Test
    void paging() {
        SceneBatchRequest sceneBatchRequest = new SceneBatchRequest();
        when(sceneService.pagingScenes(Mockito.any(SceneBatchRequest.class), Mockito.any(PageReq.class))).thenReturn(null);
        Assertions.assertNull(controller.paging(1, 2, sceneBatchRequest));
    }

    @Test
    void list() {
        SceneBatchRequest sceneBatchRequest = new SceneBatchRequest();
        when(sceneService.listScenes(Mockito.any(SceneBatchRequest.class))).thenReturn(Collections.emptyList());
        assertEquals(0, controller.list(sceneBatchRequest).size());
    }

    @Test
    void copy() {
        CopyRequest request = new CopyRequest();
        request.setKey("abc");
        request.setNewKey("new");
        request.setName("new Name");
        when(sceneService.copy(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.isNull())).thenReturn("new");
        String copy = controller.copy(request);

        assertEquals("new", copy);
    }
}
