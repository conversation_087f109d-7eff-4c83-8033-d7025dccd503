package io.terminus.trantor2.scene.management.datamanager.management;

import io.terminus.trantor2.scene.management.SceneResourceHelper;
import io.terminus.trantor2.scene.management.controller.DataManagerController;
import io.terminus.trantor2.scene.management.dto.datamanager.DataManagerSceneDTO;
import io.terminus.trantor2.scene.management.dto.datamanager.UpdateSceneConfigRequest;
import io.terminus.trantor2.scene.management.service.DataManagerSceneService;
import io.terminus.trantor2.scene.management.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.meta.SceneMeta;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DataManagerControllerTest {
    @Mock
    DataManagerSceneService dataManagerSceneService;
    @InjectMocks
    DataManagerController dataManagerController;

    @Test
    void create() throws Exception {
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);
        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setKey(dto.getKey());
        Mockito.doReturn(sceneMeta).when(dataManagerSceneService).create(dto);

        assertEquals(sceneMeta.getKey(), dataManagerController.create(dto), "场景标识不一致");

        Mockito.verify(dataManagerSceneService).create(dto);
    }

    @Test
    void get() {
        DataManagerSceneVO vo = new DataManagerSceneVO();
        vo.setKey("key");

        when(dataManagerSceneService.findSceneVoByKey(Mockito.anyString())).thenReturn(vo);
        DataManagerSceneVO res = dataManagerController.get("key");
        assertEquals(vo.getKey(), res.getKey(), "场景标识不一致");
    }

    @Test
    void update() {
        UpdateSceneConfigRequest r = new UpdateSceneConfigRequest();
        r.setKey("abc");
        Assertions.assertDoesNotThrow(() -> dataManagerController.update(r));
    }
}
