package io.terminus.trantor2.scene.management;

import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.cache.RPCMetaCacheProxy;
import io.terminus.trantor2.model.common.api.DataStructNodeQueryApi;
import io.terminus.trantor2.module.ModuleCommonConfigure;
import io.terminus.trantor2.module.filter.AccessControlWhiteListRewriter;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.MenuQueryService;
import org.mockito.Mockito;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManagerFactory;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@TestConfiguration
@EntityScan(basePackages = {"io.terminus.trantor2.scene", "io.terminus.trantor2.meta", "io.terminus.trantor2.module"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.scene", "io.terminus.trantor2.meta", "io.terminus.trantor2.module"})
@Import({RedisAutoConfiguration.class, RedissonAutoConfigurationV2.class, ModuleCommonConfigure.class})
public class SceneTestConfiguration {
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager(entityManagerFactory);
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.springframework.orm.jpa.vendor.HibernateJpaDialect");
        jpaTransactionManager.setJpaProperties(properties);
        return jpaTransactionManager;
    }

    @Bean
    public MenuQueryService menuQueryService() {
        return Mockito.mock(MenuQueryService.class);
    }

    @Bean
    public AccessControlWhiteListRewriter accessControlWhiteListRewriter() {
        return Mockito.mock(AccessControlWhiteListRewriter.class);
    }

    @Bean
    public TrantorIAMClientFactory iamClientFactory() {
        return Mockito.mock(TrantorIAMClientFactory.class);
    }

    @Bean
    public TrantorIAMClientFactory iamClientFactoryV2() {
        return Mockito.mock(TrantorIAMClientFactory.class);
    }

    @Bean
    public ConfigurationService configurationService() {
        return Mockito.mock(ConfigurationService.class);
    }

    @Bean
    public IAMClient iamClient() {
        return Mockito.mock(IAMClient.class);
    }

    @Bean
    public TrantorIAMUserService userService() {
        return Mockito.mock(TrantorIAMUserService.class);
    }

    @Bean
    public DataStructNodeQueryApi dataStructNodeQueryApi() {
        return Mockito.mock(DataStructNodeQueryApi.class);
    }

    @Bean
    @Primary
    public RPCMetaCacheProxy RPCMetaCacheProxy(){
        return Mockito.mock(RPCMetaCacheProxy.class);
    }
}
