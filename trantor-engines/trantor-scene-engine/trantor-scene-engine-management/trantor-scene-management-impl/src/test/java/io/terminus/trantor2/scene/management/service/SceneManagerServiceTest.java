package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.TrantorContext.Context;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.event.TrantorEventPublisher;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.meta.api.dto.*;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.lang.meta.CompPack;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.management.task.TaskManager;
import io.terminus.trantor2.meta.platform.PlatformConfigHolder;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.*;
import io.terminus.trantor2.module.util.ObjectUtils;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.TrantorFilterProperties;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.exception.SceneNotFoundException;
import io.terminus.trantor2.scene.management.SceneTestConfiguration;
import io.terminus.trantor2.scene.management.controller.RejudgeSceneController;
import io.terminus.trantor2.scene.management.dto.SceneBatchRequest;
import io.terminus.trantor2.scene.management.dto.SceneProfile;
import io.terminus.trantor2.scene.management.vo.SceneMetaVO;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import io.terminus.trantor2.test.tool.redis.RedisSpringTest;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfig;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@DataJpaTest(
        includeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.scene.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.meta(?!.*task).*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.properties.*"),
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {MenuConsoleQueryService.class, MenuQueryService.class})
        }
)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureJson
@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@Import(SceneTestConfiguration.class)
class SceneManagerServiceTest implements MysqlSpringTest, RedisSpringTest {
    @MockBean
    private TaskManager taskManager;
    @Autowired
    private MetaQueryService metaQueryService;
    @Autowired
    private EditorMetaEditService metaEditService;
    @MockBean(name = "menuQueryConsoleService")
    private MenuConsoleQueryService menuQueryConsoleService;
    @MockBean
    private FreeMarkerConfig freeMarkerConfig;
    @Autowired
    private SceneManagerService sceneManagerService;
    @MockBean
    private TrantorEventPublisher trantorEventPublisher;
    @MockBean
    private ConsoleService consoleService;
    @MockBean
    private ModuleManagerQueryService moduleQueryService;
    @MockBean
    private NexusApiClientFactory nexusApiClientFactory;
    @MockBean
    private NexusConfigProperties nexusConfigProperties;
    @MockBean
    private RejudgeSceneController rejudgeSceneController;
    @MockBean
    private TeamService teamService;
    @MockBean
    private OSSService ossService;
    @MockBean
    private TrantorFilterProperties trantorFilterProperties;
    private Long appId = 1L;
    private String moduleKeyPrefix = "app1$";
    private String folderRootKey;
    private SceneMeta scene;

    @BeforeAll
    public void init() {
        PlatformConfigHolder.setInfo(new HashMap<String, Object>() {{
            put(PlatformConfigHolder.CONFIG_PLATFORM_VERSION, PlatformVersion.of(
                    "0.0.0.0",
                    "0000000000000000000000000000000000000000000000000000000000000000"
            ));
        }});
        TrantorContext.init();
        User user = new User();
        user.setId(100001L);
        TrantorContext.setCurrentUser(user);
        TrantorContext.setTeamId(1L);
        TrantorContext.setTeam("t");
        scene = new SceneMeta();
        scene.setName("Hello");
        scene.setKey("app1$world");
        scene.setType(SceneType.DATA);
        scene.setEndpointType(EndpointType.PC);
        scene.setAppId(1L);
        scene.setTeamId(1L);
        scene.setTeamCode("t");

        MetaEditAndQueryContext ctx = EditUtil.newCtx(1L, 100001L);
        ctx.setTeamCode("t");

        metaEditService.initRepo(ctx);
        String rootKey = metaQueryService.queryInTeam(ctx.getTeamId()).findRoot().get().getKey();
        appId = createModule(ctx, rootKey, "app1");
        ctx.setModuleKey("app1");
        TrantorContext.setModuleKey("app1");
        metaEditService.initFirstLevelNodes(ctx, "app1");
        folderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();
        loadBehaviors();
    }

    protected Long createModule(MetaEditAndQueryContext ctx, String rootKey, String moduleKey) {
        ObjectNode moduleProps = ObjectJsonUtil.MAPPER.createObjectNode();
        MetaTreeNode node = new MetaTreeNode();
        node.setType("Module");
        node.setKey(moduleKey);
        node.setName(moduleKey + "-name");
        node.setParentKey(rootKey);
        node.setProps(moduleProps);
        Map<String, MetaTreeNodeExt> nodes = metaEditService.submitOp(ctx,
            EditUtil.createNodeOp(
                node,
                new MoveTarget(rootKey, MoveTargetType.ChildFirst)
            )
        );
        MetaContext.addModules(Collections.singletonList(ModuleInfo.of(ctx.getTeamCode(), moduleKey)));
        return nodes.get(moduleKey).getId();
    }

    @Test
    void existsByKey() {
        sceneManagerService.create(scene, folderRootKey);
        Assertions.assertTrue(metaQueryService.queryInTeam(scene.getTeamCode()).existsByKey("app1$world"));
        Assertions.assertFalse(metaQueryService.queryInTeam(scene.getTeamCode()).existsByKey("The Truman Show"));
    }


    @Test
    void findByKey_bad_ctx() {
        sceneManagerService.create(scene, folderRootKey);
        Context copy = TrantorContext.copy();
        TrantorContext.clear();
        Assertions.assertThrows(TrantorRuntimeException.class, () -> sceneManagerService.findByKeyWithViews(scene.getKey()));
        TrantorContext.setContext(copy);
    }
    @Test
    @DisplayName("根据场景标识和上下文中的项目应用 ID 查询场景元数据")
    void findByKey_ctx_has_teamId() {
        sceneManagerService.create(scene, folderRootKey);
        Context copy = TrantorContext.copy();
        TrantorContext.setTeamId(1L);
        Optional<SceneMeta> sceneOpt = sceneManagerService.findByKeyWithViews(scene.getKey());
        Assertions.assertTrue(sceneOpt.isPresent());
        Assertions.assertEquals("app1$world", sceneOpt.get().getKey());
        TrantorContext.setContext(copy);
    }

    @Test
    void findByKeyAndAppId() {
        Optional<SceneMeta> sceneOpt = sceneManagerService.findByKeyWithViews(scene.getKey());
        Assertions.assertFalse(sceneOpt.isPresent());

        sceneManagerService.create(scene, folderRootKey);
        sceneOpt = sceneManagerService.findByKeyWithViews(scene.getKey());
        Assertions.assertTrue(sceneOpt.isPresent());
        Assertions.assertEquals("app1$world", sceneOpt.get().getKey());
    }

    @Test
    void create() {
        SceneMeta sceneMeta = sceneManagerService.create(scene, folderRootKey);
        Assertions.assertEquals("app1$world", sceneMeta.getKey());
        Assertions.assertEquals("Hello", sceneMeta.getName());
        Assertions.assertEquals(EndpointType.PC, sceneMeta.getEndpointType());
    }

    @Test
    void createWithChildren() {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        String folderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();
        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setKey(moduleKeyPrefix + "hi");
        sceneMeta.setName("coco");
        sceneMeta.setType(SceneType.DATA);
        DataManagerSceneConfig sceneConfig = new DataManagerSceneConfig();
        sceneConfig.setTemplateId(1L);
        sceneMeta.setSceneConfig(sceneConfig);
        DataManagerView view = new DataManagerView();
        view.setKey("view");
        view.setTitle("name");
        sceneMeta.setSubMetas(Collections.singletonList(DataManagerViewMeta.fromView(view, sceneMeta.getKey())));

        sceneManagerService.create(sceneMeta, folderRootKey);

        Optional<SceneMeta> sceneMetaOpt = sceneManagerService.findByKey(sceneMeta.getKey(), Collections.singletonList(MetaType.View.name()));
        Assertions.assertTrue(sceneMetaOpt.isPresent());
        sceneMeta = sceneMetaOpt.get();
        Assertions.assertEquals(moduleKeyPrefix + "hi", sceneMeta.getKey());
        Assertions.assertEquals("coco", sceneMeta.getName());
        Assertions.assertTrue(sceneMeta.getSceneConfig() instanceof DataManagerSceneConfig);
        Assertions.assertEquals(1, ((DataManagerSceneConfig) sceneMeta.getSceneConfig()).getViews().size());
        Assertions.assertEquals("view", ((DataManagerSceneConfig) sceneMeta.getSceneConfig()).getViews().get(0).getKey());
    }

    @Test
    void pagingScenesInApp() {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        String folderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();
        sceneManagerService.create(scene, folderRootKey);
        SceneMeta newScene = ObjectUtils.deepCopy(scene);
        newScene.setId(null);
        newScene.setKey(moduleKeyPrefix + "newScene");
        newScene.setName("new");
        sceneManagerService.create(newScene, folderRootKey);
        PageReq page = PageReq.of(0, 3);

        Paging<SceneMetaVO> result1 = sceneManagerService.pagingScenes(SceneBatchRequest.builder().parentKey(folderRootKey).build(), page);
        Assertions.assertEquals(2, result1.getTotal());
        Assertions.assertTrue(result1.getData().stream().anyMatch(it -> it.getEndpointType().equals(EndpointType.PC)));

        Paging<SceneMetaVO> result2 = sceneManagerService.pagingScenes(SceneBatchRequest.builder().parentKey(folderRootKey).fuzzyValue("wo").build(), page);
        Assertions.assertEquals(1, result2.getTotal());
    }

    @Test
    void listScenesInApp() {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        String folderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();

        SceneMeta sceneMeta = sceneManagerService.create(scene, metaQueryService.queryInApp(ctx).findMenuRootKey());
        SceneMeta newSceneMeta = ObjectUtils.deepCopy(sceneMeta);
        newSceneMeta.setId(null);
        newSceneMeta.setKey(moduleKeyPrefix + "newScene");
        newSceneMeta.setName("new");
        sceneManagerService.create(newSceneMeta, folderRootKey);

        SceneMeta newSceneMeta2 = ObjectUtils.deepCopy(sceneMeta);
        newSceneMeta2.setId(null);
        newSceneMeta2.setKey(moduleKeyPrefix + "hi");
        newSceneMeta2.setName("hi");
        sceneManagerService.create(newSceneMeta2, folderRootKey);

        List<SceneMetaVO> result1 = sceneManagerService.listScenes(null);
        Assertions.assertEquals(3, result1.size());

        List<SceneMetaVO> result2 = sceneManagerService.listScenes(SceneBatchRequest.builder().parentKey(folderRootKey).build());
        Assertions.assertEquals(2, result2.size());

        List<SceneMetaVO> result3 = sceneManagerService.listScenes(SceneBatchRequest.builder().parentKey(folderRootKey)
            .fuzzyValue("new").build());
        Assertions.assertEquals(1, result3.size());

        List<SceneMetaVO> result5 = sceneManagerService.listScenes(SceneBatchRequest.builder().parentKey(folderRootKey)
            .fuzzyValue("z").build());
        Assertions.assertEquals(0, result5.size());
    }

    @Test
    void findAllByKeys() {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        String folderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();

        MetaContext.setCurrentDeployModules(new HashSet<>(Collections.singletonList(ModuleInfo.of(ctx.getTeamCode(), "app1"))));

        SceneMeta scene1 = sceneManagerService.create(scene, folderRootKey);

        SceneMeta scene2 = ObjectUtils.deepCopy(scene1);
        scene2.setId(null);
        scene2.setKey("app1$newScene");
        scene2.setName("new");
        MetaTreeNodeExt view = new MetaTreeNodeExt();
        view.setKey("app1$key");
        view.setName("list");
        view.setType(MetaType.View.name());
        view.setParentKey(scene1.getKey());
        view.setProps(ObjectJsonUtil.MAPPER.createObjectNode());
        view.getProps().put("type", "LIST");
        DataManagerViewMeta viewMeta = new DataManagerViewMeta();
        viewMeta.from(view);

        DataManagerSceneConfig sceneConfig = new DataManagerSceneConfig();
        sceneConfig.setViews(Collections.singletonList(viewMeta.getResourceProps()));
        scene2.setSceneConfig(sceneConfig);
        scene2 = sceneManagerService.create(scene2, folderRootKey);

        // need to rebuild object before reading cache
        metaEditService.submitOp(ctx, EditUtil.snapshotOp());

        Collection<SceneMeta> result = sceneManagerService.findByKeyIn(Arrays.asList(scene.getKey(), "app1$newScene"));
        Assertions.assertEquals(2, result.size());
        Optional<SceneMeta> scene2Opt = result.stream().filter(it -> it.getKey().equals("app1$newScene")).findFirst();
        Assertions.assertTrue(scene2Opt.isPresent());
        Assertions.assertEquals(1, ((DataManagerSceneConfig) scene2Opt.get().getSceneConfig()).getViews().size(), "视图节点不一致");
    }

    @Test
    void updateProfile() {
        sceneManagerService.create(scene, folderRootKey);
        SceneProfile sceneProfile = new SceneProfile();
        assertThrows(ValidationException.class, () -> sceneManagerService.updateProfile(sceneProfile), "key must not be empty or null");

        sceneProfile.setKey(scene.getKey());
        sceneProfile.setName("lulu");
        sceneManagerService.updateProfile(sceneProfile);
        Optional<SceneMeta> opt = sceneManagerService.findByKeyWithViews(scene.getKey());
        assertTrue(opt.isPresent());
        assertEquals("lulu", opt.get().getName());
    }

    @Test
    void delete() {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        sceneManagerService.create(scene, folderRootKey);
        Assertions.assertTrue(metaQueryService.queryInTeam(ctx.getTeamId()).existsByKey(scene.getKey()));
        sceneManagerService.delete(scene.getKey());
        Assertions.assertFalse(metaQueryService.queryInTeam(ctx.getTeamId()).existsByKey(scene.getKey()));
    }

    @Test
    void deleteRefMenus() {
        sceneManagerService.create(scene, folderRootKey);
        sceneManagerService.delete(scene.getKey());
        assertFalse(sceneManagerService.findByKeyWithViews(scene.getKey()).isPresent());
    }


    @Test
    @SuppressWarnings("unchecked")
    @DisplayName("复制数据管理场景")
    void copyDataManager() throws JsonProcessingException {
        String sceneStr = ResourceHelper.getResourceAsString(getClass(), "json/sceneJsonNode.json");
        String viewStr = ResourceHelper.getResourceAsString(getClass(), "json/sceneChildren_view.json");
        String viewStr2 = ResourceHelper.getResourceAsString(getClass(), "json/sceneChildren_view2.json");
        SceneMeta sceneMeta = ObjectJsonUtil.MAPPER.readValue(sceneStr, SceneMeta.class);
        List<ResourceBaseMeta<?>> children = new ArrayList<>();
        DataManagerViewMeta viewMeta = ObjectJsonUtil.MAPPER.readValue(viewStr, DataManagerViewMeta.class);
        DataManagerViewMeta viewMeta2 = ObjectJsonUtil.MAPPER.readValue(viewStr2, DataManagerViewMeta.class);
        children.add(viewMeta);
        children.add(viewMeta2);
        sceneMeta.setSubMetas(children);
        sceneMeta.setParentKey(folderRootKey);
        sceneManagerService.create(sceneMeta, folderRootKey);
        String newKey = "app1$new";
        assertThrows(SceneNotFoundException.class, () -> sceneManagerService.copy("notFound", newKey, "newName", null));

        sceneManagerService.copy("app1$FIN_CM_ATF", newKey, "newName", null);

        Optional<SceneMeta> opt = sceneManagerService.findByKey(newKey, Arrays.asList(MetaType.Scene.name(), MetaType.View.name()));
        assertTrue(opt.isPresent());

        SceneMeta sceneQ = opt.get();

        assertEquals(newKey, sceneQ.getKey());
        assertEquals("newName", sceneQ.getName());
        assertEquals(folderRootKey, sceneQ.getParentKey());

        DataManagerView listView = ((DataManagerSceneConfig) sceneQ.getSceneConfig()).getViews().get(0);
        DataManagerView editView = ((DataManagerSceneConfig) sceneQ.getSceneConfig()).getViews().get(1);

        assertEquals(newKey + "-record-actions-1-button-1", listView.getButtons().get(0).getKey(), "button key 更新失败");
        assertEquals(newKey + "-list", listView.getContent().get("key").textValue(), "视图 key 更新失败");
        assertEquals(newKey + "-table-container-app1$fin_cm_atf_head_tr", listView.getContent().get("children").get(0).get("key").textValue(), "容器 key 更新失败");

        ArrayNode childrenNode = (ArrayNode) listView.getContent().get("children");
        JsonNode tableNode = childrenNode.get(0);
        assertEquals(newKey + "-table-container-app1$fin_cm_atf_head_tr", tableNode.get("key").textValue(), "容器 key 更新失败");
        JsonNode stackViewNode = childrenNode.get(1);
        assertEquals(newKey + "-stackView", stackViewNode.get("key").textValue(), "StackView key 更新失败");
        assertEquals("list", stackViewNode.at("/props/items").get(0).get("key").textValue(), "StackView items 跳过更新失败");
        assertEquals("show", stackViewNode.at("/props/items").get(1).get("key").textValue(), "StackView items 跳过更新失败");

        ObjectNode buttonNode = (ObjectNode) tableNode.get("children").get(1).get("children").get(0);
        JsonNode actionConfigNode = buttonNode.get("props").get("actionConfig");
        String routeViewKey = actionConfigNode.get("endLogicOtherConfig").get(0).get("openViewConfig").get("page").get("key").asText();
        assertEquals(newKey + "-edit", routeViewKey);
        assertTrue(actionConfigNode.get("executeScriptConfig").asText().contains("'app1$new-stackView'"));
        assertTrue(actionConfigNode.get("executeScriptConfig").asText().contains("\"app1$new-edit\""));

        assertTrue(editView.getContent().get("children").get(1).get("props").get("events").get(0).get("source").asText()
                .contains("app1$new-edit"));
    }

    @Test
    @SuppressWarnings("unchecked")
    @DisplayName("跨模块复制数据管理场景")
    void crossModuleCopyDataManager() throws JsonProcessingException {
        MetaEditAndQueryContext ctx = EditUtil.ctxFromThreadLocal();
        String rootKey = metaQueryService.queryInTeam(ctx.getTeamId()).findRoot().get().getKey();
        ctx.setModuleKey("app2");
        Long app2 = createModule(ctx, rootKey, "app2");
        metaEditService.initFirstLevelNodes(ctx, "app2");
        ModuleMeta module = new ModuleMeta();
        module.setId(app2);
        when(moduleQueryService.findByKey(Mockito.anyString())).thenReturn(module);
        String app2FolderRootKey = metaQueryService.queryInApp(ctx).findFolderRootKey();

        String sceneStr = ResourceHelper.getResourceAsString(getClass(), "json/sceneJsonNode.json");
        SceneMeta sceneMeta = ObjectJsonUtil.MAPPER.readValue(sceneStr, SceneMeta.class);
        String viewStr = ResourceHelper.getResourceAsString(getClass(), "json/sceneChildren_view.json");
        List<ResourceBaseMeta<?>> children = new ArrayList<>();
        DataManagerViewMeta viewMeta = ObjectJsonUtil.MAPPER.readValue(viewStr, DataManagerViewMeta.class);
        children.add(viewMeta);
        sceneMeta.setSubMetas(children);

        sceneMeta.setParentKey(folderRootKey);
        sceneManagerService.create(sceneMeta, folderRootKey);
        String newKey = "app2$new";
        assertThrows(SceneNotFoundException.class, () -> sceneManagerService.copy("notFound", newKey, "newName", null));

        sceneManagerService.copy("app1$FIN_CM_ATF", newKey, "newName", app2FolderRootKey);

        Optional<SceneMeta> opt = sceneManagerService.findByKey(newKey, Arrays.asList(MetaType.Scene.name(), MetaType.View.name()));
        assertTrue(opt.isPresent());

        SceneMeta sceneQ = opt.get();

        assertEquals(newKey, sceneQ.getKey());
        assertEquals("newName", sceneQ.getName());
        assertEquals(app2FolderRootKey, sceneQ.getParentKey());


        DataManagerView view = ((DataManagerSceneConfig) sceneQ.getSceneConfig()).getViews().get(0);

        assertEquals(newKey + "-record-actions-1-button-1", view.getButtons().get(0).getKey(), "button key 更新失败");
        assertEquals(newKey + "-list", view.getContent().get("key").textValue(), "视图 key 更新失败");
        assertEquals(newKey + "-table-container-app1$fin_cm_atf_head_tr", view.getContent().get("children").get(0).get("key").textValue(), "容器 key 更新失败");

        ArrayNode childrenNode = (ArrayNode) view.getContent().get("children");
        JsonNode tableNode = childrenNode.get(0);
        assertEquals(newKey + "-table-container-app1$fin_cm_atf_head_tr", tableNode.get("key").textValue(), "容器 key 更新失败");
        JsonNode stackViewNode = childrenNode.get(1);
        assertEquals(newKey + "-stackView", stackViewNode.get("key").textValue(), "StackView key 更新失败");
        assertEquals("list", stackViewNode.at("/props/items").get(0).get("key").textValue(), "StackView items 跳过更新失败");
        assertEquals("show", stackViewNode.at("/props/items").get(1).get("key").textValue(), "StackView items 跳过更新失败");
    }

    @Test
    @DisplayName("复制非数据管理场景")
    void copyOtherScene() {
        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setName("bibibi");
        sceneMeta.setKey("app1$bi");
        sceneMeta.setType(SceneType.BI);
        sceneMeta.setParentKey(folderRootKey);
        sceneManagerService.create(sceneMeta, folderRootKey);
        String newKey = "app1$new";
        assertEquals(newKey, sceneManagerService.copy(sceneMeta.getKey(), newKey, "newName", null));
    }


    /**
     * {@link SceneManagerService #getReplaceFunction(String, String)}
     */
    @Test
    void getReplaceFunction() {
        Function<String, String> function = ReflectionTestUtils.invokeMethod(sceneManagerService, SceneManagerService.class,
            "getReplaceFunction", "moduleKey$ac", "moduleKey$123");

        assertNotNull(function);
        String res = function.apply("moduleKey$ac-xyz");
        assertEquals("moduleKey$123-xyz", res);

        res = function.apply("?");
        assertEquals("moduleKey$123-?", res);
    }

    /**
     * {@link SceneManagerService getSkipFieldTraversePredicate()}
     */
    @Test
    void getSkipFieldTraversePredicate() {
        BiPredicate<JsonNode, String> biPredicate = ReflectionTestUtils.invokeMethod(sceneManagerService, SceneManagerService.class,
            "getSkipFieldTraversePredicate");

        assertNotNull(biPredicate);
        assertTrue(biPredicate.test(null, "props"));
        assertTrue(biPredicate.test(NullNode.instance, "props"));

        ObjectNode jsonNode = ObjectJsonUtil.MAPPER.createObjectNode();
        jsonNode.put("key", "value");
        assertTrue(biPredicate.test(jsonNode, "props"));
        jsonNode.put("name", true);
        assertTrue(biPredicate.test(jsonNode, "props"));
        jsonNode.put("name", "abc");
        assertTrue(biPredicate.test(jsonNode, "props"));
        jsonNode.put("name", "StackView");
        assertFalse(biPredicate.test(jsonNode, "?"));

        jsonNode.put("name", "StackView");
        assertTrue(biPredicate.test(jsonNode, "props"));
    }

    public void loadBehaviors() {
        Path dir;
        try {
            dir = Paths.get(new ClassPathResource("io/terminus/trantor2/meta/index/behaviors/").getURI());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        CompPack.load(dir);
    }
}
