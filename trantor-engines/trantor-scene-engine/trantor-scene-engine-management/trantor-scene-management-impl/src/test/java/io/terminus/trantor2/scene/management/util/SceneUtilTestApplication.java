package io.terminus.trantor2.scene.management.util;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2.scene.management.util"})
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
public class SceneUtilTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(SceneUtilTestApplication.class, args);
    }
}
