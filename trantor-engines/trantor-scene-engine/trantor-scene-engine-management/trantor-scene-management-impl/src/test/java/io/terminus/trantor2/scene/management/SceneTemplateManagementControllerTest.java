package io.terminus.trantor2.scene.management;

import io.terminus.trantor2.scene.management.controller.SceneTemplateManagementController;
import io.terminus.trantor2.scene.management.dto.template.SceneTemplateBatchRequest;
import io.terminus.trantor2.scene.management.service.SceneTemplateService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class SceneTemplateManagementControllerTest {

    @Mock
    SceneTemplateService templateService;
    @InjectMocks
    SceneTemplateManagementController controller;


    @Test
    void listTemplate() {
        SceneTemplateBatchRequest request = new SceneTemplateBatchRequest();
        when(templateService.listItems(Mockito.any(SceneTemplateBatchRequest.class))).thenReturn(Collections.emptyList());
        assertEquals(0, controller.list(request).size());
    }


}
