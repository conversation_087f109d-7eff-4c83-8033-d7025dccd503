package io.terminus.trantor2.scene.management.i18n;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.management.base.MetaBaseIntegrationWithRealDataTests;
import io.terminus.trantor2.scene.management.SceneTestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = SceneTestApplication.class)
class ViewI18nVisitorTest extends MetaBaseIntegrationWithRealDataTests {

    @Autowired
    private ViewI18nVisitor viewI18nVisitor;

    @Test
    void getType() {
        assertEquals(MetaType.View, viewI18nVisitor.getType());
    }

    @Test
    void visit() {
        Set<String> visit = viewI18nVisitor.visit(Arrays.asList(modules));
        assertNotNull(visit);
        assertTrue(visit.contains("请输入"));
        assertTrue(visit.contains("删除成功!"));
        assertTrue(visit.contains("新建"));
        assertTrue(visit.contains("复制"));
    }
}