package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.management.dlock.TeamBasedLockApi;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.event.AfterRouteResourceChangedEvent;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.scene.config.SceneConfig;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.dto.IdDTO;
import io.terminus.trantor2.scene.dto.SceneDTO;
import io.terminus.trantor2.scene.exception.SceneExistException;
import io.terminus.trantor2.scene.management.SceneResourceHelper;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AbstractSceneServiceTest {
    @Mock
    SceneManagerService sceneManagerService;
    @Mock
    SceneRepo sceneRepo;
    @Mock
    MetaQueryService metaQueryService;
    @Mock
    QueryOp queryOp;
    @Mock
    TeamBasedLockApi dLockApi;
    @InjectMocks
    TestAbstractSceneService service;

    @Data
    public static class TestSceneConfig implements SceneConfig {
        private String test;

        @Override
        public SceneType getType() {
            return SceneType.CUSTOM;
        }

        @Override
        public Set<String> getI18nKeySet() {
            return null;
        }
    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TestSceneDTO extends SceneDTO<TestSceneConfig> {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TestSceneVO extends IdDTO<TestSceneConfig> {
        private String name = "name";
        private String key;

    }

    public static class TestAbstractSceneService extends AbstractSceneService<TestSceneDTO, TestSceneVO, TestSceneConfig> {

        protected TestAbstractSceneService(SceneManagerService sceneManagerService, MenuQueryService menuQueryService, MetaQueryService queryService, ServiceRepo serviceRepo, SceneRepo sceneRepo, PermissionRepo permissionRepo, ModuleRepo moduleRepo, TeamBasedLockApi dLockApi, PermissionKeyInitializer permissionKeyInitializer) {
            super(sceneManagerService, menuQueryService, queryService, serviceRepo, sceneRepo, permissionRepo, moduleRepo, dLockApi,permissionKeyInitializer);
        }

        @Override
        public TestSceneConfig initSceneConfig(TestSceneDTO sceneDTO) {
            sceneDTO.getSceneConfig().setTest(sceneDTO.getSceneConfig().getTest() + "-init");
            return sceneDTO.getSceneConfig();
        }

        @Override
        protected TestSceneVO toVO(SceneMeta meta) {
            TestSceneVO vo = new TestSceneVO();
            vo.setKey(meta.getKey());
            vo.setName(meta.getName());
            return vo;
        }
    }

    @BeforeEach
    public void init() {
        TrantorContext.init();
        TrantorContext.setTeamId(1L);
        TrantorContext.setModuleKey("app");
        ObjectJsonUtil.MAPPER.registerSubtypes(TestSceneConfig.class);
    }

    @Test
    void validateKey() throws JsonProcessingException {
        List<TestSceneDTO> dtoList = SceneResourceHelper.readValueFromResource("testSceneDTOs.json", new TypeReference<List<TestSceneDTO>>() {
        });
        when(metaQueryService.queryInTeam(any())).thenReturn(queryOp);
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(0)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(1)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(2)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(3)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(4)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(5)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(6)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(7)));
        assertThrows(ValidationException.class, () -> service.validateDTO(dtoList.get(8)));

        assertDoesNotThrow(() -> service.validateDTO(dtoList.get(9)));
        assertDoesNotThrow(() -> service.validateDTO(dtoList.get(10)));
        assertDoesNotThrow(() -> service.validateDTO(dtoList.get(11)));
        assertDoesNotThrow(() -> service.validateDTO(dtoList.get(12)));
        assertDoesNotThrow(() -> service.validateDTO(dtoList.get(13)));
    }

    @Test
    void createValidate() {
        TestSceneDTO dto = new TestSceneDTO();
        dto.setKey("app$ket");
        dto.setName("name");
        TestSceneConfig testSceneConfig = new TestSceneConfig();
        testSceneConfig.setTest("test");
        dto.setSceneConfig(testSceneConfig);
        dto.setSceneConfig(null);
        Assertions.assertThrows(ValidationException.class, () -> service.create(dto),
            "scene config must not be null");

        Assertions.assertThrows(ValidationException.class, () -> service.create(dto),
            "name must not be empty or null");

        dto.setKey(null);
        Assertions.assertThrows(ValidationException.class, () -> service.create(dto),
            "scene key must not be null");
    }

    @Test
    void create() {
        TestSceneDTO dto = new TestSceneDTO();
        dto.setKey("app$key");
        dto.setName("name");
        dto.setParentKey("parent");
        dto.setEndpointType(EndpointType.PC);
        TestSceneConfig customConfig = new TestSceneConfig();
        customConfig.setTest("a");
        dto.setSceneConfig(customConfig);

        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setKey("app$key");
        sceneMeta.setSceneConfig(customConfig);
        when(sceneRepo.create(Mockito.any(SceneMeta.class), Mockito.any(ResourceContext.class))).thenReturn(1L);
        when(metaQueryService.queryInTeam(any())).thenReturn(queryOp);

        SceneMeta meta = service.create(dto);
        assertEquals("app$key", meta.getKey());
        assertTrue(meta.getSceneConfig() instanceof TestSceneConfig);
    }

    @Test
    void initSceneConfig() {
        TestSceneDTO dto = new TestSceneDTO();
        TestSceneConfig config = new TestSceneConfig();
        config.setTest("s");
        dto.setSceneConfig(config);

        TestSceneConfig initSceneConfig = service.initSceneConfig(dto);
        assertEquals("s-init", initSceneConfig.getTest());
    }

    @Test
    void update() {
        SceneMeta sceneMeta = new SceneMeta();
        TestSceneConfig config = new TestSceneConfig();
        config.setTest("s");
        sceneMeta.setSceneConfig(config);
        sceneMeta.setType(SceneType.CUSTOM);
        service.update(sceneMeta);
    }

    @Test
    void buildBeforeCreate() {
        SceneMeta sceneMeta = new SceneMeta();
        service.buildChildrenBeforeSave(sceneMeta, true);

        verifyNoInteractions(sceneRepo);
    }


    @Test
    void validateDTO() {
        TestSceneDTO dto = new TestSceneDTO();
        TrantorContext.setTeamId(null);
        dto.setEndpointType(EndpointType.PC);
        dto.setName("name");
        dto.setKey("app$key");
        dto.setParentKey("parent");
        TestSceneConfig config = new TestSceneConfig();
        config.setTest("s");
        dto.setSceneConfig(config);
        assertThrows(ValidationException.class, dto::getTeamIdOrElseThrow, "unable to get teamId in request and context");

        dto.setTeamId(1L);
        assertEquals(1L, dto.getTeamId());

        when(metaQueryService.queryInTeam(any(Long.class))).thenReturn(queryOp);

        when(queryOp.existsByKey(Mockito.anyString())).thenReturn(true, false);
        assertThrows(SceneExistException.class, () -> service.validateDTO(dto), "scene key [exist] existed");

        when(queryOp.existsByParentKeyAndNameAndType(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        assertThrows(SceneExistException.class, () -> service.validateDTO(dto), "scene name [name] existed");
    }

    @Test
    void validateAndTidyConfig() {
        assertThrows(ValidationException.class, () -> service.validateAndTidyConfig("key", null), "route must not be null");

        TestSceneConfig config = new TestSceneConfig();
        config.setTest("s");
        assertDoesNotThrow(() -> service.validateAndTidyConfig("key", config));
    }

    @Test
    void toEntity() {
        TestSceneDTO dto = new TestSceneDTO();
        dto.setKey("app$key");

        SceneMeta entity = service.toEntity(dto);

        assertEquals("app$key", entity.getKey());
    }

    @Test
    void setApplicationEventPublisher() {
        ApplicationEventPublisher eventPublisher = Mockito.mock(ApplicationEventPublisher.class);
        service.setApplicationEventPublisher(eventPublisher);

        assertDoesNotThrow(() -> service.setApplicationEventPublisher(eventPublisher));
    }

    @Test
    void publishEvent() {
        service.publishEvent(new AfterRouteResourceChangedEvent(null));

        ApplicationEventPublisher eventPublisher = Mockito.mock(ApplicationEventPublisher.class);
        verifyNoInteractions(eventPublisher);

        service.setApplicationEventPublisher(eventPublisher);
        service.publishEvent(new AfterRouteResourceChangedEvent(null));
        verify(eventPublisher).publishEvent(Mockito.any(AfterRouteResourceChangedEvent.class));
    }
}
