package io.terminus.trantor2.scene.management;

import io.terminus.iam.spring.boot.autoconfigure.IAMAutoConfigure;
import io.terminus.iam.spring.boot.autoconfigure.IAMInternalAutoConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2"}, exclude = {IAMAutoConfigure.class, IAMInternalAutoConfigure.class})
@ImportAutoConfiguration({FeignAutoConfiguration.class})
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@Import(SceneTestConfiguration.class)
public class SceneTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(SceneTestApplication.class, args);
    }
}
