package io.terminus.trantor2.scene.management.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.management.dlock.TeamBasedLockApi;
import io.terminus.trantor2.module.event.AfterRouteResourceChangedEvent;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.management.SceneResourceHelper;
import io.terminus.trantor2.scene.management.dto.datamanager.DataManagerSceneDTO;
import io.terminus.trantor2.scene.management.meta.SceneTemplateMeta;
import io.terminus.trantor2.scene.management.meta.VersionedSceneTemplateMeta;
import io.terminus.trantor2.scene.management.repo.SceneTemplateRepo;
import io.terminus.trantor2.scene.management.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.model.ModelApiWrapper;
import io.terminus.trantor2.scene.repo.SceneRepo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DataManagerSceneServiceTest {
    @Mock
    MenuQueryService menuQueryService;
    @Mock
    ModelApiWrapper modelApiWrapper;
    @Mock
    SceneTemplateRepo sceneTemplateRepo;
    @Mock
    TrantorIAMUserService trantorIAMUserService;
    @Mock
    @Resource
    SceneManagerService sceneManagerService;
    @Spy
    ObjectMapper objectMapper;

    @Mock
    SceneRepo sceneRepo;
    @Mock
    MetaQueryService metaQueryService;
    @Mock
    QueryOp queryOp;
    @Mock
    TeamBasedLockApi dLockApi;

    @Spy
    @InjectMocks
    DataManagerSceneService dataManagerSceneService;

    @BeforeAll
    public static void init() {
        ResourceContext resourceCtx = ResourceContext.newResourceCtx("team", 1L);
        MockedStatic<ResourceContext> mockedCtx = mockStatic(ResourceContext.class);
        mockedCtx.when(ResourceContext::ctxFromThreadLocal).thenReturn(resourceCtx);
    }

    @Test
    void initSceneConfig() throws JsonProcessingException {
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);
        SceneTemplateMeta templateMeta = SceneResourceHelper.readValueFromResource("sceneTemplateMeta.json", SceneTemplateMeta.class);
        VersionedSceneTemplateMeta versionedSceneTemplateMeta = new VersionedSceneTemplateMeta();
        versionedSceneTemplateMeta.setVersion(1);
        templateMeta.setEnabledVersion(Lists.newArrayList(versionedSceneTemplateMeta));
        when(sceneTemplateRepo.findById(1L)).thenReturn(Optional.of(templateMeta));
        assertEquals(dto.getSceneConfig(), dataManagerSceneService.initSceneConfig(dto), "配置不一致");
    }

    @Test
    void create() throws JsonProcessingException {
        String parentKey = "parentKey";
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);
        SceneTemplateMeta templateMeta = SceneResourceHelper.readValueFromResource("sceneTemplateMeta.json", SceneTemplateMeta.class);
        VersionedSceneTemplateMeta versionedSceneTemplateMeta = new VersionedSceneTemplateMeta();
        versionedSceneTemplateMeta.setVersion(1);
        templateMeta.setEnabledVersion(Lists.newArrayList(versionedSceneTemplateMeta));
        when(sceneTemplateRepo.findById(1L)).thenReturn(Optional.of(templateMeta));
        when(queryOp.existsByKey(Mockito.anyString())).thenReturn(false);
        when(metaQueryService.queryInTeam(Mockito.anyLong())).thenReturn(queryOp);
        when(queryOp.existsByParentKeyAndNameAndType(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(false);

        dto.setParentKey(parentKey);

        SceneMeta sceneMeta = dataManagerSceneService.create(dto);
        assertEquals("module$world", sceneMeta.getKey(), "key 不一致");
        assertEquals("Hello", sceneMeta.getName(), "name 不一致");

        Mockito.verify(sceneRepo).create(Mockito.any(SceneMeta.class), Mockito.any(ResourceContext.class));
    }

    @Test
    void create2() throws JsonProcessingException {
        String parentKey = "parentKey";
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);

        when(queryOp.existsByKey(Mockito.anyString())).thenReturn(false);
        when(metaQueryService.queryInTeam(Mockito.anyLong())).thenReturn(queryOp);
        when(queryOp.existsByParentKeyAndNameAndType(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(false);

        dto.setParentKey(parentKey);

        SceneTemplateMeta templateMeta = SceneResourceHelper.readValueFromResource("sceneTemplateMeta.json", SceneTemplateMeta.class);
        VersionedSceneTemplateMeta versionedSceneTemplateMeta = new VersionedSceneTemplateMeta();
        versionedSceneTemplateMeta.setVersion(1);
        templateMeta.setEnabledVersion(Lists.newArrayList(versionedSceneTemplateMeta));
        when(sceneTemplateRepo.findById(1L)).thenReturn(Optional.of(templateMeta));

        SceneMeta sceneMeta = dataManagerSceneService.create(dto);
        assertEquals("module$world", sceneMeta.getKey(), "key 不一致");
        assertEquals("Hello", sceneMeta.getName(), "name 不一致");

        Mockito.verify(sceneRepo).create(Mockito.any(SceneMeta.class), Mockito.any(ResourceContext.class));
    }

    @Test
    void update() throws JsonProcessingException {
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);
        SceneMeta meta = SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);
        when(sceneManagerService.findByKey(Mockito.anyString(), Mockito.isNull())).thenReturn(Optional.of(meta));
        DataManagerView view = new DataManagerView();
        view.setKey("module$abc");
        view.setTitle("xyz");
        dto.getSceneConfig().addView(view);
        when(dLockApi.lockedBy(Mockito.anyString(), Mockito.anyString())).thenReturn(Response.ok(null));
        dataManagerSceneService.update(dto.getKey(), dto.getSceneConfig());
    }

    @Test
    void toVO() throws Exception {
        SceneMeta sceneMeta =
            SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        DataManagerSceneVO vo = dataManagerSceneService.toVO(sceneMeta);

        assertEquals(sceneMeta.getId(), vo.getId(), "场景 ID 不一致");
        Mockito.verifyNoMoreInteractions(modelApiWrapper);
    }

    @Test
    void toVOWithBadSceneType() {
        SceneMeta sceneMeta = new SceneMeta();
        sceneMeta.setType(SceneType.BI);

        Assertions.assertThrows(ValidationException.class, () -> dataManagerSceneService.toVO(sceneMeta), "expected the type of scene: DATA but it was: BI");
    }

    @Test
    void findSceneVoByKey() throws JsonProcessingException {
        SceneMeta sceneMeta =
            SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);
        when(sceneManagerService.findByKeyWithViews(Mockito.anyString())).thenReturn(Optional.of(sceneMeta));
        when(dLockApi.lockedBy(Mockito.anyString(), Mockito.anyString())).thenReturn(Response.ok(null));

        DataManagerSceneVO vo = dataManagerSceneService.findSceneVoByKey("module$world");

        Assertions.assertNotNull(vo, "场景不存在");
        assertEquals(sceneMeta.getKey(), vo.getKey(), "场景标识不一致");
        assertEquals(sceneMeta.getName(), vo.getName(), "场景名称不一致");

        Mockito.verify(sceneManagerService).findByKeyWithViews(Mockito.anyString());
    }

    @Test
    void validateDTO() throws JsonProcessingException {
        DataManagerSceneDTO dto = SceneResourceHelper.readValueFromResource("dataManagerSceneDTO.json", DataManagerSceneDTO.class);

        when(metaQueryService.queryInTeam(Mockito.anyLong())).thenReturn(queryOp);
        Assertions.assertDoesNotThrow(() -> dataManagerSceneService.validateDTO(dto));
        dto.setTemplateId(null);
        Assertions.assertThrows(ValidationException.class, () -> dataManagerSceneService.validateDTO(dto), "template not be null");
    }

    @Test
    void buildBeforeCreate() throws JsonProcessingException {
        SceneMeta sceneMeta =
            SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        dataManagerSceneService.buildChildrenBeforeSave(sceneMeta, true);
        assertEquals(2, sceneMeta.getSubMetas().size());
    }

    @Test
    void rebuildBeforeUpdate() throws JsonProcessingException {
        SceneMeta sceneMeta =
            SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);

        dataManagerSceneService.buildChildrenBeforeSave(sceneMeta, false);
        assertEquals(1, sceneMeta.getSubMetas().size());
    }

    @Test
    void afterUpdate() throws Exception {
        ApplicationEventPublisher publisher = mock(ApplicationEventPublisher.class);
        dataManagerSceneService.setApplicationEventPublisher(publisher);

        SceneMeta sceneMeta =
            SceneResourceHelper.readValueFromResource("sceneMeta.json", SceneMeta.class);
        sceneMeta.setKey("notBindMenus");
        MenuMeta menuMeta = new MenuMeta();
        menuMeta.setKey("menu");
        when(menuQueryService.findAllBySceneKeyAndTeam("notBindMenus", "team")).thenReturn(Collections.emptyList());
        when(menuQueryService.findAllBySceneKeyAndTeam("hello", "team")).thenReturn(Collections.singletonList(menuMeta));
        dataManagerSceneService.afterUpdate(sceneMeta);
        sceneMeta.setKey("hello");
        dataManagerSceneService.afterUpdate(sceneMeta);
        Mockito.verify(menuQueryService, Mockito.times(2)).findAllBySceneKeyAndTeam(Mockito.anyString(), Mockito.anyString());
        Mockito.verify(publisher).publishEvent(Mockito.any(AfterRouteResourceChangedEvent.class));
    }

}
