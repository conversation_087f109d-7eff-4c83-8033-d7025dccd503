spring:
  application:
    name: trantor2
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: false
        format_sql: true
    hibernate:
        ddl-auto:  create-drop
    database-platform: org.hibernate.dialect.MySQL57Dialect
    show-sql: true
  liquibase:
    enabled: false
  sql:
    init:
      mode: always
      schema-locations:
        - classpath*:sql/schema.sql

  flyway:
    enabled: true
    table: trantor_flyway_schema_history
    locations: classpath:trantor/db/migration
    validate-on-migrate: true
    baseline-on-migrate: true
iam:
  mock: true
logging:
  level:
    org.springframework.security:
      - debug
      - info
    org.springframework.web: error
    org.hibernate.SQL: debug
    org.hibernate.engine.QueryParameters: debug
    org.hibernate.engine.query.HQLQueryPlan: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
trantor2:
  dors:
    host: ${DORS_HOST:http://dors-web:3000}
  runtime:
    default-mode: ${RUNTIME_DEPLOY_MODE:true}
