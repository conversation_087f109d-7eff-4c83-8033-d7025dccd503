[{"id": 1, "templateConfig": {"modelRequired": true}, "name": "主数据场景模版", "key": "sys_common$master_data", "icon": null, "endpointType": "PC", "type": "MASTER_DATA", "description": "左列表右详情", "updatedAt": null, "updatedBy": 1, "updatedByName": "wjx", "sort": 7}, {"id": 2, "templateConfig": {"modelRequired": true}, "name": "主数据层级模板", "key": "sys_common$master_data_hierarchy", "icon": null, "endpointType": "PC", "type": "MASTER_DATA", "description": "左树右详情", "updatedAt": null, "updatedBy": 1, "updatedByName": "wjx", "sort": 8}, {"id": 3, "templateConfig": {"modelRequired": true}, "name": "单据场景模版", "key": "sys_common$business_document", "icon": null, "endpointType": "PC", "type": "BUSINESS_DOCUMENT", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 9}, {"id": 5, "templateConfig": {"modelRequired": false}, "name": "空白模版", "key": "sys_common$empty", "icon": null, "endpointType": "PC", "type": "EMPTY", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 10}, {"id": 6, "templateConfig": {"modelRequired": true}, "name": "规则配置模版", "key": "sys_common$rule_config", "icon": null, "endpointType": "PC", "type": "RULE_CONFIG", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 11}, {"id": 7, "templateConfig": {"modelRequired": true}, "name": "规则简单定义模板", "key": "sys_common$rule_simple_define", "icon": null, "endpointType": "PC", "type": "RULE_SIMPLE_DEFINE", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 12}, {"id": 8, "templateConfig": {"modelRequired": true}, "name": "规则分配模版", "key": "sys_common$rule_allocation", "icon": null, "endpointType": "PC", "type": "RULE_ALLOCATION", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 13}, {"id": 9, "templateConfig": {"modelRequired": true}, "name": "资产账户模板", "key": "sys_common$fine_account", "icon": null, "endpointType": "PC", "type": "FINE_ACCOUNT", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 14}, {"id": 10, "templateConfig": {"modelRequired": true}, "name": "规则全局模版", "key": "sys_common$rule_global", "icon": null, "endpointType": "PC", "type": "RULE_GLOBAL", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 15}, {"id": 11, "templateConfig": {"modelRequired": true}, "name": "报表模版", "key": "sys_common$report", "icon": null, "endpointType": "PC", "type": "REPORT", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 16}, {"id": 12, "templateConfig": {"modelRequired": true}, "name": "单据场景模版", "key": "sys_common$app_business_document", "icon": null, "endpointType": "APP", "type": "BUSINESS_DOCUMENT", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 21}, {"id": 13, "templateConfig": {"modelRequired": false}, "name": "工作台模版", "key": "sys_common$workbench", "icon": null, "endpointType": "PC", "type": "WORKBENCH", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 17}, {"id": 14, "templateConfig": {"modelRequired": false}, "name": "工作台模版", "key": "sys_common$app_workbench", "icon": null, "endpointType": "APP", "type": "WORKBENCH", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 22}, {"id": 15, "templateConfig": {"modelRequired": false}, "name": "用户列表模版", "key": "sys_common$user_list", "icon": null, "endpointType": "PC", "type": "IAM", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 18}, {"id": 16, "templateConfig": {"modelRequired": false}, "name": "角色列表模版", "key": "sys_common$role_list", "icon": null, "endpointType": "PC", "type": "IAM", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 19}, {"id": 17, "templateConfig": {"modelRequired": false}, "name": "空白模版", "key": "sys_common$app_empty", "icon": null, "endpointType": "APP", "type": "EMPTY", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 23}, {"id": 18, "templateConfig": {"modelRequired": false}, "name": "日志列表模版", "key": "sys_common$log_list", "icon": null, "endpointType": "PC", "type": "LOG", "description": null, "updatedAt": null, "updatedBy": null, "updatedByName": null, "sort": 20}]