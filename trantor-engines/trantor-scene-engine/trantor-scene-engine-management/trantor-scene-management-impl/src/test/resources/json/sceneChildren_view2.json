{"key": "app1$FIN_CM_ATF-edit", "name": "edit", "parentKey": "app1$FIN_CM_ATF", "props": {"key": "app1$FIN_CM_ATF-edit", "title": "edit", "content": {"children": [{"children": [], "key": "app1$FIN_CM_ATF-edit-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "编辑", "useExpression": false}, "type": "Meta"}, {"key": "app1$FIN_CM_ATF-UEeWSKzqi8NtISt5RzX", "name": "Field", "type": "Widget", "props": {"name": "arApDocAmt", "type": "NUMBER", "label": "应收金额", "rules": [], "width": 144, "events": [{"name": "onChange", "source": "function adjust($, formKey, record, cb) {\n  const items = $(formKey).action('getData')\n  if (!items) {\n    return\n  }\n\n  const nextItems = items.map(item => {\n    return cb(item)\n  })\n\n  $(formKey).action('setData', nextItems)\n}\n\nconst formKey = 'app1$FIN_CM_ATF-edit'\nadjust($, formKey, $context.record, function(item)  {\n  const collectedPaidDocAmt =  (item.arApDocAmt || 0)-(item.cashDiscountDocAmt || 0)-(item.transactionFeeDocAmt || 0)\n  const arApBaseAmt =  (item.arApDocAmt || 0)\n  const cashDiscountBaseAmt = (item.cashDiscountDocAmt || 0)\n  const transactionFeeBaseAmt = (item.transactionFeeDocAmt || 0)\n  const collectedPaidBaseAmt = (item.arApDocAmt || 0)-(item.cashDiscountDocAmt || 0)-(item.transactionFeeDocAmt || 0)\n  return { ...item,collectedPaidDocAmt,arApBaseAmt,cashDiscountBaseAmt,transactionFeeBaseAmt,collectedPaidBaseAmt }\n})\n"}], "hidden": false, "initialValue": 0, "componentProps": {"precision": 6, "fieldAlias": "arApDocAmt", "modelAlias": "ERP_FIN$fin_cm_pn_item_tr", "placeholder": "请输入"}, "editComponentType": "InputNumber", "editComponentProps": {"shape": "line", "precision": 2}, "displayComponentType": "Number", "displayComponentProps": {"precision": 2}}, "children": []}], "name": "Page", "props": {"showFooter": true, "showHeader": true}, "type": "Container"}, "orderBy": null, "frontendConfig": null, "conditionGroups": null, "containerSelect": {"app1$FIN_CM_ATF-table-container-app1$fin_cm_atf_head_tr": [{"field": "atfHeadCode", "selectFields": null}, {"field": "docTypeId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "docTypeCode", "selectFields": null}]}, {"field": "payColOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "name", "selectFields": null}]}, {"field": "atfDate", "selectFields": null}, {"field": "atfStatus", "selectFields": null}]}, "containerOrderBy": {}, "containerConditionGroups": {}}}