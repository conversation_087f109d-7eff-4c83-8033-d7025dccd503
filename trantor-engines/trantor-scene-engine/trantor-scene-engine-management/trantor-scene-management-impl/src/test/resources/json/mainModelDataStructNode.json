{"desc": "主模型", "name": "主模型", "alias": "main", "props": {"ddl": null, "type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "indexes": null, "mainField": "name", "tableName": "main", "physicalDelete": false}, "children": [{"key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 1, "props": {"scale": null, "length": 20, "unique": true, "comment": "ID", "dictPros": null, "required": true, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "defaultValue": null, "relationMeta": null, "autoGenerated": false, "isSystemField": true, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}, {"key": "name", "name": "名称", "type": "DataStructField", "alias": "name", "appId": 1, "props": {"scale": null, "length": 256, "unique": false, "comment": "名称", "dictPros": null, "required": true, "fieldType": "TEXT", "intLength": null, "columnName": "name", "defaultValue": null, "relationMeta": null, "autoGenerated": false, "isSystemField": false, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}, {"key": "link", "name": "link", "type": "DataStructField", "alias": "link", "appId": 1, "props": {"scale": null, "length": null, "unique": false, "comment": "link", "dictPros": null, "required": true, "fieldType": "OBJECT", "intLength": null, "columnName": "link", "defaultValue": null, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "link", "currentModelAlias": "main", "relationModelAlias": "link", "linkModelFieldAlias": null, "currentModelFieldAlias": "link"}, "autoGenerated": false, "isSystemField": false, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}, {"key": "sub", "name": "子", "type": "DataStructField", "alias": "sub", "appId": 1, "props": {"scale": null, "length": null, "unique": false, "comment": "子", "dictPros": null, "required": false, "fieldType": "OBJECT", "intLength": null, "columnName": "sub", "defaultValue": null, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "sub", "relationModelKey": "sub", "currentModelAlias": "main", "relationModelAlias": "sub", "linkModelFieldAlias": "mainId", "currentModelFieldAlias": "sub"}, "autoGenerated": false, "isSystemField": false, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}]}