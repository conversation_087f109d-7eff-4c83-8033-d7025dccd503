{"desc": "关联模型", "name": "关联模型", "alias": "link", "props": {"ddl": null, "type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "indexes": null, "mainField": "name", "tableName": "link", "physicalDelete": false}, "children": [{"key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 1, "props": {"scale": null, "length": 20, "unique": true, "comment": "ID", "dictPros": null, "required": true, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "defaultValue": null, "relationMeta": null, "autoGenerated": false, "isSystemField": true, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}, {"key": "name", "name": "名称", "type": "DataStructField", "alias": "name", "appId": 1, "props": {"scale": null, "length": 256, "unique": false, "comment": "名称", "dictPros": null, "required": true, "fieldType": "TEXT", "intLength": null, "columnName": "name", "defaultValue": null, "relationMeta": null, "autoGenerated": false, "isSystemField": false, "attachmentProps": null}, "teamId": 1, "createdAt": null, "createdBy": null, "parentKey": null, "updatedAt": null, "updatedBy": null, "createdByName": null, "updatedByName": null}]}