{"key": "app1$FIN_CM_ATF-list", "name": "list", "parentKey": "app1$FIN_CM_ATF", "props": {"key": "app1$FIN_CM_ATF-list", "type": "LIST", "title": "list", "select": null, "buttons": [{"key": "app1$FIN_CM_ATF-record-actions-1-button-1", "name": "查看", "actionKey": null, "frontendConfig": null}, {"key": "app1$FIN_CM_ATF-record-actions-1-button-2", "name": "编辑", "actionKey": null, "frontendConfig": null}, {"key": "app1$FIN_CM_ATF-record-actions-1-button-3", "name": "删除", "actionKey": null, "frontendConfig": null}, {"key": "app1$FIN_CM_ATF-batch-actions-1-button-1", "name": "新建", "actionKey": null, "frontendConfig": null}, {"key": "app1$FIN_CM_ATF-batch-actions-1-button-2", "name": "批量删除", "actionKey": null, "frontendConfig": null}], "content": {"key": "app1$FIN_CM_ATF-list", "name": "Page", "type": "Container", "props": {"showHeader": false}, "children": [{"key": "app1$FIN_CM_ATF-table-container-app1$fin_cm_atf_head_tr", "name": "Table", "type": "Container", "props": {"flow": {"type": "InvokeSystemService", "modelAlias": "app1$fin_cm_atf_head_tr", "serviceKey": "app1$SYS_PagingDataService", "containerKey": "app1$FIN_CM_ATF-table-container-app1$fin_cm_atf_head_tr"}, "fields": [{"name": "id", "type": "NUMBER", "label": "ID", "width": 144, "hidden": true, "componentProps": {"precision": null, "fieldAlias": "id", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请输入"}}, {"name": "created<PERSON>y", "type": "OBJECT", "label": "创建人", "width": 168, "hidden": true, "componentProps": {"label": "选择", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "app1$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "app1$SYS_FindDataByIdService", "searchServiceKey": "app1$SYS_PagingDataService"}, "parentModelAlias": "app1$fin_cm_atf_head_tr"}}, {"name": "updatedBy", "type": "OBJECT", "label": "更新人", "width": 168, "hidden": true, "componentProps": {"label": "选择", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "app1$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "app1$SYS_FindDataByIdService", "searchServiceKey": "app1$SYS_PagingDataService"}, "parentModelAlias": "app1$fin_cm_atf_head_tr"}}, {"name": "createdAt", "type": "DATE", "label": "创建时间", "width": 134, "hidden": true, "componentProps": {"fieldAlias": "createdAt", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请选择"}}, {"name": "updatedAt", "type": "DATE", "label": "更新时间", "width": 134, "hidden": true, "componentProps": {"fieldAlias": "updatedAt", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请选择"}}, {"name": "version", "type": "NUMBER", "label": "版本号", "width": 144, "hidden": true, "componentProps": {"precision": null, "fieldAlias": "version", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请输入"}}, {"name": "deleted", "type": "NUMBER", "label": "逻辑删除标识", "width": 144, "hidden": true, "componentProps": {"precision": null, "fieldAlias": "deleted", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请输入"}}, {"name": "atfHeadCode", "type": "TEXT", "label": "编码", "width": 146, "hidden": false, "componentProps": {"fieldAlias": "atfHeadCode", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请输入"}}, {"name": "docTypeId", "type": "OBJECT", "label": "单据类型", "width": 168, "hidden": false, "componentProps": {"label": "选择", "columns": ["docTypeCode", "docType", "doc<PERSON>ame"], "fieldAlias": "docTypeId", "labelField": "docTypeCode", "modelAlias": "app1$fin_doc_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "app1$SYS_FindDataByIdService", "searchServiceKey": "app1$SYS_PagingDataService"}, "parentModelAlias": "app1$fin_cm_atf_head_tr"}}, {"name": "payColOrgId", "type": "OBJECT", "label": "收付主体", "width": 168, "hidden": false, "componentProps": {"label": "选择", "columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "payColOrgId", "labelField": "name", "modelAlias": "app1$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "app1$SYS_FindDataByIdService", "searchServiceKey": "app1$SYS_PagingDataService"}, "parentModelAlias": "app1$fin_cm_atf_head_tr"}}, {"name": "atfDate", "type": "DATE", "label": "业务日期", "width": 134, "hidden": false, "componentProps": {"fieldAlias": "atfDate", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请选择"}}, {"name": "atfStatus", "type": "SELECT", "label": "转账单状态", "width": 116, "hidden": false, "componentProps": {"fieldAlias": "atfStatus", "modelAlias": "app1$fin_cm_atf_head_tr", "placeholder": "请选择"}}], "modelAlias": "app1$fin_cm_atf_head_tr", "serviceKey": "app1$SYS_PagingDataService"}, "children": [{"key": "app1$FIN_CM_ATF-record-actions-1", "name": "RecordActions", "props": {"label": "操作"}, "children": [{"key": "app1$FIN_CM_ATF-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"label": "查看", "actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}}, "children": []}, {"key": "app1$FIN_CM_ATF-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"label": "编辑", "actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}}, "children": []}, {"key": "app1$FIN_CM_ATF-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"label": "删除", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认删除吗？", "type": "Popconfirm", "action": "Confirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "app1$fin_cm_atf_head_tr"}, {"name": "request", "type": "expression", "expression": "{ id: primaryId }"}], "service": "app1$SYS_DeleteDataByIdService"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}]}}, "children": []}]}, {"key": "app1$FIN_CM_ATF-batch-actions-1", "name": "BatchActions", "type": "Layout", "props": {}, "children": [{"key": "app1$FIN_CM_ATF-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "app1$FIN_CM_ATF-edit", "name": "create", "type": "View"}, "params": [{"expression": "record.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}], "executeScriptConfig": "测试一下\nhhhh(\"app1$FIN_CM_ATF-edit\",'app1$FIN_CM_ATF-stackView')"}, "label": "新建", "showCondition": {}, "type": "primary"}, "children": []}, {"key": "app1$FIN_CM_ATF-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"label": "批量删除", "disabled$": "$context.selectedKeys?.length === 0", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认删除吗？", "type": "Modal", "action": "Confirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "app1$fin_cm_atf_head_tr"}, {"name": "request", "type": "expression", "expression": "{ ids: selectedKeys }"}], "service": "app1$SYS_BatchDeleteDataService"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}]}}, "children": []}, {"key": "app1$FIN_CM_ATF-batch-actions-1-button-3", "name": "ExportButton", "type": "Widget", "props": {"label": "导出"}, "children": []}, {"key": "app1$FIN_CM_ATF-list-app1$fin_cm_atf_head_tr-logs", "name": "Logs", "type": "Widget", "props": {"label": "日志"}, "children": []}]}]}, {"key": "app1$FIN_CM_ATF-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}, {"key": "edit2", "label": "编辑页2"}], "key": "list"}}]}, "orderBy": null, "frontendConfig": null, "conditionGroups": null, "containerSelect": {"app1$FIN_CM_ATF-table-container-app1$fin_cm_atf_head_tr": [{"field": "atfHeadCode", "selectFields": null}, {"field": "docTypeId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "docTypeCode", "selectFields": null}]}, {"field": "payColOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "name", "selectFields": null}]}, {"field": "atfDate", "selectFields": null}, {"field": "atfStatus", "selectFields": null}]}, "containerOrderBy": {}, "containerConditionGroups": {}}}