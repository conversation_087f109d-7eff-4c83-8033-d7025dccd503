[{"frontendConfig": {"modules": ["t-material", "t-runtime-erp"]}, "title": "list", "key": "INV$TCP72601-list", "content": {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "label": "查看"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "label": "编辑", "showCondition": [[{"key": "TERP_MIGRATE$TCP72601-1h3vqjart17", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "CREATE", "constValue": "CREATE", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3vqjsor19", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]]}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-MNH2fndxBaZ0HzfknZcWD", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "TERP_MIGRATE$TCP72601-IdYZKpDifKnMNwCRSsf9B", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "type": "Modal"}}, "buttonKey": "button_2C6fJvEtIGd36w_ef9I9", "confirmOn": "off", "label": "审批通过", "showCondition": [[{"key": "TERP_MIGRATE$TCP72601-1h3tf54an56", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "CREATE", "constValue": "CREATE", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tf5foe57", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]], "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-OJ1YD5WuuSLNhN1Xv0_TF", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "TERP_MIGRATE$TCP72601-qW2t3eZHWcQXbd1_awzKR", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "type": "Modal"}}, "buttonKey": "button_zjbtMuYGmYM6c5P56k3n", "confirmOn": "off", "label": "审批拒绝", "showCondition": {"conditions": [{"conditions": [{"key": "TERP_MIGRATE$TCP72601-U2SwlA1OoLAGULUhlLzH9", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "VAR", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "CREATE", "constValue": "CREATE", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tf3vjq53", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "TERP_MIGRATE$TCP72601-CF4YMJ2wq_6LSFuhavNEr", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "TERP_MIGRATE$TCP72601-GRcVYLXjAPXwnkmwUW068", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-wNZlwIzVnqcQF4T-TRQU2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认？", "type": "Popconfirm"}], "bindFlowConfig": {"service": "INV$INV_COUNT_PLAN_AUDIT_CANCEL_EVENT_SERVICE", "serviceParams": [{"key": "TERP_MIGRATE$TCP72601-request", "leftValue": {"value": {"fieldAlias": "TERP_MIGRATE$TCP72601-request", "fieldKey": "TERP_MIGRATE$TCP72601-request", "fieldName": "TERP_MIGRATE$TCP72601-request", "fieldType": "Object", "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TERP_MIGRATE$TCP72601-table-container-TERP_MIGRATE$inv_count_plan_tr"}], "executeLogic": "BindFlow"}, "buttonKey": "button_WvYpi8LNgKeSiIUoeK4l", "confirmOn": "off", "label": "取消审核", "showCondition": {"conditions": [{"conditions": [{"key": "TERP_MIGRATE$TCP72601-6SizkTn4Kx6B5RBsvF5ys", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "VAR", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "AUDIT", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "TERP_MIGRATE$TCP72601-mJc48pqwyJfBvnQECtJXO", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "TERP_MIGRATE$TCP72601-ODRqqjk7-PVPZtV-2rTIM", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-HSEyNrKRbHzl_aelYksa8", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "TERP_MIGRATE$TCP72601-6tmVC4x8G11yqWFxernqK", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"key": "TERP_MIGRATE$TCP72601-countPlanId", "value": [{"value": {"alias": "id", "defaultValue": null, "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "hidden": false, "hiddenOnCreate": false, "label": "id", "modelKey": "TERP_MIGRATE$inv_count_plan_tr", "modelName": "盘点方案", "nodeKey": "none", "nodeTitle": "", "readonly": false, "required": true, "tips": "ID"}, "valueType": "MODEL"}]}], "type": "Modal"}}, "buttonKey": "button_n7jx3ktkUQrxmJn1FEjD", "confirmOn": "off", "label": "查看盘点作业", "showCondition": [[{"key": "TERP_MIGRATE$TCP72601-1h3tfg34f83", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "COMPLETE", "constValue": "COMPLETE", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tfglh287", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], [{"key": "TERP_MIGRATE$TCP72601-1h3tfib0092", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "EXECUTING", "constValue": "EXECUTING", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tfiii593", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], [{"key": "TERP_MIGRATE$TCP72601-1h3tfisgs96", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "AUDIT", "constValue": "AUDIT", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tfj9qh98", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]], "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-XKC9GpV0_tXlEzvjmeiMM", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "INV$inv_count_plan_tr"}, {"expression": "{ id: primaryId }", "name": "TERP_MIGRATE$TCP72601-request", "type": "expression"}], "service": "INV$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TERP_MIGRATE$TCP72601-table-container-TERP_MIGRATE$inv_count_plan_tr"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonKey": "button_fJkFzDyCNCH_cyT9ZbL4", "confirmOn": "off", "label": "删除", "showCondition": [[{"key": "TERP_MIGRATE$TCP72601-1h4ff56851", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "已创建", "value": "CREATE"}, {"label": "已审核", "value": "AUDIT"}, {"label": "执行中", "value": "EXECUTING"}, {"label": "已完成", "value": "COMPLETE"}, {"label": "已作废", "value": "INVALID"}], "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "TERP_MIGRATE$inv_count_plan_tr.status", "valueType": "MODEL", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constVal": "CREATE", "constValue": "CREATE", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h4ff5vkk2", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]], "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-record-actions-1", "name": "RecordActions", "props": {"label": "操作"}}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "INV$inv_count_plan_tr"}, {"expression": "{ ids: selectedKeys }", "name": "TERP_MIGRATE$TCP72601-request", "type": "expression"}], "service": "INV$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "批量删除"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-batch-actions-1-button-3", "name": "ExportButton", "props": {"label": "导出"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-list-TERP_MIGRATE$inv_count_plan_tr-logs", "name": "Logs", "props": {"label": "日志"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-table-container-TERP_MIGRATE$inv_count_plan_tr", "name": "Table", "props": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "hidden": false, "label": "编码", "name": "code", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "hidden": false, "label": "名称 ", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "公司", "name": "comOrgId", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "状态", "name": "status", "type": "SELECT", "width": 116}, {"componentProps": {"fieldAlias": "remark", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "hidden": false, "label": "备注", "name": "remark", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "baFreezeTime", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "账存时间", "name": "baFreezeTime", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "countType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "盘点类型", "name": "countType", "type": "SELECT", "width": 116}, {"componentProps": {"fieldAlias": "createType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "创建方式", "name": "createType", "type": "SELECT", "width": 116}, {"componentProps": {"fieldAlias": "isAddRowAllowed", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "是否允许增加行", "name": "isAddRowAllowed", "type": "BOOL", "width": 116}, {"componentProps": {"fieldAlias": "isZeroInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "零库存参与盘点", "name": "isZeroInvCount", "type": "BOOL", "width": 116}, {"componentProps": {"fieldAlias": "isTransitInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "在途物料参与盘点", "name": "isTransitInvCount", "type": "BOOL", "width": 116}, {"componentProps": {"fieldAlias": "isPostBlock", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "是否冻结过账", "name": "isPostBlock", "type": "BOOL", "width": 116}, {"componentProps": {"fieldAlias": "checkScope", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "抽查范围", "name": "checkScope", "type": "MULTISELECT", "width": 146}, {"componentProps": {"fieldAlias": "dateFrom", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "开始日期", "name": "dateFrom", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "dateTo", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "label": "结束日期", "name": "dateTo", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "distributeRule", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "分配规则", "name": "distributeRule", "type": "MULTISELECT", "width": 146}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": false, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"defaultValue": null, "fieldAlias": "approveNote", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "initialValue": null, "label": "审批意见", "name": "approveNote", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "label": "名称 ", "name": "name", "type": "TEXT"}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "公司", "name": "comOrgId", "type": "OBJECT"}, {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "状态", "name": "status", "type": "SELECT"}, {"componentProps": {"fieldAlias": "baFreezeTime", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "账存时间", "name": "baFreezeTime", "type": "DATE"}, {"componentProps": {"fieldAlias": "countType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "盘点类型", "name": "countType", "type": "SELECT"}, {"componentProps": {"fieldAlias": "createType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "创建方式", "name": "createType", "type": "SELECT"}, {"componentProps": {"columns": ["planCode", "planName", "comOrgId", "effectDate", "expireDate", "cycleNum", "cycleUnit", "status", "seq", "lastCountDate", "lastScheduleDate", "nextScheduleDate", "note", "isAddRowAllowed", "isZeroInvCount", "isPostBlock", "isTransitInvCount", "isAutoApproveCountPlan", "distributeRule"], "fieldAlias": "countStrategyId", "label": "选择盘点计划", "labelField": "planCode", "modelAlias": "INV$inv_count_strategy_md", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "盘点计划", "name": "countStrategyId", "type": "OBJECT"}], "flow": {"containerKey": "TERP_MIGRATE$TCP72601-table-container-TERP_MIGRATE$inv_count_plan_tr", "modelAlias": "INV$inv_count_plan_tr", "serviceKey": "INV$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "INV$inv_count_plan_tr", "serviceKey": "INV$SYS_PagingDataService", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"key": "TERP_MIGRATE$TCP72601-_WCRqiHcZOd4EdzMhj8mM", "leftValue": {"fieldType": "Number", "options": [], "scope": "row", "title": "ID", "type": "VarValue", "val": "id", "value": "TERP_MIGRATE$inv_count_plan_tr.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "NEQ", "rightValue": {"constValue": "123213", "fieldType": "Number", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "TERP_MIGRATE$TCP72601-tvtkTfSof50gRIR0tRH9u", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "TERP_MIGRATE$TCP72601-CfdgpUuvhfn1uXhQ_de2p", "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-JnVXNmsJIurQI2Ivuuv-U", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-ghaKlpo0XOjfOIS3A022A", "name": "FormField", "props": {"componentProps": {"fieldAlias": "approveNote", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "审批意见", "lookup": [{"action": "clear", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "TERP_MIGRATE$TCP72601-Xv8cLyYdAqwC0B0EO9UQN", "operator": "clear", "valueRules": null}], "name": "approveNote", "required": false, "type": "TEXT"}, "type": "Widget"}], "displayName": "审批通过表单组", "key": "TERP_MIGRATE$TCP72601-H70-8dXjKPP7h-y_Lt75S", "name": "FormGroup", "props": {"flow": {}, "flow22": {"containerKey": "TERP_MIGRATE$TCP72601-H70-8dXjKPP7h-y_Lt75S", "modelAlias": "INV$inv_count_plan_tr", "params$": "{ id: parentRecord.id }", "serviceKey": "INV$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "INV$count_audit"}, "type": "Container"}], "displayName": "审批通过弹窗内容", "key": "TERP_MIGRATE$TCP72601-73_T1PE9-fKbq0JV58gaf", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-xe5EIRldhECEr4ymeBP7Y", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["IdYZKpDifKnMNwCRSsf9B"]}]}, "buttonKey": "button_xiGCsbXE1JuaetSXeqCm", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-HqxN0la6cFOhdSUO8RewB", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"expression": "parent?.record?.id", "name": "id", "type": "expression"}, {"action": {"selector": "approveNote", "target": "TERP_MIGRATE$TCP72601-H70-8dXjKPP7h-y_Lt75S"}, "name": "approveNote", "type": "action"}], "service": "INV$INV_COUNT_PLAN_AUDIT_PASS_EVENT_SERVICE", "serviceParams": [{"key": "TERP_MIGRATE$TCP72601-request", "leftValue": {"value": {"fieldAlias": "TERP_MIGRATE$TCP72601-request", "fieldKey": "TERP_MIGRATE$TCP72601-request", "fieldName": "TERP_MIGRATE$TCP72601-request", "fieldType": "Object", "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["IdYZKpDifKnMNwCRSsf9B"]}, {"action": "Refresh", "target": "TERP_MIGRATE$TCP72601-list"}], "executeLogic": "BindFlow"}, "buttonKey": "button_SyCquCMRN6jHLPjBZBel", "confirmOn": "off", "label": "保存", "showCondition": null, "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-xKvh8mq0pUGRtaUEpErA_", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "displayName": "审批通过", "key": "TERP_MIGRATE$TCP72601-IdYZKpDifKnMNwCRSsf9B", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "title": "审批通过"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-v1UH8-L6Nd9WFx53xqmaU", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-PDJzcqPAdrD0vNEZ5oAYT", "name": "FormField", "props": {"componentProps": {"fieldAlias": "approveNote", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "审批意见", "lookup": [{"action": "clear", "conditionGroup": [[{"key": "TERP_MIGRATE$TCP72601-1h4kg4puf136", "leftValue": {"constValue": null, "fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "TERP_MIGRATE$inv_count_plan_tr.id", "valueType": "MODEL", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}]], "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "TERP_MIGRATE$TCP72601-SS7HkFhXXWXqfOMAKuBzl", "operator": "clear", "valueRules": null}], "name": "approveNote", "required": false, "type": "TEXT"}, "type": "Widget"}], "displayName": "审批拒绝表单组", "key": "TERP_MIGRATE$TCP72601-QsmcbF_19kfYc3tnb7uFv", "name": "FormGroup", "props": {"flow": {}, "modelAlias": "INV$count_audit"}, "type": "Container"}], "displayName": "审批拒绝", "key": "TERP_MIGRATE$TCP72601-872lqAbEYhwhzfo6zuW3x", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-DpfKDCoiEqyuQ1tOFOAeA", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["qW2t3eZHWcQXbd1_awzKR"]}]}, "buttonKey": "button_XsgI7Rea_5cEkIbV7sY0", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-iLHXtVzirDrvtJ045DRUx", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"expression": "parent?.record?.id", "name": "id", "type": "expression"}, {"action": {"selector": "approveNote", "target": "TERP_MIGRATE$TCP72601-QsmcbF_19kfYc3tnb7uFv"}, "name": "approveNote", "type": "action"}], "service": "INV$INV_COUNT_PLAN_AUDIT_FAIL_EVENT_SERVICE", "serviceParams": [{"key": "TERP_MIGRATE$TCP72601-request", "leftValue": {"value": {"fieldAlias": "TERP_MIGRATE$TCP72601-request", "fieldKey": "TERP_MIGRATE$TCP72601-request", "fieldName": "TERP_MIGRATE$TCP72601-request", "fieldType": "Object", "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["qW2t3eZHWcQXbd1_awzKR"]}, {"action": "Refresh", "target": "TERP_MIGRATE$TCP72601-list"}], "executeLogic": "BindFlow"}, "buttonKey": "button_4uSJAAbQVyQu7YktonkF", "confirmOn": "off", "label": "保存", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-sn1TTusfxnzIU38GjO8Vw", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "displayName": "审批拒绝", "key": "TERP_MIGRATE$TCP72601-qW2t3eZHWcQXbd1_awzKR", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "title": "审批拒绝"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-kdgFUMBilgvWUEYU2XgvG", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-8TTYylp9VF83cJAYNyiMB", "name": "RecordActions", "props": {}, "type": "Layout"}], "displayName": "作业单表格", "key": "TERP_MIGRATE$TCP72601-rQkqWMX9oDGK11gZqBgvx", "name": "Table", "props": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "���本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "code", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "status", "remark", "baFreezeTime", "countType", "createType", "isAddRowAllowed", "isZeroInvCount", "isTransitInvCount", "isPostBlock", "checkScope", "dateFrom", "dateTo", "distributeRule", "approveNote"], "fieldAlias": "TERP_MIGRATE$TCP72601-countPlanId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_count_plan_tr", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "盘点方案", "name": "TERP_MIGRATE$TCP72601-countPlanId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "baFreezeTime", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账存时间", "name": "baFreezeTime", "rules": [], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "approveNote", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "审批意见", "name": "approveNote", "rules": [], "type": "TEXT", "width": 120}], "flow": {"containerKey": "TERP_MIGRATE$TCP72601-rQkqWMX9oDGK11gZqBgvx", "modelAlias": "INV$inv_count_doc_head_tr", "serviceKey": "INV$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "INV$inv_count_doc_head_tr", "showFilterFields": false, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"key": "dfAb8vcRUX1n2E7TSDTrg", "leftValue": {"fieldType": "Number", "options": [], "scope": "row", "title": "盘点方案.ID", "type": "VarValue", "val": "countPlanId.id", "value": "TERP_MIGRATE$inv_count_doc_head_tr.countPlanId.id", "valueType": "VAR", "varVal": "countPlanId.id", "varValue": [{"valueKey": "countPlanId", "valueName": "countPlanId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "scope": "row", "target": "TERP_MIGRATE$TCP72601-table-container-TERP_MIGRATE$inv_count_plan_tr", "title": "id", "type": "VarValue", "val": "id", "value": "id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "type": "ConditionLeaf"}], "key": "TERP_MIGRATE$TCP72601-nKrcfpkRW2oj3owKRmRPD", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "TERP_MIGRATE$TCP72601-RQ5_RcMc0tTw_re4Kc0lB", "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-nQkFfOgPt365EHwz-yv-Y", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-FV0-4k_adv4wJuo90r1H1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "displayName": "查看盘点作业", "key": "TERP_MIGRATE$TCP72601-6tmVC4x8G11yqWFxernqK", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": true, "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "���本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "code", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "status", "remark", "baFreezeTime", "countType", "createType", "isAddRowAllowed", "isZeroInvCount", "isTransitInvCount", "isPostBlock", "checkScope", "dateFrom", "dateTo", "distributeRule", "approveNote"], "fieldAlias": "TERP_MIGRATE$TCP72601-countPlanId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_count_plan_tr", "parentModelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "盘点方案", "name": "TERP_MIGRATE$TCP72601-countPlanId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "baFreezeTime", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账存时间", "name": "baFreezeTime", "rules": [], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "approveNote", "modelAlias": "INV$inv_count_doc_head_tr", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "审批意见", "name": "approveNote", "rules": [], "type": "TEXT", "width": 120}], "layout": "modal", "showScope": "all", "tableCondition": null, "title": "查看盘点作业"}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-list", "name": "Page", "props": {"showHeader": false}, "type": "Container"}, "type": "LIST", "containerConditionGroups": {}, "containerOrderBy": {}, "select": null, "conditionGroups": null, "orderBy": null}, {"frontendConfig": {"modules": ["t-material", "t-runtime-erp"]}, "title": "detail", "key": "INV$TCP72601-detail", "content": {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-page-title-observable-text", "name": "Text", "props": {"value": "盘点方案详情"}}], "key": "TERP_MIGRATE$TCP72601-detail-page-title-observable", "name": "Observable", "props": {}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-TERP_MIGRATE$inv_count_plan_tr-logs", "name": "Logs", "props": {"label": "日志"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-code", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-name", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "名称 ", "name": "name", "type": "TEXT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-comOrgId", "name": "DetailField", "props": {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司组织编码", "name": "code", "rules": [{"message": "请输入公司组织编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司组织名称", "name": "name", "rules": [{"message": "请输入公司组织名称", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择", "labelField": "currName", "modelAlias": "GEN$gen_curr_type_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币种", "name": "currId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "status"], "fieldAlias": "comId", "label": "选择", "labelField": "name", "modelAlias": "GEN$gen_com_type_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联公司", "name": "comId", "rules": [{"message": "请输入关联公司", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["chaccCode", "chaccDesc", "accnoLevel"], "fieldAlias": "chaccId", "label": "选择", "labelField": "chaccCode", "modelAlias": "GEN$gen_chart_acc_head_md", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "公司后续会计科目表", "name": "chaccId", "rules": [{"message": "请输入公司后续会计科目表", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["comPeriodCode", "comPeriodName", "comPeriodYear"], "fieldAlias": "comPeriodId", "label": "选择", "labelField": "comPeriodYear", "modelAlias": "GEN$gen_com_period_head_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "公司后续记账期间", "name": "comPeriodId", "rules": [{"message": "请输入公司后续记账期间", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}], "mainField": "name", "modelAlias": "GEN$org_com_org_cf"}, "editComponentType": "RelationSelect", "label": "公司", "name": "comOrgId", "type": "OBJECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-remark", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "remark", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "备注", "name": "remark", "type": "TEXT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-baFreezeTime", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "baFreezeTime", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "label": "账存时间", "name": "baFreezeTime", "type": "DATE"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-createType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "创建方式", "name": "createType", "type": "SELECT"}}], "displayName": "基础信息", "key": "TERP_MIGRATE$TCP72601-b1rntZNiQ6KMc_bRnvp3Z", "name": "DetailGroupItem", "props": {"title": "基础信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-isAddRowAllowed", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isAddRowAllowed", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "label": "是否允许增加行", "name": "isAddRowAllowed", "type": "BOOL"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-isZeroInvCount", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isZeroInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "label": "零库存参与盘点", "name": "isZeroInvCount", "type": "BOOL"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-isTransitInvCount", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isTransitInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "label": "在途物料参与盘点", "name": "isTransitInvCount", "type": "BOOL"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-isPostBlock", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isPostBlock", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "label": "是否冻结过账", "name": "isPostBlock", "type": "BOOL"}}], "displayName": "盘点参数", "key": "TERP_MIGRATE$TCP72601-zakiMN5kxBjg8OZjiXBd9", "name": "DetailGroupItem", "props": {"title": "盘点参数"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-countType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "countType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "盘点类型", "name": "countType", "type": "SELECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-checkScope", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "checkScope", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "label": "抽查范围", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "TERP_MIGRATE$TCP72601-rlxNpiXzrS7eB2TVNHpn9", "operator": null, "valueRules": null}], "name": "checkScope", "type": "MULTISELECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-distributeRule", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "distributeRule", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "label": "分配规则", "name": "distributeRule", "type": "MULTISELECT"}}], "displayName": "方案规则", "key": "TERP_MIGRATE$TCP72601-G9iWWGVUQJAOcyZshqxvA", "name": "DetailGroupItem", "props": {"title": "方案规则"}, "type": "Layout"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-Xt0ItWvs1y0qMfap3xGbs", "name": "FormGroupItem", "props": {"title": "表单组标题"}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-dateFrom", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "dateFrom", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "displayComponentType": "Date", "editComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "editComponentType": "DatePicker", "label": "开始日期", "name": "dateFrom", "type": "DATE"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-For-DetailField-dateTo", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "dateTo", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "displayComponentType": "Date", "editComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "editComponentType": "DatePicker", "label": "结束日期", "name": "dateTo", "type": "DATE"}}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invOrg-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invOrg-meta-container", "name": "Table", "props": {"fieldName": "invOrg", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status"], "fieldAlias": "invOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_inv_org_cf", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "库存组织", "name": "invOrgId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "invOrg", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_org_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invOrg-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invOrg"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invOrg", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invLoc-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invLoc-meta-container", "name": "Table", "props": {"fieldName": "invLoc", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "orgId"], "fieldAlias": "invLocId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_inv_loc_cf", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "库存地点", "name": "invLocId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "invLoc", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_loc_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invLoc-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invLoc"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invLoc", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-batch-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-batch-meta-container", "name": "Table", "props": {"fieldName": "batch", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "referBatchId", "matId", "invTypeId", "status", "charaClassId"], "fieldAlias": "batchId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_batch_md", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "批次", "name": "batchId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "batch", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_batch_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-batch-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "batch"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-batch", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invType-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invType-meta-container", "name": "Table", "props": {"fieldName": "invType", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name"], "fieldAlias": "invTypeId", "label": "选择", "labelField": "name", "modelAlias": "INV$inv_inv_type_cf", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "库存类型", "name": "invTypeId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "invType", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invType-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invType"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-invType", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-spcStkType-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-spcStkType-meta-container", "name": "Table", "props": {"fieldName": "spcStkType", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name", "action<PERSON>ey"], "fieldAlias": "spcStkTypeId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_spc_stk_type_cf", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "特殊库存标识", "name": "spcStkTypeId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "spcStkType", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-spcStkType-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "spcStkType"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-spcStkType", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-mat-meta-container-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-mat-meta-container", "name": "Table", "props": {"fieldName": "mat", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas"], "fieldAlias": "matId", "label": "选择", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "label": "物料", "name": "matId", "type": "OBJECT", "width": 168}], "flow": {"context$": "$context", "name": "mat", "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_mat_link_tr", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-mat-meta", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "mat"}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-2-tabs-detail-mat", "name": "DetailGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "TERP_MIGRATE$TCP72601-detail-detail-group-item-3-tabs-detail", "name": "Detail", "props": {"flow": {"context$": "$context", "name": null, "type": "RelationData"}, "modelAlias": "INV$inv_count_plan_tr"}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-2-Tabs", "name": "Tabs", "props": {"items": [{"label": "库存组织"}, {"label": "库存地点"}, {"label": "批次"}, {"label": "库存类型"}, {"label": "特殊库存标识"}, {"label": "物料"}, {"label": "系统信息"}]}, "type": "Layout"}], "displayName": "筛选范围", "key": "TERP_MIGRATE$TCP72601-detailView-detail-Layout-2", "name": "DetailGroupItem", "props": {"showSplit": true, "title": "筛选范围"}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-detailView-detail", "name": "Detail", "props": {"flow": {"containerKey": "TERP_MIGRATE$TCP72601-detailView-detail", "modelAlias": "INV$inv_count_plan_tr", "serviceKey": "INV$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "INV$inv_count_plan_tr", "serviceKey": "INV$SYS_FindDataByIdService"}, "type": "Container"}], "key": "TERP_MIGRATE$TCP72601-detail", "name": "Page", "props": {}, "type": "Container"}, "type": "DETAIL", "containerConditionGroups": {}, "containerOrderBy": {}, "select": null, "conditionGroups": null, "orderBy": null}, {"frontendConfig": {"modules": ["t-material"]}, "title": "edit", "key": "INV$TCP72601-edit", "content": {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-editView-title-observable-text", "name": "Text", "props": {"value$": "route?.action === \"edit\" ? \"编辑盘点方案\" : \"创建盘点方案\""}}], "key": "TERP_MIGRATE$TCP72601-editView-title-observable", "name": "Observable", "props": {}, "type": "Meta"}], "key": "TERP_MIGRATE$TCP72601-editView-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-ID", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-创建人", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-更新人", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-创建时间", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-更新时间", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-版本号", "name": "FormField", "props": {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-逻辑删除标识", "name": "FormField", "props": {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-名称 ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "名称 ", "name": "name", "rules": [{"message": "请输入名称 ", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-公司", "name": "FormField", "props": {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司组织编码", "name": "code", "rules": [{"message": "请输入公司组织编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司组织名称", "name": "name", "rules": [{"message": "请输入公司组织名称", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择", "labelField": "currName", "modelAlias": "GEN$gen_curr_type_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币种", "name": "currId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "status"], "fieldAlias": "comId", "label": "选择", "labelField": "name", "modelAlias": "GEN$gen_com_type_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联公司", "name": "comId", "rules": [{"message": "请输入关联公司", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["chaccCode", "chaccDesc", "accnoLevel"], "fieldAlias": "chaccId", "label": "选择", "labelField": "chaccCode", "modelAlias": "GEN$gen_chart_acc_head_md", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "公司后续会计科目表", "name": "chaccId", "rules": [{"message": "请输入公司后续会计科目表", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["comPeriodCode", "comPeriodName", "comPeriodYear"], "fieldAlias": "comPeriodId", "label": "选择", "labelField": "comPeriodYear", "modelAlias": "GEN$gen_com_period_head_cf", "parentModelAlias": "GEN$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "公司后续记账期间", "name": "comPeriodId", "rules": [{"message": "请输入公司后续记账期间", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "GEN$org_com_org_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}], "mainField": "name", "modelAlias": "GEN$org_com_org_cf"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "公司", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-EqrTpKT1XJ5KIvFk6c1QH", "operator": null, "valueRules": null}], "name": "comOrgId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-备注", "name": "FormField", "props": {"componentProps": {"fieldAlias": "remark", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请输入"}, "displayComponentProps": {"labelField": []}, "displayComponentType": "Text", "editComponentProps": {"showScope": "all", "tableCondition": null}, "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "备注", "name": "remark", "rules": [], "type": "TEXT"}, "type": "Widget"}], "displayName": "基础信息", "key": "TERP_MIGRATE$TCP72601-qI4h9XZINRz0YhihW8y8L", "name": "FormGroupItem", "props": {"title": "基础信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-是否��许增加行", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isAddRowAllowed", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": false, "label": "是否允许增加行", "name": "isAddRowAllowed", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-零库存参与盘点", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isZeroInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": false, "label": "零库存参与盘点", "name": "isZeroInvCount", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-在途物料参与盘点", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isTransitInvCount", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": false, "label": "在途物料参与盘点", "name": "isTransitInvCount", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-是否冻结过账", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isPostBlock", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": false, "label": "是否冻结过账", "name": "isPostBlock", "rules": [], "type": "BOOL"}, "type": "Widget"}], "displayName": "盘点参数", "key": "TERP_MIGRATE$TCP72601-7gXQBHR3P8DzWna1rzW1y", "name": "FormGroupItem", "props": {"title": "盘点参数"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-盘点类型", "name": "FormField", "props": {"componentProps": {"fieldAlias": "countType", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "initialValue": null, "label": "盘点类别", "name": "countType", "rules": [{"message": "请输入盘点类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-抽查范围", "name": "FormField", "props": {"componentProps": {"fieldAlias": "checkScope", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "hidden": false, "initialValue": null, "label": "抽查范围", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "TERP_MIGRATE$TCP72601-bamViQHPxgtjsrD3nlGVD", "operator": null, "valueRules": null}], "name": "checkScope", "rules": [], "type": "MULTISELECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-分配规则", "name": "FormField", "props": {"componentProps": {"fieldAlias": "distributeRule", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "dot", "labelField": []}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "hidden": false, "initialValue": null, "label": "分配规则", "name": "distributeRule", "rules": [{"message": "请输入分配规则", "required": true}], "type": "MULTISELECT"}, "type": "Widget"}], "displayName": "方案规则", "key": "TERP_MIGRATE$TCP72601-BTu5gds7gfD_1mqN7FAj8", "name": "FormGroupItem", "props": {"title": "方案规则"}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-开始日期", "name": "FormField", "props": {"componentProps": {"fieldAlias": "dateFrom", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "displayComponentType": "Date", "editComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "开始日期", "lookup": [{"action": null, "conditionGroup": [[{"key": "TERP_MIGRATE$TCP72601-1h3tv69lm125", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "EQ", "rightValue": {"constVal": "COUNT_SPOT", "constValue": "COUNT_SPOT", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tv6gn6126", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], [{"key": "TERP_MIGRATE$TCP72601-1h40k5o0q67", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}]], "fieldRules": {"disabled": false, "hidden": true, "readOnly": false, "required": false}, "key": "TERP_MIGRATE$TCP72601-BX1FAqlDB_AP-An1Yq585", "operator": null, "valueRules": null}, {"action": null, "conditionGroup": [[{"key": "TERP_MIGRATE$TCP72601-1h3tv7pji129", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "EQ", "rightValue": {"constVal": "COUNT_DYNAMICS", "constValue": "COUNT_DYNAMICS", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tv81ia130", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]], "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-9JIb4uz0wzBQ10JdxP4HX", "operator": null, "valueRules": null}], "name": "dateFrom", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-widget-结束日期", "name": "FormField", "props": {"componentProps": {"fieldAlias": "dateTo", "modelAlias": "INV$inv_count_plan_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "displayComponentType": "Date", "editComponentProps": {"format": "YYYY-MM-DD HH:mm:ss"}, "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "结束日期", "lookup": [{"action": null, "conditionGroup": [[{"key": "TERP_MIGRATE$TCP72601-1h3tv8mfh133", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "EQ", "rightValue": {"constVal": "COUNT_SPOT", "constValue": "COUNT_SPOT", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tv8us6134", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], [{"key": "TERP_MIGRATE$TCP72601-1h40jn7jg66", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}]], "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "TERP_MIGRATE$TCP72601-AkqPJZalef0xc02PMx7AB", "operator": null, "valueRules": null}, {"action": null, "conditionGroup": [[{"key": "TERP_MIGRATE$TCP72601-1h3tv9kk3137", "leftValue": {"constValue": null, "fieldType": "Enum", "options": [{"label": "抽盘", "value": "COUNT_SPOT"}, {"label": "动盘", "value": "COUNT_DYNAMICS"}], "scope": "form", "title": "盘点类型", "type": "VarValue", "val": "countType", "value": "TERP_MIGRATE$inv_count_plan_tr.countType", "valueType": "MODEL", "varValue": [{"valueKey": "countType", "valueName": "count_type"}]}, "operator": "EQ", "rightValue": {"constVal": "COUNT_DYNAMICS", "constValue": "COUNT_DYNAMICS", "fieldType": "Enum", "key": "TERP_MIGRATE$TCP72601-1h3tv9rtt138", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}]], "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-D71KkW0bixidS7jpbZFat", "operator": null, "valueRules": null}], "name": "dateTo", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invOrg-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-Nn_Z4SBC_CoajhVgXDxuR", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_lhBSkJYwLMzIaciBpJhK", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invOrg-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invOrg-3-layout-meta-table", "name": "TableForm", "props": {"editComponentProps": {"tableCondition": [[{"key": "TERP_MIGRATE$TCP72601-1h434angd1", "leftValue": {"constValue": null, "fieldType": "Number", "options": [], "title": "所属公司组织.ID", "type": "VarValue", "val": "comOrgId.id", "value": "TERP_MIGRATE$org_inv_org_cf.comOrgId.id", "valueType": "MODEL", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "scope": "form", "title": "公司.ID", "type": "VarValue", "valueType": "VAR", "varVal": "comOrgId.id", "varValue": "TERP_MIGRATE$inv_count_plan_tr.comOrgId.id"}, "type": "ConditionLeaf"}]]}, "fieldName": "invOrg", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status"], "fieldAlias": "invOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_inv_org_cf", "parentModelAlias": "INV$inv_count_plan_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更���人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织编码", "name": "code", "rules": [{"message": "请输入组织编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织名称", "name": "name", "rules": [{"message": "请输入组织名称", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "所属公司组织", "name": "comOrgId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择", "labelField": "languageName", "modelAlias": "GEN$gen_language_type_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "语言", "name": "languageId", "rules": [{"message": "请输入语言", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "area", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "区号", "name": "area", "rules": [{"message": "请输入区号", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "tele", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "电话/分机号", "name": "tele", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "fax", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "传真/分机号", "name": "fax", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mail", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮箱", "name": "mail", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "phone", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "手机", "name": "phone", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择", "labelField": "addrName", "modelAlias": "GEN$gen_addr_type_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "地址", "name": "addressId", "rules": [{"message": "请输入地址", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "详细地址", "name": "addressDetail", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择", "labelField": "timezoneDesc", "modelAlias": "GEN$gen_timezone_type_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "时区", "name": "timezoneId", "rules": [{"message": "请输入时区", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "postcode", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮编", "name": "postcode", "rules": [{"message": "请输入邮编", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "coordinate", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "经纬度坐标", "name": "coordinate", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "vendCode", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司间开票的供应商编号", "name": "vendCode", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgCode", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "custCode", "status", "invOrgId"], "fieldAlias": "purOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_pur_org_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "采购组织", "name": "purOrgId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currTypeId", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "custCode", "status", "invOrgId"], "fieldAlias": "slsOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_sls_org_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "销售组织", "name": "slsOrgId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["custCode", "custName", "custShortName", "<PERSON><PERSON><PERSON>", "comCode", "custRole", "isVend", "custStatus", "remark", "custTypeId", "custCateId", "personId", "industryId", "custLevelId", "vendId", "custPreId", "counId", "creditCheck", "invOrgId"], "fieldAlias": "slsCustInfoId", "label": "选择", "labelField": "custCode", "modelAlias": "SLS$sls_cust_info_md", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "客户", "name": "slsCustInfoId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["vendCode", "name", "vendNameShort", "<PERSON><PERSON><PERSON>", "comId", "personId", "vendSource", "status", "isCust", "custId", "remark", "vendType", "invOrgId"], "fieldAlias": "purVendInfoId", "label": "选择", "labelField": "name", "modelAlias": "PUR$pur_vend_info_md", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "供应商", "name": "purVendInfoId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name"], "fieldAlias": "orgSlsDcId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_sls_dc_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "销售渠道", "name": "orgSlsDcId", "rules": [], "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "GEN$org_inv_org_cf", "placeholder": "请输入"}, "label": "组织名称", "name": "name", "type": "TEXT"}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择", "labelField": "name", "modelAlias": "GEN$org_com_org_cf", "parentModelAlias": "GEN$org_inv_org_cf", "placeholder": "��选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "所属公司组织", "name": "comOrgId", "type": "OBJECT"}], "mainField": "name", "modelAlias": "GEN$org_inv_org_cf", "showFilterFields": true, "showScope": "filter"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "库存组织", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-qWmJL_VjI-MFHEgTk_2Sm", "operator": null, "valueRules": null}], "name": "invOrgId", "rules": [], "type": "OBJECT", "width": 168}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_org_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invOrg-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invOrg"}, "type": "Meta"}], "displayName": "库存组织", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-invOrg-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "库存组织"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invLoc-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-Fr0qIRWwEsc2Ogx9Cq0iz", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_T1BoRr8WWNQgl6nalNS0", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invLoc-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invLoc-3-layout-meta-table", "name": "TableForm", "props": {"fieldName": "invLoc", "fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "orgId"], "fieldAlias": "invLocId", "label": "选择库存地点", "labelField": "name", "modelAlias": "GEN$org_inv_loc_cf", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "库存地点", "name": "invLocId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "status", "remark", "baFreezeTime", "countType", "createType", "isAddRowAllowed", "isZeroInvCount", "isTransitInvCount", "isPostBlock", "checkScope", "dateFrom", "dateTo", "distributeRule", "approveNote", "countStrategyId", "countStrategySeq"], "fieldAlias": "countPlanId", "label": "选择盘点方案", "labelField": "name", "modelAlias": "INV$inv_count_plan_tr", "parentModelAlias": "INV$inv_count_plan_loc_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "盘点方案", "name": "countPlanId", "required": false, "type": "OBJECT", "width": 120}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_loc_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invLoc-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invLoc"}, "type": "Meta"}], "displayName": "库存地点", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-invLoc-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "库存地点"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-batch-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-HhJX1pdqO2FsHueAb3F99", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_rjD31uFnLOOhAFToHHyy", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-batch-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-batch-3-layout-meta-table", "name": "TableForm", "props": {"fieldName": "batch", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "referBatchId", "matId", "invTypeId", "status", "charaClassId"], "fieldAlias": "batchId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_batch_md", "parentModelAlias": "INV$inv_count_plan_batch_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "code"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_batch_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_batch_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_batch_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_batch_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_batch_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_batch_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "code", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "referBatchId", "matId", "invTypeId", "status", "charaClassId"], "fieldAlias": "referBatchId", "label": "选择参考批次号", "labelField": "code", "modelAlias": "INV$inv_batch_md", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "参考批次号", "name": "referBatchId", "rules": [], "type": "SELFRELATION", "width": 120}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas"], "fieldAlias": "matId", "label": "选择", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "物料", "name": "matId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name"], "fieldAlias": "invTypeId", "label": "选择", "labelField": "name", "modelAlias": "INV$inv_inv_type_cf", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "库存类型", "name": "invTypeId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "INV$inv_batch_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "rules": [], "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "charaClassType", "name", "status", "remark"], "fieldAlias": "charaClassId", "label": "选择", "labelField": "name", "modelAlias": "GEN$gen_chara_class_md", "parentModelAlias": "INV$inv_batch_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "类", "name": "charaClassId", "rules": [], "type": "OBJECT", "width": 120}], "mainField": "code", "modelAlias": "INV$inv_batch_md"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "批次", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-8Nwuq5tEyNjwjcgaxiJTC", "operator": null, "valueRules": null}], "name": "batchId", "rules": [], "type": "OBJECT", "width": 168}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_batch_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-batch-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "batch"}, "type": "Meta"}], "displayName": "批次", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-batch-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "批次"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invType-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-GaIkUUy6qO83nWRNVeY8-", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_N9i4ayYsMjCDluuM0wEV", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invType-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invType-3-layout-meta-table", "name": "TableForm", "props": {"fieldName": "invType", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name"], "fieldAlias": "invTypeId", "label": "选择", "labelField": "name", "modelAlias": "INV$inv_inv_type_cf", "parentModelAlias": "INV$inv_count_plan_inv_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_inv_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_inv_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "code", "rules": [{"message": "请输入编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_inv_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "name", "rules": [{"message": "请输入名称", "required": true}], "type": "TEXT", "width": 120}], "mainField": "name", "modelAlias": "INV$inv_inv_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "库存类型", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-utBOGDzRwzJUWY5oz4tb1", "operator": null, "valueRules": null}], "name": "invTypeId", "rules": [], "type": "OBJECT", "width": 168}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_inv_type_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-invType-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invType"}, "type": "Meta"}], "displayName": "库存类型", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-invType-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "库存类型"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-spcStkType-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-xsLw6iUiJpl3_WOF0O5cm", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_TbTRgn6WNUIfknyug4iU", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-spcStkType-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-spcStkType-3-layout-meta-table", "name": "TableForm", "props": {"fieldName": "spcStkType", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创���人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["code", "name", "action<PERSON>ey"], "fieldAlias": "spcStkTypeId", "label": "选择", "labelField": "code", "modelAlias": "INV$inv_spc_stk_type_cf", "parentModelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "code", "rules": [{"message": "请输入编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "name", "rules": [{"message": "请输入名称", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "action<PERSON>ey", "modelAlias": "INV$inv_spc_stk_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "action<PERSON>ey", "name": "action<PERSON>ey", "rules": [{"message": "请输入actionKey", "required": true}], "type": "TEXT", "width": 120}], "mainField": "name", "modelAlias": "INV$inv_spc_stk_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "特殊库存标识", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-1MaizIahDCmPAk6p6SF9N", "operator": null, "valueRules": null}], "name": "spcStkTypeId", "rules": [], "type": "OBJECT", "width": 168}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_spc_stk_type_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-spcStkType-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "spcStkType"}, "type": "Meta"}], "displayName": "特殊库存标识", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-spcStkType-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "特殊库存标识"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-mat-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-NsHs3tiULpD4Ndr5hOge_", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_Q1a2kY42dg4eQBM9KWxu", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-mat-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-mat-3-layout-meta-table", "name": "TableForm", "props": {"fieldName": "mat", "fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建��间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "version", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas"], "fieldAlias": "matId", "label": "选择", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "parentModelAlias": "INV$inv_count_plan_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "<PERSON><PERSON><PERSON>"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "INV$user", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "version", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "deleted", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "物料编码", "name": "matCode", "rules": [{"message": "请输入物料编码", "required": true}], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "rules": [], "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matOldId", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "旧物料 Material号", "name": "matOldId", "rules": [], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择", "labelField": "matTypeName", "modelAlias": "GEN$gen_mat_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "物料类型", "name": "genMatTypeCfId", "rules": [{"message": "请输入物料类型", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择", "labelField": "uomDesc", "modelAlias": "GEN$gen_uom_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "基本计量单位", "name": "baseUomId", "rules": [{"message": "请输入基本计量单位", "required": true}], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "weightUomId", "label": "选择", "labelField": "uomDesc", "modelAlias": "GEN$gen_uom_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "重量单位", "name": "weightUomId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "grossWeight", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "毛重", "name": "grossWeight", "rules": [], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "netWeight", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "净重", "name": "netWeight", "rules": [], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "volumUomId", "label": "选择", "labelField": "uomDesc", "modelAlias": "GEN$gen_uom_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "体积单位", "name": "volumUomId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "matVolum", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "体积", "name": "matVolum", "rules": [], "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "mat<PERSON>ate<PERSON><PERSON>nt", "testItemId"], "fieldAlias": "cateId", "label": "选择", "labelField": "matCateName", "modelAlias": "GEN$gen_mat_cate_md", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "类目ID", "name": "cateId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "effectiveDate", "modelAlias": "GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "有效起始期", "name": "effectiveDate", "rules": [], "type": "DATE", "width": 120}, {"componentProps": {"columns": ["brandCode", "brandName", "testItemId"], "fieldAlias": "brandId", "label": "选择", "labelField": "brandCode", "modelAlias": "GEN$gen_brand_md", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "品牌ID", "name": "brandId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["attriCode", "attriName", "attriValue", "status", "isRequired"], "fieldAlias": "attriId", "label": "选择", "labelField": "attriCode", "modelAlias": "GEN$gen_attri_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "属性ID", "name": "attriId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["matStatusCode", "matStatusName", "eventId", "msgType"], "fieldAlias": "statusId", "label": "选择", "labelField": "matStatusCode", "modelAlias": "GEN$gen_mat_status_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "跨工厂物料状态", "name": "statusId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "batchRelv", "modelAlias": "GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "批次管理", "name": "batchRelv", "rules": [], "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isBatchDetermination", "modelAlias": "GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "是��必须匹配批次确定", "name": "isBatchDetermination", "rules": [], "type": "BOOL", "width": 120}, {"componentProps": {"columns": ["code", "charaClassType", "name", "status", "remark"], "fieldAlias": "genCharaClassId", "label": "选择", "labelField": "name", "modelAlias": "GEN$gen_chara_class_md", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "物料分类类别", "name": "genCharaClassId", "rules": [], "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "shelfLife", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入", "precision": 0}, "hidden": false, "initialValue": null, "label": "物料保质期", "name": "shelfLife", "rules": [], "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "charas", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "特征数据", "name": "charas", "rules": [], "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料编码", "name": "matCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT"}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择", "labelField": "matTypeName", "modelAlias": "GEN$gen_mat_type_cf", "parentModelAlias": "GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "INV$SYS_FindDataByIdService", "searchServiceKey": "INV$SYS_PagingDataService"}}, "label": "物料类型", "name": "genMatTypeCfId", "type": "OBJECT"}], "mainField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN$gen_mat_md", "showFilterFields": true}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "物料", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TERP_MIGRATE$TCP72601-61aPPW52-v1LtGnqkkO32", "operator": null, "valueRules": null}], "name": "matId", "rules": [], "type": "OBJECT", "width": 168}], "hideDefaultDelete": true, "modelAlias": "INV$inv_count_plan_mat_link_tr", "subTableEnabled": false}}], "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-for-mat-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "mat"}, "type": "Meta"}], "displayName": "物料", "key": "TERP_MIGRATE$TCP72601-total-config-container-TERP_MIGRATE$inv_count_plan_tr-mat-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "物料"}, "type": "Layout"}], "displayName": "筛选范围", "key": "TERP_MIGRATE$TCP72601-Q3Fcr8UAbMmpM6PIlSx35", "name": "FormGroupItem", "props": {"title": "筛选范围"}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-total-config-TERP_MIGRATE$inv_count_plan_tr", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "TERP_MIGRATE$TCP72601-total-config-TERP_MIGRATE$inv_count_plan_tr", "modelAlias": "INV$inv_count_plan_tr", "params$": "{ id: route.query?.copyId || route.recordId }", "serviceKey$": "route.query?.copyId ? \"SYS_CopyDataConverterService\" : \"SYS_FindDataByIdService\"", "test$": "route.action === \"edit\" || !!route.query?.copyId", "type": "InvokeSystemService"}, "modelAlias": "INV$inv_count_plan_tr", "serviceKey": "INV$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [], "key": "TERP_MIGRATE$TCP72601-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$TCP72601-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"action": {"target": "TERP_MIGRATE$TCP72601-total-config-TERP_MIGRATE$inv_count_plan_tr"}, "name": "request", "type": "action"}], "service": "INV$INV_COUNT_PLAN_SAVE_EVENT_SERVICE", "serviceParams": [{"key": "TERP_MIGRATE$TCP72601-request", "leftValue": {"value": {"fieldAlias": "TERP_MIGRATE$TCP72601-request", "fieldKey": "TERP_MIGRATE$TCP72601-request", "fieldName": "TERP_MIGRATE$TCP72601-request", "fieldType": "Object", "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "list"}, {"action": "Message", "message": "保存成功"}], "executeLogic": "BindFlow"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "TERP_MIGRATE$TCP72601-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$TCP72601-editView", "name": "Page", "props": {}, "type": "Container"}, "type": "FORM", "containerConditionGroups": {}, "containerOrderBy": {}, "select": null, "conditionGroups": null, "orderBy": null}]