CREATE TABLE IF NOT EXISTS `trantor_task_run`
(
    `id`                 BIGINT       NOT NULL AUTO_INCREMENT,
    `team_id`            BIGINT       NOT NULL COMMENT 'team id',
    `team_code`          VARCHAR(64)  NOT NULL COMMENT 'team code, which the task belongs to',
    `task_code`          VARCHAR(64)  NOT NULL COMMENT 'task code, indicate what task to run',
    `dry_run`            BIT(1)       NOT NULL COMMENT 'is dry run',
    `comment`            VARCHAR(255) NOT NULL COMMENT 'comment',
    `run_display_name`   VARCHAR(255) NULL DEFAULT NULL COMMENT 'run display name',
    `exec_type`          VARCHAR(25)  NOT NULL COMMENT 'execution type, e.g. <PERSON>NUAL, CRON, SUB_TASK',
    `exec_user_id`       BIGINT       NOT NULL COMMENT 'user id, who execute this task',
    `exec_cron_id`       BIGINT       NULL DEFAULT NULL COMMENT 'cron id, NULL means not scheduled',
    `status`             VARCHAR(25)  NOT NULL COMMENT 'status',
    `options`            JSON         NOT NULL COMMENT 'options, json object, structure is defined by task (task_name)',
    `result`             JSON         NOT NULL COMMENT 'result, json object, output.data structure is defined by task (task_name)',
    `logs`               JSON         NOT NULL COMMENT 'logs, json array of string',
    `panic_message`      VARCHAR(500) NOT NULL COMMENT 'panic message',
    `parent_task_run_id` BIGINT       NULL DEFAULT NULL COMMENT 'parent task run id, NULL means this is root task',
    `sub_order_no`       INT          NULL DEFAULT NULL COMMENT 'order no of sub task, NULL means this is root task',
    `start_at`           DATETIME     NULL DEFAULT NULL COMMENT 'start time',
    `end_at`             DATETIME     NULL DEFAULT NULL COMMENT 'end time',
    `created_at`         DATETIME     NOT NULL COMMENT 'created time',
    `updated_at`         DATETIME     NOT NULL COMMENT 'updated time',
    PRIMARY KEY (`id`),
    KEY `idx_team_code_task_code` (`team_code`, `task_code`),
    KEY `idx_team_code_status` (`team_code`, `status`),
    KEY `idx_team_code_parent_task_run_id` (`team_code`, `parent_task_run_id`),
    KEY `idx_team_code_exec_user_id` (`team_code`, `exec_user_id`),
    KEY `idx_team_code_exec_cron_id` (`team_code`, `exec_cron_id`),
    KEY `idx_status_parent_task_run_id` (`status`, `parent_task_run_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_general_ci
    COMMENT = 'trantor task run (run detail of task)';
