package io.terminus.trantor2.scene.management.meta;

import io.terminus.trantor2.application.item.scene.SceneTemplateContent;
import io.terminus.trantor2.application.po.TrantorVersionedAppItemPO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * <AUTHOR>
 */
@Slf4j
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("SCENE_TEMPLATE")
public class VersionedSceneTemplateMeta extends TrantorVersionedAppItemPO<SceneTemplateContent> {
    private static final long serialVersionUID = -5948123741057592025L;
}
