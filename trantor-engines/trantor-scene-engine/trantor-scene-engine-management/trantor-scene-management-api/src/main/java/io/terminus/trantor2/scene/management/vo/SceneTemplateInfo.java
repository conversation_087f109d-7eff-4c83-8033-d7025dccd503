package io.terminus.trantor2.scene.management.vo;

import io.terminus.trantor2.scene.template.TemplateType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 场景模版历史数据，兼容用
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class SceneTemplateInfo {
    public static Map<Long, SceneTemplateInfo> history = new HashMap<>();

    static {
        history.put(1L, new SceneTemplateInfo("sys_common$master_data", "主数据场景模版", TemplateType.MASTER_DATA));
        history.put(2L, new SceneTemplateInfo("sys_common$master_data_hierarchy", "主数据层级模板", TemplateType.MASTER_DATA));
        history.put(3L, new SceneTemplateInfo("sys_common$business_document", "单据场景模版", TemplateType.BUSINESS_DOCUMENT));
        history.put(5L, new SceneTemplateInfo("sys_common$empty", "空白模板", TemplateType.EMPTY));
        history.put(6L, new SceneTemplateInfo("sys_common$rule_config", "规则配置模板", TemplateType.RULE_CONFIG));
        history.put(7L, new SceneTemplateInfo("sys_common$rule_simple_define", "规则简单定义模板", TemplateType.RULE_SIMPLE_DEFINE));
        history.put(8L, new SceneTemplateInfo("sys_common$rule_allocation", "规则分配模版", TemplateType.RULE_ALLOCATION));
        history.put(9L, new SceneTemplateInfo("sys_common$fine_account", "资产账户模板", TemplateType.FINE_ACCOUNT));
        history.put(10L, new SceneTemplateInfo("sys_common$rule_global", "规则全局模板", TemplateType.RULE_GLOBAL));
        history.put(11L, new SceneTemplateInfo("sys_common$report", "报表模板", TemplateType.REPORT));
        history.put(12L, new SceneTemplateInfo("sys_common$app_business_document", "单据场景模板", TemplateType.BUSINESS_DOCUMENT));
        history.put(13L, new SceneTemplateInfo("sys_common$workbench", "工作台模版", TemplateType.WORKBENCH));
        history.put(14L, new SceneTemplateInfo("sys_common$app_workbench", "工作台模板", TemplateType.WORKBENCH));
        history.put(15L, new SceneTemplateInfo("sys_common$user_list", "用户列表模板", TemplateType.IAM));
        history.put(16L, new SceneTemplateInfo("sys_common$role_list", "角色列表模板", TemplateType.IAM));
        history.put(17L, new SceneTemplateInfo("sys_common$app_empty", "空白模板", TemplateType.EMPTY));
        history.put(18L, new SceneTemplateInfo("sys_common$log_list", "日志列表模板", TemplateType.LOG));
    }

    private String key;
    private String name;
    private TemplateType type;
    private final String version = "1.0.0-SNAPSHOT";

    public static Optional<SceneTemplateInfo> findById(Long id) {
        return Optional.ofNullable(history.get(id));
    }
}
