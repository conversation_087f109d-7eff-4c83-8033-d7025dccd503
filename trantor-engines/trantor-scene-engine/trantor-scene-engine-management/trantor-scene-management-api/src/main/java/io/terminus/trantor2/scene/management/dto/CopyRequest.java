package io.terminus.trantor2.scene.management.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CopyRequest extends KeyRequest {
    @Schema(description = "场景名称")
    @NotNull(message = "name can not be null")
    private String name;

    @Schema(description = "复制的场景标识")
    @NotNull(message = "newKey can not be null")
    private String newKey;

    @Schema(description = "复制到指定资源目录的标识(可跨模块）", nullable = true)
    private String folderKey;
}
