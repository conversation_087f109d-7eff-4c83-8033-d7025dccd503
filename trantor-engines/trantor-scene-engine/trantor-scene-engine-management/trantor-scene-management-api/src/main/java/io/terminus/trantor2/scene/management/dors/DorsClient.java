package io.terminus.trantor2.scene.management.dors;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.OkHttpUtils;
import io.terminus.trantor2.properties.management.dors.DorsProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nonnull;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static io.terminus.trantor2.meta.util.OkHttpUtils.JSON_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DorsClient {
    private final DorsProperties dorsProperties;
    private final OkHttpClient httpClient;

    public DorsClient(DorsProperties dorsProperties) {
        this.dorsProperties = dorsProperties;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 获取 dors 报表国际化消息
     */
    public Set<String> getDorsI18nKeySet(@Nonnull String workspace, @Nonnull String pageId) {
        try {
            Request request = new Request.Builder()
                    .url(dorsProperties.getHost() + "/api/dors/v1/" + workspace + "/pages/" + pageId + "/messages")
                    .get()
                    .build();
            DorsI18n dorsI18n = OkHttpUtils.handleResponse(httpClient.newCall(request).execute(), DorsI18n.class);
            if (dorsI18n != null) {
                return dorsI18n.getMessages();
            }
            return Collections.emptySet();
        } catch (Exception e) {
            log.error("get dors messages error", e);
            throw new RuntimeException("get dors messages error", e);
        }
    }

    /**
     * 批量获取 dors 报表国际化消息
     *
     * @param page2workspace dors.pageId -> workspace
     */
    public Set<String> obtainDorsI18nKeySet(Map<String, String> page2workspace) {
        try {
            List<PageInfo> pageInfos = new ArrayList<>(page2workspace.size());
            page2workspace.forEach((pageId, workspace) -> pageInfos.add(new PageInfo(pageId, workspace)));
            DorsBatchRequest body = new DorsBatchRequest(pageInfos);
            Request request = new Request.Builder()
                    .url(dorsProperties.getHost() + "/api/v1/i18n/messages")
                    .post(RequestBody.create(JsonUtil.toJson(body), JSON_TYPE))
                    .build();
            DorsI18n dorsI18n = OkHttpUtils.handleResponse(httpClient.newCall(request).execute(), DorsI18n.class);
            if (dorsI18n != null) {
                return dorsI18n.getMessages();
            }
            return Collections.emptySet();
        } catch (Exception e) {
            log.error("get dors messages error", e);
            throw new RuntimeException("get dors messages error", e);
        }
    }

    @Data
    public static class DorsI18n {
        private Set<String> messages;
    }

    @Data
    @AllArgsConstructor
    private static class DorsBatchRequest {
        private List<PageInfo> pages;
    }

    @Data
    @AllArgsConstructor
    private static class PageInfo {
        private String pageId;
        private String workspace;
    }
}
