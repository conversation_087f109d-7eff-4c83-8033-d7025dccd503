package io.terminus.trantor2.scene.management.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "市场场景模版基本信息")
public class MarketSceneTemplate {
    @Schema(description = "模版名称")
    private String name;
    @Schema(description = "模版描述")
    private String description;
    @Schema(description = "模版标识")
    private String key;
    @Schema(description = "模版图标")
    private String icon;
    @Schema(description = "模版下载 ID")
    private String downloadId;
}
