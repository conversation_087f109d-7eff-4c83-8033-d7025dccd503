package io.terminus.trantor2.scene.management.meta;

import io.terminus.trantor2.application.item.scene.SceneTemplateBaseInfo;
import io.terminus.trantor2.application.item.scene.SceneTemplateContent;
import io.terminus.trantor2.application.po.TrantorAppItemPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * <AUTHOR>
 */
@Slf4j
@Generated
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("SCENE_TEMPLATE")
public class SceneTemplateMeta extends TrantorAppItemPO<SceneTemplateBaseInfo, SceneTemplateContent> {
    private static final long serialVersionUID = -4672870979929493175L;
}
