package io.terminus.trantor2.scene.management.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "场景批量请求")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SceneBatchRequest {
    @Schema(description = "资源树节点标识")
    private String parentKey;
    @Schema(description = "模型标识")
    private String modelKey;
    @Schema(description = "场景名称或场景标识模糊匹配值")
    private String fuzzyValue;
}
