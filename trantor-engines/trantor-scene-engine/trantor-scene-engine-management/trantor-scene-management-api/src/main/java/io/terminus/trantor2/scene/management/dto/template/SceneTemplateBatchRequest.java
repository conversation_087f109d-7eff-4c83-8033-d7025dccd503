package io.terminus.trantor2.scene.management.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.dto.AppItemListRequest;
import io.terminus.trantor2.scene.template.TemplateType;
import io.terminus.trantor2.scene.config.SceneType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场景批量请求")
@AllArgsConstructor
@NoArgsConstructor
public class SceneTemplateBatchRequest extends AppItemListRequest {
    @Schema(description = "加载草稿，默认 false 为加载发布版本")
    private boolean draft;
    @Schema(description = "模版类型")
    private Set<TemplateType> types;
    @Schema(description = "场景类型")
    private Set<SceneType> sceneTypes;
}
