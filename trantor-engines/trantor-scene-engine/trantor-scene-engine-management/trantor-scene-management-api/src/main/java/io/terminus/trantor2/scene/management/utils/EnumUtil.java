package io.terminus.trantor2.scene.management.utils;

import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EnumUtil {
    public static Set<String> enums2StringSet(Collection<? extends Enum<?>> enums) {
        return ObjectUtils.isEmpty(enums) ?
            null : enums.stream().map(Enum::name).collect(Collectors.toSet());
    }
}
