package io.terminus.trantor2.scene.management.vo;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.item.scene.SceneTemplateBaseInfo;
import io.terminus.trantor2.scene.template.TemplateType;
import io.terminus.trantor2.application.vo.TrantorApplicationItemVO;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.management.meta.VersionedSceneTemplateMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "场景模版基本信息", deprecated = true)
@NoArgsConstructor
@Deprecated
public class SceneTemplateInfo__ {
    @Schema(description = "模版ID，兼容用")
    private Long id;
    @Schema(description = "模版配置，兼容用")
    private JsonNode templateConfig;

    @Schema(description = "模版名称")
    private String name;
    @Schema(description = "模版标识")
    private String key;
    @Schema(description = "模版图标")
    private String icon;
    @Schema(description = "终端类型")
    private EndpointType endpointType;
    @Schema(description = "模版类型")
    private TemplateType type;
    @Schema(description = "模版描述")
    private String description;
    @Schema(description = "更新时间")
    private Date updatedAt;
    @Schema(description = "更新人")
    private Long updatedBy;
    @Schema(description = "更新人姓名")
    private String updatedByName;
    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "模版配置")
    private JsonNode viewTemplate;

    public static SceneTemplateInfo__ convertFrom(TrantorApplicationItemVO info) {
        SceneTemplateInfo__ template = new SceneTemplateInfo__();
        BeanUtils.copyProperties(info, template);
        SceneTemplateBaseInfo baseInfo = info.getBaseInfo();
        if (baseInfo == null) {
            return template;
        }
        template.setTemplateConfig(baseInfo.getTemplateConfig());
        template.setEndpointType(baseInfo.getEndpointType());
        template.setType(baseInfo.getType());
        return template;
    }

    public static SceneTemplateInfo__ convertFrom(VersionedSceneTemplateMeta versioned) {
        SceneTemplateInfo__ template = new SceneTemplateInfo__();
        BeanUtils.copyProperties(versioned, template);
        SceneTemplateBaseInfo sceneTemplateBaseInfo = (SceneTemplateBaseInfo) versioned.getApp().getBaseInfo();
        template.setTemplateConfig(sceneTemplateBaseInfo.getTemplateConfig());
        template.setEndpointType(sceneTemplateBaseInfo.getEndpointType());
        template.setType(sceneTemplateBaseInfo.getType());
        Optional.ofNullable(versioned.getContent()).ifPresent(
                c -> template.setViewTemplate(c.getViewTemplate())
        );
        return template;
    }
}
