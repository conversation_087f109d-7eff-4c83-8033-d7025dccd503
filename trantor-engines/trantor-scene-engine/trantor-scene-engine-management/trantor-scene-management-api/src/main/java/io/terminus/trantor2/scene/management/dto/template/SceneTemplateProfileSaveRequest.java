package io.terminus.trantor2.scene.management.dto.template;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.dto.AppItemSaveRequest;
import io.terminus.trantor2.application.item.scene.SceneTemplateBaseInfo;
import io.terminus.trantor2.scene.template.TemplateType;
import io.terminus.trantor2.module.meta.EndpointType;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "场景模版保存请求", description = "key 存在则更新，否则创建")
public class SceneTemplateProfileSaveRequest {
    @NotBlank(message = "key must not be null or empty")
    @Schema(description = "模版标识")
    private String key;
    @NotBlank(message = "key must not be null or empty")
    @Schema(description = "模版名称")
    private String name;
    @Schema(description = "模版图标")
    private String icon;
    @NotNull(message = "endpointType must not be null")
    @Schema(description = "终端类型")
    private EndpointType endpointType;
    @NotNull(message = "type must not be null")
    @Schema(description = "模版类型")
    private TemplateType type;
    @Schema(description = "模版描述")
    private String description;
    @Schema(description = "模版配置(json)")
    private ObjectNode templateConfig;

    public AppItemSaveRequest convert() {
        AppItemSaveRequest appItemSaveRequest = new AppItemSaveRequest();
        appItemSaveRequest.setName(name);
        appItemSaveRequest.setKey(key);
        appItemSaveRequest.setIcon(icon);
        appItemSaveRequest.setDescription(description);
        SceneTemplateBaseInfo baseInfo = new SceneTemplateBaseInfo();
        baseInfo.setEndpointType(endpointType);
        baseInfo.setType(type);
        baseInfo.setTemplateConfig(templateConfig);
        appItemSaveRequest.setBaseInfo(baseInfo);
        return appItemSaveRequest;
    }
}
