package io.terminus.trantor2.scene.management.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.meta.SceneMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SceneMetaVO extends BaseMeta {
    private static final long serialVersionUID = -638488315708609139L;
    @Schema(description = "终端类型")
    private EndpointType endpointType;

    @Schema(description = "是否存在扩展视图")
    private Boolean extended = false;

    @Schema(description = "是否可被扩展")
    private Boolean extensible;

    @Schema(description = "是否为可定制扩展资源，可任意修改")
    private Boolean customExt;

    @Schema(description = "更新人")
    private String updatedByName;
    private Long updatedBy;

    @Override
    public MetaType getMetaType() {
        return MetaType.Scene;
    }

    public static SceneMetaVO convertFrom(SceneMeta sceneMeta) {
        SceneMetaVO sceneMetaVO = new SceneMetaVO();
        BeanUtils.copyProperties(sceneMeta, sceneMetaVO);
        return sceneMetaVO;
    }
}
