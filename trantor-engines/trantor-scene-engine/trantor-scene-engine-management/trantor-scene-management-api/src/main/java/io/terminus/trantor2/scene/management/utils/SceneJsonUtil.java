package io.terminus.trantor2.scene.management.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.BiPredicate;
import java.util.function.UnaryOperator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SceneJsonUtil {
    private SceneJsonUtil() {
    }

    public static List<DataManagerViewMeta> updateViews(String newSceneKey, List<DataManagerView> views, UnaryOperator<String> updateFunction,
                                                        BiPredicate<JsonNode, String> skipFieldTraverse) {

        ObjectMapper objectMapper = JsonUtil.NON_INDENT.getObjectMapper();
        List<String> viewKeys = views.stream().map(DataManagerView::getKey).collect(Collectors.toList());
        return views.parallelStream().map(view -> {
            ObjectNode viewNode = objectMapper.valueToTree(view);
            updateNode(viewNode, "key", updateFunction, skipFieldTraverse, true);
            updateNode(viewNode, "permissionKey", s -> null, (jsonNode, s) -> false, false);
            viewKeys.stream()
                    .filter(viewKey -> !viewKey.equals(view.getKey()))
                    .forEach(viewKey -> replaceNodeValue(viewNode, viewKey, updateFunction.apply(viewKey)));
            viewNode.put("title", view.getTitle());

            DataManagerView updatedView = objectMapper.convertValue(viewNode, DataManagerView.class);
            return DataManagerViewMeta.fromView(updatedView, newSceneKey);
        }).collect(Collectors.toList());
    }

    /**
     * 更新 json 中的 key 为 keyToUpdate 的节点的值和其引用
     *
     * @param rootNode          根节点, {"key":"abc","abc": {}}
     * @param keyToUpdate       要更新的 key，比如 abc -> abc_123
     * @param updateFunction    要更新的函数，比如 oldValue -> oldValue + "_" + 123
     * @param skipFieldTraverse 是否跳过某些字段的遍历，一些写死的值不需要更新
     * @param updateKey         是否更新 key 为 keyToUpdate 节点的值的节点，比如 "abc": {} -> "abc_123": {}
     */
    public static void updateNode(JsonNode rootNode, String keyToUpdate,
                                  UnaryOperator<String> updateFunction,
                                  BiPredicate<JsonNode, String> skipFieldTraverse,
                                  boolean updateKey) {
        Set<JsonNode> nodesToUpdate = new HashSet<>();
        findNodesToUpdate(skipFieldTraverse, rootNode, nodesToUpdate, keyToUpdate);

        for (JsonNode node : nodesToUpdate) {
            String oldValue = node.asText();
            String newValue = updateFunction.apply(oldValue);

            replaceNodeValue(rootNode, oldValue, newValue);
            if (updateKey) {
                replaceNodeKey(rootNode, oldValue, newValue);
            }
        }
    }

    /**
     * 查找所有 key 为 name 的组件
     */
    public static List<ObjectNode> findViewContentWidgets(JsonNode content, String name) {
        List<ObjectNode> widgets = new ArrayList<>();
        findWidgetsRecursively(content, name, widgets);
        return widgets;
    }

    /**
     * 递归查找所有 key 为 name 的组件
     */
    private static void findWidgetsRecursively(JsonNode node, String name, List<ObjectNode> widgets) {
        if (node == null || node.isNull()) {
            return;
        }
        if (node.isObject()) {
            Iterator<Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Entry<String, JsonNode> field = fields.next();
                if (field.getKey().equals("props")) {
                    continue;
                }
                if (field.getKey().equals("name") && field.getValue().asText().equals(name)) {
                    widgets.add((ObjectNode) node);
                }
                findWidgetsRecursively(field.getValue(), name, widgets);
            }
        } else if (node.isArray()) {
            Iterator<JsonNode> elements = node.elements();
            while (elements.hasNext()) {
                findWidgetsRecursively(elements.next(), name, widgets);
            }
        }
    }


    /**
     * 查找所有 key 为 keyToUpdate 的节点
     */
    private static void findNodesToUpdate(BiPredicate<JsonNode, String> skipFieldTraverse, JsonNode node, Set<JsonNode> nodes, String keyToUpdate) {
        if (node == null || node.isNull()) {
            return;
        }
        if (node.isObject()) {
            Iterator<Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Entry<String, JsonNode> field = fields.next();
                if (field.getKey().equals(keyToUpdate)) {
                    nodes.add(field.getValue());
                } else if (skipFieldTraverse.test(node, field.getKey())) {
                    continue;
                }
                findNodesToUpdate(skipFieldTraverse, field.getValue(), nodes, keyToUpdate);
            }
        } else if (node.isArray()) {
            Iterator<JsonNode> elements = node.elements();
            while (elements.hasNext()) {
                findNodesToUpdate(skipFieldTraverse, elements.next(), nodes, keyToUpdate);
            }
        }
    }

    /**
     * 替换节点的值
     *
     * @param node
     * @param oldValue
     * @param newValue
     */
    private static void replaceNodeValue(JsonNode node, String oldValue, String newValue) {
        if (node.isObject()) {
            Iterator<Entry<String, JsonNode>> fields = node.fields();
            String regex = "(\"\\Q" + oldValue + "\\E\")|('\\Q" + oldValue + "\\E')";
            Pattern pattern = Pattern.compile(regex);

            while (fields.hasNext()) {
                Entry<String, JsonNode> field = fields.next();
                JsonNode value = field.getValue();
                if (value.isTextual()) {
                    String text = value.asText();
                    if (text.equals(oldValue)) {
                        if (newValue != null) {
                            ((ObjectNode) node).replace(field.getKey(), TextNode.valueOf(newValue));
                        } else {
                            fields.remove();
                        }
                    } else if (text.contains(oldValue)) {
                        Matcher matcher = pattern.matcher(text);
                        StringBuffer newScript = new StringBuffer();
                        boolean found = false;
                        while (matcher.find()) {
                            found = true;
                            String matchedValue = matcher.group();
                            if (matchedValue.equals("\"" + oldValue + "\"") || matchedValue.equals("'" + oldValue + "'")) {
                                matchedValue = matchedValue.replace(oldValue, newValue);
                            }
                            matcher.appendReplacement(newScript, Matcher.quoteReplacement(matchedValue));
                        }
                        matcher.appendTail(newScript);
                        if (found) {
                            ((ObjectNode) node).replace(field.getKey(), TextNode.valueOf(newScript.toString()));
                        }
                    }
                } else {
                    replaceNodeValue(field.getValue(), oldValue, newValue);
                }
            }
        } else if (node.isArray()) {
            if (replaceStrArray((ArrayNode) node, oldValue, newValue)) {
                return;
            }
            Iterator<JsonNode> elements = node.elements();
            while (elements.hasNext()) {
                replaceNodeValue(elements.next(), oldValue, newValue);
            }
        }
    }
    
    /**
     * 替换 String 数组中的值
     */
    private static boolean replaceStrArray(ArrayNode arrayNode, String oldValue, String newValue) {
        if (arrayNode.isEmpty()) {
            return false;
        }
        if (!arrayNode.get(0).isTextual()) {
            return false;
        }
        for (int i = 0; i < arrayNode.size(); i++) {
            if (arrayNode.get(i).asText().equals(oldValue)) {
                arrayNode.set(i, TextNode.valueOf(newValue));
                return true;
            }
        }
        return false;
    }

    /**
     * 替换节点的 key
     */
    private static void replaceNodeKey(JsonNode node, String keyToUpdate, String newValue) {
        if (node.isObject()) {
            Iterator<Entry<String, JsonNode>> iterator = node.fields();
            List<Entry<String, JsonNode>> entriesToUpdate = new ArrayList<>(); // 记录要修改的 entry
            while (iterator.hasNext()) {
                Entry<String, JsonNode> entry = iterator.next();
                if (entry.getKey().equals(keyToUpdate)) {
                    entriesToUpdate.add(entry);
                }
                replaceNodeKey(entry.getValue(), keyToUpdate, newValue);
            }
            // 在第二次迭代时进行替换
            entriesToUpdate.forEach(entry ->
                ((ObjectNode) node).replace(newValue, node.get(keyToUpdate)));
            ((ObjectNode) node).remove(keyToUpdate);
        } else if (node.isArray()) {
            Iterator<JsonNode> elements = node.elements();
            while (elements.hasNext()) {
                replaceNodeKey(elements.next(), keyToUpdate, newValue);
            }
        }
    }
}
