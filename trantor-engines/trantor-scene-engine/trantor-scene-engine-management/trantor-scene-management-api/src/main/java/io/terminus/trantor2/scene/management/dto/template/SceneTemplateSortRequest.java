package io.terminus.trantor2.scene.management.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "场景模版排序请求")
public class SceneTemplateSortRequest {
    @NotBlank(message = "key must not be null or empty")
    @Schema(description = "模版标识")
    private String key;

    @NotNull(message = "sort must not be null")
    @Min(value = 0, message = "sort must be greater than or equal to 0")
    @Schema(title = "排序", description = "目标序号, 从 0 开始")
    private Integer sort;
}
