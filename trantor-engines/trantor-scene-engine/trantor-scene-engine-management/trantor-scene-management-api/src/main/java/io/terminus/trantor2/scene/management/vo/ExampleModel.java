package io.terminus.trantor2.scene.management.vo;


import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.scene.config.datamanager.DataManagerModelConfig;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class ExampleModel {
    private String name;
    private DataManagerModelConfig config;
    private Set<DataStructNode> models;

}
