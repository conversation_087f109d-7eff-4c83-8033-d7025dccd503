package io.terminus.trantor2.scene.management.dto.datamanager;


import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.dto.SceneDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataManagerSceneDTO extends SceneDTO<DataManagerSceneConfig> {
    private static final long serialVersionUID = -2508176247109241441L;

    @Schema(description = "模版 ID")
    private Long templateId;

    @Schema(description = "模版")
    private TemplateConfig templateConfig;

    @Data
    public static class TemplateConfig {
        @Schema(description = "模版标识")
        private String templateKey;

        @Schema(description = "是否为本地,默认非本地即中心市场")
        private Boolean local = false;
    }
}


