package io.terminus.trantor2.scene.management.dto.datamanager;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UpdateSceneConfigRequest {
    @Schema(description = "场景标识")
    @NotNull(message = "key must not be null")
    private String key;
    @Schema(description = "场景配置")
    @NotNull(message = "sceneConfig must not be null")
    private DataManagerSceneConfig sceneConfig;
}
