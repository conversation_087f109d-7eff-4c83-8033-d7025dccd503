package io.terminus.trantor2.scene.management.service;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.service.SceneConsoleQueryService;
import io.terminus.trantor2.scene.service.SceneQueryService;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface SceneManagerQueryService extends SceneConsoleQueryService {
    /**
     * 根据场景标识查找场景，不包含子节点
     */
    Optional<SceneMeta> findByKeyWithOutChild(@NonNull String key, @Nullable String teamCode);

    /**
     * 根据条件分页查询应用下场景
     *
     * @param parentKey  资源树节点标识
     * @param fuzzyValue 模糊匹配值
     * @param pageable   分页
     * @return 查询结果
     */
    Paging<SceneMeta> pagingScenes(@Nullable String parentKey, @Nullable String fuzzyValue, PageReq pageable);


    /**
     * 根据条件查询应用下场景列表
     *
     * @param parentKey  父节点标识
     * @param fuzzyValue 模糊匹配值
     * @return 查询结果
     */
    List<SceneMeta> listScenes(@Nullable String parentKey, @Nullable String fuzzyValue);
}
