package io.terminus.trantor2.scene.management.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.dto.AppRequest;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SceneProfile extends AppRequest {
    private static final long serialVersionUID = -5843673761057988142L;

    @Schema(description = "场景唯一标识")
    private String key;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "名称", required = true)
    private String name;
}
