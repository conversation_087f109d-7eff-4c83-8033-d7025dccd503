package io.terminus.trantor2.scene.management.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.dto.IdDTO;
import io.terminus.trantor2.scene.meta.SceneMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "DataManagerSceneManagementVO")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DataManagerSceneVO extends IdDTO<Long> {
    private static final long serialVersionUID = -759823072948971801L;

    @Schema(description = "唯一标识")
    private String key;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所属应用ID")
    private Long appId;

    @Schema(description = "所属团队ID")
    private Long teamId;

    @Schema(description = "描述")
    private String description;

    private SceneType type;

    private DataManagerSceneConfig sceneConfig;

    @Schema(description = "终端类型")
    private EndpointType endpointType;

    @Schema(description = "锁定人")
    private User lockedBy;

    @Schema(description = "是否存在扩展视图")
    private Boolean extended = false;

    @Schema(description = "是否可被扩展")
    private Boolean extensible = false;
    
    @Schema(description = "是否为可定制扩展资源，可任意修改")
    private Boolean customExt;
}
