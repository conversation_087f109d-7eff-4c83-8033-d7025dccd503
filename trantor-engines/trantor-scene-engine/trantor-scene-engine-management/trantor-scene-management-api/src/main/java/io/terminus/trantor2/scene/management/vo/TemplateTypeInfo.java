package io.terminus.trantor2.scene.management.vo;

import io.terminus.trantor2.scene.template.TemplateType;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class TemplateTypeInfo {
    public static final List<TemplateTypeInfo> ALL = Arrays.stream(TemplateType.values())
        .map(TemplateTypeInfo::getInstanceOf)
        .collect(Collectors.toList());

    private String value;
    private String label;

    public TemplateTypeInfo(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static TemplateTypeInfo getInstanceOf(TemplateType type) {
        return new TemplateTypeInfo(type.name(), type.getLabel());
    }
}
