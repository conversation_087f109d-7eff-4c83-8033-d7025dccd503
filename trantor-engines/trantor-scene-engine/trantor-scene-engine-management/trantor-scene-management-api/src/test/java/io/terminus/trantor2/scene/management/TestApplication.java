package io.terminus.trantor2.scene.management;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2.scene.management"})
@EntityScan(basePackages = {"io.terminus.trantor2.scene.management"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.scene.management"})
public class TestApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
