package io.terminus.trantor2.scene.management.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class SceneJsonUtilTest {

    @Test
    void updateNode() throws JsonProcessingException {
        String json = ResourceHelper.getResourceAsString(DataManagerView.class, "json/view.json");

        JsonNode jsonNode = ObjectJsonUtil.MAPPER.readTree(json);
        String key = "TERP_MIGRATE$FIN_CM_ATF";
        String newKey = "new$new";

        Assertions.assertDoesNotThrow(() -> SceneJsonUtil.updateNode(
            jsonNode,
            "key",
            it -> it.startsWith(key) ? newKey + it.substring(key.length()) : newKey + "-" + it,
            (node, k) -> k.contains("button"),
            true));
    }
}
