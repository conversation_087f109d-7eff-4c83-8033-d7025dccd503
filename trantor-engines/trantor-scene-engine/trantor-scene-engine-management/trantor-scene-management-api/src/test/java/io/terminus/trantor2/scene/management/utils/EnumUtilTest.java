package io.terminus.trantor2.scene.management.utils;

import io.terminus.trantor2.scene.config.SceneType;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class EnumUtilTest {

    @Test
    void enums2StringSet() {
        Set<String> res = EnumUtil.enums2StringSet(null);
        assertNull(res);
        res = EnumUtil.enums2StringSet(Arrays.asList(SceneType.DATA, SceneType.BI));
        assertNotNull(res);
        assertEquals(2, res.size());
        assertTrue(res.containsAll(Arrays.asList(SceneType.DATA.name(), SceneType.BI.name())));
    }
}
