INSERT INTO `trantor_application_item`(id,
                                       application_type,
                                       st_draft,
                                       `local`,
                                       st_sort,
                                        `key`,
                                        name,
                                       st_type,
                                       st_scene_type,
                                       st_view_template,
                                       st_template_config,
                                       st_endpoint_type,
                                        description,
                                        version)
VALUES (1, 'SCENE_TEMPLATE', false, true, null, 'sys_common$master_data', '主数据场景模版', 'MASTER_DATA', 'DATA',
        null,
        '{"modelRequired":true}',
        'PC',
        '左列表右详情',0),
       (2, 'SCENE_TEMPLATE', false, true, null, 'sys_common$master_data_hierarchy', '主数据层级模板', 'MASTER_DATA',
        'DATA', null,
        '{"modelRequired":true}',
        'PC', '左树右详情',0),
       (3, 'SCENE_TEMPLATE', false, true, null, 'sys_common$business_document', '单据场景模版', 'BUSINESS_DOCUMENT',
        'DATA', null,
        '{"modelRequired":true}',
        'PC', null,0),
       (4, 'SCENE_TEMPLATE', false, true, null, 'sys_common$empty', '空白模版', 'EMPTY', 'DATA', null,
        '{"modelRequired":false}', 'PC',
        null, 0),
       (5, 'SCENE_TEMPLATE', false, true, null, 'sys_common$log', '日志模版', 'LOG', 'DATA', null,
        '{"modelRequired":false}',
        'PC',
        null,0),

       -- 以下为草稿 --
       (6, 'SCENE_TEMPLATE', true, true, 0, 'sys_common$master_data', '主数据场景模版草稿', 'MASTER_DATA', 'DATA',
        null,
        '{"modelRequired":true}',
        'PC',
        '左列表右详情',0),
       (7, 'SCENE_TEMPLATE', true, true, 1, 'sys_common$master_data_hierarchy', '主数据层级模板草稿', 'MASTER_DATA',
        'DATA', null,
        '{"modelRequired":true}',
        'PC', '左树右详情',0),
       (8, 'SCENE_TEMPLATE', true, true, 2, 'sys_common$business_document', '单据场景模版草稿', 'BUSINESS_DOCUMENT',
        'DATA', null,
        '{"modelRequired":true}',
        'PC', null,0),
       (9, 'SCENE_TEMPLATE', true, true, 3, 'sys_common$empty', '空白模版草稿', 'EMPTY', 'DATA', null,
        '{"modelRequired":false}', 'PC',
        null, 0),
       (10, 'SCENE_TEMPLATE', true, true, 4, 'sys_common$log', '日志模版草稿', 'LOG', 'DATA', null,
        '{"modelRequired":true}',
        'PC',
        null, 0),
       -- 以下为市场 --
        (11, 'SCENE_TEMPLATE', false, false, 0, 'master_data', '主数据场景模版草稿', 'MASTER_DATA', 'DATA', null,
        '{"modelRequired":true}',
        'PC',
        '左列表右详情',0);