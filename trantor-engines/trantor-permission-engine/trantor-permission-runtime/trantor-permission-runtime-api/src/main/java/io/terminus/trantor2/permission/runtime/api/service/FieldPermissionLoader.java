package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.iam.api.response.permission.FieldRule;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;

/**
 * 字段权限加载逻辑
 *
 * <AUTHOR>
 * 2024/7/11 09:38
 **/
public interface FieldPermissionLoader {

    /**
     * 查询用户字段权限规则
     *
     * @param userId        用户id
     * @param iamAppId      IAM应用id
     * @param permissionKey 权限标识
     * @return 字段权限规则列表
     */
    Collection<FieldRule> getUserFieldPermissionRules(@NotNull final Long userId,
                                                      @NotNull final Long iamAppId,
                                                      @NotNull final String permissionKey);
}
