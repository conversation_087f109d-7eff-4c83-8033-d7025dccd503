package io.terminus.trantor2.permission.runtime.api.cache;

import io.terminus.trantor2.meta.querytree.QueryContext;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.permission.api.common.cache.IResourcePermissionTreeCacheGetter;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * 2023/7/27 8:59 下午
 **/
public interface ResourcePermissionRuntimeCacheGetter extends IResourcePermissionTreeCacheGetter {

    /**
     * 查询指定门户下的资源树（菜单、视图、按钮）
     *
     * @param moduleMeta        模块meta
     * @param needResourceTypes 需要加载的资源节点类型
     * @param propsRewriter     {@link PermissionResourceDTO#props} 处理函数
     * @return 权限资源对象树
     */
    List<PermissionResourceDTO> findResourceTreeByPortal(ModuleMeta moduleMeta,
                                                         List<ACLResourceType> needResourceTypes,
                                                         PermissionResourceDTO.PermissionPropsRewriter propsRewriter);

    /**
     * 查询指定门户下的资源树（菜单、视图、按钮）
     *
     * @param q                 查询上下文 (queryTree)
     * @param moduleMeta        模块meta
     * @param needResourceTypes 需要加载的资源节点类型
     * @param propsRewriter     {@link PermissionResourceDTO#props} 处理函数
     * @return 权限资源对象树
     */
    List<PermissionResourceDTO> findResourceTreeByPortal(QueryContext q, ModuleMeta moduleMeta,
                                                         List<ACLResourceType> needResourceTypes,
                                                         PermissionResourceDTO.PermissionPropsRewriter propsRewriter);
}
