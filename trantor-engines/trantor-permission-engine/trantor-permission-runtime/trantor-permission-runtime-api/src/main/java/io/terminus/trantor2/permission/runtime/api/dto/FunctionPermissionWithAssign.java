package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/5/30 20:58
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "功能权限项授权信息")
public class FunctionPermissionWithAssign extends PermissionWithAssign {

    @Schema(description = "关联资源")
    private List<ResourceNodeLite> resources;
}
