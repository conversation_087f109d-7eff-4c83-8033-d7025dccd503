package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.response.role.Role;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <EMAIL>
 * 2024/8/9 15:38
 */


@Data
@EqualsAndHashCode(callSuper = true)
public class RoleDTO extends Role {

    @Schema(
            description = "所属应用端ID"
    )
    private Long endpointId;
}
