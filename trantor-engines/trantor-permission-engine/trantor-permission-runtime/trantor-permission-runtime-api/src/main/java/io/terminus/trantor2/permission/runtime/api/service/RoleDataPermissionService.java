package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.trantor2.permission.api.common.dto.DataControlDimensionWithEffect;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionWithAssignFindRequest;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Map;

/**
 * 角色数据权限
 *
 * <AUTHOR>
 * 2024/5/28 15:56
 **/
public interface RoleDataPermissionService {

    /**
     * 查询门户角色数据控权维度列表
     *
     * @param request 查询授权权请求参数
     * @return 控权维度授权列表
     */
    Collection<DataControlDimensionWithEffect> findDataControlDimension(PermissionWithAssignFindRequest request);

    /**
     * 检查是否允许访问目标服务
     *
     * @param serviceKey 服务key
     */
    void checkAllowAccess(String serviceKey, String dataPermissionKey);

    /**
     * 查询数据权限规则引用服务摘要信息
     *
     * @param dataConditionKeys 数据权限规则标识
     * @param viewKeys          视图标识，在给定视图内检索引用服务信息
     * @return Map 数据权限规则key -> 引用服务信息
     */
    Map<String, Collection<PermissionResourceDTO>> findRefServiceSummary(Collection<String> dataConditionKeys, Collection<String> viewKeys);

    /**
     * 查询数据权限树，仅返回树结构，不包含权限项
     * @param request 查询授权权请求参数
     * @return 数据权限树
     */
    Collection<PermissionResourceDTO> findDataPermissionOnlyTree(@NotNull PermissionWithAssignFindRequest request);

    /**
     * 查询数据权限树，仅返回权限项，不包含树结构
     * @param request 查询授权权请求参数
     * @return 数据权限
     */
    Collection<PermissionResourceDTO> findDataPermissionOnlyDetail(@NotNull PermissionWithAssignFindRequest request);

}
