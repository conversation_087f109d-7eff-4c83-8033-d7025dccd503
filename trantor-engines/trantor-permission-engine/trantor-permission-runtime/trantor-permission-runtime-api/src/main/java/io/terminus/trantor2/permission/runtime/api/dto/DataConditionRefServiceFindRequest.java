package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.model.AbstractRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.Collection;

/**
 * 数据权限规则引用服务查询请求参数
 *
 * <AUTHOR>
 * 2024/10/28 14:27
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
@Schema(description = "数据权限规则引用服务查询请求参数")
public class DataConditionRefServiceFindRequest extends AbstractRequest {

    @Schema(description = "数据权限规则标识集合", required = true)
    @NotEmpty
    private Collection<String> dataConditionKeys;

    @Schema(description = "视图标识，在给定视图内检索引用服务信息", required = true)
    @NotEmpty
    private Collection<String> viewKeys;
}
