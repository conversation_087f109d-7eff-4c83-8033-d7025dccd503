package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 门户访问控制接口
 *
 * <AUTHOR>
 * 2023/8/15 7:19 下午
 **/
@Data
@Builder
@Schema(description = "角色授权服务权限对象")
public class RoleAuthorizationServicePermission implements Serializable {

    @Schema(description = "接口ID")
    private String resourceId;

    @Schema(description = "关联权限点ID")
    private Long permissionId;

    @Schema(description = "权限点资源类型")
    private String resourceType;

    @Schema(description = "授权接口标识")
    private String resourceKey;

    @Schema(description = "授权接口名称")
    private String resourceName;

    @Schema(description = "功能描述")
    private String resourceDesc;

    @Schema(description = "是否开启用户身份认证")
    private Boolean userAuthenticationEnabled;

    @Schema(description = "是否开启用户资源授权校验")
    private Boolean userAuthorizationEvaluateEnabled;

    @Schema(description = "是否是OpenAPI")
    private Boolean openApi;

    @Schema(description = "是否授权")
    private Boolean granted;
}
