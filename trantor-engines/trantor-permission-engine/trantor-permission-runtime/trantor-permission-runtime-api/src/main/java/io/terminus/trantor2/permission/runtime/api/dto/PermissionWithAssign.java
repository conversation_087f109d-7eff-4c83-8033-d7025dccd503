package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/5/31 11:50
 **/
@Setter
@Getter
@EqualsAndHashCode
@Schema(description = "权限授权信息")
public class PermissionWithAssign implements Serializable {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "类型")
    private PermissionType type;

    @Schema(description = "标识")
    private String key;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "授权效果")
    private AuthorizationEffect effect;
}
