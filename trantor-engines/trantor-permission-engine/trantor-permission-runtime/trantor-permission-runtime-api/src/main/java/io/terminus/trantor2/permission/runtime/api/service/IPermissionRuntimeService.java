package io.terminus.trantor2.permission.runtime.api.service;

/**
 * <AUTHOR>
 * 2023/7/27 6:10 下午
 **/
public interface IPermissionRuntimeService {

    /**
     * 判断是否需要执行功能权限处理，返回 true 的情况包含：
     * 1. 用户是管理员
     * 2. 权限开关处于关闭状态
     *
     * @return true: 需要，false: 不需要
     */
    boolean needExecFunctionPermission();

    /**
     * 判断是否需要执行数据权限处理，返回 true 的情况包含：
     * 1. 用户是管理员
     * 2. 权限开关处于关闭状态
     *
     * @return true: 需要，false: 不需要
     */
    boolean needExecDataPermission();

    /**
     * 判断是否需要执行字段权限处理，返回 true 的情况包含：
     * 1. 用户是管理员
     * 2. 权限开关处于关闭状态
     *
     * @return true: 需要，false: 不需要
     */
    boolean needExecFieldPermission();
}
