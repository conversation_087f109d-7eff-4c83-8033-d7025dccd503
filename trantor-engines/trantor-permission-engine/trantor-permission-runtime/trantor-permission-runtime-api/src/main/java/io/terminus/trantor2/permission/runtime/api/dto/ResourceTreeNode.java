package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Schema(description = "资源树节点")
public class ResourceTreeNode implements Serializable {

    @Schema(title = "资源类型")
    private String resourceType;

    @Schema(title = "资源id")
    private Long resourceId;

    @Schema(title = "资源标识")
    private String resourceKey;

    @Schema(title = "上级资源标识")
    private String resourceName;

    @Schema(title = "资源属性")
    private Map<String, Object> props;

    @Schema(title = "子级资源列表")
    private List<ResourceTreeNode> children;
}
