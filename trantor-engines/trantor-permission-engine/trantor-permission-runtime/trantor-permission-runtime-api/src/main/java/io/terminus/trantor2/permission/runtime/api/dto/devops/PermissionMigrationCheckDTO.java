package io.terminus.trantor2.permission.runtime.api.dto.devops;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceProps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 2024/8/20 15:13
 **/
@Getter
@Setter
@EqualsAndHashCode
public class PermissionMigrationCheckDTO implements Serializable {

    @JsonIgnore
    @Schema(title = "资源类型")
    private ACLResourceType resourceType;

    @JsonIgnore
    @Schema(title = "资源id")
    private Long resourceId;

    @JsonIgnore
    @Schema(title = "资源标识")
    private String resourceKey;

    @JsonIgnore
    @Schema(title = "上级资源标识")
    private String resourceName;

    @JsonIgnore
    @Schema(title = "资源属性")
    private PermissionResourceProps props;

    // ###################################################################################################

    @Schema(description = "当前节点信息")
    private String resource;

    @JsonIgnore
    @Schema(description = "升级前授权状态")
    private AuthorizationEffect beforeGrant;

    @JsonIgnore
    @Schema(description = "升级后授权状态")
    private AuthorizationEffect afterGrant;

    @Schema(description = "差异描述")
    private String diff;

    @Schema(description = "不存在资源")
    private Collection<String> notFoundMetaKeys;

    @Schema(description = "引用资源列表")
    private List<PermissionMigrationCheckDTO> services;

    @Schema(description = "子级资源列表")
    private List<PermissionMigrationCheckDTO> children;

    public String generateResourceInfo() {
        return String.format("%s %s %s", getResourceType(), getResourceKey(), getResourceName());
    }

    @JsonIgnore
    public Collection<String> getRefMetaKeys() {
        if (Objects.isNull(resourceKey)) {
            return Collections.emptyList();
        }
        if (ACLResourceType.SystemService.equals(resourceType)) {
            String[] split = resourceKey.split("/");
            return Arrays.asList(split[2], split[4]);
        } else if (ACLResourceType.Service.equals(resourceType)) {
            return Collections.singleton(resourceKey);
        } else if (ACLResourceType.Api.equals(resourceType)) {
            // todo WebAPI 没有元数据
        }
        return Collections.emptyList();
    }
}
