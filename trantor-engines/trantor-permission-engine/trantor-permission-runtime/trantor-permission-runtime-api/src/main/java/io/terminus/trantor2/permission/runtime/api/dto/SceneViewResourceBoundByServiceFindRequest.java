package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.api.common.enums.BackendResourceType;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "根据服务查找场景视图资源查询请求参数")
public class SceneViewResourceBoundByServiceFindRequest {

    @Deprecated
    @Schema(description = "门户ID", nullable = true, hidden = true, deprecated = true)
    private Long portalId;

    @Schema(description = "门户code", required = true)
    @NotNull(message = "portalCode can not be null")
    private String portalCode;

    @Schema(description = "服务类型", required = true)
    @NotNull(message = "serviceType can not be null")
    private BackendResourceType serviceType;

    @Schema(description = "服务标识", required = true)
    @NotNull(message = "serviceKey can not be null")
    private String serviceKey;
}
