package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/6/10 18:15
 **/
@Data
@Schema(description = "查询角色权限请求参数")
public class RolePermissionRequest implements Serializable {

    @Schema(description = "门户标识")
    private String portalCode;

    @Schema(description = "角色id")
    private String roleId;
}
