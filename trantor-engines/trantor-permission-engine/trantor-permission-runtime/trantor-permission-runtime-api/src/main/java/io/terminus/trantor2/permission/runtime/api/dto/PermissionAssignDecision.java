package io.terminus.trantor2.permission.runtime.api.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/6/4 16:04
 **/
@Getter
@Setter
@EqualsAndHashCode
@Schema(description = "权限授权策略")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "permissionType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = FunctionPermissionAssignDecision.class, name = "FUNCTION_PERMISSION"),
    @JsonSubTypes.Type(value = DataPermissionAssignDecision.class, name = "DATA_PERMISSION"),
    @JsonSubTypes.Type(value = FieldPermissionAssignDecision.class, name = "FIELD_PERMISSION")
})
public abstract class PermissionAssignDecision implements Serializable {

    @Schema(description = "权限标识", required = true)
    @NotNull(message = "permissionKey can not be null")
    private String permissionKey;

    @Schema(description = "权限授权效果", required = true)
    @NotNull(message = "effect can not be null")
    private AuthorizationEffect effect = AuthorizationEffect.NONE;

    @Schema(description = "JSON多态类型信息，需要与外层结构 permissionType 值保持一致", required = true)
    public abstract PermissionType getPermissionType();
}
