package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.api.common.dto.ResourceAccessControlPageRequest;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "角色资源访问控制分页查询参数")
public class RoleResourceAccessControlPageRequest extends ResourceAccessControlPageRequest {

    @Schema(description = "角色ID", required = true)
    @NotNull(message = "roleId can not be null")
    private Long roleId;
}
