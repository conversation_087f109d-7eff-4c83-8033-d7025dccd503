package io.terminus.trantor2.permission.runtime.api.service.v2;

import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.trantor2.scene.runtime.vo.ViewPermissionVO;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024/6/18 16:13
 **/
public interface UserPermissionService {

    /**
     * 查询用户在指定门户下的功能权限授权信息
     *
     * @param userId         用户id
     * @param portalCode     门户编号
     * @param permissionKeys 目标权限项标识
     * @return Collection 权限授权列表
     */
    Collection<PermissionAssign> findUserPermissionAssigns(Long userId,
                                                           String portalCode,
                                                           Collection<String> permissionKeys);

    /**
     * 查询用户在指定门户下的功能权限授权效果
     *
     * @param userId         用户id
     * @param portalCode     门户编号
     * @param permissionKeys 目标权限项标识
     * @return Map 权限项标识 -> 授权效果
     */
    Map<String, AuthorizationEffect> findUserFunctionPermissionEffect(Long userId,
                                                                      String portalCode,
                                                                      Collection<String> permissionKeys);

    /**
     * 填充视图权限信息
     *
     * @param viewPermissionVO 视图权限对象
     */
    void fillPermissionProps(ViewPermissionVO viewPermissionVO);

    /**
     * 查询用户在指定门户下的字段权限规则
     *
     * @param userId         用户id
     * @param portalCode     门户编号
     * @param permissionKeys 目标权限项标识
     * @return Map 权限项标识 -> 字段权限规则列表
     */
    Map<String, Collection<FieldRule>> findUserFieldPermissionRules(Long userId,
                                                                    String portalCode,
                                                                    Collection<String> permissionKeys);
}
