package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.props.DataControlDimensionPermissionProps;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/5/31 11:50
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "带数据范围的数据控权维度属性")
public class DataControlDimensionWithValueRangePermissionProps extends DataControlDimensionPermissionProps {

    @Schema(description = "数据范围")
    private List<VarValue> valueRange;

}
