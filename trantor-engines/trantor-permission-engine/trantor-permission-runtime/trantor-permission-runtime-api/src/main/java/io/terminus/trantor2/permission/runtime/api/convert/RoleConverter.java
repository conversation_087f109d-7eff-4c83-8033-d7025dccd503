package io.terminus.trantor2.permission.runtime.api.convert;

import io.terminus.iam.api.request.permission.v2.PermissionCreateParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionGrantRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/5/27 20:27
 **/
@Mapper
public interface RoleConverter {

    RoleConverter INSTANCE = Mappers.getMapper(RoleConverter.class);

    @Mapping(target = "endpointId", source = "sourceApplicationId")
    RoleDTO convert(Role role);
    @Mapping(target = "endpointId", source = "sourceApplicationId")
    List<RoleDTO> convert(List<Role> role);
}
