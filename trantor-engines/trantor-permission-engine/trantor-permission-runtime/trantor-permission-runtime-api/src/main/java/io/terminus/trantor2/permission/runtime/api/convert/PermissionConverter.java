package io.terminus.trantor2.permission.runtime.api.convert;

import io.terminus.iam.api.request.permission.v2.PermissionCreateParams;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionGrantRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * 2024/5/27 20:27
 **/
@Mapper
public interface PermissionConverter {

    PermissionConverter INSTANCE = Mappers.getMapper(PermissionConverter.class);

    PermissionCreateParams convert(PermissionGrantRequest request);
}
