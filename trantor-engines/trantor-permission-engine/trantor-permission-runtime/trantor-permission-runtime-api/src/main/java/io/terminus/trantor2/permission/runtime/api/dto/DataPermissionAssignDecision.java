package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.dto.permission.DataPermissionAssignProps;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.dsl.properties.Value;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/6/4 16:08
 **/
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据权限授权策略")
public class DataPermissionAssignDecision extends PermissionAssignDecision {

    @Schema(description = "属性类型")
    private DataPermissionAssignProps.PropsType propsType;

    @Schema(description = "数据控权维度的数据范围，当 propsType = VALUE_RANGE 时必填")
    private List<Value> valueRange;

    @Schema(description = "数据规则，当 propsType = CONDITION_RULE 时必填")
    private ConditionGroup condition;

    @Schema(description = "已忽略的条件对象标识")
    private Collection<String> ignoredConditionKeys;

    @Override
    public PermissionType getPermissionType() {
        return PermissionType.DATA_PERMISSION;
    }

    @Setter
    @Getter
    @EqualsAndHashCode
    public static class ValueRange implements Serializable {

        @Schema(description = "实际值")
        private Object value;
        @Schema(description = "展示标签，该参数只会冗余展示使用，存在与真实业务数据不一致情况")
        private String label;
    }

}
