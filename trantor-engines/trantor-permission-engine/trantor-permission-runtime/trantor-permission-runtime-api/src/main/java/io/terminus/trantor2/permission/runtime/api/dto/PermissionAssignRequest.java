package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;

/**
 * <AUTHOR>
 * 2024/5/28 19:48
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限授权请求参数")
public class PermissionAssignRequest extends AbstractRequest {

    @Schema(description = "门户code", required = true)
    @NotNull(message = "portalCode can not be null")
    private String portalCode;

    @Schema(description = "角色ID", required = true)
    @NotNull(message = "targetId can not be null")
    private String roleId;

    @Schema(description = "权限类型", required = true)
    @NotNull(message = "permissionType can not be null")
    private PermissionType permissionType;

    /**
     * @deprecated 将 decisions 按照需要删除和新增的规则拆分到 {@link #deleteDecisions} 和 {@link #createDecisions}
     */
    @Schema(description = "授权策略", required = true, deprecated = true)
    @NotEmpty(message = "decisions can not be empty")
    @Valid
    @Deprecated
    private Collection<PermissionAssignDecision> decisions;

    @Schema(description = "要删除的授权策略")
    @Valid
    private Collection<PermissionAssignDecision> deleteDecisions;

    @Schema(description = "要新增的授权策略")
    @Valid
    private Collection<PermissionAssignDecision> createDecisions;
}
