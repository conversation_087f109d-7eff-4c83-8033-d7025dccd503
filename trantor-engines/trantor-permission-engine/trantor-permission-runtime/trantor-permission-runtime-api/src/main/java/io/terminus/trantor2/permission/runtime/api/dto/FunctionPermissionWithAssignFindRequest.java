package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 2024/5/30 20:35
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询角色功能权限授权请求参数")
public class FunctionPermissionWithAssignFindRequest extends PermissionWithAssignFindRequest {

    @Schema(description = "模糊查询值")
    private String fuzzyValue;

    @Schema(description = "分页页码", defaultValue = "1")
    private Integer pageNumber = 1;

    @Schema(description = "分页每页大小", defaultValue = "100")
    private Integer pageSize = 100;
}
