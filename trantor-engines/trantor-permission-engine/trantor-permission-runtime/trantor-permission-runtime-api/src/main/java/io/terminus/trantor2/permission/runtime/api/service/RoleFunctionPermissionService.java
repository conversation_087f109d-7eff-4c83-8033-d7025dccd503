package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.permission.api.common.dto.PermissionAuthorizationVO;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssign;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleApiAccessControl;
import io.terminus.trantor2.permission.runtime.api.dto.RolePermissionRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleResourceAccessControlPageRequest;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 角色功能权限
 *
 * <AUTHOR>
 * 2024/5/28 15:56
 **/
public interface RoleFunctionPermissionService {

    /**
     * 查询门户和角色的功能授权树
     *
     * @param request 查询授权权请求参数
     * @return 功能授权树
     */
    List<PermissionResourceDTO> findFuncPermissionTree(PermissionWithAssignFindRequest request);

    /**
     * 查询资源引用服务摘要信息
     *
     * @param resourceKeys 资源标识集合
     * @return Map 资源key -> 资源引用服务信息（包含服务被引用信息）
     */
    Map<String, List<PermissionResourceDTO>> findRefServiceSummary(List<String> resourceKeys);

    /**
     * 分页查询指定门户功能权限项
     *
     * @param request 查询授权权请求参数
     * @return 功能权限项分页列表
     */
    Paging<FunctionPermissionWithAssign> pagingFunctionPermission(FunctionPermissionWithAssignFindRequest request);

    /**
     * 分页查询指定门户接口访问控制列表
     *
     * @param request 查询授权权请求参数
     * @return 接口访问控制列表
     */
    Paging<RoleApiAccessControl> pagingApiAccessControl(RoleResourceAccessControlPageRequest request);

    /**
     * 查询智能体权限授权列表
     *
     * @param request 查询智能体权限请求参数
     * @return 智能体权限授权列表
     */
    Collection<PermissionAuthorizationVO> findAIAgentPermissions(RolePermissionRequest request);
}
