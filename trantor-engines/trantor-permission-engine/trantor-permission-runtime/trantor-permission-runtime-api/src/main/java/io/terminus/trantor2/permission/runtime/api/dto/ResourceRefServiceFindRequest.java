package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.common.api.request.AbstractRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/6/25 11:56
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
@Schema(description = "资源引用服务摘要信息查询请求")
public class ResourceRefServiceFindRequest extends AbstractRequest {

    @Schema(description = "资源标识集合", required = true)
    @NotEmpty
    private List<String> resourceKeys;
}
