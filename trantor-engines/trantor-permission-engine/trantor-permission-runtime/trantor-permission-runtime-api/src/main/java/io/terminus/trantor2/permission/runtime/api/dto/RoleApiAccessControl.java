package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.props.ApiMetaProps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/9/13 11:00
 **/
@Data
@NoArgsConstructor
public class RoleApiAccessControl implements Serializable {

    @Schema(description = "元数据标识")
    private String key;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "权限项标识")
    protected String permissionKey;

    @Schema(description = "权限授权效果")
    private AuthorizationEffect effect;

    @Schema(description = "资源描述")
    private ApiMetaProps props;

    public static RoleApiAccessControl of(ApiMeta apiMeta, String permissionKey, AuthorizationEffect effect) {
        RoleApiAccessControl roleApiAccessControl = new RoleApiAccessControl();
        roleApiAccessControl.key = apiMeta.getKey();
        roleApiAccessControl.name = apiMeta.getName();
        roleApiAccessControl.description = apiMeta.getDescription();
        roleApiAccessControl.setPermissionKey(permissionKey);
        roleApiAccessControl.effect = effect;
        roleApiAccessControl.props = apiMeta.getResourceProps();
        return roleApiAccessControl;
    }
}
