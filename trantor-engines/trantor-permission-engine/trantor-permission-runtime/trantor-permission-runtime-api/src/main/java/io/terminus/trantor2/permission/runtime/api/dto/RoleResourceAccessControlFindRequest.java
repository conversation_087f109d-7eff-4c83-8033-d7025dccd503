package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.api.common.dto.ResourceAccessControlFindRequest;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
@Schema(description = "角色资源访问控制查询参数")
public class RoleResourceAccessControlFindRequest extends ResourceAccessControlFindRequest {

    @Schema(description = "角色ID", required = true)
    @NotNull(message = "roleId can not be null")
    private Long roleId;
}
