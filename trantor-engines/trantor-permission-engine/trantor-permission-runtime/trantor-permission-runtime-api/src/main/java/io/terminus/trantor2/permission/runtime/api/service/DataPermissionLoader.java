package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.iam.api.dto.condition.ConditionGroup;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 数据权限查询逻辑
 *
 * <AUTHOR>
 * 2024/6/11 13:57
 **/
public interface DataPermissionLoader {

    List<PermissionAssign> getUserAllowEffectDataPermissionAssign(Long userId,
                                                                  Long iamAppId);

    ConditionGroup getUserDataPermissionConditionGroup(@NotNull final Long userId,
                                                       @NotNull final Long iamAppId,
                                                       @NotNull final String permissionKey);

}
