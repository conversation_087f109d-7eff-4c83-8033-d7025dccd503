package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 2024/5/30 20:35
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询角色授权权限参数")
public class PermissionWithAssignFindRequest extends RolePermissionRequest {

    /**
     * 数据权限-个性化设置：传递菜单key
     * 字段权限：视图key
     */
    @Schema(description = "资源Key")
    private String resourceKey;

}
