package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.iam.api.request.role.UserRoleRelationCreateParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.api.response.user.UserGroup;
import io.terminus.trantor2.iam.dto.user.AssignUserRequest;
import io.terminus.trantor2.iam.dto.usergroup.AssignUserGroupRequest;
import io.terminus.trantor2.module.model.dto.role.DefaultRoleRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleCopyRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleCreateRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleFindRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RolePageRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleUpdateRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleDTO;

import java.util.Collection;
import java.util.List;

public interface PortalRoleService {

    Long createRole(RoleCreateRequest request);

    void updateRole(RoleUpdateRequest request);

    void deleteById(Long roleId);

    PageResult<RoleDTO> pagingPortalRole(RolePageRequest request);

    List<Role> queryPortalRole(RoleFindRequest request);

    void setToDefault(DefaultRoleRequest request);

    void cancelDefault(DefaultRoleRequest request);

    void copyRole(RoleCopyRequest request);

    Collection<User> findAssignedUsersByRoleId(Long roleId);

    void removeOrAssignUsers(Long roleId, AssignUserRequest request);

    Collection<UserGroup> findAssignedPortalUserGroupsByRoleId(Long roleId);

    void removeOrAssignUserGroups(Long roleId, AssignUserGroupRequest request);

    void flushUserRoleRelation(UserRoleRelationCreateParams roleRelationCreateParams);

    List<Role> findRoleByUserId(Long userId);
}
