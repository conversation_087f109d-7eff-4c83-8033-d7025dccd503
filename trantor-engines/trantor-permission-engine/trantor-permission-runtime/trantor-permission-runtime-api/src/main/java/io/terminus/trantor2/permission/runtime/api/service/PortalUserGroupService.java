package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.iam.api.request.user.UpdateUserRelationActiveParam;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.User;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.iam.dto.role.AssignRoleRequest;
import io.terminus.trantor2.iam.dto.user.AssignUserRequest;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupPageRequest;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupVO;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupWriteRequest;

import java.util.Collection;

/**
 * 门户运行时用户组服务
 *
 * <AUTHOR>
 * 2025/5/14 09:51
 **/
public interface PortalUserGroupService {

    Long create(UserGroupWriteRequest request);

    void deleteById(Long id);

    void updateById(Long id, UserGroupWriteRequest request);

    void copyWithId(Long id, UserGroupWriteRequest request);

    Paging<UserGroupVO> paging(UserGroupPageRequest request);

    Collection<User> findUsersByUserGroupId(Long id);

    void removeOrAssignUsers(Long id, AssignUserRequest request);

    Collection<Role> findRolesByUserGroupId(Long id);

    void removeOrAssignRoles(Long id, AssignRoleRequest request);

    // 用户组关联用户 生效
    void activeById(Long id);

    // 用户组关联用户 失效
    void inactiveById(Long id);

    // 用户组关联用户 批量 生效/失效
    void changeActiveByUserIdsAndGroupId(UpdateUserRelationActiveParam param);
}
