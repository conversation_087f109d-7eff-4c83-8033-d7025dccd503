package io.terminus.trantor2.permission.runtime.api.service;

import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionWithAssignFindRequest;
import io.terminus.trantor2.scene.runtime.vo.ViewPermissionVO;

import java.util.List;

/**
 * 角色字段权限
 *
 * <AUTHOR>
 * 2024/7/8 14:59
 **/
public interface RoleFieldPermissionService {

    /**
     * 查询用户在指定门户下的字段权限规则
     *
     * @param viewKey    视图key
     * @return
     */
    List<ViewPermissionVO.ComponentFieldRules> findFieldPermission(String viewKey);

    /**
     * 只查询门户角色字段授权树
     *
     * @param request 查询授权权请求参数
     * @return 门户字段授权树无详情
     */
    List<PermissionResourceDTO> findFieldPermissionOnlyTree(PermissionWithAssignFindRequest request);

    /**
     * 只查询门户角色字段授权树详情
     *
     * @param request 查询授权权请求参数
     * @return 门户字段授权树详情
     */
    List<PermissionResourceDTO> findFieldPermissionOnlyDetail(PermissionWithAssignFindRequest request);

}
