package io.terminus.trantor2.permission.runtime.api.dto.devops;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/8/23 18:53
 **/
@Getter
@Setter
@EqualsAndHashCode
public class PermissionMigrationCheckRequest implements Serializable {

    @Schema(description = "门户编号")
    private String portalCode;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "是否只展示存在差异的资源节点", defaultValue = "true")
    private Boolean onlyShowDiffNode = Boolean.TRUE;

    @Schema(description = "差异类型", defaultValue = "ALL")
    private DiffType diffType = DiffType.ALL;

    @Schema(description = "是否比较服务迁移差异", defaultValue = "true")
    private Boolean compareServiceDiff = Boolean.TRUE;

    @Schema(description = "是否隐藏不存在的服务节点", defaultValue = "false")
    private Boolean hiddenNotExistService = Boolean.FALSE;

    public enum DiffType {
        ALLOW_TO_NONE,
        NONE_TO_ALLOW,
        ALL;
    }
}
