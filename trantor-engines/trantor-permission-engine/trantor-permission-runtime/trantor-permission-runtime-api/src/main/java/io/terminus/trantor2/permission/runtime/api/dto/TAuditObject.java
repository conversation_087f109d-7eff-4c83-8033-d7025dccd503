package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.dto.audit.AuditObject;
import lombok.Data;

/**
 * <AUTHOR>
 * 2023/11/28 6:59 PM
 **/
@Data
public class TAuditObject extends AuditObject {

    @Schema(description = "操作类型名称")
    private String operationTypeName;

    public TAuditObject(AuditObject auditObject) {
        this.setId(auditObject.getId());
        this.setAuditLogId(auditObject.getAuditLogId());
        this.setOperationType(auditObject.getOperationType());
        this.operationTypeName = AuditObject.OperationType.valueOf(auditObject.getOperationType()).getName();
        this.setObjectType(auditObject.getObjectType());
        this.setObjectId(auditObject.getObjectId());
        this.setObjectKey(auditObject.getObjectKey());
        this.setObjectName(auditObject.getObjectName());
    }
}
