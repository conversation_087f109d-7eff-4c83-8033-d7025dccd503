package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.iam.api.response.permission.FieldRule;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/7/8 16:30
 **/
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "字段权限授权策略")
public class FieldPermissionAssignDecision extends PermissionAssignDecision {

    @Schema(description = "字段访问规则", required = true)
    @NotEmpty(message = "fieldRules can not be empty")
    private List<FieldRule> fieldRules;

    @Override
    public PermissionType getPermissionType() {
        return PermissionType.FIELD_PERMISSION;
    }
}
