package io.terminus.trantor2.permission.runtime.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 2024/6/4 16:08
 **/
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据权限授权策略")
public class FunctionPermissionAssignDecision extends PermissionAssignDecision {

    @Override
    public PermissionType getPermissionType() {
        return PermissionType.FUNCTION_PERMISSION;
    }
}
