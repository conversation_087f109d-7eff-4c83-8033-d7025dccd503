package io.terminus.trantor2.doc.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "DocEngineEventPageQueryRequest", description = "Event翻页查询参数")
public class DocEngineEventPageQueryRequest {

    @Schema(description = "页码")
    private int pageNo = 1;

    @Schema(description = "单页数量")
    private int pageSize = 10;

    @Schema(description= "编码或名称查询")
    private String search;

    @Schema(description= "模块key")
    private String module;

    @Schema(description= "模糊查询参数")
    private String fuzzyValue;
}
