package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.doc.api.ActionMeta;
import io.terminus.trantor2.doc.api.dict.DocEngineActionLanguageTypeEnum;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.api.props.EventActionRelProps;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.api.request.ActionUploadRequest;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.dict.DocEngineActionStatusEnum;
import io.terminus.trantor2.doc.exception.DocEngineActionManagementException;
import io.terminus.trantor2.doc.repo.ActionRepo;
import io.terminus.trantor2.doc.request.DocEngineActionPageQueryRequest;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.TeamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEngineActionManagementService {

    private static final Pattern PATTERN_GROOVY_IMPORT = Pattern.compile("import\\s+([\\w\\.]+);?");
    private static final Integer DEFAULT_ACTION_ORDER = 10;
    private final MetaQueryService metaQueryService;
    private final MetaEditService metaEditService;
    private final ActionRepo actionRepo;
    private final TeamService teamService;

    public Paging<ActionDTO> page(DocEngineActionPageQueryRequest request) {
        List<Cond> conds = new ArrayList<>();
        if(StringUtils.hasLength(request.getModule())) {
            conds.add(Field.parentKey().equal(request.getModule()));
        }
        if(StringUtils.hasLength(request.getSearch())) {
            Cond searchCond = Cond.or(Field.key().like("%" + request.getSearch() + "%"), Field.name().like("%" + request.getSearch() + "%"));
            conds.add(searchCond);
        }
        if (StringUtils.hasLength(request.getFuzzyValue())) {
            Cond fuzzy = Cond.or(Field.key().like("%" + request.getFuzzyValue() + "%"), Field.name().like("%" + request.getFuzzyValue() + "%"));
            conds.add(fuzzy);
        }
        Cond cond = Cond.and(conds.toArray(new Cond[conds.size()]));

        Paging<ActionMeta> pageResult = actionRepo.findAll(cond,
                PageReq.of(request.getPageNo() - 1, request.getPageSize(), Order.byKey()), ResourceContext.ctxFromThreadLocal());
        return new Paging<>(pageResult.getTotal(), pageResult.getData().stream().map(this::meta2Dto).collect(Collectors.toList()));
    }

    public ActionDTO queryById(Long actionId) {
        return actionRepo.findOneById(actionId)
                .map(this::meta2Dto)
                .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "action.not.existed"));
    }

    public ActionDTO queryByKey(String actionKey) {
        return actionRepo.findOneByKey(actionKey, ResourceContext.ctxFromThreadLocal())
                .map(this::meta2Dto)
                .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "action.not.existed"));
    }

    public ActionDTO create(ActionDTO action) {
        ResourceContext context = ResourceContext.ctxFromThreadLocal();

        // 查重校验
        if (actionRepo.existKey(action.getCode(), context)) {
            throw new DocEngineActionManagementException(ErrorType.BAD_REQUEST, "action.code.duplicated");
        }

        if(Objects.isNull(action.getOrder())) {
            action.setOrder(DEFAULT_ACTION_ORDER);
        }

        String status = DocEngineActionLanguageTypeEnum.GROOVY.getType().equals(action.getLanguageType())
            && StringUtils.hasLength(action.getGroovyScript())
            ? DocEngineActionStatusEnum.ENABLED.getStatus() : DocEngineActionStatusEnum.DRAFT.getStatus();
        ActionMeta actionMeta = this.dto2ActionMeta(action, status);
        actionRepo.create(actionMeta, context);
        return this.queryByKey(actionMeta.getKey());
    }

    public ActionDTO update(ActionDTO action) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();

        ActionMeta actionMeta = actionRepo.findOneByKey(action.getCode(), ctx)
            .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "action.not.existed"));

        // 只更新能更新的属性
        ActionProps actionProps = actionMeta.getResourceProps();
        actionProps.setOrder(Objects.isNull(action.getOrder()) ? actionProps.getOrder() : action.getOrder());
        actionProps.setDesc(action.getDesc());
        actionProps.setReturnModel(action.getReturnModel());
        String languageType = StringUtils.hasLength(action.getLanguageType()) ?
            action.getLanguageType() : DocEngineActionLanguageTypeEnum.JAVA.getType();
        actionProps.setLanguageType(languageType);
        actionProps.setGroovyScript(action.getGroovyScript());
        // 类型可能变更
        if(DocEngineActionLanguageTypeEnum.GROOVY.getType().equals(languageType)) {
            actionProps.setBean(null);
        }
        if(DocEngineActionLanguageTypeEnum.JAVA.getType().equals(languageType)) {
            actionProps.setGroovyScript(null);
        }
        // 状态
        if(DocEngineActionLanguageTypeEnum.GROOVY.getType().equals(languageType)
            && StringUtils.hasLength(action.getGroovyScript()) ) {
            actionProps.setStatus(DocEngineActionStatusEnum.ENABLED.getStatus());
//            if(!action.getGroovyScript().equals(oldGroovyScript)) {
//                // 脚本变更后校验
//                this.validateGroovyScript(action.getGroovyScript());
//            }
        } else if(DocEngineActionLanguageTypeEnum.JAVA.getType().equals(languageType)
            && StringUtils.hasLength(action.getBean())) {
            actionProps.setStatus(DocEngineActionStatusEnum.ENABLED.getStatus());
        } else {
            actionProps.setStatus(DocEngineActionStatusEnum.DRAFT.getStatus());
        }
        this.groovyParamType(actionProps);
        actionMeta.setResourceProps(actionProps);

        actionMeta.setName(action.getName());
        actionRepo.update(actionMeta, ctx);

        return this.queryByKey(action.getCode());
    }

    public void delete(ActionDTO action) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        ActionMeta actionMeta = actionRepo.findOneByKey(action.getCode(), ResourceContext.ctxFromThreadLocal())
            .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "action.not.existed"));

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        String actionCode = actionMeta.getKey();

        // 删除对应event下的link
        List<MetaTreeNode> eventMetaNodes = metaQueryService.findUsageByKey(context, actionCode);
        eventMetaNodes.stream().filter(eventMetaNode -> DocEngineMetaTypeConstant.EVENT.equals(eventMetaNode.getType()))
            .forEach(eventMetaNode -> {
                EventProps eventProps;
                try {
                    eventProps = ObjectJsonUtil.MAPPER.treeToValue(eventMetaNode.getProps(), EventProps.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                // 排除自己
                List<EventActionRelProps> relations = eventProps.getRelations().stream()
                    .filter(relation -> !relation.getCode().equals(action.getCode()))
                    .collect(Collectors.toList());
                eventProps.setRelations(relations);
                eventMetaNode.setProps(ObjectJsonUtil.MAPPER.valueToTree(eventProps));
                metaEditService.submitOp(context, EditUtil.updateNodeOp(eventMetaNode));
            });

        actionRepo.deleteByKey(actionCode, ctx);
    }

    public void upload(ActionUploadRequest request) {
        log.info("[Doc Engine Action Management] upload request : {}", request);

        // Trantor 用户处理
        User trantorUser = new User();
        trantorUser.setId(request.getUserId());
        if (Objects.isNull(TrantorContext.getContext())) {
            TrantorContext.init();
        }

        TrantorContext.setCurrentUser(trantorUser);
        TrantorContext.setModuleKey(request.getAppKey());
        TrantorContext.setTeamId(request.getTeamId());
        String teamCode;
        if (StringUtils.hasLength(request.getTeamCode())) {
            teamCode = request.getTeamCode();
        } else {
            teamCode = teamService.getTeamCode(request.getTeamId());
        }
        TrantorContext.setTeamCode(teamCode);
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        request.getActions().forEach(action -> action.setCode(KeyUtil.newKeyUnderModule(request.getAppKey(), action.getCode())));

        List<ActionDTO> actionUploads = request.getActions();
        List<String> actionCodes = actionUploads.stream().map(ActionDTO::getCode).collect(Collectors.toList());
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        Map<String, ActionMeta> actionMetaMap = actionRepo.findAll(Field.key().in(actionCodes), ctx)
            .stream()
                .collect(Collectors.toMap(ActionMeta::getKey, Function.identity()));

        // 更新
        actionUploads.stream()
            .filter(action -> actionMetaMap.containsKey(action.getCode()))
            .forEach(action -> {
                ActionMeta actionMeta = actionMetaMap.get(action.getCode());
                ActionProps existActionProps = actionMeta.getResourceProps();

                // 目录 名称和描述以研发态维护为准
                action.setModule(actionMeta.getParentKey());
                action.setDesc(existActionProps.getDesc());
                action.setReturnModel(existActionProps.getReturnModel());

                action.setLanguageType(DocEngineActionLanguageTypeEnum.JAVA.getType());
                ActionMeta actionMetaNode = dto2ActionMeta(action, DocEngineActionStatusEnum.ENABLED.getStatus());

                // 无变更 不修改
                boolean needUpdate = this.needUpdateActionMeta(actionMeta, actionMetaNode);
                if(!needUpdate) {
                    return;
                }

                try{
                    actionRepo.update(actionMetaNode, ctx);
                }catch (Throwable t){
                    log.error("[Doc Engine Action Management] action upload update failed, action code : {}", action.getCode(), t);
                }
            });

        // 新增
        MetaTreeNode folderMeta = this.findFolder(request.getFolderPath(), context);
        actionUploads.stream()
            .filter(action -> !actionMetaMap.containsKey(action.getCode()))
            .forEach(action -> {
                action.setModule(folderMeta.getKey());
                action.setLanguageType(DocEngineActionLanguageTypeEnum.JAVA.getType());
                ActionMeta actionMetaNode = dto2ActionMeta(action, DocEngineActionStatusEnum.ENABLED.getStatus());

                try{
                    actionRepo.create(actionMetaNode, ctx);
                }catch (Throwable t){
                    log.error("[Doc Engine Action Management] action upload create failed, action code : {}", action.getCode(), t);
                }
            });
    }

    private MetaTreeNode findFolder(String folderPath, MetaEditAndQueryContext context) {
        MetaTreeNode targetFolderNode = null;
        if (StringUtils.hasLength(folderPath)) {
            String parentKey = metaQueryService.queryInApp(context).findFolderRootKey();
            List<String> parentKeys = new ArrayList<>();
            parentKeys.add(parentKey);
            parentKeys.add(KeyUtil.moduleKey(parentKey));
            String[] folderNames = folderPath.split("/");
            for (int i = 0; i < folderNames.length; i++) {
                String folderName = folderNames[i];
                MetaTreeNode folderNode = metaQueryService.queryInApp(context).findOne(Field.parentKey().in(parentKeys).and(Field.name().equal(folderName)).and(Field.type().equal("Folder")))
                    .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "folder.not.existed"));
                if (i == folderNames.length - 1) {
                    targetFolderNode = folderNode;
                } else {
                    parentKeys.clear();
                    parentKeys.add(folderNode.getKey());
                }
            }
        } else {
            targetFolderNode = metaQueryService.queryInApp(context).findUngroupFolder()
                .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "folder.not.existed"));
        }
        return targetFolderNode;
    }

    private ActionMeta dto2ActionMeta(ActionDTO dto, String status) {
        ActionMeta actionMeta = new ActionMeta();

        actionMeta.setKey(dto.getCode());
        actionMeta.setName(dto.getName());
        actionMeta.setParentKey(dto.getModule());

        ActionProps actionProps = new ActionProps();
        actionProps.setStatus(status);
        actionProps.setBean(dto.getBean());
        actionProps.setMethod(dto.getMethod());
        actionProps.setDesc(dto.getDesc());
        actionProps.setOrder(dto.getOrder());
        actionProps.setRequestType(dto.getRequestType());
        actionProps.setResponseType(dto.getResponseType());
        actionProps.setInput(dto.getInput());
        actionProps.setOutput(dto.getOutput());
        actionProps.setRequestModelKey(dto.getRequestModelKey());

        String languageType = StringUtils.hasLength(dto.getLanguageType()) ?
                dto.getLanguageType() : DocEngineActionLanguageTypeEnum.JAVA.getType();
        actionProps.setLanguageType(languageType);

        actionProps.setGroovyScript(dto.getGroovyScript());
        actionProps.setReturnModel(dto.getReturnModel());
        this.groovyParamType(actionProps);
        actionMeta.setResourceProps(actionProps);

        return actionMeta;
    }

    private void groovyParamType(ActionProps actionProps) {
        if(!DocEngineActionLanguageTypeEnum.GROOVY.getType().equals(actionProps.getLanguageType())) {
            return;
        }
        if(!StringUtils.hasLength(actionProps.getGroovyScript())) {
            return;
        }

        // 特殊约定：默认第一行import是入参类 第二行import是出参类(可以无出参类)
        Matcher importMatcher = PATTERN_GROOVY_IMPORT.matcher(actionProps.getGroovyScript());
        if(importMatcher.find()) {
            actionProps.setRequestType(importMatcher.group(1));
        }else {
            throw new DocEngineActionManagementException(ErrorType.EVENT_ACTION_GROOVY_ERROR, "groovy.import.error");
        }

        if(importMatcher.find()) {
            actionProps.setResponseType(importMatcher.group(1));
        }else {
            actionProps.setResponseType(Void.class.getTypeName());
        }
    }

    private ActionDTO meta2Dto(ActionMeta actionMeta) {
        ActionDTO actionDTO = new ActionDTO();
        actionDTO.setCode(actionMeta.getKey());
        actionDTO.setName(actionMeta.getName());
        actionDTO.setModule(actionMeta.getParentKey());

        ActionProps actionProps = actionMeta.getResourceProps();
        actionDTO.setDesc(actionProps.getDesc());
        actionDTO.setBean(actionProps.getBean());
        actionDTO.setMethod(actionProps.getMethod());
        actionDTO.setOrder(actionProps.getOrder());
        actionDTO.setRequestType(actionProps.getRequestType());
        actionDTO.setResponseType(actionProps.getResponseType());
        actionDTO.setReturnList(StringUtils.hasLength(actionProps.getResponseType()) && actionProps.getResponseType().startsWith(List.class.getName()));
        actionDTO.setStatus(actionProps.getStatus());
        actionDTO.setReturnModel(actionProps.getReturnModel());
        actionDTO.setAccess(actionMeta.getAccess());

        String languageType = StringUtils.hasLength(actionProps.getLanguageType()) ?
                actionProps.getLanguageType() : DocEngineActionLanguageTypeEnum.JAVA.getType();
        actionDTO.setLanguageType(languageType);

        if (DocEngineActionLanguageTypeEnum.JAVA.getType().equals(languageType)) {
            actionDTO.setJavaCode(convertJavaCode(actionDTO));
        }

        actionDTO.setGroovyScript(actionProps.getGroovyScript());
        actionDTO.setId(actionDTO.getId());
        actionDTO.setUpdatedAt(actionDTO.getUpdatedAt());
        actionDTO.setExtended(actionMeta.getExtended());
        actionDTO.setCustomExt(actionMeta.getCustomExt());
        return actionDTO;
    }

    private String convertJavaCode(ActionDTO dto) {
        return "public Response<" + dto.getResponseType() + "> " + dto.getMethod() + "(" + dto.getRequestType() + " request" + ")" + " {\n" +
                "       // 业务实现" + "\n" +
                "}";
    }

    public void validateGroovyScript(String groovyScript) {
        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("groovy");
        try {
            engine.eval(groovyScript);
        } catch (ScriptException e) {
            log.error("[Doc Engine Action Management] action groovy eval failed, groovyScript : {}", groovyScript, e);
            throw new DocEngineActionManagementException(ErrorType.EVENT_ACTION_GROOVY_ERROR, "groovy.eval.error");
        }
    }

    private boolean needUpdateActionMeta(ActionMeta old, ActionMeta last) {
        if(!old.getName().equals(last.getName())) {
            return true;
        }
        return !old.getResourceProps().equals(last.getResourceProps());
    }

}
