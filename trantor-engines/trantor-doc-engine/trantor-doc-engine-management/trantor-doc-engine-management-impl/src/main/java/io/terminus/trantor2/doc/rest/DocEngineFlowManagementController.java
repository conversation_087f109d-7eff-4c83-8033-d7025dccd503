package io.terminus.trantor2.doc.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.dto.FlowDTO;
import io.terminus.trantor2.doc.api.dto.FlowViewDTO;
import io.terminus.trantor2.doc.request.DocEngineFlowPageQueryRequest;
import io.terminus.trantor2.doc.service.DocEngineFlowManagementService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "业务流管理")
@RestController
@RequestMapping(path = "/api/trantor/console/doc/engine/flow")
public class DocEngineFlowManagementController {

    @Resource
    private DocEngineFlowManagementService docEngineFlowManagementService;

    @PostMapping("/page")
    @Operation(summary = "翻页查询业务流")
    public Response<Paging<FlowDTO>> page(@RequestBody DocEngineFlowPageQueryRequest request) {
        return Response.ok(docEngineFlowManagementService.page(request));
    }

    @GetMapping("/query/by/id")
    @Operation(summary = "按id查询业务流")
    public Response<FlowDTO> queryById(@RequestParam Long flowId) {
        return Response.ok(docEngineFlowManagementService.queryById(flowId));
    }

    @PostMapping("/create")
    @Operation(summary = "新增业务流")
    public Response<FlowDTO> create(@RequestBody FlowDTO flow) {
        return Response.ok(docEngineFlowManagementService.create(flow));
    }

    @PostMapping("/update")
    @Operation(summary = "更新业务流")
    public Response<FlowDTO> update(@RequestBody FlowDTO flow) {
        return Response.ok(docEngineFlowManagementService.update(flow));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除业务流")
    public Response<Void> delete(@RequestBody FlowDTO flow) {
        docEngineFlowManagementService.delete(flow);
        return Response.ok();
    }

    @GetMapping("/view")
    @Operation(summary = "业务流视图")
    public Response<FlowViewDTO> view(@RequestParam Long flowId) {
        return Response.ok(docEngineFlowManagementService.view(flowId));
    }

    @PostMapping("/pre/view")
    @Operation(summary = "业务流视图预览")
    public Response<FlowViewDTO> preView(@RequestBody FlowDTO flow) {
        return Response.ok(docEngineFlowManagementService.preView(flow));
    }
}
