package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.constant.TrantorServiceSpiConstant;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import io.terminus.trantor2.service.management.api.template.model.request.GenerateEventServiceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEngineEventSyncService {

    private final MetaQueryService metaQueryService;
    private final ServiceTemplateApi serviceTemplateApi;
    private final DocEngineEventManagementService docEngineEventManagementService;

    public void syncEvent2Service() {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        metaQueryService.queryInTeam(context.getTeamId())
            .findAll(Field.type().equal(DocEngineMetaTypeConstant.EVENT))
            .forEach(this::trantorServiceNotice);
    }

    private void trantorServiceNotice(MetaTreeNodeExt eventMeta) {
        EventProps eventProps;
        try {
            eventProps = ObjectJsonUtil.MAPPER.treeToValue(eventMeta.getProps(), EventProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        GenerateEventServiceRequest generateRequest =
            new GenerateEventServiceRequest();

        generateRequest.setEventKey(eventMeta.getKey());
        generateRequest.setEventName(eventMeta.getName());
        generateRequest.setModelKey(eventProps.getModel().getKey());
        generateRequest.setParentKey(eventMeta.getParentKey());
        if(Objects.nonNull(eventProps.getReturnModel())) {
            generateRequest.setReturnModelKey(eventProps.getReturnModel().getKey());
        }
        generateRequest.setSpiKey(TrantorServiceSpiConstant.EVENT_SERVICE_EXECUTE_SPI_KEY);
        generateRequest.setTeamId(eventMeta.getTeamId());
        generateRequest.setIsBatch(eventProps.getModelArrayWhether());
        generateRequest.setIsOutputBatch(eventProps.getReturnModelArrayWhether());
        generateRequest.setAccess(eventMeta.getAccess() != null ? eventMeta.getAccess().name() : MetaNodeAccessLevel.Private.name());
        generateRequest.setOpenTransaction(eventProps.getEnabledTransaction());

        serviceTemplateApi.generate(generateRequest);
    }

    public void syncConditions() {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        metaQueryService.queryInApp(context)
            .findAll(Field.type().equal(DocEngineMetaTypeConstant.EVENT))
            .forEach(this::syncOneEventCondition);
    }

    private void syncOneEventCondition(MetaTreeNodeExt eventMeta) {
        try{
            EventDTO event = docEngineEventManagementService.queryById(eventMeta.getId());
            this.updateExpress(event);
            docEngineEventManagementService.update(event);
        }catch (Throwable t) {
            log.error("[syncCondition] failed, eventCode : {}", eventMeta.getKey(), t);
        }
    }

    public void syncCondition(String eventKey) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        metaQueryService.findByKey(context, eventKey).ifPresent(eventMeta -> {
            EventDTO event = docEngineEventManagementService.queryById(eventMeta.getId());
            this.updateExpress(event);
            docEngineEventManagementService.update(event);
        });
    }

    private void updateExpress(EventDTO event) {
        if(CollectionUtils.isEmpty(event.getActions())) {
            return;
        }
        event.getActions().stream()
            .filter(actionRel -> StringUtils.hasLength(actionRel.getExpress()))
            .forEach(actionRel -> actionRel.setExpress(actionRel.getExpress().replaceAll("\\((\\w+)\\)", "\\(TERP_MIGRATE\\$$1\\)")));
    }
}
