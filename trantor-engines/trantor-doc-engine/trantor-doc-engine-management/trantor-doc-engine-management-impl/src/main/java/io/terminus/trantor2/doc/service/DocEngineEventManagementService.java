package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import io.terminus.trantor.workflow.runtime.v2.model.dto.WorkflowGroupProps;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.condition.common.JsonUtil;
import io.terminus.trantor2.doc.api.ActionMeta;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.dto.NoticeDTO;
import io.terminus.trantor2.doc.api.node.EventActionRelNode;
import io.terminus.trantor2.doc.api.node.EventActionRelStateNode;
import io.terminus.trantor2.doc.api.node.EventStateConfigNode;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.api.props.EventActionRelProps;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.api.props.NoticeProps;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.constant.TrantorServiceSpiConstant;
import io.terminus.trantor2.doc.exception.DocEngineActionManagementException;
import io.terminus.trantor2.doc.repo.ActionRepo;
import io.terminus.trantor2.doc.request.DocEngineEventPageQueryRequest;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.rule.engine.util.ConditionConvertUtil;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.management.api.management.ServiceMetaApi;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import io.terminus.trantor2.service.management.api.template.model.request.GenerateEventServiceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEngineEventManagementService {

    private final ServiceTemplateApi serviceTemplateApi;
    private final ServiceMetaApi serviceMetaApi;
    private final MetaQueryService metaQueryService;
    private final ActionRepo actionRepo;
    private final MetaEditService metaEditService;
    private final DocEventStateManagementService docEventStateManagementService;

    private static final Pattern EVENT_CODE_PATTERN = Pattern.compile("^\\w+\\$\\w+$");

    public Paging<EventDTO> page(DocEngineEventPageQueryRequest request) {
        List<Cond> conds = new ArrayList<>();
        conds.add(Field.type().equal(DocEngineMetaTypeConstant.EVENT));
        if (StringUtils.hasLength(request.getModule())) {
            conds.add(Field.parentKey().equal(request.getModule()));
        }
        if (StringUtils.hasLength(request.getSearch())) {
            Cond searchCond = Cond.or(Field.key().like("%" + request.getSearch() + "%"), Field.name().like("%" + request.getSearch() + "%"));
            conds.add(searchCond);
        }
        if (StringUtils.hasLength(request.getFuzzyValue())) {
            Cond fuzzy = Cond.or(Field.key().like("%" + request.getFuzzyValue() + "%"), Field.name().like("%" + request.getFuzzyValue() + "%"));
            conds.add(fuzzy);
        }
        Cond cond = Cond.and(conds.toArray(new Cond[conds.size()]));

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        Paging<MetaTreeNodeExt> pageResult = metaQueryService.queryInApp(context).findPageData(cond,
                PageReq.of(request.getPageNo() - 1, request.getPageSize(), Order.byKey()));
        return new Paging<>(pageResult.getTotal(), this.meta2dto(context, pageResult.getData()));
    }

    public EventDTO queryById(Long eventId) {
        MetaTreeNodeExt entity = metaQueryService.findById(eventId)
                .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "event.not.existed"));
        return this.meta2dto(entity);
    }

    public EventDTO queryByKey(String eventKey) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        MetaTreeNodeExt entity = metaQueryService.findByKey(context, eventKey)
                .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "event.not.existed"));
        return this.meta2dto(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public EventDTO create(EventDTO event) {
        Matcher eventCodeMatcher = EVENT_CODE_PATTERN.matcher(event.getCode());
        if (!eventCodeMatcher.find()) {
            throw new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "event.code.regex.match.failed");
        }


        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        // 新建event
        MetaTreeNode eventMetaNode = this.dto2Meta(event);
        // 默认私有
        if (eventMetaNode.getAccess() == null) {
            eventMetaNode.setAccess(MetaNodeAccessLevel.Private);
        }
        EditOpRequest createEventRequest = EditUtil.createNodeOp(eventMetaNode, new MoveTarget(event.getModule(), MoveTargetType.ChildLast));
        Map<String, MetaTreeNodeExt> resultMap = metaEditService.submitOp(context, createEventRequest);
        if (resultMap.containsKey(eventMetaNode.getKey())) {
            event.setId(resultMap.get(eventMetaNode.getKey()).getId());
        } else {
            throw new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "event.create.failed");
        }

        // 新建状态变更
        if (!CollectionUtils.isEmpty(event.getStates())) {
            EventStateConfigNode eventStateConfigNode = EventProps.dto2Config(event);
            docEventStateManagementService.saveEventStateConfig(eventStateConfigNode);
        }

        // 同步到服务
        this.trantorServiceNotice(event);
        this.postUpdatePermissionKey(event, context);

        return event;
    }

    @Transactional(rollbackFor = Exception.class)
    public EventDTO update(EventDTO event) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        Optional<MetaTreeNodeExt> existEventMetaOptional = metaQueryService.findByKey(context, event.getCode());
        Assert.isTrue(existEventMetaOptional.isPresent(), "event.not.existed");

        // 更新event
        MetaTreeNode eventMetaNode = this.dto2Meta(event);
        // 编辑不变更公私有状态
        eventMetaNode.setAccess(existEventMetaOptional.get().getAccess());
        // 默认私有
        if (eventMetaNode.getAccess() == null) {
            eventMetaNode.setAccess(MetaNodeAccessLevel.Private);
        }
        metaEditService.submitOp(context, EditUtil.updateNodeOp(eventMetaNode));

        // 删除状态变更
        List<MetaTreeNodeExt> stateNodes = metaQueryService.queryInTeam(context.getTeamId()).findChildrenByKey(event.getCode());
        if (!CollectionUtils.isEmpty(stateNodes)) {
            stateNodes.forEach(stateNode -> metaEditService.submitOp(context, EditUtil.deleteNodeOp(stateNode.getKey(), true)));
        }

        // 新建状态变更
        if (!CollectionUtils.isEmpty(event.getStates())) {
            EventStateConfigNode eventStateConfigNode = EventProps.dto2Config(event);
            docEventStateManagementService.saveEventStateConfig(eventStateConfigNode);
        }

        // 同步到服务
        this.trantorServiceNotice(event);
        this.postUpdatePermissionKey(event, context);

        return event;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(EventDTO event) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        serviceMetaApi.deleteByEventCode(event.getCode(), TrantorContext.getTeamId());
        metaEditService.submitOp(context, EditUtil.deleteNodeOp(event.getCode(), true));
    }


    private void trantorServiceNotice(EventDTO event) {
        GenerateEventServiceRequest generateRequest =
                new GenerateEventServiceRequest();

        generateRequest.setEventKey(event.getCode());
        generateRequest.setEventName(event.getName());
        generateRequest.setModelKey(event.getModel().getKey());
        generateRequest.setParentKey(event.getModule());
        if (Objects.nonNull(event.getReturnModel())) {
            generateRequest.setReturnModelKey(event.getReturnModel().getKey());
        }
        generateRequest.setSpiKey(TrantorServiceSpiConstant.EVENT_SERVICE_EXECUTE_SPI_KEY);
        generateRequest.setTeamId(TrantorContext.getTeamId());
        generateRequest.setIsBatch(event.getModelArrayWhether());
        generateRequest.setIsOutputBatch(event.getReturnModelArrayWhether());
        generateRequest.setOpenTransaction(event.getEnabledTransaction());

        EventProps eventProps = this.dto2Props(event);
        generateRequest.setEventProps(eventProps);

        serviceTemplateApi.generate(generateRequest);
    }

    private void postUpdatePermissionKey(EventDTO event, MetaEditAndQueryContext ctx) {
        String serviceKey = event.getCode() + "_SERVICE";
        MetaTreeNodeExt node = metaQueryService.findByKey(ctx, serviceKey).orElse(null);
        if (node == null) {
            return;
        }
        if (!MetaType.ServiceDefinition.name().equals(node.getType())) {
            return;
        }
        if (!Objects.equals(node.getProps().path("serviceType").asText(), ServiceType.EVENT.name())) {
            return;
        }
        JsonNode _s = node.getProps().get("serviceDslJson");
        if (_s == null || !_s.isObject()) {
            _s = node.getProps().putObject("serviceDslJson");
        }
        JsonNode _sp = _s.get("props");
        if (_sp == null || !_sp.isObject()) {
            _sp = ((ObjectNode) _s).putObject("props");
        }
        // update service
        ((ObjectNode) _sp).put("permissionKey", event.getPermissionKey());
        metaEditService.submitOp(ctx, EditUtil.updateNodeOp(node));
    }

    private MetaTreeNode dto2Meta(EventDTO event) {
        MetaTreeNode eventMetaNode = new MetaTreeNode();
        eventMetaNode.setType(DocEngineMetaTypeConstant.EVENT);
        eventMetaNode.setKey(event.getCode());
        eventMetaNode.setName(event.getName());
        eventMetaNode.setParentKey(event.getModule());

        EventProps eventProps = this.dto2Props(event);
        eventMetaNode.setProps(ObjectJsonUtil.MAPPER.valueToTree(eventProps));

        return eventMetaNode;
    }

    private EventProps dto2Props(EventDTO event) {
        EventProps eventProps = new EventProps();
        eventProps.setDesc(event.getDesc());
        eventProps.setModel(event.getModel());
        eventProps.setModelArrayWhether(event.getModelArrayWhether());
        eventProps.setReturnModel(event.getReturnModel());
        eventProps.setReturnModelArrayWhether(event.getReturnModelArrayWhether());
        eventProps.setEnabledStatusVerify(event.getEnabledStatusVerify());
        // 是否开启事务 默认开启
        if (Objects.isNull(event.getEnabledTransaction())) {
            eventProps.setEnabledTransaction(true);
        } else {
            eventProps.setEnabledTransaction(event.getEnabledTransaction());
        }
        eventProps.setPermissionKey(event.getPermissionKey());

        if (!CollectionUtils.isEmpty(event.getActions())) {
            List<EventActionRelProps> relations = new ArrayList<>(event.getActions().size());
            for (int i = 0; i < event.getActions().size(); i++) {
                EventActionRelNode actionRel = event.getActions().get(i);

                EventActionRelProps relProps = new EventActionRelProps();

                if (!CollectionUtils.isEmpty(actionRel.getConditions())) {
                    relProps.setConditions(actionRel.getConditions());
                }

                if (i < event.getActions().size() - 1) {
                    relProps.setNextCode(event.getActions().get(i + 1).getCode());
                } else {
                    relProps.setNextCode(null);
                }

                relProps.setActionType(actionRel.getActionType());
                relProps.setSourceCode(actionRel.getSourceCode());
                relProps.setExpress(actionRel.getExpress());
                relProps.setConvert(actionRel.getConvert());
                relProps.setCode(actionRel.getCode());
                relProps.setEnabledTransaction(Boolean.TRUE.equals(actionRel.getEnabledTransaction()));

                if (Objects.isNull(actionRel.getEnabledParamCheck())) {
                    // 入参校验默认关闭
                    relProps.setEnabledParamCheck(false);
                } else {
                    relProps.setEnabledParamCheck(actionRel.getEnabledParamCheck());
                }

                relations.add(relProps);
            }
            eventProps.setRelations(relations);
        }

        eventProps.setStates(event.getStates());

        if (Objects.nonNull(event.getNotice()) && CollectionUtils.isEmpty(event.getNotices())) {
            // 历史数据割接处理
            eventProps.setNotices(Lists.newArrayList(this.noticeDto2Props(event.getNotice())));
        }
        if (!CollectionUtils.isEmpty(event.getNotices())) {
            eventProps.setNotices(event.getNotices().stream().map(this::noticeDto2Props).collect(Collectors.toList()));
        }
        return eventProps;
    }

    private NoticeProps noticeDto2Props(NoticeDTO notice) {
        NoticeProps noticeProps = new NoticeProps();

        if (!CollectionUtils.isEmpty(notice.getConditions())) {
            noticeProps.setConditionExpress(ConditionConvertUtil.convertConditionExpress(notice.getConditions()));
            noticeProps.setConditions(notice.getConditions());
        }

        noticeProps.setNoticeSceneName(notice.getNoticeSceneName());
        noticeProps.setNoticeSceneCode(notice.getNoticeSceneCode());
        noticeProps.setPlaceholderExpressMap(notice.getPlaceholderExpressMap());
        noticeProps.setPhoneExpress(notice.getPhoneExpress());
        noticeProps.setEmailExpress(notice.getEmailExpress());
        noticeProps.setRecipientExpress(notice.getRecipientExpress());
        noticeProps.setRecipientIds(notice.getRecipientIds());
        return noticeProps;
    }

    private EventDTO meta2dto(MetaTreeNodeExt entity) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        EventDTO event = new EventDTO();
        event.setId(entity.getId());
        event.setName(entity.getName());
        event.setCode(entity.getKey());
        event.setModule(entity.getParentKey());
        event.setAccess(entity.getAccess());
        // 所属模块名称
        metaQueryService.findByKey(context, entity.getParentKey()).ifPresent(moduleMetaNode -> event.setModuleName(moduleMetaNode.getName()));

        EventProps eventProps;
        try {
            eventProps = ObjectJsonUtil.MAPPER.treeToValue(entity.getProps(), EventProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        event.setDesc(eventProps.getDesc());
        event.setModel(eventProps.getModel());
        event.setModelArrayWhether(Boolean.TRUE.equals(eventProps.getModelArrayWhether()));
        event.setReturnModel(eventProps.getReturnModel());
        event.setReturnModelArrayWhether(Boolean.TRUE.equals(eventProps.getReturnModelArrayWhether()));
        event.setEnabledStatusVerify(eventProps.getEnabledStatusVerify());
        if (Objects.isNull(eventProps.getEnabledTransaction())) {
            event.setEnabledTransaction(true);
        } else {
            event.setEnabledTransaction(eventProps.getEnabledTransaction());
        }
        event.setPermissionKey(eventProps.getPermissionKey());

        // Action
        if (CollectionUtils.isEmpty(eventProps.getRelations())) {
            event.setActions(new ArrayList<>());
        } else {
            event.setActions(this.meta2ActionRelNode(context, eventProps));
        }

        if (Objects.nonNull(eventProps.getNotice()) && CollectionUtils.isEmpty(eventProps.getNotices())) {
            // 历史数据割接处理
            eventProps.setNotices(Lists.newArrayList(eventProps.getNotice()));
        }
        if (!CollectionUtils.isEmpty(eventProps.getNotices())) {
            event.setNotices(eventProps.getNotices().stream().map(this::meta2Notice).collect(Collectors.toList()));
        }

        if (eventProps.getStates() != null && !eventProps.getStates().isEmpty()) {
            event.setStates(eventProps.getStates());
        } else {
            EventStateConfigNode stateConfigNode = docEventStateManagementService.getStateConfigByEventKey(entity.getKey());
            event.setStates(this.config2DtoList(context, stateConfigNode));
        }
        return event;
    }

    private List<EventActionRelNode> meta2ActionRelNode(MetaEditAndQueryContext context, EventProps eventProps) {
        if (CollectionUtils.isEmpty(eventProps.getRelations())) {
            return new ArrayList<>();
        }

        Map<String, MetaTreeNodeExt> actionMetaMap = metaQueryService.findByKeyIn(context, eventProps.getRelations().stream().map(EventActionRelProps::getCode).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));

        Map<String, ActionMeta> actionMap = actionRepo.findAllByKeys(eventProps.getRelations().stream()
                        .filter(it -> DocEngineMetaTypeConstant.ACTION.equals(it.getActionType()))
                        .map(EventActionRelProps::getCode).collect(Collectors.toList()), ResourceContext.newResourceCtx(context))
                .stream().collect(Collectors.toMap(ActionMeta::getKey, Function.identity()));
        Map<String, MetaTreeNode> moduleMetaMap = metaQueryService.findByKeyIn(context, actionMetaMap.values().stream().map(MetaTreeNode::getParentKey).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));

        // action rel前端展示信息
        Map<String, EventActionRelNode> actionRelNodeMap = eventProps.getRelations().stream().map(rel -> {
            EventActionRelNode node = new EventActionRelNode();
            node.setActionType(rel.getActionType());
            node.setExpress(rel.getExpress());
            node.setConvert(rel.getConvert());
            node.setCode(rel.getCode());
            node.setSourceCode(rel.getSourceCode());
            node.setEnabledTransaction(Boolean.TRUE.equals(rel.getEnabledTransaction()));
            if (Objects.isNull(rel.getEnabledParamCheck())) {
                // 入参校验默认关闭
                node.setEnabledParamCheck(false);
            } else {
                node.setEnabledParamCheck(rel.getEnabledParamCheck());
            }

            if (actionMetaMap.containsKey(rel.getCode())) {
                MetaTreeNodeExt actionMetaNode = actionMetaMap.get(rel.getCode());
                node.setName(actionMetaNode.getName());
                node.setAppId(actionMetaNode.getAppId());
                node.setTeamId(actionMetaNode.getTeamId());

                if (DocEngineMetaTypeConstant.ACTION.equals(actionMetaNode.getType()) && actionMap.containsKey(rel.getCode())) {
                    ActionProps actionProps = actionMap.get(rel.getCode()).getResourceProps();
                    node.setReturnModel(actionProps.getReturnModel());
                    node.setActionProps(actionProps);
                } else if (DocEngineMetaTypeConstant.EVENT.equals(actionMetaNode.getType())) {
                    try {
                        EventProps childEventProps = ObjectJsonUtil.MAPPER.treeToValue(actionMetaNode.getProps(), EventProps.class);
                        node.setReturnModel(childEventProps.getReturnModel());
                        node.setEventProps(childEventProps);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                } else if (DocEngineMetaTypeConstant.SERVICE.equals(actionMetaNode.getType())) {
                    ServiceMeta ServiceMeta = BaseMeta.newInstanceFrom(actionMetaNode, ServiceMeta.class);
                    if (ServiceMeta.getResourceProps().getServiceType() == ServiceType.EVENT) {
                        node.setEventProps(ServiceMeta.getResourceProps().getEventProps());
                    }
                    node.setServiceProps(ServiceMeta.getResourceProps());
                } else if (DocEngineMetaTypeConstant.APPROVAL_WORKFLOW.equals(actionMetaNode.getType())) {
                    WorkflowGroupProps workflowGroupProps = JsonUtil.convertObject(actionMetaNode.getProps(), WorkflowGroupProps.class);
                    node.setWorkflowGroupProps(workflowGroupProps);
                }

                if (moduleMetaMap.containsKey(actionMetaNode.getParentKey())) {
                    MetaTreeNode moduleMetaNode = moduleMetaMap.get(actionMetaNode.getParentKey());
                    node.setModule(moduleMetaNode.getKey());
                    node.setModuleName(moduleMetaNode.getName());
                }
            }

            if (!CollectionUtils.isEmpty(rel.getConditions())) {
                node.setConditions(rel.getConditions());
            }

            return node;
        }).collect(Collectors.toMap(EventActionRelNode::getCode, Function.identity()));

        // 元数据action rel信息
        Map<String, EventActionRelProps> actionRelPropsMap = eventProps.getRelations().stream()
                .collect(Collectors.toMap(EventActionRelProps::getCode, Function.identity()));

        Set<String> nextCodes = eventProps.getRelations().stream()
                .map(EventActionRelProps::getNextCode)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toSet());
        String firstCode = eventProps.getRelations().stream()
                .map(EventActionRelProps::getCode)
                .filter(code -> !nextCodes.contains(code))
                .findFirst().orElseThrow(() -> new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "action.rel.error"));

        List<EventActionRelNode> actions = new ArrayList<>(eventProps.getRelations().size());
        String code = firstCode;
        while (StringUtils.hasLength(code)) {
            actions.add(actionRelNodeMap.get(code));
            code = actionRelPropsMap.get(code).getNextCode();
        }
        return actions;
    }

    private NoticeDTO meta2Notice(NoticeProps noticeProps) {
        NoticeDTO notice = new NoticeDTO();

        if (!CollectionUtils.isEmpty(noticeProps.getConditions())) {
            notice.setConditions(noticeProps.getConditions());
        }

        notice.setNoticeSceneName(noticeProps.getNoticeSceneName());
        notice.setNoticeSceneCode(noticeProps.getNoticeSceneCode());
        notice.setPlaceholderExpressMap(noticeProps.getPlaceholderExpressMap());
        notice.setPhoneExpress(noticeProps.getPhoneExpress());
        notice.setEmailExpress(noticeProps.getEmailExpress());
        notice.setRecipientExpress(noticeProps.getRecipientExpress());
        notice.setRecipientIds(noticeProps.getRecipientIds());
        return notice;
    }

    private List<EventDTO> meta2dto(MetaEditAndQueryContext context, List<MetaTreeNodeExt> entities) {
        List<String> moduleKeys = entities.stream().map(MetaTreeNode::getParentKey).collect(Collectors.toList());
        Map<String, MetaTreeNode> moduleMetaMap = metaQueryService.findByKeyIn(context, moduleKeys)
                .stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        return entities.stream().map(entity -> {
            EventDTO event = new EventDTO();
            event.setId(entity.getId());
            event.setName(entity.getName());
            event.setCode(entity.getKey());
            event.setModule(entity.getParentKey());
            event.setAccess(entity.getAccess());

            EventProps eventProps;
            try {
                eventProps = ObjectJsonUtil.MAPPER.treeToValue(entity.getProps(), EventProps.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            event.setDesc(eventProps.getDesc());
            event.setModel(eventProps.getModel());
            event.setModelArrayWhether(Boolean.TRUE.equals(eventProps.getModelArrayWhether()));
            event.setReturnModel(eventProps.getReturnModel());
            event.setReturnModelArrayWhether(Boolean.TRUE.equals(eventProps.getReturnModelArrayWhether()));
            event.setEnabledStatusVerify(eventProps.getEnabledStatusVerify());
            if (Objects.isNull(eventProps.getEnabledTransaction())) {
                event.setEnabledTransaction(true);
            } else {
                event.setEnabledTransaction(eventProps.getEnabledTransaction());
            }

            if (moduleMetaMap.containsKey(entity.getParentKey())) {
                event.setModuleName(moduleMetaMap.get(entity.getParentKey()).getName());
            }
            return event;
        }).collect(Collectors.toList());
    }

    private List<EventActionRelStateNode> config2DtoList(MetaEditAndQueryContext context, EventStateConfigNode stateConfigNode) {
        if (CollectionUtils.isEmpty(stateConfigNode.getModelStateConfigNodes())) {
            return new ArrayList<>();
        }

        List<String> modelKeys = stateConfigNode.getModelStateConfigNodes()
                .stream()
                .filter(Objects::nonNull)
                .map(EventStateConfigNode.ModelStateConfigNode::getModelAlisa)
                .collect(Collectors.toList());
        List<MetaTreeNodeExt> modelMetaList = metaQueryService.findByKeyIn(context, modelKeys);
        Map<String, MetaTreeNode> modelMetaMap = modelMetaList.stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        Map<String, MetaTreeNodeExt> moduleMetaMap = metaQueryService.findByKeyIn(context, modelMetaList.stream().map(MetaTreeNode::getParentKey).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));


        List<EventActionRelStateNode> states = new ArrayList<>();
        stateConfigNode.getModelStateConfigNodes().stream()
                .filter(Objects::nonNull)
                .forEach(modelStateConfigNode -> modelStateConfigNode.getTransitionNodes().forEach(transitionNode -> {
                    EventActionRelStateNode eventActionRelStateNode = new EventActionRelStateNode();

                    if (!CollectionUtils.isEmpty(transitionNode.getConditions())) {
                        eventActionRelStateNode.setConditions(transitionNode.getConditions());
                    }

                    eventActionRelStateNode.setModelKey(modelStateConfigNode.getModelAlisa());
                    eventActionRelStateNode.setFieldKey(modelStateConfigNode.getFieldAlisa());
                    eventActionRelStateNode.setSourceState(transitionNode.getSourceState());
                    eventActionRelStateNode.setTargetState(transitionNode.getTargetState());
                    if (modelMetaMap.containsKey(modelStateConfigNode.getModelAlisa())) {
                        MetaTreeNode modelMeta = modelMetaMap.get(modelStateConfigNode.getModelAlisa());
                        eventActionRelStateNode.setModule(modelMeta.getParentKey());
                        if (moduleMetaMap.containsKey(modelMeta.getParentKey())) {
                            MetaTreeNodeExt moduleMeta = moduleMetaMap.get(modelMeta.getParentKey());
                            eventActionRelStateNode.setModuleName(moduleMeta.getName());
                            eventActionRelStateNode.setTeamId(moduleMeta.getTeamId());
                            eventActionRelStateNode.setAppId(moduleMeta.getAppId());
                        }
                    }
                    states.add(eventActionRelStateNode);
                }));
        return states;
    }

}
