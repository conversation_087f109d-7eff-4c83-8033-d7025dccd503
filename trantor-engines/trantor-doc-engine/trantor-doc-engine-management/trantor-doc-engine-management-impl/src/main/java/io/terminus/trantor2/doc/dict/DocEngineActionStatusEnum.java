package io.terminus.trantor2.doc.dict;

/**
 * <AUTHOR>
 */

public enum DocEngineActionStatusEnum {

    /**
     * 草稿
     */
    DRAFT("draft"),

    /**
     * 启用
     */
    ENABLED("enabled"),

    /**
     * 禁用
     */
    DISABLED("disabled");

    /**
     * 状态
     */
    private final String status;

    public String getStatus() {
        return status;
    }

    DocEngineActionStatusEnum(String status) {
        this.status = status;
    }

}
