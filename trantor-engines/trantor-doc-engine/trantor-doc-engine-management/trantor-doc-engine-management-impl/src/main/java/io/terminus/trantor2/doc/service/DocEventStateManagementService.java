package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Throwables;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.node.EventStateConfigNode;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.meta.api.dto.*;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.api.DataStructNodeApi;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldProperties;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.model.QueryByAliasRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/13 10:22 上午
 */
@Deprecated
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEventStateManagementService {

    private final MetaQueryService metaQueryService;
    private final MetaEditService metaEditService;
    private final DataStructNodeApi dataStructNodeApi;

    /**
     * 保存事件状态配置
     *
     * @param eventStateConfigNode
     */
    public void saveEventStateConfig(EventStateConfigNode eventStateConfigNode) {
        eventStateConfigNode.checkParam();

        buildModelRelation(eventStateConfigNode);

        MetaTreeNode stateRootNode = new MetaTreeNode();
        stateRootNode.setType(DocEngineMetaTypeConstant.STATE_CONFIG_ROOT);
        String nodeKey = eventStateConfigNode.getKey();
        stateRootNode.setKey(nodeKey);
        stateRootNode.setName("事件状态配置子树根节点");
        stateRootNode.setParentKey(eventStateConfigNode.getEventCode());

        List<MetaTreeNode> metaNode = eventStateConfigNode.getModelStateConfigNodes().stream()
            .map(item -> {
                MetaTreeNode metaTreeNode = new MetaTreeNode();
                metaTreeNode.setKey(item.getKey(eventStateConfigNode.getEventCode()));
                metaTreeNode.setType(DocEngineMetaTypeConstant.STATE_CONFIG);
                metaTreeNode.setParentKey(nodeKey);
                metaTreeNode.setName(item.getName());
                ObjectNode config = ObjectJsonUtil.MAPPER.valueToTree(item).deepCopy();
                metaTreeNode.setProps(config);
                return metaTreeNode;
            })
            .collect(Collectors.toList());
        metaNode.add(stateRootNode);
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        Boolean existIndex = metaQueryService.queryInTeam(context.getTeamId()).existsByKey(nodeKey);
        EditOpRequest treeOp;
        if (Boolean.FALSE.equals(existIndex)) {
            MoveTarget moveTarget = new MoveTarget();
            moveTarget.setTargetKey(eventStateConfigNode.getEventCode());
            moveTarget.setTargetType(MoveTargetType.ChildLast);
            treeOp = EditUtil.createTreeOp(metaNode, nodeKey, moveTarget);
        } else {
            treeOp = EditUtil.updateTreeOp(metaNode, nodeKey);
        }

        metaEditService.submitOp(context, treeOp);
    }

    /**
     * 通过事件key获取状态配置
     *
     * @param eventCode
     * @return
     */
    public EventStateConfigNode getStateConfigByEventKey(String eventCode) {
        EventStateConfigNode eventStateConfigNode = new EventStateConfigNode();
        eventStateConfigNode.setEventCode(eventCode);

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        List<MetaTreeNodeExt> metaTreeNodes = metaQueryService.findSubTree(context, eventStateConfigNode.getKey());
        metaTreeNodes = metaTreeNodes.stream().filter(node -> DocEngineMetaTypeConstant.STATE_CONFIG.equals(node.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metaTreeNodes)) {
            return eventStateConfigNode;
        }

        List<EventStateConfigNode.ModelStateConfigNode> modelStateConfigNodes = metaTreeNodes.stream()
            .map(item -> {
                try {
                    return ObjectJsonUtil.MAPPER.treeToValue(item.getProps(), EventStateConfigNode.ModelStateConfigNode.class);
                } catch (JsonProcessingException e) {
                    log.error("state config get fail,because object node convert state config node fail:{}", Throwables.getStackTraceAsString(e));
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        eventStateConfigNode.setModelStateConfigNodes(modelStateConfigNodes);

        return eventStateConfigNode;
    }

    /**
     * 建立状态配置模型之间的关系
     *
     * @param configNode
     */
    private void buildModelRelation(EventStateConfigNode configNode) {

        if (configNode.getModelStateConfigNodes().size() == 1) {
            return;
        }

        List<String> modelAlias = configNode.getModelStateConfigNodes().stream()
            .map(EventStateConfigNode.ModelStateConfigNode::getModelAlisa)
            .collect(Collectors.toList());

        QueryByAliasRequest queryByAliasRequest = new QueryByAliasRequest(modelAlias);
        queryByAliasRequest.setTeamId(TrantorContext.getTeamId());
        Response<List<DataStructNode>> modelStructs = dataStructNodeApi.findByAlias(queryByAliasRequest);

        modelStructs.getData().forEach(model -> {
            model.getChildren().forEach(field -> {
                if (field.getProps() == null) {
                    return;
                }

                DataStructFieldProperties props = field.getProps();

                if (!FieldType.OBJECT.equals(props.getFieldType())) {
                    return;
                }

                if (props.getRelationMeta() == null) {
                    return;
                }

                RelationMeta relationMeta = props.getRelationMeta();
                if (!modelAlias.contains(relationMeta.getRelationModelAlias())) {
                    return;
                }

                if (!ModelRelationTypeEnum.LINK.equals(relationMeta.getRelationType())) {
                    return;
                }

                EventStateConfigNode.ModelStateConfigNode relationModel = configNode.getByModelAlias(relationMeta.getRelationModelAlias());
                EventStateConfigNode.ModelStateConfigNode currentModel = configNode.getByModelAlias(relationMeta.getCurrentModelAlias());

                EventStateConfigNode.ModelRelationInfo modelRelationInfo = new EventStateConfigNode.ModelRelationInfo();
                modelRelationInfo.setRelatedFieldAlias(field.getAlias());
                modelRelationInfo.setRelatedModelAlias(relationModel.getModelAlisa());
                configNode.addModelRelated(currentModel.getModelAlisa(), modelRelationInfo);
            });
        });
    }
}
