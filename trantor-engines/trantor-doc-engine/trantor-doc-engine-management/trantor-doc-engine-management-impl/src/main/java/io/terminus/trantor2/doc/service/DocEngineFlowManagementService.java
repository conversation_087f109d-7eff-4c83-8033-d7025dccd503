package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.dto.FlowDTO;
import io.terminus.trantor2.doc.api.dto.FlowViewDTO;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.api.props.FlowEventRelProps;
import io.terminus.trantor2.doc.api.props.FlowProps;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.exception.DocEngineActionManagementException;
import io.terminus.trantor2.doc.request.DocEngineFlowPageQueryRequest;
import io.terminus.trantor2.meta.api.dto.*;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEngineFlowManagementService {

    private final MetaQueryService metaQueryService;
    private final MetaEditService metaEditService;
    private final DocEngineFlowViewService docEngineFlowViewService;

    public Paging<FlowDTO> page(DocEngineFlowPageQueryRequest request) {
        List<Cond> condList = new ArrayList<>();
        condList.add(Field.type().equal(DocEngineMetaTypeConstant.FLOW));
        if (StringUtils.hasLength(request.getModule())) {
            condList.add(Field.parentKey().equal(request.getModule()));
        }
        if (StringUtils.hasLength(request.getSearch())) {
            Cond searchCond = Cond.or(Field.key().like("%" + request.getSearch() + "%"), Field.name().like("%" + request.getSearch() + "%"));
            condList.add(searchCond);
        }
        Cond cond = Cond.and(condList.toArray(new Cond[condList.size()]));

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        Paging<MetaTreeNodeExt> pageResult = metaQueryService.queryInApp(context).findPageData(cond,
            PageReq.of(request.getPageNo() - 1, request.getPageSize(), Order.byKey()));
        return new Paging<>(pageResult.getTotal(), this.meta2Dto(pageResult.getData()));
    }

    public FlowDTO queryById(Long flowId) {
        MetaTreeNodeExt entity = metaQueryService.findById(flowId)
            .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "flow.not.existed"));
        return this.meta2Dto(entity);
    }

    public FlowDTO create(FlowDTO flow) {
        this.commonValidate(flow);

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        Assert.isTrue(Boolean.FALSE.equals(metaQueryService.queryInApp(context).existsByKey(flow.getKey())), "flow.has.existed");

        MetaTreeNode flowMetaNode = this.dto2Meta(flow);
        EditOpRequest createEventRequest = EditUtil.createNodeOp(flowMetaNode, new MoveTarget(flow.getModule(), MoveTargetType.ChildLast));
        Map<String, MetaTreeNodeExt> resultMap = metaEditService.submitOp(context, createEventRequest);
        if(resultMap.containsKey(flowMetaNode.getKey())) {
            flow.setId(resultMap.get(flowMetaNode.getKey()).getId());
        }else {
            throw new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "flow.create.failed");
        }

        return flow;
    }

    public FlowDTO update(FlowDTO flow) {
        this.commonValidate(flow);

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        Assert.isTrue(Boolean.TRUE.equals(metaQueryService.queryInApp(context).existsByKey(flow.getKey())), "flow.not.existed");

        MetaTreeNode flowMetaNode = this.dto2Meta(flow);
        metaEditService.submitOp(context, EditUtil.updateNodeOp(flowMetaNode));

        return flow;
    }

    public void delete(FlowDTO flow) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        metaEditService.submitOp(context, EditUtil.deleteNodeOp(flow.getKey(), true));
    }

    public FlowViewDTO view(Long flowId) {
        MetaTreeNodeExt entity = metaQueryService.findById(flowId)
            .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.RESOURCE_NOT_FOUND, "flow.not.existed"));
        return docEngineFlowViewService.buildView(entity);
    }

    public FlowViewDTO preView(FlowDTO flow) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        MetaTreeNode flowMetaNode = this.dto2Meta(flow);
        return docEngineFlowViewService.buildView(flowMetaNode);
    }

    private void commonValidate(FlowDTO flow) {
        Assert.isTrue(StringUtils.hasLength(flow.getKey()), "key.is.empty");
        Assert.isTrue(StringUtils.hasLength(flow.getModelAlias()), "model.alias.is.empty");
        Assert.isTrue(StringUtils.hasLength(flow.getModule()), "module.is.empty");
        Assert.isTrue(!CollectionUtils.isEmpty(flow.getEvents()), "events.is.empty");
    }

    private MetaTreeNode dto2Meta(FlowDTO flow) {
        MetaTreeNode flowMetaNode = new MetaTreeNode();
        flowMetaNode.setType(DocEngineMetaTypeConstant.FLOW);
        flowMetaNode.setName(flow.getName());
        flowMetaNode.setKey(flow.getKey());
        flowMetaNode.setParentKey(flow.getModule());

        FlowProps flowProps = new FlowProps();
        flowProps.setDesc(flow.getDesc());
        flowProps.setModelAlias(flow.getModelAlias());

        List<FlowEventRelProps> events = flow.getEvents().stream().map(event -> {
            FlowEventRelProps rel = new FlowEventRelProps();
            rel.setEventCode(event.getCode());

            return rel;
        }).collect(Collectors.toList());
        flowProps.setEvents(events);

        flowMetaNode.setProps(ObjectJsonUtil.MAPPER.valueToTree(flowProps));

        return flowMetaNode;
    }

    private FlowDTO meta2Dto(MetaTreeNodeExt meta) {
        FlowDTO flow = new FlowDTO();
        flow.setId(meta.getId());
        flow.setKey(meta.getKey());
        flow.setName(meta.getName());
        flow.setModule(meta.getParentKey());
        flow.setAccess(meta.getAccess());

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        metaQueryService.findByKey(context, meta.getParentKey())
            .ifPresent(moduleMetaNode -> flow.setModuleName(moduleMetaNode.getName()));

        FlowProps flowProps;
        try {
            flowProps = ObjectJsonUtil.MAPPER.treeToValue(meta.getProps(), FlowProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        flow.setDesc(flowProps.getDesc());
        flow.setModelAlias(flowProps.getModelAlias());

        metaQueryService.findByKey(context, flowProps.getModelAlias())
            .ifPresent(modelMetaNode -> {
                try {
                    DataStructNode dataStructNode = ObjectJsonUtil.MAPPER.treeToValue(modelMetaNode.getProps(), DataStructNode.class);
                    flow.setModelName(dataStructNode.getName());
                } catch (Exception e) {
                    log.warn("[Flow] 模型名称获取失败, alias : {}", flowProps.getModelAlias(), e);
                }
            });

        List<String> eventCodes = flowProps.getEvents().stream().map(FlowEventRelProps::getEventCode).collect(Collectors.toList());
        Map<String, EventDTO> eventMap = metaQueryService.findByKeyIn(context, eventCodes)
            .stream()
            .map(eventMetaNode -> {
                EventDTO eventDTO = new EventDTO();
                eventDTO.setCode(eventMetaNode.getKey());
                eventDTO.setName(eventMetaNode.getName());

                EventProps eventProps;
                try {
                    eventProps = ObjectJsonUtil.MAPPER.treeToValue(eventMetaNode.getProps(), EventProps.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                eventDTO.setDesc(eventProps.getDesc());
                return eventDTO;
            }).collect(Collectors.toMap(EventDTO::getCode, Function.identity()));
        List<EventDTO> events = flowProps.getEvents().stream().map(event -> eventMap.get(event.getEventCode())).collect(Collectors.toList());
        flow.setEvents(events);

        return flow;
    }

    private List<FlowDTO> meta2Dto(List<MetaTreeNodeExt> metaList) {
        return metaList.stream().map(flowMetaNode -> {
            FlowDTO flow = new FlowDTO();
            flow.setId(flowMetaNode.getId());
            flow.setKey(flowMetaNode.getKey());
            flow.setName(flowMetaNode.getName());
            flow.setAccess(flowMetaNode.getAccess());
            return flow;
        }).collect(Collectors.toList());
    }

}
