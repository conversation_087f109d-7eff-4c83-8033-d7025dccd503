package io.terminus.trantor2.doc.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.doc.service.DocEngineEventSyncService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "Event同步")
@RestController
@RequestMapping(path = "/api/trantor/console/doc/engine/eventSync")
public class DocEngineEventSyncController {

    @Resource
    private DocEngineEventSyncService docEngineEventSyncService;

    @PostMapping("/sync")
    @Operation(summary = "Event同步")
    public Response<Void> sync(@RequestParam Long teamId, @RequestParam Long userId) {
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
        }
        TrantorContext.setTeamId(teamId);
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);
        docEngineEventSyncService.syncEvent2Service();
        return Response.ok();
    }

    @PostMapping("/syncConditions")
    @Operation(summary = "Event同步条件")
    public Response<Void> syncConditions() {
        docEngineEventSyncService.syncConditions();
        return Response.ok();
    }

    @PostMapping("/syncCondition/{eventKey}")
    @Operation(summary = "Event同步条件")
    public Response<Void> syncCondition(@PathVariable("eventKey") String eventKey) {
        docEngineEventSyncService.syncCondition(eventKey);
        return Response.ok();
    }
}
