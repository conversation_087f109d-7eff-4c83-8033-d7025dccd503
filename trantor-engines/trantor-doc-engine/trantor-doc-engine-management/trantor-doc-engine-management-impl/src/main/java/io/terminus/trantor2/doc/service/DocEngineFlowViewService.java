package io.terminus.trantor2.doc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.*;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.ActionMeta;
import io.terminus.trantor2.doc.api.dto.FlowViewDTO;
import io.terminus.trantor2.doc.api.node.EventActionRelNode;
import io.terminus.trantor2.doc.api.node.EventActionRelStateNode;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.api.props.FlowEventRelProps;
import io.terminus.trantor2.doc.api.props.FlowProps;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.exception.DocEngineActionManagementException;
import io.terminus.trantor2.doc.repo.ActionRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataDictProperties;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocEngineFlowViewService {

    private static final String LABEL = "label";
    private static final String VALUE = "value";
    private static final String STATE_INIT_LABEL = "初始化";
    private static final String STATE_INIT_VALUE = "INIT";

    private final MetaQueryService metaQueryService;
    private final ActionRepo actionRepo;

    public FlowViewDTO buildView(MetaTreeNode flowMetaNode) {
        FlowProps flowProps;
        try {
            flowProps = ObjectJsonUtil.MAPPER.treeToValue(flowMetaNode.getProps(), FlowProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        List<FlowViewDTO.Event> events = this.queryEvents(context, flowMetaNode);
        List<FlowViewDTO.Node> nodes = this.buildNodes(context, flowProps.getModelAlias(), events);
        List<FlowViewDTO.Lane> lanes = this.buildLanes(nodes);
        List<FlowViewDTO.Edge> edges = this.buildEdges(nodes);

        FlowViewDTO dto = new FlowViewDTO();
        dto.setNodes(nodes);
        dto.setLanes(lanes);
        dto.setEdges(edges);
        return dto;
    }

    public List<FlowViewDTO.Event> queryEvents(MetaEditAndQueryContext context, MetaTreeNode flowMetaNode) {
        FlowProps flowProps;
        try {
            flowProps = ObjectJsonUtil.MAPPER.treeToValue(flowMetaNode.getProps(), FlowProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        List<String> eventCodes = flowProps.getEvents().stream().map(FlowEventRelProps::getEventCode).collect(Collectors.toList());
        Map<String, MetaTreeNode> eventMetaNodeMap = metaQueryService.findByKeyIn(context, eventCodes)
            .stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        List<FlowViewDTO.Event> events = eventCodes.stream().filter(eventMetaNodeMap::containsKey)
            .map(eventCode -> {
                MetaTreeNode eventMetaNode = eventMetaNodeMap.get(eventCode);

                FlowViewDTO.Event event = new FlowViewDTO.Event();
                event.setCode(eventMetaNode.getKey());
                event.setName(eventMetaNode.getName());
                event.setModule(eventMetaNode.getParentKey());

                EventProps eventProps;
                try {
                    eventProps = ObjectJsonUtil.MAPPER.treeToValue(eventMetaNode.getProps(), EventProps.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }

                List<EventActionRelNode> actions = new ArrayList<>();
                if(!CollectionUtils.isEmpty(eventProps.getRelations())) {
                    eventProps.getRelations().stream().forEach(rel -> {
                        EventActionRelNode node = new EventActionRelNode();
                        node.setActionType(rel.getActionType());
                        node.setCode(rel.getCode());
                        actions.add(node);
                    });
                }
                event.setActions(actions);

                // 只关心Flow的主体模型的状态变更
                List<EventActionRelStateNode> states = eventProps.getStates()
                    .stream().filter(state -> flowProps.getModelAlias().equals(state.getModelKey())).collect(Collectors.toList());
                event.setStates(states);

                return event;
            }).collect(Collectors.toList());

        //补充Action名称
        Map<String, ActionMeta> actionMap = actionRepo.findAllByKeys(
            events.stream().flatMap(event -> event.getActions().stream())
                .filter(action -> DocEngineMetaTypeConstant.ACTION.equals(action.getActionType()))
                .map(EventActionRelNode::getCode).collect(Collectors.toList()), ResourceContext.newResourceCtx(context)
        ).stream().collect(Collectors.toMap(ActionMeta::getKey, Function.identity()));
        events.stream().flatMap(event -> event.getActions().stream())
            .filter(action -> DocEngineMetaTypeConstant.ACTION.equals(action.getActionType()))
            .filter(action -> actionMap.containsKey(action.getCode()))
            .forEach(action -> action.setName(actionMap.get(action.getCode()).getName()));

        //补充eventAction名称
        Map<String, MetaTreeNode> eventMap = metaQueryService.findByKeyIn(
            context,
            events.stream().flatMap(event -> event.getActions().stream())
                .filter(action -> DocEngineMetaTypeConstant.EVENT.equals(action.getActionType()))
                .map(EventActionRelNode::getCode).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        events.stream().flatMap(event -> event.getActions().stream())
            .filter(action -> DocEngineMetaTypeConstant.EVENT.equals(action.getActionType()))
            .filter(action -> eventMap.containsKey(action.getCode()))
            .forEach(action -> action.setName(eventMap.get(action.getCode()).getName()));

        //补充模块名称
        Map<String, MetaTreeNode> moduleMap = metaQueryService.findByKeyIn(
            context,
            events.stream().map(FlowViewDTO.Event::getModule).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        events.stream().filter(event -> moduleMap.containsKey(event.getModule()))
            .forEach(event -> event.setModuleName(moduleMap.get(event.getModule()).getName()));

        return events;
    }

    private List<FlowViewDTO.Node> buildNodes(MetaEditAndQueryContext context, String modelAlias, List<FlowViewDTO.Event> events) {
        Map<String, DataStructFieldNode> dictMap = this.getDictMap(context, modelAlias);

        List<FlowViewDTO.Node> nodes = events.stream().map(event -> {
            FlowViewDTO.Node node = new FlowViewDTO.Node();
            node.setKey(event.getCode());
            node.setLabel(event.getName());
            node.setProps(event);
            node.setLaneKey(event.getModule());
            return node;
        }).collect(Collectors.toList());

        Multimap<String, FlowViewDTO.Node> sourceStateNodeMap = HashMultimap.create();
        nodes.stream()
            .filter(node -> !CollectionUtils.isEmpty(node.getProps().getStates()))
            .forEach(node -> node.getProps().getStates().forEach(state -> sourceStateNodeMap.put(getSourceKey(state), node)));

        // 存储所有被作为下一个节点的key 用于后续找到头节点
        Set<String> nextNodeKeys = Sets.newHashSetWithExpectedSize(nodes.size());

        for (int i = 0; i < nodes.size(); i++) {
            FlowViewDTO.Node node = nodes.get(i);
            if (CollectionUtils.isEmpty(node.getProps().getStates()) && i != (nodes.size() - 1)) {
                FlowViewDTO.Node nextNode = nodes.get(i + 1);

                FlowViewDTO.Edge edge = new FlowViewDTO.Edge();
                edge.setKey(getEdgeKey(node, nextNode));
                edge.setSource(node.getKey());
                edge.setTarget(nextNode.getKey());
                nextNodeKeys.add(nextNode.getKey());
                node.setEdges(Lists.newArrayList(edge));
            } else if (!CollectionUtils.isEmpty(node.getProps().getStates())) {
                List<FlowViewDTO.Edge> edges = new ArrayList<>();
                node.getProps().getStates().stream()
                    .filter(state -> sourceStateNodeMap.containsKey(getTargetKey(state)))
                    .forEach(state -> sourceStateNodeMap.get(getTargetKey(state)).forEach(targetNode -> {
                        FlowViewDTO.Edge edge = new FlowViewDTO.Edge();
                        edge.setKey(getEdgeKey(node, targetNode));
                        edge.setSource(node.getKey());
                        edge.setTarget(targetNode.getKey());
                        nextNodeKeys.add(targetNode.getKey());

                        FlowViewDTO.EdgeLabel edgeLabel = new FlowViewDTO.EdgeLabel();
                        if (dictMap.containsKey(state.getFieldKey())) {
                            edgeLabel.setLabel(this.getEdgeLabel(dictMap.get(state.getFieldKey()), state));
                        }
                        if (!CollectionUtils.isEmpty(targetNode.getProps().getStates())) {
                            targetNode.getProps().getStates().stream()
                                .filter(targetState -> targetState.getFieldKey().equals(state.getFieldKey()))
                                .filter(targetState -> targetState.getSourceState().equals(state.getTargetState()))
                                .findFirst().ifPresent(targetState -> edgeLabel.setConditions(targetState.getConditions()));
                        }
                        edge.setLabels(Lists.newArrayList(edgeLabel));
                        edges.add(edge);
                    }));

                // 后续没有状态的节点要挂上线
                if (i != (nodes.size() - 1)) {
                    FlowViewDTO.Node nextNode = nodes.get(i + 1);
                    if (CollectionUtils.isEmpty(nextNode.getProps().getStates())) {
                        FlowViewDTO.Edge edge = new FlowViewDTO.Edge();
                        edge.setKey(getEdgeKey(node, nextNode));
                        edge.setSource(node.getKey());
                        edge.setTarget(nextNode.getKey());
                        nextNodeKeys.add(nextNode.getKey());
                        edges.add(edge);
                    }
                }

                node.setEdges(edges);
            } else {
                node.setEdges(new ArrayList<>());
            }
        }

        // 重新排序 方便后续生成 泳道 和 坐标
        // 所有node
        Map<String, FlowViewDTO.Node> nodeMap = nodes.stream().collect(Collectors.toMap(FlowViewDTO.Node::getKey, Function.identity()));
        // 重排后的结果
        List<FlowViewDTO.Node> result = new ArrayList<>(nodes.size());
        // 已重排过的节点
        Set<String> sortedNodeKeys = Sets.newHashSetWithExpectedSize(nodes.size());
        // 头节点key 环结构找不到头节点时默认列表第一个为头节点
        String firstKey = nodes.stream()
            .map(FlowViewDTO.Node::getKey)
            .filter(key -> !nextNodeKeys.contains(key))
            .findFirst().orElse(nodes.get(0).getKey());
        // 递归排序
        this.resort(result, nodeMap, sortedNodeKeys, firstKey);
        // 存在部分未连线的情况（已知是有状态变更但连不上线） 单独放最后
        if(result.size() < nodes.size()) {
            Set<String> resultKeys = result.stream().map(FlowViewDTO.Node::getKey).collect(Collectors.toSet());
            nodes.stream().filter(node -> !resultKeys.contains(node.getKey())).forEach(result::add);
        }

        return result;
    }

    private void resort(List<FlowViewDTO.Node> result, Map<String, FlowViewDTO.Node> nodeMap, Set<String> sortedNodeKeys, String nodeKey) {
        // 避免环结构导致的无限递归
        if (sortedNodeKeys.contains(nodeKey)) {
            return;
        }

        FlowViewDTO.Node node = nodeMap.get(nodeKey);
        result.add(node);
        sortedNodeKeys.add(nodeKey);

        node.getEdges().forEach(edge -> this.resort(result, nodeMap, sortedNodeKeys, edge.getTarget()));
    }

    private Map<String, DataStructFieldNode> getDictMap(MetaEditAndQueryContext context, String modelAlias) {
        MetaTreeNode modelMetaNode = metaQueryService.findByKey(context, modelAlias)
            .orElseThrow(() -> new DocEngineActionManagementException(ErrorType.SERVER_ERROR, "model.not.existed"));

        DataStructNode dataStructNode;
        try {
            dataStructNode = ObjectJsonUtil.MAPPER.treeToValue(modelMetaNode.getProps(), DataStructNode.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return dataStructNode.getChildren().stream()
            .filter(fieldNode -> fieldNode.getProps().getFieldType().equals(FieldType.ENUM))
            .collect(Collectors.toMap(DataStructFieldNode::getAlias, Function.identity()));
    }

    private String getSourceKey(EventActionRelStateNode stateNode) {
        return String.format("%s_%s", stateNode.getFieldKey(), stateNode.getSourceState());
    }

    private String getTargetKey(EventActionRelStateNode stateNode) {
        return String.format("%s_%s", stateNode.getFieldKey(), stateNode.getTargetState());
    }

    private String getEdgeKey(FlowViewDTO.Node source, FlowViewDTO.Node target) {
        return String.format("%s_%s", source.getKey(), target.getKey());
    }

    private String getEdgeLabel(DataStructFieldNode dataStructFieldNode, EventActionRelStateNode stateNode) {
        DataDictProperties dictProperties = dataStructFieldNode.getProps().getDictPros();

        AtomicReference<String> sourceStateName = new AtomicReference<>("");
        if (STATE_INIT_VALUE.equals(stateNode.getSourceState())) {
            sourceStateName.set(STATE_INIT_LABEL);
        } else {
            dictProperties.getDictValues().stream()
                .filter(map -> map.get(VALUE).equals(stateNode.getSourceState()))
                .findFirst().ifPresent(map -> sourceStateName.set((String) map.get(LABEL)));
        }

        AtomicReference<String> targetStateName = new AtomicReference<>("");
        dictProperties.getDictValues().stream()
            .filter(map -> map.get(VALUE).equals(stateNode.getTargetState()))
            .findFirst().ifPresent(map -> targetStateName.set((String) map.get(LABEL)));

        return String.format("%s : %s -> %s", dataStructFieldNode.getName(), sourceStateName.get(), targetStateName.get());
    }

    private List<FlowViewDTO.Lane> buildLanes(List<FlowViewDTO.Node> nodes) {
        Map<String, FlowViewDTO.Position> moduleMap = Maps.newHashMapWithExpectedSize(nodes.size());
//        Map<String, FlowViewDTO.Node> nodeMap = nodes.stream().collect(Collectors.toMap(FlowViewDTO.Node::getKey, Function.identity()));
        List<FlowViewDTO.Lane> lanes = new ArrayList<>(nodes.size());

        Integer positionX = 0;
        for (int i = 0; i < nodes.size(); i++) {
            FlowViewDTO.Node node = nodes.get(i);

            FlowViewDTO.Position position;
            if (moduleMap.containsKey(node.getProps().getModule())) {
                position = moduleMap.get(node.getProps().getModule());
            } else {
                FlowViewDTO.Lane lane = new FlowViewDTO.Lane();
                lane.setKey(node.getProps().getModule());
                lane.setLabel(node.getProps().getModuleName());
                lanes.add(lane);

                FlowViewDTO.Position initPosition = new FlowViewDTO.Position();
                initPosition.setX(positionX);
                initPosition.setY(0);
                positionX++;
                position = initPosition;
            }

            FlowViewDTO.Position nodePosition = new FlowViewDTO.Position(position.getX(), position.getY());
            // 特殊逻辑 为兼容前端展示 下一个节点需要指向其他泳道的下一行时 提前将本节点下移
//            if (!CollectionUtils.isEmpty(node.getEdges())) {
//                List<FlowViewDTO.Node> nextNodes = node.getEdges().stream().map(edge -> nodeMap.get(edge.getTarget())).collect(Collectors.toList());
//                nextNodes.stream()
//                    .filter(nextNode -> moduleMap.containsKey(nextNode.getProps().getModule()))
//                    .filter(nextNode -> {
//                        FlowViewDTO.Position nextPosition = moduleMap.get(nextNode.getProps().getModule());
//                        return !nextPosition.getX().equals(nodePosition.getX()) && nextPosition.getY().compareTo(nodePosition.getY()) > 0;
//                    }).findFirst().ifPresent(nextNode -> nodePosition.setY(nodePosition.getY() + 1));
//            }

            node.setPosition(nodePosition);

            position.setY(nodePosition.getY() + 1);
            moduleMap.put(node.getProps().getModule(), position);
        }

        return lanes;
    }

    private List<FlowViewDTO.Edge> buildEdges(List<FlowViewDTO.Node> nodes) {
        Map<String, List<FlowViewDTO.Edge>> edgeMap = nodes.stream()
            .flatMap(node -> node.getEdges().stream())
            .collect(Collectors.groupingBy(FlowViewDTO.Edge::getKey));
        return edgeMap.values().stream()
            .map(edges -> {
                FlowViewDTO.Edge edge = new FlowViewDTO.Edge();
                FlowViewDTO.Edge firstEdge = edges.get(0);
                edge.setKey(firstEdge.getKey());
                edge.setSource(firstEdge.getSource());
                edge.setTarget(firstEdge.getTarget());
                List<FlowViewDTO.EdgeLabel> labels = edges.stream()
                    .filter(one -> !CollectionUtils.isEmpty(one.getLabels()))
                    .flatMap(one -> one.getLabels().stream())
                    .collect(Collectors.toList());
                edge.setLabels(labels);
                return edge;
            })
            .collect(Collectors.toList());
    }

}
