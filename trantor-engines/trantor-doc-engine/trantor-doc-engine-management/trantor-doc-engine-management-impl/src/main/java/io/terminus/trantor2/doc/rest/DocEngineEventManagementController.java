package io.terminus.trantor2.doc.rest;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.node.EventStateConfigNode;
import io.terminus.trantor2.doc.request.DocEngineEventPageQueryRequest;
import io.terminus.trantor2.doc.service.DocEngineEventManagementService;
import io.terminus.trantor2.doc.service.DocEventStateManagementService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "Event管理")
@RestController
@RequestMapping(path = "/api/trantor/console/doc/engine/event")
public class DocEngineEventManagementController {

    @Resource
    private DocEngineEventManagementService docEngineEventManagementService;

    @Resource
    private DocEventStateManagementService docEventStateManagementService;

    @PostMapping("/page")
    @Operation(summary = "翻页查询Event")
    @InjectUserInfos
    public Response<Paging<EventDTO>> page(@RequestBody DocEngineEventPageQueryRequest request) {
        return Response.ok(docEngineEventManagementService.page(request));
    }

    @GetMapping("/query/by/id")
    @Operation(summary = "按id查询Event")
    public Response<EventDTO> queryById(@RequestParam Long eventId) {
        return Response.ok(docEngineEventManagementService.queryById(eventId));
    }

    @GetMapping("/query/by/key")
    @Operation(summary = "按key查询Event")
    public Response<EventDTO> queryByKey(@RequestParam String eventKey) {
        return Response.ok(docEngineEventManagementService.queryByKey(eventKey));
    }

    @PostMapping("/create")
    @Operation(summary = "新增Event")
    public Response<EventDTO> create(@RequestBody EventDTO event) {
        return Response.ok(docEngineEventManagementService.create(event));
    }

    @PostMapping("/update")
    @Operation(summary = "更新Event")
    public Response<EventDTO> update(@RequestBody EventDTO event) {
        return Response.ok(docEngineEventManagementService.update(event));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除Event")
    public Response<Void> delete(@RequestBody EventDTO event) {
        docEngineEventManagementService.delete(event);
        return Response.ok();
    }


    // 临时使用
    @PostMapping(path = "/saveStateConfig")
    public Response<Boolean> saveStateConfig(@RequestBody EventStateConfigNode configNode) {
        docEventStateManagementService.saveEventStateConfig(configNode);
        return Response.ok();
    }
}
