package io.terminus.trantor2.doc.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.doc.request.DocEngineActionPageQueryRequest;
import io.terminus.trantor2.doc.service.DocEngineActionManagementService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "Action管理")
@RestController
@RequestMapping(path = "/api/trantor/console/doc/engine/action")
public class DocEngineActionManagementController {

    @Resource
    private DocEngineActionManagementService docEngineActionManagementService;

    @PostMapping("/page")
    @Operation(summary = "翻页查询Action")
    @InjectUserInfos
    public Response<Paging<ActionDTO>> page(@RequestBody DocEngineActionPageQueryRequest request) {
        return Response.ok(docEngineActionManagementService.page(request));
    }

    @GetMapping("/query/by/id")
    @Operation(summary = "按id查询Action")
    public Response<ActionDTO> queryById(@RequestParam Long actionId) {
        return Response.ok(docEngineActionManagementService.queryById(actionId));
    }

    @GetMapping("/query/by/key")
    @Operation(summary = "按key查询Action")
    public Response<ActionDTO> queryByKey(@RequestParam String actionKey) {
        return Response.ok(docEngineActionManagementService.queryByKey(actionKey));
    }

    @PostMapping("/create")
    @Operation(summary = "新增Action")
    public Response<ActionDTO> create(@RequestBody ActionDTO action) {
        return Response.ok(docEngineActionManagementService.create(action));
    }

    @PostMapping("/update")
    @Operation(summary = "更新Action")
    public Response<ActionDTO> update(@RequestBody ActionDTO action) {
        return Response.ok(docEngineActionManagementService.update(action));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除Action")
    public Response<Void> delete(@RequestBody ActionDTO action) {
        docEngineActionManagementService.delete(action);
        return Response.ok();
    }
}
