package io.terminus.trantor2.doc.rest;

import io.terminus.trantor2.doc.api.DocEngineActionApi;
import io.terminus.trantor2.doc.api.request.ActionUploadRequest;
import io.terminus.trantor2.doc.service.DocEngineActionManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class DocEngineActionLoadController implements DocEngineActionApi {

    private final DocEngineActionManagementService docEngineActionManagementService;

    @Override
    public void upload(@RequestBody ActionUploadRequest request) {
        docEngineActionManagementService.upload(request);
    }



}
