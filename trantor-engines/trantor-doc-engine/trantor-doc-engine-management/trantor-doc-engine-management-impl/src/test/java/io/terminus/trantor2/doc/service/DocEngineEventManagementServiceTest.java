package io.terminus.trantor2.doc.service;

import io.terminus.trantor2.ide.test.Trantor2IDETestApp;
import io.terminus.trantor2.meta.management.base.MetaBaseIntegrationTests;
import io.terminus.trantor2.model.management.meta.api.DataStructNodeApi;
import io.terminus.trantor2.service.management.api.management.ServiceMetaApi;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@SpringBootTest(
    classes = {Trantor2IDETestApp.class, DocEngineEventManagementServiceTest.DocConfig.class},
    properties = {
        "TRANTOR_LOCK_TIME_OUT=PT2S"
    }
)
class DocEngineEventManagementServiceTest extends MetaBaseIntegrationTests {

    @Configuration
    @ComponentScan(basePackages = {
        "io.terminus.trantor2.doc",
    })
    static class DocConfig {
    }

    @MockBean
    private DataStructNodeApi dataStructNodeApi;

    @MockBean
    private ServiceTemplateApi serviceTemplateApi;

    @MockBean
    private ServiceMetaApi serviceMetaApi;

    @Autowired
    private DocEngineEventManagementService docEngineEventManagementService;

    @Test
    void testCreate() {

    }

}
