<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-doc-engine-management</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-doc-engine-management-impl</artifactId>
    <packaging>jar</packaging>

    <name>trantor-doc-engine-management-impl</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-doc-engine-management-api</artifactId>
            <version>${project.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.terminus.trantor2</groupId>-->
<!--            <artifactId>trantor-doc-engine-runtime-api</artifactId>-->
<!--            <version>${project.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-rule-engine-management-impl</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <artifactId>trantor-service-management-api</artifactId>
            <groupId>io.terminus.trantor2</groupId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-impl-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-jsr223</artifactId>
            <version>${groovy-jsr223.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-management-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-ide-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
