<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-doc-engine-runtime</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>trantor-doc-engine-runtime-impl</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-doc-engine-runtime-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <artifactId>trantor-service-management-api</artifactId>
            <groupId>io.terminus.trantor2</groupId>
        </dependency>
        <dependency>
            <artifactId>trantor-service-runtime-api</artifactId>
            <groupId>io.terminus.trantor2</groupId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>operation-log-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-jsr223</artifactId>
            <version>${groovy-jsr223.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>terminus-notice-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.erp</groupId>
                    <artifactId>erp-framework-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-rule-engine-runtime-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
