package io.terminus.trantor2.doc;

import io.terminus.trantor2.doc.api.context.EventRequestContext;
import io.terminus.trantor2.doc.api.node.EventActionRelStateNode;
import io.terminus.trantor2.doc.api.node.EventStateConfigNode;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.meta.EventMeta;
import io.terminus.trantor2.doc.meta.EventMetaCache;
import io.terminus.trantor2.doc.state.StateEngineExecutor;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.rule.engine.client.RuleEngineExecutor;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/7/7 16:02
 */
public class StateTestUnit {
    @Mock
    private MetaCache metaCache;

    @Mock
    private EventMetaCache eventMetaCache;

    @Mock
    private RuleEngineExecutor ruleEngineExecutor;

    @Mock
    private DataStructDataApi dataStructDataApi;

    @InjectMocks
    private StateEngineExecutor stateEngineExecutor;

    @Test
    public void testExecute() {
        MockitoAnnotations.openMocks(this);

        // Prepare test data
        String eventCode = "test_event";
        EventRequestContext.init(eventCode);
        when(eventMetaCache.get(eventCode)).thenReturn(buildTestEventMeta(eventCode));


        TestModel testMode = new TestModel(1L, "INIT");

        // Execute the method
        stateEngineExecutor.execute(testMode);

        Assert.assertEquals("状态变更失败", testMode.getStatus(), "COMPLETE");

        // Verify the interactions with the mocks
        verify(eventMetaCache, times(1)).get(eventCode);
        verifyNoMoreInteractions(eventMetaCache);

        verify(ruleEngineExecutor, times(0)).executeResult(anyString(), anyMap());
        verifyNoMoreInteractions(ruleEngineExecutor);

        verify(dataStructDataApi, times(0)).batchUpdateById(any());
        verifyNoMoreInteractions(dataStructDataApi);
    }

    private EventMeta buildTestEventMeta(String evenCode) {
        EventMeta eventMeta = new EventMeta();
        eventMeta.setKey(evenCode);
        eventMeta.setName("测试事件");
        EventProps eventProps = new EventProps();
        EventActionRelStateNode eventActionRelStateNode = new EventActionRelStateNode();
        eventActionRelStateNode.setTeamId(1L);
        eventActionRelStateNode.setAppId(1L);
        eventActionRelStateNode.setModelKey("test_po");
        eventActionRelStateNode.setFieldKey("status");
        eventActionRelStateNode.setSourceState("INIT");
        eventActionRelStateNode.setTargetState("COMPLETE");
        eventActionRelStateNode.setModule("TEST");
        eventActionRelStateNode.setModuleName("测试");
        List<EventActionRelStateNode> list = Arrays.asList(eventActionRelStateNode);
        eventProps.setStates(list);
        eventMeta.setProps(eventProps);
        return eventMeta;
    }

}
