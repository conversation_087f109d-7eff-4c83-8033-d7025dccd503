package io.terminus.trantor2.doc.executor.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import io.terminus.trantor.workflow.runtime.v1.activiti.model.FieldRecord;
import io.terminus.trantor.workflow.sdk.core.Workflow;
import io.terminus.trantor.workflow.sdk.model.request.StartWorkflowByGroupRequest;
import io.terminus.trantor.workflow.sdk.model.response.StartWorkflowByGroupResponse;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.dto.ApprovalDTO;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.doc.executor.ModelDataDiff;
import io.terminus.trantor2.doc.executor.event.StartApprovalEvent;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.common.model.request.IdQueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/9 5:24 PM
 */
@Slf4j
@Component
public class EventApprovalActionListener {

    private final static String BEFORE = "before";
    private final static String AFTER = "after";

    @Resource
    private Workflow workflow;
    @Resource
    private MetaCache metaCache;

    @Resource
    private EventExecuteTool eventExecuteTool;

    @Autowired
    private ModelDataDiff modelDataDiff;

    public void subscribe(StartApprovalEvent event) {
        TrantorContext.setContext(event.getContext());

        JSON request = (JSON)JSON.toJSON(event.getRequest());
        ApprovalDTO approval = event.getActionExecuteNode().getApproval();

        // 通过流程组发起审批流实例
        StartWorkflowByGroupRequest workflowRequest = new StartWorkflowByGroupRequest();
        workflowRequest.setWorkflowGroupKey(approval.getCode());
        workflowRequest.setUserId(TrantorContext.getCurrentUserId().toString());

        // convert
        String convert = event.getActionExecuteNode().getEventActionRel().getConvert();
        if (StringUtils.isNotEmpty(convert)) {
            Object param = eventExecuteTool.convert(request, convert);
            workflowRequest.setBizObj(param);
        } else {
            // 按审批流入参要求 去除模型中不包含的字段信息
            if (request instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) request;
                if (this.judgeApprovalRequest(request, event.getActionExecuteNode().getActionModelKey())) {
                    // ApprovalRequest
                    Map<String, DataStructNode> dataStructNodeMap = new HashMap<>();
                    Map<String, Object> afterParamMap = new HashMap<>(jsonObject.getJSONObject(AFTER).getInnerMap());

                    this.filterParam(afterParamMap, event.getActionExecuteNode().getActionModelKey(), dataStructNodeMap);
                    workflowRequest.setBizObj(afterParamMap);

                    if (jsonObject.containsKey(BEFORE)) {
                        Map<String, Object> beforeParamMap = new HashMap<>(jsonObject.getJSONObject(BEFORE).getInnerMap());
                        this.filterParam(beforeParamMap, event.getActionExecuteNode().getActionModelKey(), dataStructNodeMap);
                        List<DataStructNode> dataStructs = new ArrayList<>(dataStructNodeMap.values());

                        List<FieldRecord> fieldRecords = modelDataDiff.diff(event.getActionExecuteNode().getActionModelKey(), dataStructs, beforeParamMap, afterParamMap);
                        workflowRequest.setFieldRecords(fieldRecords);
                    }
                } else {
                    Map<String, Object> paramMap = new HashMap<>((jsonObject).getInnerMap());
                    this.filterParam(paramMap, event.getActionExecuteNode().getActionModelKey());
                    workflowRequest.setBizObj(paramMap);
                }
            } else {
                Object param = ((JSONArray) request).stream().map(obj -> {
                    Map<String, Object> paramMap = new HashMap<>(((JSONObject) obj).getInnerMap());
                    this.filterParam(paramMap, event.getActionExecuteNode().getActionModelKey());
                    return paramMap;
                }).collect(Collectors.toList());
                workflowRequest.setBizObj(param);
            }
        }

        // 发起审批流
        StartWorkflowByGroupResponse workflowResponse = workflow.execute(workflowRequest);
        log.info("response:{}", JSON.toJSON(workflowResponse));

    }

    private void filterParam(Map<String, Object> paramMap, String modelKey) {
        filterParam(paramMap, modelKey, null);
    }

    private void filterParam(Map<String, Object> paramMap, String modelKey, Map<String, DataStructNode> dataStructNodeMap) {
        MetaTreeNodeExt modelMeta = metaCache.get(modelKey);
        if (Objects.isNull(modelMeta)) {
            log.warn("[Approval Action Executor] model meta query failed, model : {}", modelKey);
            return;
        }

        DataStructNode dataStructNode = null;
        try {
            dataStructNode = ObjectJsonUtil.MAPPER.treeToValue(modelMeta.getProps(), DataStructNode.class);
            if (Objects.nonNull(dataStructNodeMap)) {
                dataStructNodeMap.putIfAbsent(dataStructNode.getAlias(), dataStructNode);
            }
        } catch (Exception e) {
            log.warn("[Approval Action Executor] model meta props convert failed, model : {}", modelKey, e);
        }

        if (Objects.isNull(dataStructNode) || CollectionUtils.isEmpty(dataStructNode.getChildren())) {
            log.warn("[Approval Action Executor] model meta props convert null, model : {}", modelKey);
            return;
        }

        Map<String, DataStructFieldNode> fieldMap = dataStructNode.getChildren().stream()
            .collect(Collectors.toMap(DataStructFieldNode::getAlias, Function.identity()));
        Iterator<Map.Entry<String, Object>> iterator = paramMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if (!fieldMap.containsKey(entry.getKey())) {
                iterator.remove();
                continue;
            }

            DataStructFieldNode fieldNode = fieldMap.get(entry.getKey());
            if (!FieldType.OBJECT.equals(fieldNode.getProps().getFieldType())) {
                continue;
            }

            if (ModelRelationTypeEnum.PARENT_CHILD.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                if (entry.getValue() instanceof List) {
                    ((List<Map<String, Object>>) entry.getValue()).forEach(child -> this.filterParam(child, fieldNode.getProps().getRelationMeta().getRelationModelAlias(), dataStructNodeMap));
                }
            } else if (entry.getValue() instanceof Map) {
                this.filterParam((Map<String, Object>) entry.getValue(), fieldNode.getProps().getRelationMeta().getRelationModelAlias(), dataStructNodeMap);
            }
        }
    }

    private boolean judgeApprovalRequest(JSON request, String modelKey) {
        if (!(request instanceof JSONObject)) {
            // 数组？反正不是变更前后的对象
            return false;
        }

        JSONObject jsonObject = (JSONObject) request;

        if (!jsonObject.containsKey(AFTER)) {
            // 入参不包含after 一定不是变更前后的对象
            return false;
        }

        // 查询元数据 获取所有业务字段（不考虑系统字段）
        // 查询元数据 获取所有业务字段（不考虑系统字段）
        MetaTreeNodeExt metaTreeNodeExt = metaCache.get(modelKey);
        DataStructNode dataStructNode = DataStructNode.newInstanceFrom(metaTreeNodeExt);
        Set<String> bizFieldKeys = dataStructNode.getChildren().stream()
            .filter(field -> Boolean.FALSE.equals(field.getProps().getIsSystemField()))
            .map(DataStructFieldNode::getAlias).collect(Collectors.toSet());

        if (!bizFieldKeys.contains(AFTER)) {
            // 业务字段无after 一定是变更前后的对象
            return true;
        }

        // 求交集
        bizFieldKeys.retainAll(jsonObject.keySet());
        // 交集为0 则一定是变更前后的对象
        return bizFieldKeys.size() == 0;
    }


}
