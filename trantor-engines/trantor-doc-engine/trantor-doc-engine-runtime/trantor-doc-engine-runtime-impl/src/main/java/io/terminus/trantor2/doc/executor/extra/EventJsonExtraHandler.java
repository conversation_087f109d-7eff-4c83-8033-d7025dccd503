package io.terminus.trantor2.doc.executor.extra;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventJsonExtraHandler {

    private static final String EXTRA = "extra";
    private static final String CONTEXT = "context";
    private static final String EXTRA_JSON = "extraJson";
    private static final String ID = "id";

    @Resource
    private MetaCache metaCache;

    public void jsonExtraHandle(String modelAlias, JSON response) {
        Map<String, Map<String, DataStructFieldNode>> dataStructNodeMap = new HashMap<>(32);
        jsonExtraHandle(dataStructNodeMap, modelAlias, response);
    }

    private void jsonExtraHandle(Map<String, Map<String, DataStructFieldNode>> dataStructNodeMap, String modelAlias, JSON response) {
        if(response instanceof JSONArray) {
            ((JSONArray) response).stream()
                .filter(item -> item instanceof JSONObject)
                .forEach(item -> this.jsonExtraHandle(dataStructNodeMap, modelAlias, (JSONObject)item));
        }

        if(!(response instanceof JSONObject)) {
            return;
        }
        JSONObject jsonObject = (JSONObject)response;

        // 查询模型元数据
        Map<String, DataStructFieldNode> fieldMetaMap;
        if(!StringUtils.hasLength(modelAlias)) {
            fieldMetaMap = new HashMap<>(2);
        }else if(dataStructNodeMap.containsKey(modelAlias)) {
            fieldMetaMap = dataStructNodeMap.get(modelAlias);
        }else {
            fieldMetaMap = this.queryDataStructNode(modelAlias);
            dataStructNodeMap.put(modelAlias, fieldMetaMap);
        }

        // 递归处理子模型
        Set<String> long2JsonKeys = Sets.newHashSetWithExpectedSize(jsonObject.getInnerMap().values().size());
        for (Map.Entry<String, Object> entry : jsonObject.getInnerMap().entrySet()) {
            if (EXTRA.equals(entry.getKey()) || EXTRA_JSON.equals(entry.getKey()) || CONTEXT.equals(entry.getKey()) || ID.equals(entry.getKey())) {
                continue;
            }

            if (!fieldMetaMap.containsKey(entry.getKey())) {
                if(entry.getValue() instanceof JSON) {
                    this.jsonExtraHandle(dataStructNodeMap, null, (JSON) entry.getValue());
                }
                continue;
            }

            DataStructFieldNode fieldNode = fieldMetaMap.get(entry.getKey());
            if (FieldType.OBJECT.equals(fieldNode.getProps().getFieldType())) {
                if (entry.getValue() instanceof JSONArray && ModelRelationTypeEnum.PARENT_CHILD.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    JSONArray jsonArray = (JSONArray) entry.getValue();
                    // 遍历集合
                    for (Object o : jsonArray) {
                        this.jsonExtraHandle(dataStructNodeMap, fieldNode.getProps().getRelationMeta().getRelationModelAlias(), (JSONObject) o);
                    }
                }else if (entry.getValue() instanceof JSONObject && ModelRelationTypeEnum.LINK.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    // 子模型
                    this.jsonExtraHandle(dataStructNodeMap, fieldNode.getProps().getRelationMeta().getRelationModelAlias(), (JSONObject) entry.getValue());
                }else if (entry.getValue() instanceof Number && ModelRelationTypeEnum.LINK.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    long2JsonKeys.add(entry.getKey());
                }
            }
        }

        // 数字转对象
        if(CollectionUtils.isNotEmpty(long2JsonKeys)) {
            long2JsonKeys.forEach(key -> {
                JSONObject modelJson = new JSONObject();
                modelJson.put(ID, jsonObject.getInnerMap().get(key));
                jsonObject.put(key, modelJson);
            });
        }

        if(!jsonObject.containsKey(EXTRA) || !(jsonObject.get(EXTRA) instanceof JSONObject)) {
            return;
        }
        JSONObject extra = jsonObject.getJSONObject(EXTRA);
        for (Map.Entry<String, Object> entry : extra.getInnerMap().entrySet()) {
            if(jsonObject.containsKey(entry.getKey()) && Objects.nonNull(jsonObject.get(entry.getKey()))) {
                // extra 和 对象本身同时存在 以对象本身数据为准
                continue;
            }

            if (!fieldMetaMap.containsKey(entry.getKey())) {
                jsonObject.put(entry.getKey(), entry.getValue());
                continue;
            }

            DataStructFieldNode fieldNode = fieldMetaMap.get(entry.getKey());
            if (FieldType.OBJECT.equals(fieldNode.getProps().getFieldType())) {
                // 一对多扩展字段不处理
                if (ModelRelationTypeEnum.PARENT_CHILD.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    continue;
                }
                // Number 转对象
                if(entry.getValue() instanceof Number) {
                    JSONObject modelJson = new JSONObject();
                    modelJson.put(ID, entry.getValue());
                    jsonObject.put(entry.getKey(), modelJson);
                } else {
                    log.warn("extra处理异常, fieldProps : {}, value : {}", fieldNode.getProps(), entry.getValue());
                    jsonObject.put(entry.getKey(), entry.getValue());
                }
            }else {
                jsonObject.put(entry.getKey(), entry.getValue());
            }
        }
        jsonObject.remove(EXTRA);
        jsonObject.remove(EXTRA_JSON);
    }

    public Map<String, DataStructFieldNode> queryDataStructNode(String modelKey) {
        Map<String, DataStructFieldNode> fieldMetaMap = new HashMap<>(16);

        MetaTreeNodeExt modelMeta = metaCache.get(modelKey);
        if(Objects.isNull(modelMeta)) {
            return fieldMetaMap;
        }
        DataStructNode dataStructNode = DataStructNode.newInstanceFrom(modelMeta);

        if(Objects.nonNull(dataStructNode)) {
            dataStructNode.getChildren().forEach(field -> fieldMetaMap.put(field.getAlias(), field));
        }
        return fieldMetaMap;
    }
}
