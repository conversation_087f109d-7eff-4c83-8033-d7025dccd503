package io.terminus.trantor2.doc.executor;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.terminus.operation.log.api.model.OperationLogContextInfo;
import io.terminus.operation.log.api.model.dto.WriteOperationLogDTO;
import io.terminus.operation.log.sdk.client.OperationLogClient;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.PageInfo;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.datasource.util.DateUtils;
import io.terminus.trantor2.doc.api.context.EventLogInfo;
import io.terminus.trantor2.doc.api.context.EventRequest;
import io.terminus.trantor2.doc.api.context.EventRequestContext;
import io.terminus.trantor2.doc.api.dto.EventActionRelDTO;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.executor.EventExecutor;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.api.props.NoticeProps;
import io.terminus.trantor2.doc.api.request.ActionNodeRequest;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.context.EventContext;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.executor.impl.EventApprovalActionExecutor;
import io.terminus.trantor2.doc.executor.impl.EventCallEventActionExecutor;
import io.terminus.trantor2.doc.executor.impl.EventNormalActionExecutor;
import io.terminus.trantor2.doc.executor.impl.EventNoticeExecutor;
import io.terminus.trantor2.doc.executor.impl.EventServiceActionExecutor;
import io.terminus.trantor2.doc.executor.impl.EventValidationActionExecutor;
import io.terminus.trantor2.doc.loader.ActionLoader;
import io.terminus.trantor2.doc.state.StateEngineExecutor;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.runtime.meta.transaction.manager.TrantorPlatformTransactionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Deprecated
@Slf4j
@RequiredArgsConstructor
//@Component
public class EventExecutorImpl implements EventExecutor {

    @Value("${trantor.engine.singleton:false}")
    private boolean eventCallEventInJvm;
    private static final Pattern PATTERN_EXPRESS_VAR = Pattern.compile("(\\([\\w\\$]+\\))?(\\w+)");
    private static final Pattern PATTERN_EXPRESS_ARRAY = Pattern.compile("^\\[\\]");
    private static final Pattern PATTERN_EXPRESS_SPLIT = Pattern.compile("^\\.");
    private static final String EXPRESS_ARRAY = "[]";
    private static final String EXPRESS_PREFIX_ARRAY = "[].";

    private final ActionLoader actionLoader;
    private final EventNormalActionExecutor normalActionExecutor;
    private final EventServiceActionExecutor serviceActionExecutor;
    private final EventApprovalActionExecutor eventApprovalActionExecutor;
    private final EventValidationActionExecutor eventValidationActionExecutor;
    private final EventCallEventActionExecutor eventCallEventActionExecutor;
    private final StateEngineExecutor stateEngineExecutor;
    private final EventNoticeExecutor eventNoticeExecutor;
    private final TrantorPlatformTransactionManager trantorPlatformTransactionManager;
    private final EventExecuteTool eventExecuteTool;

    @Override
    public JSON execute(EventRequest eventRequest) {
        return this.serviceEventExecute(eventRequest).getData();
    }

    @Override
    public JSON executeWithTransaction(EventRequest eventRequest) {
        setPageInfoContext(eventRequest);
        ActionExecuteNode actionExecuteNode = actionLoader.load(new ActionNodeRequest(eventRequest.getEventCode()));
        if (Objects.isNull(actionExecuteNode)) {
            throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "event.load.failed");
        }

        if (Boolean.FALSE.equals(actionExecuteNode.getEnabledTransaction())
            || TransactionSynchronizationManager.isActualTransactionActive()) {
            return this.execute(eventRequest, actionExecuteNode).getData();
        } else if (TransactionSynchronizationManager.isActualTransactionActive()) {
            return this.execute(eventRequest, actionExecuteNode).getData();
        } else {
            if (Objects.isNull(trantorPlatformTransactionManager)) {
                throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "trantorPlatformTransactionManager.load.failed");
            }

            TransactionTemplate transactionTemplate = this.buildTransactionTemplate(eventRequest.getEventCode());
            try {
                return transactionTemplate.execute(status -> execute(eventRequest, actionExecuteNode).getData());
            } finally {
                TransactionContext.deleteTransactionId(transactionTemplate.getName());
                // 如果当前上下文中事务id为空了，则强制清空事务上下文
                TransactionContext.removeContextIfNecessary(transactionTemplate.getName(), true);
            }
        }
    }

    private static void setPageInfoContext(EventRequest eventRequest) {
        if (eventRequest.getPageInfo() != null) {
            if (TrantorContext.getContext() == null) {
                TrantorContext.init();
            }
            TrantorContext.setPageInfo(eventRequest.getPageInfo());
        }
    }

    @Override
    public Response<JSON> serviceEventExecute(EventRequest eventRequest) {
        setPageInfoContext(eventRequest);
        ActionExecuteNode actionExecuteNode = actionLoader.load(new ActionNodeRequest(eventRequest.getEventCode()));
        if (Objects.isNull(actionExecuteNode)) {
            throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "event.load.failed");
        }

        return this.execute(eventRequest, actionExecuteNode);
    }

    public Response<JSON> execute(EventRequest eventRequest, ActionExecuteNode actionExecuteNode) {
        // 通知配置
        List<NoticeProps> noticePropsList = actionExecuteNode.getNoticePropsList();
        String eventName = actionExecuteNode.getEventName();
        String mainModelName = actionExecuteNode.getMainModelName();
        String mainModelKey = actionExecuteNode.getMainModelKey();
        // 上下文初始化
        EventRequestContext.init(eventRequest, mainModelKey, mainModelName, actionExecuteNode.getEnabledStatusVerify());
        // Event日志初始化
        EventLogInfo eventLogInfo = new EventLogInfo(actionExecuteNode.getAppKey(), eventRequest.getEventCode(), eventName, actionExecuteNode.getMainModelKey(), actionExecuteNode.getReturnModelKey());
        eventLogInfo.setReqBody((JSON) JSON.toJSON(eventRequest.getParam()));

        JSON result = null;
        Response.Info info = null;
        if (Objects.nonNull(actionExecuteNode.getEventActionRel())) {
            EventContext context = new EventContext(eventRequest);
            do {
                this.oneExecute(actionExecuteNode, context);
                actionExecuteNode = actionExecuteNode.getNextNode();
            } while (Objects.nonNull(actionExecuteNode));

            result = context.getResponse();
            info = context.getInfo();
        } else {
            changeStatusEvent(eventRequest, mainModelKey);
        }

        // 状态变更处理
        stateEngineExecutor.eventCompleteHandle();

        // 推送操作日志
        pushOperateLog(result);

        // 推送Event日志
//        eventLogInfo.setResBody(result);
//        eventLogInfo.setStatusList(EventRequestContext.getEventInfoContext().getEventStateChangeLogs());
//        eventLogPublisher.publishLog(eventLogInfo);

        // 发通知
        this.sendNotices(eventRequest, noticePropsList, eventName, mainModelKey);

        // 上下文清空
        EventRequestContext.clear();

        Response<JSON> response = Response.ok();
        // 返回为null时的特殊处理
        if (result instanceof JSONObject && ((JSONObject) result).getInnerMap().size() == 0) {
            return response;
        }

        // 构建返回对象 data和info
        response.setData(result);
        response.setInfo(info);

        return response;
    }

    private void changeStatusEvent(EventRequest eventRequest, String modelKey) {
        Object param = eventRequest.getParam();
        if (param instanceof Collection) {
            Iterator<?> iterator = ((Collection<?>) param).iterator();
            while (iterator.hasNext()) {
                Object next = iterator.next();
                if (next instanceof Map) {
                    Map<String, Object> model = (Map<String, Object>) next;
                    Object id = model.get("id");
                    if (id == null) {
                        return;
                    }
                    EventRequestContext.getEventInfoContext().setModeIds(id, modelKey);
                }
            }
        } else if (param instanceof Map) {
            Map<String, Object> model = (Map<String, Object>) param;
            Object id = model.get("id");
            if (id == null) {
                return;
            }
            EventRequestContext.getEventInfoContext().setModeIds(id, modelKey);
        }
    }

    private void pushOperateLog(JSON result) {
        EventRequestContext.EventInfoContext eventInfoContext = EventRequestContext.getEventInfoContext();
        OperationLogContextInfo logContext = new OperationLogContextInfo(eventInfoContext.getEventParam(), result, null);
        WriteOperationLogDTO oplog = buildLog();
        if (oplog == null) {
            return;
        }
        OperationLogClient.pushOplog(logContext, oplog);
    }

    private WriteOperationLogDTO buildLog() {
        LocalDateTime now = LocalDateTime.now();
        Long appId = TrantorContext.getCurrentPortalOptional().map(Portal::getId).orElse(null);
        Long teamId = TrantorContext.getTeamId();
        EventRequestContext.EventInfoContext eventInfoContext = EventRequestContext.getEventInfoContext();
        String eventCode = eventInfoContext.getEventCode();
        String mainModelKey = eventInfoContext.getMainModelKey();
        String shortMainModelKey = mainModelKey == null ? "" : KeyUtil.shortKey(mainModelKey);
        String mainModelName = eventInfoContext.getMainModelName();

        WriteOperationLogDTO log = new WriteOperationLogDTO();
        log.setAppId(appId);
        log.setTeamId(teamId);

        WriteOperationLogDTO.LogSummary logSummary = new WriteOperationLogDTO.LogSummary();
        logSummary.setUserId(TrantorContext.safeGetCurrentUser().map(User::getId).orElse(null));
        logSummary.setUsername(TrantorContext.safeGetCurrentUser().map(User::getUsername).orElse(null));
        logSummary.setMainFullModelKey(mainModelKey);
        logSummary.setMainModelKey(shortMainModelKey);
        logSummary.setMainModelName(mainModelName);
        logSummary.setSummaryKey(eventCode);
        logSummary.setOperateAt(now);
        TrantorContext.Context context = TrantorContext.getContext();

        PageInfo pageInfo;
        if (context != null && (pageInfo = context.getPageInfo()) != null) {
            String traceId = pageInfo.getTraceId();
            String summaryCode = org.apache.commons.lang3.StringUtils.isEmpty(traceId)
                ? OperationLogClient.createOplogCode(mainModelKey)
                : traceId;
            logSummary.setSummaryCode(summaryCode);
            logSummary.setActionKey(pageInfo.getButtonKey());
            logSummary.setActionName(pageInfo.getButtonName());
            logSummary.setSceneKey(pageInfo.getSceneKey());
            Map<String, EventRequestContext.EventOplogInfo> eventOplogInfoMap = eventInfoContext.getEventOplogInfoMap();
            if (MapUtils.isEmpty(eventOplogInfoMap)) {
                return null;
            }
            List<WriteOperationLogDTO.LogEvent> logEvents = eventOplogInfoMap.values()
                .stream()
                .map(eventOplogInfo -> {
                    String modelKey = eventOplogInfo.getModelKey();
                    if (shortMainModelKey.equals(modelKey)) {
                        logSummary.setMainModelBizId(eventOplogInfo.getBizId());
                    }
                    WriteOperationLogDTO.LogEvent logEvent = new WriteOperationLogDTO.LogEvent();
                    logEvent.setModelKey(modelKey);
                    logEvent.setModelName(eventOplogInfo.getModelName());
                    logEvent.setEventKey(eventOplogInfo.getEventKey());
                    Long bizId = eventOplogInfo.getBizId();
                    logEvent.setBizId(bizId);
                    logEvent.setBizKey(eventOplogInfo.getBizKey());
                    logEvent.setBizKeyField(eventOplogInfo.getBizKeyField());
                    logEvent.setBizVersion(eventOplogInfo.getBizVersion());
                    logEvent.setSummaryCode(summaryCode);
                    logEvent.setEventCode(OperationLogClient.createOplogCode(modelKey));
                    logEvent.setPageKey(pageInfo.getPageKey());
                    logEvent.setPageName(pageInfo.getPageName());
                    logEvent.setRelationBizId(eventOplogInfo.getRelationBizId());
                    return logEvent;
                })
                .collect(Collectors.toList());
            log.setSummaries(Collections.singletonList(logSummary));
            log.setEvents(logEvents);
            return log;
        }
        return null;
    }

    private void sendNotices(EventRequest eventRequest, List<NoticeProps> noticePropsList, String eventName, String mainModelKey) {
        if (!CollectionUtils.isEmpty(noticePropsList)) {
            JSON request = (JSON) JSON.toJSON(eventRequest.getParam());
            noticePropsList.stream().filter(noticeProps -> StringUtils.hasLength(noticeProps.getNoticeSceneCode()))
                .forEach(noticeProps -> {
                    try {
                        eventNoticeExecutor.execute(request, eventRequest.getEventCode(), eventName, mainModelKey, noticeProps);
                    } catch (Throwable t) {
                        log.error("[EventExecutor] Event Notice failed, event code : {}, noticeProps : {}",
                            eventRequest.getEventCode(), noticeProps, t);
                    }
                });
        }
    }

    private void oneExecute(ActionExecuteNode actionExecuteNode, EventContext eventContext) {
        if (StringUtils.hasLength(actionExecuteNode.getEventActionRel().getSourceCode())) {
            //取值从之前action出参结果获取入参
            lastActionResultExecute(actionExecuteNode, eventContext);
            return;
        }

        Map<String, Object> conditionParam = new HashMap<>(16);
        conditionParam.put(actionExecuteNode.getMainModelKey(), eventContext.getRequest().getParam());
        actionExecuteNode.setActionModelKey(actionExecuteNode.getMainModelKey());

        JSON request = (JSON) JSON.toJSON(eventContext.getRequest().getParam());
        if (request instanceof JSONArray) {
            this.actionExecute(request, eventContext, actionExecuteNode, conditionParam);
        } else if (request instanceof JSONObject) {
            expressExecute((JSONObject) request, eventContext, actionExecuteNode.getEventActionRel().getExpress(), actionExecuteNode, conditionParam);
        }
        eventContext.getRequest().setParam(request);
    }

    private void lastActionResultExecute(ActionExecuteNode actionExecuteNode, EventContext eventContext) {
        EventActionRelDTO eventActionRel = actionExecuteNode.getEventActionRel();
        if (!eventContext.getActionResponseMap().containsKey(eventActionRel.getSourceCode())) {
            // 由于之前action可能条件未命中导致未执行 因此获取不到对应的出参
            log.warn("[EventExecutor] source code result is null, source code : {}, this code : {}",
                eventActionRel.getSourceCode(), eventActionRel.getCode());

            if (Boolean.TRUE.equals(eventActionRel.getEnabledParamCheck())) {
                // 开启入参校验
                throw new DocEngineActionRuntimeException(
                    String.format("action.param.is.null, source code : %s, this code : %s",
                        eventActionRel.getSourceCode(), eventActionRel.getCode())
                );
            }
            return;
        }

        Map<String, Object> conditionParam = new HashMap<>(16);
        eventContext.getActionResponseMap().get(eventActionRel.getSourceCode()).forEach(lastActionResultInfo -> {
            if (eventActionRel.getExpress().startsWith(EXPRESS_ARRAY)) {
                ((JSONArray) lastActionResultInfo.getActionResponse()).forEach(json -> {
                    if (StringUtils.hasLength(lastActionResultInfo.getModel())) {
                        conditionParam.put(lastActionResultInfo.getModel(), json);
                        actionExecuteNode.setActionModelKey(lastActionResultInfo.getModel());
                    }
                    this.expressExecute(
                        (JSONObject) json,
                        eventContext,
                        EXPRESS_ARRAY.equals(eventActionRel.getExpress()) ? "" : eventActionRel.getExpress().substring(EXPRESS_PREFIX_ARRAY.length()),
                        actionExecuteNode, conditionParam
                    );
                });
            } else {
                if (StringUtils.hasLength(lastActionResultInfo.getModel())) {
                    conditionParam.put(lastActionResultInfo.getModel(), lastActionResultInfo.getActionResponse());
                    actionExecuteNode.setActionModelKey(lastActionResultInfo.getModel());
                }
                if (lastActionResultInfo.getActionResponse() instanceof JSONArray) {
                    this.actionExecute(lastActionResultInfo.getActionResponse(), eventContext, actionExecuteNode, conditionParam);
                } else if (lastActionResultInfo.getActionResponse() instanceof JSONObject) {
                    this.expressExecute(
                        (JSONObject) lastActionResultInfo.getActionResponse(),
                        eventContext,
                        eventActionRel.getExpress(),
                        actionExecuteNode,
                        conditionParam
                    );
                }
            }
        });
    }

    private void expressExecute(JSONObject request, EventContext eventContext, String express, ActionExecuteNode
        actionExecuteNode, Map<String, Object> conditionParam) {
        if (!StringUtils.hasLength(express)) {
            this.actionExecute(request, eventContext, actionExecuteNode, conditionParam);
            return;
        }

        int index = 0;
        Matcher varMatch = PATTERN_EXPRESS_VAR.matcher(express);
        if (!varMatch.find()) {
            throw new DocEngineActionRuntimeException("express analysis failed");
        }

        String model = varMatch.group(1);
        String var = varMatch.group(2);

        index = index + varMatch.end();
        if (index > express.length()) {
            request = request.getJSONObject(var);
            if (StringUtils.hasLength(model)) {
                String modelKey = model.substring(1, model.length() - 1);
                conditionParam.put(modelKey, request);
                actionExecuteNode.setActionModelKey(modelKey);
            }
            this.actionExecute(request, eventContext, actionExecuteNode, conditionParam);
            return;
        }

        Matcher arrayMatch = PATTERN_EXPRESS_ARRAY.matcher(express.substring(index));

        if (arrayMatch.find()) {
            index = index + 2;
            Matcher splitMatch = PATTERN_EXPRESS_SPLIT.matcher(express.substring(index));

            if (!request.containsKey(var)) {
                log.warn("[EventExecutor] json array is null, rel code : {}, var : {}",
                    actionExecuteNode.getEventActionRel().getCode(), var);
                return;
            }

            JSONArray array = request.getJSONArray(var);
            boolean splitMatchResult = splitMatch.find();
            for (int i = 0; i < array.size(); i++) {
                JSONObject child = array.getJSONObject(i);
                if (StringUtils.hasLength(model)) {
                    String modelKey = model.substring(1, model.length() - 1);
                    conditionParam.put(modelKey, child);
                    actionExecuteNode.setActionModelKey(modelKey);
                }
                if (splitMatchResult) {
                    this.expressExecute(child, eventContext, express.substring(index + 1), actionExecuteNode, conditionParam);
                } else {
                    this.actionExecute(child, eventContext, actionExecuteNode, conditionParam);
                }
            }
        } else {
            if (request.get(var) instanceof List) {
                JSONArray array = request.getJSONArray(var);
                if (StringUtils.hasLength(model)) {
                    String modelKey = model.substring(1, model.length() - 1);
                    conditionParam.put(modelKey, array);
                    actionExecuteNode.setActionModelKey(modelKey);
                }
                actionExecute(array, eventContext, actionExecuteNode, conditionParam);
                return;
            }

            request = request.getJSONObject(var);
            Matcher splitMatch = PATTERN_EXPRESS_SPLIT.matcher(express.substring(index));
            if (StringUtils.hasLength(model)) {
                String modelKey = model.substring(1, model.length() - 1);
                conditionParam.put(modelKey, request);
                actionExecuteNode.setActionModelKey(modelKey);
            }
            if (splitMatch.find()) {
                this.expressExecute(request, eventContext, express.substring(index + 1), actionExecuteNode, conditionParam);
            } else {
                this.actionExecute(request, eventContext, actionExecuteNode, conditionParam);
            }
        }
    }

    private void actionExecute(JSON request, EventContext eventContext, ActionExecuteNode
        actionExecuteNode, Map<String, Object> conditionParam) {
        switch (actionExecuteNode.getEventActionRel().getActionType()) {
            case DocEngineMetaTypeConstant.ACTION:
                this.actionTransactionExecute(request, eventContext, actionExecuteNode, conditionParam, normalActionExecutor);
                break;
            case DocEngineMetaTypeConstant.EVENT:
                if (eventCallEventInJvm) {
                    // 单例部署时 直接内部调用 可入参回写
                    // 佛燃重点关注
                    this.eventCallEvent(request, eventContext, actionExecuteNode, conditionParam);
                } else {
                    this.actionTransactionExecute(request, eventContext, actionExecuteNode, conditionParam, eventCallEventActionExecutor);
                }
                break;
            case DocEngineMetaTypeConstant.SERVICE:
                this.actionTransactionExecute(request, eventContext, actionExecuteNode, conditionParam, serviceActionExecutor);
                break;
            case DocEngineMetaTypeConstant.APPROVAL_WORKFLOW:
                eventApprovalActionExecutor.execute(request, actionExecuteNode, conditionParam);
                break;
            case DocEngineMetaTypeConstant.VALIDATION:
                eventValidationActionExecutor.execute(request, actionExecuteNode, conditionParam);
                break;
            default:
                throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "action.rel.type.not.existed");
        }
    }

    private void actionTransactionExecute(JSON request, EventContext eventContext, ActionExecuteNode
        actionExecuteNode, Map<String, Object> conditionParam, ActionContextExecutor actionContextExecutor) {
        if (Boolean.FALSE.equals(actionExecuteNode.getEnabledTransaction())
            && Boolean.TRUE.equals(actionExecuteNode.getEventActionRel().getEnabledTransaction())
            && !TransactionSynchronizationManager.isActualTransactionActive()) {
            // Action单独开启事务
            TransactionTemplate transactionTemplate = this.buildTransactionTemplate(actionExecuteNode.getEventActionRel().getCode());
            try {
                transactionTemplate.execute(status -> {
                    actionContextExecutor.execute(request, eventContext, actionExecuteNode, conditionParam);
                    return null;
                });
            } finally {
                TransactionContext.deleteTransactionId(transactionTemplate.getName());
                // 如果当前上下文中事务id为空了，则强制清空事务上下文
                TransactionContext.removeContextIfNecessary(transactionTemplate.getName(), true);
            }
        } else {
            actionContextExecutor.execute(request, eventContext, actionExecuteNode, conditionParam);
        }
    }

    private void eventCallEvent(JSON request, EventContext eventContext, ActionExecuteNode
        actionExecuteNode, Map<String, Object> conditionParam) {
        if (Objects.isNull(actionExecuteNode.getEvent())) {
            log.warn("[Event call Event] event not existed, event code : {}", actionExecuteNode.getEventActionRel().getCode());
            throw new DocEngineActionRuntimeException("event.not.existed");
        }
        EventDTO event = actionExecuteNode.getEvent();

        // 条件判断
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);
        if (Boolean.FALSE.equals(condition)) {
            log.info("[Event call Event] action condition not pass, event code : {}", event.getCode());
            return;
        }

        // convert
        String convert = actionExecuteNode.getEventActionRel().getConvert();
        JSON param;
        if (StringUtils.hasLength(convert)) {
            param = (JSON) eventExecuteTool.convert(request, convert);
        } else {
            param = request;
        }

        // 暂存event上下文
        EventRequestContext.EventInfoContext snapshotContext = EventRequestContext.snapshot();

        // 调用Event
        EventRequest eventRequest = new EventRequest();
        eventRequest.setEventCode(event.getCode());
        eventRequest.setParam(param);
        JSON result = this.execute(eventRequest);

        // 重置event上下文
        EventRequestContext.reset(snapshotContext);

        // action入参回写 佛燃重点关注
        if (!StringUtils.hasLength(convert) && !StringUtils.hasLength(actionExecuteNode.getEventActionRel().getSourceCode())) {
            eventExecuteTool.requestWriteBack(request, param, null);
        }

        // action返回回写
        eventExecuteTool.responseWriteBack(event.getCode(), event.getReturnModel(), result, eventContext);
    }

    private TransactionTemplate buildTransactionTemplate(String code) {
        // 上下文
        TransactionContext.setTeamId(TrantorContext.getTeamId());
        TransactionContext.setTeamCode(TrantorContext.getTeamCode());
        TransactionContext.setModuleKey(KeyUtil.moduleKey(code));

        // 事务id设置
        List<String> alreadyExistsTransactionId = TransactionContext.getTransactionId();
        String nowTransactionId = code
            + "#" + DateUtils.formatDate(new Date(), "yyyyMMdd_HHmmss_SSS_")
            + "#" + RandomUtil.randomString(4);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(alreadyExistsTransactionId)) {
            log.info("event begin execute new tx, txId:{}", nowTransactionId);
        } else {
            log.info("event begin execute already exist tx, nowTxId:{},topTxId:{}",
                nowTransactionId, alreadyExistsTransactionId);
        }
        TransactionContext.addTransactionId(nowTransactionId);

        TransactionTemplate transactionTemplate = new TransactionTemplate(trantorPlatformTransactionManager);
        transactionTemplate.setPropagationBehavior(TransactionTemplate.PROPAGATION_REQUIRED);
        transactionTemplate.setName(nowTransactionId);
        return transactionTemplate;
    }
}
