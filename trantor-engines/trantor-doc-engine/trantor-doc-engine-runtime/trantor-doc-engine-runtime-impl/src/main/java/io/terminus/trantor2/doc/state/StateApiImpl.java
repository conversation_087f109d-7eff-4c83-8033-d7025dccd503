package io.terminus.trantor2.doc.state;

import io.terminus.trantor2.doc.api.executor.StateApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/16 16:07
 */
//@Component
@RequiredArgsConstructor
@Slf4j
@Deprecated
public class StateApiImpl implements StateApi {

    private final StateEngineExecutor stateEngineExecutor;

    @Override
    public <T> T changeStatus(T model) {
        stateEngineExecutor.execute(model);
        return model;
    }

    @Override
    public <T> List<T> changeStatus(List<T> modelList) {
        stateEngineExecutor.execute(modelList);
        return modelList;
    }

    @Override
    public <T> T changeStatus(T model, Class<?> modelclass) {
        stateEngineExecutor.execute(model, modelclass);
        return model;
    }

    @Override
    public <T> List<T> changeStatus(List<T> modelList, Class<?> modelclass) {
        stateEngineExecutor.execute(modelList, modelclass);
        return modelList;
    }
}
