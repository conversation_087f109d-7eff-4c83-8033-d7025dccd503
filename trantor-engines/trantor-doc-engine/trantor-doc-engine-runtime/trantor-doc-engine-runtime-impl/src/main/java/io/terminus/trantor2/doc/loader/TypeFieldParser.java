package io.terminus.trantor2.doc.loader;

import io.terminus.trantor2.doc.api.dto.ActionFieldDTO;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 类型字段解析工具类
 * 用于解析 Java 对象的字段结构，生成 ActionFieldDTO 列表
 *
 * <AUTHOR>
 */
@Slf4j
public class TypeFieldParser {

    /**
     * 最大递归层数
     */
    private static final int MAX_RECURSION_LEVEL = 3;

    /**
     * ApiModelProperty 注解的类名
     */
    private static final String API_MODEL_PROPERTY_CLASS_NAME = "io.swagger.annotations.ApiModelProperty";

    /**
     * 解析类型为字段列表
     *
     * @param requestType 要解析的类型
     * @param level 当前递归层数
     * @return 字段列表
     */
    public static List<ActionFieldDTO> type2FieldList(Type requestType, int level) {
        // 递归层数限制，最多3层
        if (level > MAX_RECURSION_LEVEL) {
            return null;
        }

        List<ActionFieldDTO> fieldList = new ArrayList<>();

        try {
            if (requestType instanceof Class) {
                Class<?> clazz = (Class<?>) requestType;
                return parseClassFields(clazz, level);
            } else if (requestType instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) requestType;
                Type rawType = parameterizedType.getRawType();

                if (rawType instanceof Class) {
                    Class<?> rawClass = (Class<?>) rawType;

                    // 处理集合类型
                    if (Collection.class.isAssignableFrom(rawClass)) {
                        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                        if (actualTypeArguments.length == 1) {
                            return parseClassFields(actualTypeArguments[0].getClass(), 1);
                        }
                    }

                    // 不处理其他泛型类型
                    return new ArrayList<>();
                }
            }
        } catch (Exception e) {
            log.warn("[TypeFieldParser] Parse type failed: {}", requestType.getTypeName(), e);
        }

        return fieldList;
    }

    /**
     * 解析类字段
     *
     * @param clazz 要解析的类
     * @param level 当前递归层数
     * @return 字段列表
     */
    private static List<ActionFieldDTO> parseClassFields(Class<?> clazz, int level) {
        List<ActionFieldDTO> fieldList = new ArrayList<>();

        // 跳过基本类型和常用包装类型
        if (isBasicType(clazz)) {
            return fieldList;
        }

        // 获取所有字段（包括继承的字段）
        List<java.lang.reflect.Field> allFields = getAllFields(clazz);

        for (java.lang.reflect.Field field : allFields) {
            // 跳过静态字段和合成字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || field.isSynthetic()) {
                continue;
            }

            ActionFieldDTO actionField = new ActionFieldDTO();
            String fieldKey = field.getName();
            actionField.setFieldKey(fieldKey);
            actionField.setFieldAlias(fieldKey);

            // 处理 @ApiModelProperty 注解
            String fieldName = getFieldNameFromApiModelProperty(field);
            actionField.setFieldName(fieldName != null ? fieldName : fieldKey);

            Class<?> fieldType = field.getType();

            // 处理基本类型
            if (isBasicType(fieldType)) {
                actionField.setFieldType(getFieldType(fieldType));
            }
            // 处理数组类型
            else if (fieldType.isArray()) {
                actionField.setFieldType(FieldType.Array);
                Class<?> componentType = fieldType.getComponentType();
                List<ActionFieldDTO> elementFields = type2FieldList(componentType, level + 1);
                actionField.setElements(elementFields);
            }
            // 处理集合类型
            else if (Collection.class.isAssignableFrom(fieldType)) {
                actionField.setFieldType(FieldType.Array);

                // 尝试获取泛型类型信息
                Type genericType = field.getGenericType();
                if (genericType instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType) genericType;
                    Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                    if (actualTypeArguments.length == 1) {
                        // 递归解析数组元素类型
                        List<ActionFieldDTO> elementFields = type2FieldList(actualTypeArguments[0], level + 1);
                        actionField.setElements(elementFields);
                    }
                }
            }
            // 处理复杂对象类型
            else {
                actionField.setFieldType(FieldType.Object);
                // 递归解析对象字段
                List<ActionFieldDTO> nestedFields = type2FieldList(fieldType, level + 1);
                actionField.setElements(nestedFields);
            }

            fieldList.add(actionField);
        }

        return fieldList;
    }

    /**
     * 从 @ApiModelProperty 注解中获取字段名称
     *
     * @param field 字段
     * @return 注解中的 value 值，如果没有注解或 value 为空则返回 null
     */
    private static String getFieldNameFromApiModelProperty(java.lang.reflect.Field field) {
        try {
            // 使用反射获取 @ApiModelProperty 注解
            Annotation[] annotations = field.getAnnotations();
            for (Annotation annotation : annotations) {
                if (API_MODEL_PROPERTY_CLASS_NAME.equals(annotation.annotationType().getName())) {
                    // 使用反射调用 value() 方法
                    java.lang.reflect.Method valueMethod = annotation.annotationType().getMethod("value");
                    String value = (String) valueMethod.invoke(annotation);
                    return value != null && !value.trim().isEmpty() ? value.trim() : null;
                }
            }
        } catch (Exception e) {
            log.debug("[TypeFieldParser] Failed to get ApiModelProperty value for field: {}", field.getName(), e);
        }
        return null;
    }

    /**
     * 判断是否为基本类型
     *
     * @param clazz 要判断的类
     * @return 是否为基本类型
     */
    private static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == String.class ||
               clazz == Integer.class || clazz == int.class ||
               clazz == Long.class || clazz == long.class ||
               clazz == Double.class || clazz == double.class ||
               clazz == Float.class || clazz == float.class ||
               clazz == Boolean.class || clazz == boolean.class ||
               clazz == Short.class || clazz == short.class ||
               clazz == Byte.class || clazz == byte.class ||
               clazz == Character.class || clazz == char.class ||
               clazz == Date.class ||
               clazz == LocalDate.class ||
               clazz == LocalDateTime.class ||
               clazz == LocalTime.class ||
               clazz == BigDecimal.class ||
               clazz == BigInteger.class;
    }

    /**
     * 根据类获取字段类型
     *
     * @param clazz 类
     * @return 字段类型
     */
    private static FieldType getFieldType(Class<?> clazz) {
        if (clazz == String.class || clazz == char.class || clazz == Character.class) {
            return FieldType.Text;
        } else if (clazz == Boolean.class || clazz == boolean.class) {
            return FieldType.Boolean;
        } else if (clazz == Integer.class || clazz == int.class ||
                   clazz == Long.class || clazz == long.class ||
                   clazz == Double.class || clazz == double.class ||
                   clazz == Float.class || clazz == float.class ||
                   clazz == Short.class || clazz == short.class ||
                   clazz == Byte.class || clazz == byte.class ||
                   clazz == BigDecimal.class ||
                   clazz == BigInteger.class) {
            return FieldType.Number;
        } else if (clazz == Date.class ||
                   clazz == LocalDate.class ||
                   clazz == LocalDateTime.class) {
            return FieldType.DateTime;
        } else if (clazz == LocalTime.class) {
            return FieldType.Time;
        } else {
            return FieldType.Object;
        }
    }

    /**
     * 获取类的所有字段（包括继承的字段）
     *
     * @param clazz 类
     * @return 所有字段列表
     */
    private static List<java.lang.reflect.Field> getAllFields(Class<?> clazz) {
        List<java.lang.reflect.Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            fields.addAll(Arrays.asList(currentClass.getDeclaredFields()));
            currentClass = currentClass.getSuperclass();
        }

        return fields;
    }
}
