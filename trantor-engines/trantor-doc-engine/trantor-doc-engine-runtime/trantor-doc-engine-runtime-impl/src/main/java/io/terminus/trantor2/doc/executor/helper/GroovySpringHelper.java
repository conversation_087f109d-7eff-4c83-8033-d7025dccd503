package io.terminus.trantor2.doc.executor.helper;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 提供groovy脚本使用的静态获取bean的方法
 * <AUTHOR>
 */
@Component
public final class GroovySpringHelper implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        GroovySpringHelper.applicationContext = applicationContext;
    }

    public static Object getBean(String bean) {
        return GroovySpringHelper.applicationContext.getBean(bean);
    }

    public static <T> T getBean(Class<T> clazz) {
        return GroovySpringHelper.applicationContext.getBean(clazz);
    }
}
