package io.terminus.trantor2.doc.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.context.EventRequest;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EventContext {

    private EventRequest request;

    private JSON response;

    private Response.Info info;

    private Multimap<String, ActionResponseInfo> actionResponseMap;

    public EventContext() {
    }

    public EventContext(EventRequest request) {
        this.request = request;
        this.response = new JSONObject();
        this.actionResponseMap = HashMultimap.create();
    }

    @Data
    public static class ActionResponseInfo {

        private String model;

        private JSON actionResponse;

        public ActionResponseInfo() {

        }

        public ActionResponseInfo(String model, JSON actionResponse) {
            this.model = model;
            this.actionResponse = actionResponse;
        }

        public ActionResponseInfo(JSON actionResponse) {
            this.actionResponse = actionResponse;
        }

    }
}
