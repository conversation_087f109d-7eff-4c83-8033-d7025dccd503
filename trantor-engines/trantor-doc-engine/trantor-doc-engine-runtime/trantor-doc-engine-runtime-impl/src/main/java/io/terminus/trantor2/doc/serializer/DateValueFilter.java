package io.terminus.trantor2.doc.serializer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.ValueFilter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateValueFilter implements ValueFilter {
    @Override
    public Object process(Object object, String name, Object value) {
        if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).atZone(JSON.defaultTimeZone.toZoneId()).toInstant().toEpochMilli();
        }
        if (value instanceof Date) {
            return ((Date) value).getTime();
        }
        return value;
    }
}
