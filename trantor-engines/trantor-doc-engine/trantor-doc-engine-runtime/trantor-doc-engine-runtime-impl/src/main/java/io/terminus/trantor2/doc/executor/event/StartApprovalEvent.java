package io.terminus.trantor2.doc.executor.event;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/9 5:28 PM
 */
@Data
@NoArgsConstructor
public class StartApprovalEvent implements Serializable {

    private static final long serialVersionUID = -7594387509990590616L;

    public static final String TAG = "START_APPROVAL_EVENT";

    private Object request;

    private ActionExecuteNode actionExecuteNode;

    private Map<String, Object> conditionParam;

    private TrantorContext.Context context;

    public StartApprovalEvent(Object request, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        this.request = request;
        this.actionExecuteNode = actionExecuteNode;
        this.conditionParam = conditionParam;
        this.context = TrantorContext.getContext();
    }

}
