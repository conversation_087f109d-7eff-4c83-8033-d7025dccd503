package io.terminus.trantor2.doc.serializer;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONLexer;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.LongCodec;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import com.alibaba.fastjson.util.TypeUtils;
import io.terminus.trantor2.doc.annotation.MetaModelField;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class MetaModelCodec implements ObjectSerializer, ObjectDeserializer {
    public static MetaModelCodec instance = new MetaModelCodec();
    private static LongCodec longCodec = LongCodec.instance;

    private static final String ID_FIELD = "id";
    private static final int LEFT_BRACE = '{';
    private static final int RIGHT_BRACE = '}';

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type clazz, Object fieldName) {
        Object param = parser.parse();
        if ((clazz.equals(Long.class) || clazz.equals(Long.TYPE)) && (param instanceof JSONObject)) {
            if (((JSONObject) param).containsKey(ID_FIELD)) {
                return (T) ((JSONObject) param).getLong(ID_FIELD);
            } else {
                return null;
            }
        }

        // 以下代码来源自com.alibaba.fastjson.serializer.LongCodec 但去除了重复的parser.parse()避免报错
        final JSONLexer lexer = parser.lexer;
        Long longObject;
        try {
            final int token = lexer.token();
            if (token == JSONToken.LITERAL_INT) {
                long longValue = lexer.longValue();
                lexer.nextToken(JSONToken.COMMA);
                longObject = Long.valueOf(longValue);
            } else if (token == JSONToken.LITERAL_FLOAT) {
                BigDecimal number = lexer.decimalValue();
                longObject = TypeUtils.longValue(number);
                lexer.nextToken(JSONToken.COMMA);
            } else {
                if (token == JSONToken.LBRACE) {
                    JSONObject jsonObject = new JSONObject(true);
                    parser.parseObject(jsonObject);
                    longObject = TypeUtils.castToLong(jsonObject);
                } else {
                    longObject = TypeUtils.castToLong(param);
                }
                if (longObject == null) {
                    return null;
                }
            }
        } catch (Exception ex) {
            throw new JSONException("parseLong error, field : " + fieldName, ex);
        }

        return clazz == AtomicLong.class
            ? (T) new AtomicLong(longObject.longValue())
            : (T) longObject;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }

    @Override
    public void write(JSONSerializer jsonSerializer, Object object, Object fieldName, Type type, int i) throws IOException {
        if (!Long.class.equals(type) && !Long.TYPE.equals(type)) {
            longCodec.write(jsonSerializer, object, fieldName, type, i);
            return;
        }

        if (!(fieldName instanceof String)) {
            // Long 集合等序列化会报错
            longCodec.write(jsonSerializer, object, fieldName, type, i);
            return;
        }

        if (Objects.isNull(jsonSerializer.getContext())) {
            // 部分项目的FastJson参数会导致context为空
            longCodec.write(jsonSerializer, object, fieldName, type, i);
            return;
        }

        SerializeWriter out = jsonSerializer.out;
        Class<?> clazz = jsonSerializer.getContext().object.getClass();

        // 类上字段
        Map<String, Field> noSuperFiledMap = Arrays.stream(clazz.getDeclaredFields())
            .collect(Collectors.toMap(Field::getName, Function.identity(), (a, b) -> b));

        if (noSuperFiledMap.containsKey(fieldName)) {
            Field field = noSuperFiledMap.get(fieldName);
            longWriteObject(jsonSerializer, object, fieldName, type, i, out, field);
        } else {
            // 父类字段
            Map<String, Field> includeSuperFieldMap = getAllNotStaticField(clazz);
            if (includeSuperFieldMap.containsKey(fieldName)) {
                Field field = includeSuperFieldMap.get(fieldName);
                longWriteObject(jsonSerializer, object, fieldName, type, i, out, field);
            } else {
                longCodec.write(jsonSerializer, object, fieldName, type, i);
            }
        }
    }

    private void longWriteObject(JSONSerializer jsonSerializer, Object object, Object fieldName, Type type, int i, SerializeWriter out, Field field) throws IOException {
        MetaModelField metaModelField = field.getAnnotation(MetaModelField.class);
        if (Objects.nonNull(metaModelField)) {
            out.write(LEFT_BRACE);
            out.writeFieldName(ID_FIELD);
            long value = (Long) object;
            out.writeLong(value);
            out.write(RIGHT_BRACE);
        } else {
            longCodec.write(jsonSerializer, object, fieldName, type, i);
        }
    }

    private Map<String, Field> getAllNotStaticField(Class<?> clazz) {
        Map<String, Field> fieldMap = new HashMap<>(16);
        while (clazz != null) {
            new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()))
                .stream().filter(field -> !Modifier.isStatic(field.getModifiers()))
                .forEach(field -> fieldMap.put(field.getName(), field));
            clazz = clazz.getSuperclass();
        }
        return fieldMap;
    }

}
