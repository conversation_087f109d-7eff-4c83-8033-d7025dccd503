package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq0;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq1;
import io.terminus.notice.sdk.service.NoticeService;
import io.terminus.notice.spi.model.res.NoticeTarget;
import io.terminus.trantor2.doc.api.dto.NoticePlaceHolderDTO;
import io.terminus.trantor2.doc.api.props.NoticeProps;
import io.terminus.trantor2.doc.constant.NoticePlaceHolderType;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventNoticeExecutor {

    @Resource
    private EventExecuteTool eventExecuteTool;
    @Resource
    private NoticeService noticeService;

    private static final String EXPRESS_SPLIT = "\\.";

    public void execute(JSON request, String eventCode, String eventName, String mainModelKey, NoticeProps noticeProps) {
        // 条件判断
        Map<String, Object> conditionParam = new HashMap<>(2);
        conditionParam.put(mainModelKey, request);
        Boolean condition = eventExecuteTool.conditionJudge(noticeProps.getConditionExpress(), conditionParam);
        if (Boolean.FALSE.equals(condition)) {
            log.info("[Event Notice Executor] condition not pass, event code : {}", eventCode);
            return;
        }

        // 变量获取
        JSONObject requestJsonObject = request instanceof JSONObject ? (JSONObject) request : null;
        Map<String, Object> objectMap = null;
        if (!CollectionUtils.isEmpty(noticeProps.getPlaceholderExpressMap())) {
            objectMap = Maps.newHashMapWithExpectedSize(noticeProps.getPlaceholderExpressMap().size());
            for (Map.Entry<String, NoticePlaceHolderDTO> entry : noticeProps.getPlaceholderExpressMap().entrySet()) {
                NoticePlaceHolderDTO noticePlaceHolder = entry.getValue();
                String noticePlaceHolderValue = NoticePlaceHolderType.VARIABLE.equals(noticePlaceHolder.getType()) && Objects.nonNull(requestJsonObject) ?
                    this.getExpressStringValue(requestJsonObject, noticePlaceHolder.getValue()) : noticePlaceHolder.getValue();
                objectMap.put(entry.getKey(), noticePlaceHolderValue);
            }
        }

        // 指定用户发送消息
        if (!CollectionUtils.isEmpty(noticeProps.getRecipientIds())) {
            NoticeTaskCreateReq0 specifyUserCreateReq = new NoticeTaskCreateReq0();
            specifyUserCreateReq.setNoticeBusinessCode(noticeProps.getNoticeSceneCode());
            specifyUserCreateReq.setTaskName(eventName);
            specifyUserCreateReq.setNoticeTargetIds(noticeProps.getRecipientIds());
            specifyUserCreateReq.setParams(objectMap);
            noticeService.createTaskByScene(specifyUserCreateReq);
        }

        if (Objects.isNull(requestJsonObject)) {
            return;
        }

        // 邮箱 短信 站内信
        NoticeTaskCreateReq1 createReq = new NoticeTaskCreateReq1();
        createReq.setNoticeBusinessCode(noticeProps.getNoticeSceneCode());
        createReq.setTaskName(eventName);
        createReq.setParams(objectMap);

        List<NoticeTarget> noticeTargets = new ArrayList<>();
        NoticeTarget noticeTarget = new NoticeTarget();
        noticeTarget.setEmail(this.getExpressStringValue(requestJsonObject, noticeProps.getEmailExpress()));
        noticeTarget.setMobile(this.getExpressStringValue(requestJsonObject, noticeProps.getPhoneExpress()));
        noticeTarget.setId(this.getExpressLongValue(requestJsonObject, noticeProps.getRecipientExpress()));
        if (StringUtils.hasLength(noticeTarget.getEmail()) || StringUtils.hasLength(noticeTarget.getMobile()) || Objects.nonNull(noticeTarget.getId())) {
            noticeTargets.add(noticeTarget);
        } else {
            return;
        }
        createReq.setNoticeTargets(noticeTargets);

        noticeService.createTaskBySceneWithNoticeTarget(createReq);
    }

    private String getExpressStringValue(JSONObject request, String express) {
        if (!StringUtils.hasLength(express)) {
            return null;
        }

        String[] fields = express.split(EXPRESS_SPLIT);
        JSONObject node = request;
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            if (Objects.isNull(node) || !node.containsKey(field)) {
                log.warn("[EventNoticeExecutor] express match failed, express : {}, field : {}, request : {}", express, field, request);
                return null;
            }
            if (i == fields.length - 1) {
                return node.getString(field);
            } else {
                node = node.getJSONObject(field);
            }
        }
        return null;
    }

    private Long getExpressLongValue(JSONObject request, String express) {
        if (!StringUtils.hasLength(express)) {
            return null;
        }

        String[] fields = express.split(EXPRESS_SPLIT);
        JSONObject node = request;
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            if (Objects.isNull(node) || !node.containsKey(field)) {
                log.warn("[EventNoticeExecutor] express match failed, express : {}, field : {}, request : {}", express, field, request);
                return null;
            }
            if (i == fields.length - 1) {
                return node.getLong(field);
            } else {
                node = node.getJSONObject(field);
            }
        }
        return null;
    }
}
