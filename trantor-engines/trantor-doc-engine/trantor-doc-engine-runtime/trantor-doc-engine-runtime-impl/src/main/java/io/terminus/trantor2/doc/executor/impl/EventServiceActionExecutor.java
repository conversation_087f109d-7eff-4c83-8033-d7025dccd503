package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.dto.ServiceDTO;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.context.EventContext;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.executor.ActionContextExecutor;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.service.runtime.api.ServiceExecuteApi;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventServiceActionExecutor implements ActionContextExecutor {

    private static final String REQUEST = "request";
    private static final String MODEL_KEY = "modelKey";

    @Resource
    private EventExecuteTool eventExecuteTool;
    @Resource
    private ServiceExecuteApi serviceExecuteApi;

    public void execute(JSON request, EventContext eventContext, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        ServiceDTO service = actionExecuteNode.getService();

        // 条件判断
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);
        if(Boolean.FALSE.equals(condition)){
            log.info("[Service Action Executor] action condition not pass, action code : {}", service.getCode());
            return;
        }

        // convert
        String convert = actionExecuteNode.getEventActionRel().getConvert();
        Object param = request instanceof JSONObject ?
            ((JSONObject) request).getInnerMap() :
            ((JSONArray) request).stream().map(obj -> ((JSONObject) obj).getInnerMap()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(convert)) {
            param = eventExecuteTool.convert(request, convert);
        }

        // 调用Service
        ServiceExecuteRequest serviceExecuteRequest = new ServiceExecuteRequest();
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(REQUEST, param);
        paramMap.put(MODEL_KEY, actionExecuteNode.getActionModelKey());
        serviceExecuteRequest.setParams(paramMap);
        serviceExecuteRequest.setPortalKey(TrantorContext.getPortalCode());
        serviceExecuteRequest.setTeamId(TrantorContext.getTeamId());

        Response<Object> serviceRequest = serviceExecuteApi.execute(service.getCode(), serviceExecuteRequest);
        if(!serviceRequest.isSuccess()) {
            log.error("[Service Action Executor] service execute failed, service key : {}", service.getCode());
            throw new DocEngineActionRuntimeException("service.execute.failed");
        }
        Object result = serviceRequest.getData();

        // action入参回写
        if (StringUtils.isEmpty(convert) && StringUtils.isEmpty(actionExecuteNode.getEventActionRel().getSourceCode())) {
            eventExecuteTool.requestWriteBack(request, param, null);
        }

        // action返回回写
        if (Objects.nonNull(result)) {
            try{
                eventExecuteTool.responseWriteBack(service.getCode(), null, result, eventContext);
            }catch (Exception e) {
                log.error("[Service Action Executor] response write back failed, result : {}", result, e);
                throw new DocEngineActionRuntimeException("response.write.back.failed");
            }
        }
    }

}
