package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.context.EventRequestContext;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.context.EventContext;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.executor.ActionContextExecutor;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.service.runtime.api.ServiceExecuteApi;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventCallEventActionExecutor implements ActionContextExecutor {

    private static final String REQUEST = "request";
    private static final String MODEL_KEY = "modelKey";
    private static final String EVENT_SERVICE_SUFFIX = "_SERVICE";
    @Resource
    private EventExecuteTool eventExecuteTool;
    @Resource
    private ServiceExecuteApi serviceExecuteApi;

    @Override
    public void execute(JSON request, EventContext eventContext, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        if (Objects.isNull(actionExecuteNode.getEvent())) {
            log.warn("[Event call Event] event not existed, event code : {}", actionExecuteNode.getEventActionRel().getCode());
            throw new DocEngineActionRuntimeException("event.not.existed");
        }
        EventDTO event = actionExecuteNode.getEvent();

        // 条件判断
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);
        if (Boolean.FALSE.equals(condition)) {
            log.info("[Event call Event] action condition not pass, event code : {}", event.getCode());
            return;
        }

        // convert
        String convert = actionExecuteNode.getEventActionRel().getConvert();
        JSON param;
        if (StringUtils.hasLength(convert)) {
            param = (JSON) eventExecuteTool.convert(request, convert);
        } else {
            param = request;
        }

        // 暂存event上下文
        EventRequestContext.EventInfoContext snapshotContext = EventRequestContext.snapshot();

        // 调用EventService
        ServiceExecuteRequest serviceExecuteRequest = new ServiceExecuteRequest();
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(REQUEST, param);
        paramMap.put(MODEL_KEY, actionExecuteNode.getActionModelKey());
        serviceExecuteRequest.setParams(paramMap);
        serviceExecuteRequest.setPortalKey(TrantorContext.getPortalCode());
        serviceExecuteRequest.setTeamId(TrantorContext.getTeamId());

        String serviceCode = event.getCode() + EVENT_SERVICE_SUFFIX;
        Response<Object> serviceRequest = serviceExecuteApi.execute(serviceCode, serviceExecuteRequest);
        if (!serviceRequest.isSuccess()) {
            log.error("[Event call Event Executor] service execute failed, service key : {}", serviceCode);
            throw new DocEngineActionRuntimeException("service.execute.failed");
        }
        Object result = serviceRequest.getData();

        // 重置event上下文
        EventRequestContext.reset(snapshotContext);

        // action入参回写无用 服务不支持

        // action返回回写
        eventExecuteTool.responseWriteBack(event.getCode(), event.getReturnModel(), result, eventContext);
    }

}
