package io.terminus.trantor2.doc.loader;

import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.doc.config.ActionAppConfiguration;
import io.terminus.trantor2.doc.config.ActionConfiguration;
import io.terminus.trantor2.doc.serializer.DateTime2LongCodeC;
import io.terminus.trantor2.doc.serializer.MetaModelCodec;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventStarter implements ApplicationContextAware, CommandLineRunner {

    private ApplicationContext applicationContext;

    @Resource
    private ActionConfiguration actionConfiguration;

    @Resource
    private MetaQueryService metaQueryService;

    @Resource
    private ActionUploader actionUploader;
    @Resource
    private TeamService teamService;

    @Override
    public void run(String... args) throws Exception {
        this.initFastJsonConfig();
        this.initAppConfig();
        actionUploader.upLoadAction(this.applicationContext);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private void initAppConfig() {
        if(CollectionUtils.isEmpty(actionConfiguration.getApps())) {
            return;
        }

        if(Objects.isNull(TrantorContext.getContext())) {
            TrantorContext.init();
        }

        if(StringUtils.hasLength(actionConfiguration.getTeamCode())) {
            actionConfiguration.setTeamId(teamService.getTeamIdByCode(actionConfiguration.getTeamCode()));
        }
        TrantorContext.setTeamId(actionConfiguration.getTeamId());
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();

        Map<String, Long> appKeyIdMap = metaQueryService
            .findByKeyIn(context, actionConfiguration.getApps().stream().map(ActionAppConfiguration::getAppKey).filter(Objects::nonNull).collect(Collectors.toList()))
            .stream()
            .collect(Collectors.toMap(MetaTreeNode::getKey, MetaTreeNodeExt::getId));

        actionConfiguration.getApps().stream()
            .filter(appConfiguration -> appKeyIdMap.containsKey(appConfiguration.getAppKey()))
            .forEach(appConfiguration -> appConfiguration.setAppId(appKeyIdMap.get(appConfiguration.getAppKey())));

        actionConfiguration.getApps().stream()
            .filter(appConfiguration -> !appKeyIdMap.containsKey(appConfiguration.getAppKey()))
            .forEach(appConfiguration -> log.error("[ActionLoader] app not existed, app key : {}", appConfiguration.getAppKey()));
    }

    private void initFastJsonConfig() {
        ParserConfig.global.putDeserializer(Long.class, MetaModelCodec.instance);
        ParserConfig.global.putDeserializer(Long.TYPE, MetaModelCodec.instance);
        SerializeConfig.getGlobalInstance().put(Long.class, MetaModelCodec.instance);
        SerializeConfig.getGlobalInstance().put(Long.TYPE, MetaModelCodec.instance);

        if (actionConfiguration.isDate2long()) {
            SerializeConfig.getGlobalInstance().put(LocalDateTime.class, DateTime2LongCodeC.instance);
        }
    }

}
