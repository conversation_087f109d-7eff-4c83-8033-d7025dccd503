package io.terminus.trantor2.doc.meta;

import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2.5.23.1228
 */
@Data
@Component
public class EventMetaCache {

    private final MetaCache metaCache;

    public EventMeta get(String key) {
        return fromService(key);
    }

    private EventMeta fromService(String key) {
        EventMeta eventMeta = fromService_guess(key, key);
        if (eventMeta != null) {
            return eventMeta;
        }
        eventMeta = fromService_guess(key, key + "_SERVICE");
        return eventMeta;
    }

    private EventMeta fromService_guess(String key, String serviceKey) {
        MetaTreeNodeExt node = metaCache.get(serviceKey);
        if (node == null) {
            return null;
        }
        if (!Objects.equals(node.getType(), MetaType.ServiceDefinition.name())) {
            return null;
        }
        ServiceMeta serviceMeta = ServiceMeta.newInstanceFrom(node, ServiceMeta.class);
        if (serviceMeta.getResourceProps() == null) {
            return null;
        }
        if (serviceMeta.getResourceProps().getServiceType() != ServiceType.EVENT) {
            return null;
        }
        if (serviceMeta.getResourceProps().getEventProps() == null) {
            return null;
        }
        EventMeta eventMeta = new EventMeta();
        eventMeta.setKey(key);
        eventMeta.setName(serviceMeta.getName());
        eventMeta.setProps(serviceMeta.getResourceProps().getEventProps());
        return eventMeta;
    }
}
