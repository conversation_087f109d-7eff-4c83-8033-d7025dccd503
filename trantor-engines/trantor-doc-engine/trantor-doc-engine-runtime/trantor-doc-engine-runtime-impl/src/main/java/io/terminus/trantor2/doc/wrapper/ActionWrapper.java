package io.terminus.trantor2.doc.wrapper;

import lombok.Data;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
@Data
public class ActionWrapper {

    private Object bean;

    private Method method;

    private String name;

    private String code;

    private int order;

    private Type requestType;

    private Type responseType;

    private String requestModelKey;



}
