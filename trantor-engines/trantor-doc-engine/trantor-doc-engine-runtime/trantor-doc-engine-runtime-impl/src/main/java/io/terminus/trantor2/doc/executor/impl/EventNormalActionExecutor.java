package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.util.TypeUtils;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.RootModel;
import io.terminus.common.runtime.context.RequestContext;
import io.terminus.common.runtime.util.I18nUtils;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.dict.DocEngineActionLanguageTypeEnum;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.config.ActionAppConfiguration;
import io.terminus.trantor2.doc.config.ActionConfiguration;
import io.terminus.trantor2.doc.context.EventContext;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.executor.ActionContextExecutor;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.doc.loader.ActionWrapperContainer;
import io.terminus.trantor2.doc.wrapper.ActionWrapper;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.runtime.api.permission.ActionDataPermissionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventNormalActionExecutor implements ActionContextExecutor {

    @Resource
    private EventExecuteTool eventExecuteTool;
    @Resource
    private ActionConfiguration actionConfiguration;

    @Resource
    private ActionDataPermissionHandler actionDataPermissionHandler;

    private static final String GROOVY_DEFAULT_FUNCTION = "execute";
    private static final String EXT_FIELD_PREFIX = "ext";
    private static final String EXTRA = "extra";
    private static final String END_WITH_DATE = "Date";
    private static final String END_WITH_TIME = "Time";
    private static final String END_WITH_AT = "At";

    private static final String EXTRA_JSON = "extraJson";
    private static final String ID = "id";
    private final ConcurrentHashMap<String, Invocable> groovyScriptEngineMap = new ConcurrentHashMap<>();

    public void execute(JSON request, EventContext eventContext, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        ActionDTO action = actionExecuteNode.getAction();

        // 条件判断
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);
        if(Boolean.FALSE.equals(condition)){
            log.info("[Normal Action Executor] action condition not pass, action code : {}, event code : {}",
                action.getCode(), actionExecuteNode.getEventActionRel().getEventCode());
            return;
        }

        // 调用Action
        String convert = actionExecuteNode.getEventActionRel().getConvert();
        Object param = null;
        Object result = null;
        if(DocEngineActionLanguageTypeEnum.GROOVY.getType().equals(action.getLanguageType())) {
            // convert
            try {
                param = this.convert(request, convert, action.getRequestType());
            } catch (ClassNotFoundException e) {
                log.error("[Normal Action Executor] action request type error, action code : {}, request type : {}",
                    action.getCode(), action.getRequestType(), e);
                throw new DocEngineActionRuntimeException("action.request.type.error");
            }
            // Groovy
            long groovyExecuteStartTime = System.currentTimeMillis();
            result = this.groovyExecute(param, action);
            log.info("[Normal Action Executor] groovy execute spend [{}]ms, actionCode : {}, eventCode : {}",
                System.currentTimeMillis() - groovyExecuteStartTime, action.getCode(), actionExecuteNode.getEventActionRel().getEventCode());
        }else {
            // 特殊处理 去除前缀以找到注解对应的bean
            ActionWrapper actionWrapper = ActionWrapperContainer.getActionMap().get(KeyUtil.shortKey(action.getCode()));
            if (Objects.isNull(actionWrapper)) {
                log.warn("[Normal Action Executor] actionWrapper not existed, action code : {}", action.getCode());
                throw new DocEngineActionRuntimeException("event.action.bean.not.existed");
            }
            // convert
            param = this.convert(request, convert, actionWrapper, actionExecuteNode.getActionModelKey());

            // 植入数据权限查询条件
            fillDataPermissionCondition(eventContext.getRequest().getDataConditionPermissionKey(), param);

            // 设置上下文appId
            ActionAppConfiguration actionAppConfiguration = actionConfiguration.matchApp(actionWrapper.getBean().getClass());
            RequestContext.setAppId(actionAppConfiguration.getAppId());
            RequestContext.setAppKey(actionAppConfiguration.getAppKey());

            // Java
            long javaExecuteStartTime = System.currentTimeMillis();
            result = this.javaExecute(param, actionWrapper);
            log.info("[Normal Action Executor] java execute spend [{}]ms, actionCode : {}, eventCode : {}",
                System.currentTimeMillis() - javaExecuteStartTime, action.getCode(), actionExecuteNode.getEventActionRel().getEventCode());
        }

        // action入参回写
        if (StringUtils.isEmpty(convert) && StringUtils.isEmpty(actionExecuteNode.getEventActionRel().getSourceCode())) {
            eventExecuteTool.requestWriteBack(request, param, actionExecuteNode.getActionModelKey());
        }

        // action返回回写
        if (!Void.class.getTypeName().equals(action.getResponseType()) && Objects.nonNull(result)) {
            try{
                eventExecuteTool.responseWriteBack(action.getCode(), action.getReturnModel(), result, eventContext);
            }catch (Exception e) {
                log.error("[Normal Action Executor] response write back failed, result : {}", result, e);
                throw new DocEngineActionRuntimeException("response.write.back.failed");
            }
        }
    }

    private Object convert(JSON request, String convert, String requestType) throws ClassNotFoundException {
        Object param;
        if (StringUtils.isNotEmpty(convert)) {
            param = eventExecuteTool.convert(request, convert);
        } else {
            param = TypeUtils.cast(request, Class.forName(requestType), ParserConfig.global);
        }
        return param;
    }

    private Object groovyExecute(Object param, ActionDTO action) {
        // groovy实例需要缓存 否则每次会新建class最后导致元空间内存不足
        String key = String.format("%s_%d", action.getCode(), action.getGroovyScript().length());
        Invocable invocable;
        if(groovyScriptEngineMap.containsKey(key)) {
            invocable = groovyScriptEngineMap.get(key);
        }else {
            ScriptEngineManager factory = new ScriptEngineManager();
            ScriptEngine engine = factory.getEngineByName("groovy");
            try {
                engine.eval(action.getGroovyScript());
                invocable = (Invocable) engine;
                groovyScriptEngineMap.put(key, invocable);
            } catch (ScriptException e) {
                log.error("[Normal Action Executor] action groovy eval failed, action code : " + action.getCode(), e);
                throw new DocEngineActionRuntimeException("action.groovy.eval.failed");
            }
        }

        try {
            return invocable.invokeFunction(GROOVY_DEFAULT_FUNCTION, param);
        } catch (ScriptException e) {
            log.error("[Normal Action Executor] action groovy execute failed, param : " + param, e);
            throw new DocEngineActionRuntimeException(ErrorType.EVENT_ACTION_EXECUTE_BIZ_ERROR, ExceptionUtils.getRootCauseMessage(e), action.getCode(), e,
                new Object[]{ ExceptionUtils.getRootCauseMessage(e), action.getCode()});
        } catch (NoSuchMethodException e) {
            log.error("[Normal Action Executor] action groovy execute failed, no such method, action code : " + action.getCode(), e);
            throw new DocEngineActionRuntimeException(ErrorType.EVENT_ACTION_EXECUTE_ERROR, ExceptionUtils.getRootCauseMessage(e), action.getCode(), e,
                new Object[]{ ExceptionUtils.getRootCauseMessage(e), action.getCode()});
        }
    }

    private Object convert(JSON request, String convert, ActionWrapper actionWrapper, String actionModelKey) {
        Object param;
        if (StringUtils.isNotEmpty(convert)) {
            param = eventExecuteTool.convert(request, convert);
            if(param instanceof JSON) {
                param = TypeUtils.cast(param, actionWrapper.getRequestType(), ParserConfig.global);
            }
        } else {
            param = TypeUtils.cast(request, actionWrapper.getRequestType(), ParserConfig.global);

            // extra 特殊处理
            if(request instanceof JSONObject) {
                this.initParamExtra(param, ((JSONObject) request).getInnerMap(), new HashMap<>(16));
            }else if(param instanceof List && request instanceof JSONArray) {
                JSONArray requestArray = (JSONArray) request;
                List<Object> paramList = (List) param;
                Map<String, List<Field>> objFieldMap = new HashMap<>(16);
                for(int i = 0; i < requestArray.size(); i ++) {
                    Object item = paramList.get(i);
                    this.initParamExtra(item, ((JSONObject) requestArray.get(i)).getInnerMap(), objFieldMap);
                }
            }
        }
        return param;
    }

    private void initParamExtra(Object obj, Map<String, Object> paramMap, Map<String, List<Field>> objFieldMap) {
        if (Objects.isNull(obj)) {
            return;
        }

        if (!(obj instanceof RootModel)) {
            return;
        }

        // 获取类所有字段 使用map避免反复获取
        List<Field> fields;
        String className = obj.getClass().getName();
        if (objFieldMap.containsKey(className)) {
            fields = objFieldMap.get(className);
        } else {
            Class clazz = obj.getClass();
            fields = new ArrayList<>();
            while (clazz != null) {
                new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()))
                    .stream().filter(field -> !Modifier.isStatic(field.getModifiers()))
                    .forEach(fields::add);
                clazz = clazz.getSuperclass();
            }
            objFieldMap.put(className, fields);
        }

        Map<String, Field> fieldMap = fields.stream().collect(Collectors.toMap(Field::getName, Function.identity(), (a,b) -> a));
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {

            if (!fieldMap.containsKey(entry.getKey())) {
                // 不包含的字段需要放入extra
                if (EXTRA.equals(entry.getKey()) || EXTRA_JSON.equals(entry.getKey())) {
                    continue;
                }

                if (!entry.getKey().startsWith(EXT_FIELD_PREFIX)) {
                    continue;
                }

                if (entry.getValue() instanceof List) {
                    continue;
                }

                if (Objects.isNull(entry.getValue())) {
                    // 一对多同步的扩展字段可能为null
                    continue;
                }

                RootModel rootModel = (RootModel) obj;
                // 初始化extra
                if (Objects.isNull(rootModel.getExtra())) {
                    rootModel.setExtra(new HashMap<>(16));
                }

                if (entry.getValue() instanceof JSONObject) {
                    if(((JSONObject) entry.getValue()).containsKey(ID)) {
                        //ext开头的关联模型
                        rootModel.getExtra().put(entry.getKey(), ((JSONObject) entry.getValue()).getLong(ID));
                    } else {
                        //ext开头 是对象 但没有id
                        log.warn("action.param.extra.field.illegal : {}", entry.getKey());
                    }
                } else if (entry.getValue() instanceof Long && (entry.getKey().endsWith(END_WITH_DATE) || entry.getKey().endsWith(END_WITH_TIME) || entry.getKey().endsWith(END_WITH_AT))) {
                    //ext开头的时间戳
                    LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli((Long) entry.getValue()), ZoneId.systemDefault());
                    rootModel.getExtra().put(entry.getKey(), dateTime);
                } else {
                    rootModel.getExtra().put(entry.getKey(), entry.getValue());
                }
                continue;
            }

            if(entry.getValue() instanceof JSONArray){
                // 集合
                // 获取Java对象
                Field field = fieldMap.get(entry.getKey());
                field.setAccessible(true);
                List<Object> objs = null;
                try {
                    Object fieldValue = field.get(obj);
                    if(!(fieldValue instanceof List)) {
                        // Set无法遍历处理 因为无序
                        continue;
                    }
                    objs = (List<Object>) fieldValue;
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                JSONArray jsonArray = (JSONArray) entry.getValue();
                // 遍历集合
                for (int i = 0; i < jsonArray.size(); i++) {
                    if(Objects.nonNull(jsonArray.get(i)) && jsonArray.get(i) instanceof JSONObject) {
                        this.initParamExtra(objs.get(i), (JSONObject) jsonArray.get(i), objFieldMap);
                    }
                }
            }

            if(entry.getValue() instanceof JSONObject){
                // 单个对象
                Field field = fieldMap.get(entry.getKey());
                field.setAccessible(true);
                try {
                    this.initParamExtra(field.get(obj), (JSONObject) entry.getValue(), objFieldMap);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private static List<Field> getAllNotStaticFields(Object object) {
        Class clazz = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()))
                .stream().filter(field -> !Modifier.isStatic(field.getModifiers()))
                .forEach(fieldList::add);
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }

    private Object javaExecute(Object param, ActionWrapper actionWrapper) {
        try {
            return actionWrapper.getMethod().invoke(actionWrapper.getBean(), param);
        }
        catch (BusinessException e) {
//            log.error("[Normal Action Executor] action java execute failed, code : " + actionWrapper.getCode() + ", param : " + param, e);
            String message = I18nUtils.getMessage(e.getErrorCode(), e.getArgs());
            throw new DocEngineActionRuntimeException(ErrorType.EVENT_ACTION_EXECUTE_BIZ_ERROR, message, actionWrapper.getCode(), e, new Object[]{message,actionWrapper.getCode()});
        }
        catch (Exception e) {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            String message;
            if (rootCause instanceof BusinessException) {
                message = I18nUtils.getMessage(((BusinessException) rootCause).getErrorCode(), ((BusinessException) rootCause).getArgs());
            } else {
                message = ExceptionUtils.getMessage(rootCause);
            }
//            log.error("[Normal Action Executor] action java execute failed, code : " + actionWrapper.getCode() + ", param : " + param + "error:" + Throwables.getStackTraceAsString(e));
            throw new DocEngineActionRuntimeException(ErrorType.EVENT_ACTION_EXECUTE_ERROR, message, actionWrapper.getCode(), e, new Object[]{message,actionWrapper.getCode()});
        }
    }

    private void fillDataPermissionCondition(String dataConditionPermissionKey, Object param) {
        actionDataPermissionHandler.handle(dataConditionPermissionKey, param);
    }
}
