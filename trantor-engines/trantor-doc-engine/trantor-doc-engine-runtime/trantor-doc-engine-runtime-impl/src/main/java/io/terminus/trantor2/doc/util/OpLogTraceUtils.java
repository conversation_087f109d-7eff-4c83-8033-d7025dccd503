package io.terminus.trantor2.doc.util;

import com.google.common.base.CaseFormat;
import io.terminus.operation.log.api.model.OperationLogModelRelationDO;
import io.terminus.operation.log.sdk.client.OperationLogClient;
import io.terminus.trantor2.doc.api.context.EventRequestContext;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * OpLogTraceUtils
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OpLogTraceUtils {

    public static final String ID_FIELD = "id";
    public static final String VERSION_FIELD = "updatedAt";
    public static final String DELETED_FIELD = "deleted";
    public static final String CREATE_DATA_EVENT = "CreateDataEvent";
    public static final String DELETE_DATA_EVENT = "DeleteDataEvent";
    public static final String UPDATE_DATA_EVENT = "UpdateDataEvent";
    public static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");

    /**
     * 记录操作日志信息
     *
     * @param param                   入参
     * @param eventKey                事件key {@link OpLogTraceUtils#CREATE_DATA_EVENT}、{@link OpLogTraceUtils#DELETE_DATA_EVENT}、{@link OpLogTraceUtils#UPDATE_DATA_EVENT}
     * @param lowerUnderscoreModelKey 下划线格式的模型key
     */
    public static void trace(Object param, String eventKey, String lowerUnderscoreModelKey) {
        EventRequestContext.EventInfoContext eventInfoContext = EventRequestContext.getEventInfoContext();
        String eventCode = eventInfoContext.getEventCode();
        if (StringUtils.isEmpty(eventCode) || param == null || StringUtils.isEmpty(lowerUnderscoreModelKey)) {
            return;
        }
        OperationLogModelRelationDO oplogModelRelation = OperationLogClient.getOplogModelRelation(lowerUnderscoreModelKey);
        if (oplogModelRelation == null) {
            return;
        }
        Map<String, EventRequestContext.EventOplogInfo> eventOplogInfoMap = buildEventOplogInfoMap(oplogModelRelation, eventKey, param);
        if (MapUtils.isEmpty(eventOplogInfoMap)) {
            return;
        }
        eventInfoContext.getEventOplogInfoMap().putAll(eventOplogInfoMap);
    }

    private static Map<String, EventRequestContext.EventOplogInfo> buildEventOplogInfoMap(OperationLogModelRelationDO oplogModelRelation,
                                                                                          String eventKey,
                                                                                          Object param) {
        Map<String, EventRequestContext.EventOplogInfo> eventOplogInfoMap;
        if (StringUtils.isEmpty(oplogModelRelation.getBizKeyField())) {
            return Collections.emptyMap();
        }
        if (param instanceof Collection && !CollectionUtils.isEmpty((Collection<?>) param)) {
            Collection<?> collectionParam = (Collection<?>) param;
            eventOplogInfoMap = new HashMap<>(collectionParam.size());
            collectionParam.forEach(item -> {
                EventRequestContext.EventOplogInfo eventOplogInfo = buildEventOplogInfo(item, eventKey, oplogModelRelation);
                putLogInfo(eventOplogInfo, eventOplogInfoMap);
            });
        } else {
            eventOplogInfoMap = new HashMap<>(2);
            EventRequestContext.EventOplogInfo eventOplogInfo = buildEventOplogInfo(param, eventKey, oplogModelRelation);
            putLogInfo(eventOplogInfo, eventOplogInfoMap);
        }
        return eventOplogInfoMap;
    }

    private static EventRequestContext.EventOplogInfo buildEventOplogInfo(Object param, String eventKey, OperationLogModelRelationDO relationDO) {
        String bizKeyField = relationDO.getBizKeyField();
        EventRequestContext.EventOplogInfo logInfo = new EventRequestContext.EventOplogInfo();
        logInfo.setModelKey(relationDO.getModelKey());
        logInfo.setModelName(relationDO.getModelName());
        logInfo.setBizKeyField(bizKeyField);
        logInfo.setEventKey(eventKey);
        if (param instanceof Long) {
            logInfo.setBizId((Long) param);
            return logInfo;
        }
        Object relationBizId = getFieldValue(param, CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, relationDO.getRelationKeyField()));
        logInfo.setRelationBizId(relationBizId == null ? null : Long.valueOf(relationBizId.toString()));
        Object bizFieldValue = getFieldValue(param, CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, bizKeyField));
        logInfo.setBizKey(bizFieldValue == null ? null : bizFieldValue.toString());
        Object idFieldValue = getFieldValue(param, ID_FIELD);
        logInfo.setBizId(idFieldValue == null ? null : compatibleLongValue(idFieldValue));
        Object versionFieldValue = getFieldValue(param, VERSION_FIELD);
        if (versionFieldValue != null) {
            Long versionMills = compatibleDateTimeValue(versionFieldValue);
            logInfo.setBizVersion(versionMills);
        }
        Object deletedFieldValue = getFieldValue(param, DELETED_FIELD);
        if (deletedFieldValue != null) {
            Long deletedFieldLongValue = compatibleLongValue(deletedFieldValue);
            if (deletedFieldLongValue != null && deletedFieldLongValue != 0) {
                logInfo.setEventKey(DELETE_DATA_EVENT);
            }
        }
        return logInfo;
    }

    private static void putLogInfo(EventRequestContext.EventOplogInfo eventOplogInfo,
                                   Map<String, EventRequestContext.EventOplogInfo> eventOplogInfoMap) {
        eventOplogInfoMap.put(eventOplogInfo.getModelKey() + "@" + eventOplogInfo.getEventKey() + "@" + eventOplogInfo.getBizId(), eventOplogInfo);
    }

    private static Long compatibleLongValue(Object o) {
        if (o instanceof Long) {
            return (Long) o;
        } else if (o instanceof BigDecimal) {
            return ((BigDecimal) o).longValue();
        } else if (o instanceof Integer) {
            return ((Integer) o).longValue();
        }
        return null;
    }

    private static Long compatibleDateTimeValue(Object o) {
        if (o instanceof LocalDateTime) {
            long versionLongValue = ((LocalDateTime) o).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 四舍五入
            BigDecimal versionSeconds = new BigDecimal(versionLongValue).divide(ONE_THOUSAND, 0, RoundingMode.HALF_UP);
            return versionSeconds.longValue() * 1000;
        } else if (o instanceof Long) {
            return (Long) o;
        } else if (o instanceof Integer) {
            return ((Integer) o).longValue();
        }
        return null;
    }

    private static Object getFieldValue(Object model, String fieldName) {
        if (model instanceof Map) {
            return ((Map<?, ?>) model).get(fieldName);
        }
        Field field = ReflectionUtils.findField(model.getClass(), fieldName);
        if (field == null) {
            return null;
        }
        field.setAccessible(true);
        return ReflectionUtils.getField(field, model);
    }
}
