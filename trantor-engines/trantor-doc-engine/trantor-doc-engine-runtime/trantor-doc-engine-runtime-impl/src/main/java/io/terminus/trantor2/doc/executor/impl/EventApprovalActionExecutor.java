package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor.workflow.common.utils.JsonUtil;
import io.terminus.trantor.workflow.runtime.v2.model.dto.WorkflowGroupProps;
import io.terminus.trantor2.doc.api.dto.ApprovalDTO;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.doc.executor.event.StartApprovalEvent;
import io.terminus.trantor2.doc.executor.listener.EventApprovalActionListener;
import io.terminus.trantor2.doc.executor.producer.EventApprovalActionProducer;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventApprovalActionExecutor {

    private final static String AFTER = "after";

    @Resource
    private EventExecuteTool eventExecuteTool;
    @Resource
    private MetaCache metaCache;

    @Autowired
    private EventApprovalActionListener eventApprovalActionListener;

    public void execute(JSON request, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        ApprovalDTO approval = actionExecuteNode.getApproval();
        MetaTreeNodeExt metaTreeNodeExt = metaCache.get(approval.getCode());
        if (Objects.nonNull(metaTreeNodeExt)) {
            WorkflowGroupProps workflowGroupProps = JsonUtil.convertObject(metaTreeNodeExt.getProps(), WorkflowGroupProps.class);
            actionExecuteNode.setActionModelKey(workflowGroupProps.getRelatedModel().getModelKey());
        }

        // 条件判断
        if (this.judgeApprovalRequest(request, actionExecuteNode.getActionModelKey())) {
            conditionParam.put(actionExecuteNode.getActionModelKey(), ((JSONObject) request).getJSONObject(AFTER));
        }
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);

        if (Boolean.FALSE.equals(condition)) {
            log.info("[Approval Action Executor] action condition not pass, action code : {}", approval.getCode());
            return;
        }

        eventApprovalActionListener.subscribe(new StartApprovalEvent(request, actionExecuteNode, conditionParam));
    }

    private boolean judgeApprovalRequest(JSON request, String modelKey) {
        if (!(request instanceof JSONObject)) {
            // 数组？反正不是变更前后的对象
            return false;
        }

        JSONObject jsonObject = (JSONObject) request;

        if (!jsonObject.containsKey(AFTER)) {
            // 入参不包含after 一定不是变更前后的对象
            return false;
        }

        // 查询元数据 获取所有业务字段（不考虑系统字段）
        MetaTreeNodeExt metaTreeNodeExt = metaCache.get(modelKey);
        DataStructNode dataStructNode = DataStructNode.newInstanceFrom(metaTreeNodeExt);
        Set<String> bizFieldKeys = dataStructNode.getChildren().stream()
            .filter(field -> Boolean.FALSE.equals(field.getProps().getIsSystemField()))
            .map(DataStructFieldNode::getAlias).collect(Collectors.toSet());

        if (!bizFieldKeys.contains(AFTER)) {
            // 业务字段无after 一定是变更前后的对象
            return true;
        }

        // 求交集
        bizFieldKeys.retainAll(jsonObject.keySet());
        // 交集为0 则一定是变更前后的对象
        return bizFieldKeys.size() == 0;
    }

}
