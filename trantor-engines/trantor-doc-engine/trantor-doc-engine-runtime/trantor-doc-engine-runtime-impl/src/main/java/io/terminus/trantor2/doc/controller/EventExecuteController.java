package io.terminus.trantor2.doc.controller;

import com.alibaba.fastjson.JSON;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.context.EventRequest;
import io.terminus.trantor2.doc.constant.TrantorServiceSpiConstant;
import io.terminus.trantor2.doc.api.executor.EventExecutor;
import io.terminus.trantor2.service.common.spi.annotation.ServiceSPI;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(path = "/api/trantor/event")
public class EventExecuteController {

    @Resource
    private EventExecutor eventExecutor;

    @PostMapping(path = "/execute")
    public Response<JSON> execute(@RequestBody EventRequest request){
        JSON result = eventExecutor.executeWithTransaction(request);
        return Response.ok(result);
    }

//    @ServiceSPI(key = TrantorServiceSpiConstant.EVENT_SERVICE_EXECUTE_SPI_KEY)
    @PostMapping(path = "/service/execute")
    public Response<JSON> serviceEventExecute(@RequestBody EventRequest request){
        return eventExecutor.serviceEventExecute(request);
    }

//    @ServiceSPI(key = TrantorServiceSpiConstant.SPI_KEY)
    public Response<JSON> spiExecute(@RequestBody EventRequest request){
        JSON result = eventExecutor.execute(request);
        return Response.ok(result);
    }
}
