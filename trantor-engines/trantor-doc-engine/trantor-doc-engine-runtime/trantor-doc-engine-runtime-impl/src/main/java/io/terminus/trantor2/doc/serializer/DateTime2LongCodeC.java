package io.terminus.trantor2.doc.serializer;

import com.alibaba.fastjson.parser.deserializer.Jdk8DateCodec;
import com.alibaba.fastjson.serializer.BeanContext;
import com.alibaba.fastjson.serializer.JSONSerializer;

import java.io.IOException;
import java.lang.reflect.Type;

public class DateTime2LongCodeC extends Jdk8DateCodec {
    public static final DateTime2LongCodeC instance = new DateTime2LongCodeC();
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        fillDefaultFormat(serializer, null);
        super.write(serializer, object, fieldName, fieldType, features);
    }

    @Override
    public void write(JSONSerializer serializer, Object object, BeanContext context) throws IOException {
        fillDefaultFormat(serializer, context);
        super.write(serializer, object, context);
    }

    private static void fillDefaultFormat(JSONSerializer serializer ,BeanContext context) {
        if(context == null || context.getFormat() == null) {
            serializer.setDateFormat("millis");
        }
    }

    private DateTime2LongCodeC() {

    }
}
