package io.terminus.trantor2.doc.executor;

import com.alibaba.fastjson.JSON;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.context.EventContext;

import java.util.Map;

/**
 * <AUTHOR>
 */
@FunctionalInterface
public interface ActionContextExecutor {

    /**
     * Action执行
     * @param request 入参
     * @param eventContext 上下文
     * @param actionExecuteNode action节点信息
     * @param conditionParam 执行条件
     */
    void execute(JSO<PERSON> request, EventContext eventContext, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam);

}
