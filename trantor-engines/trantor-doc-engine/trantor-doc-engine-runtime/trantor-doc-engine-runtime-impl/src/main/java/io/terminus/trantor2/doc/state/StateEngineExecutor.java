package io.terminus.trantor2.doc.state;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.base.CaseFormat;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.api.model.RootModel;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.context.EventRequestContext;
import io.terminus.trantor2.doc.api.node.EventStateConfigNode;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.meta.EventMeta;
import io.terminus.trantor2.doc.meta.EventMetaCache;
import io.terminus.trantor2.doc.util.OpLogTraceUtils;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import io.terminus.trantor2.model.common.model.condition.ConditionValue;
import io.terminus.trantor2.model.common.model.condition.SingleCondition;
import io.terminus.trantor2.model.common.model.request.BatchUpdateObject;
import io.terminus.trantor2.model.common.model.request.QueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.rule.engine.api.model.result.RuleEngineResult;
import io.terminus.trantor2.rule.engine.client.RuleEngineExecutor;
import io.terminus.trantor2.rule.engine.util.ConditionConvertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 状态配置加载
 *
 * <AUTHOR>
 * @date 2023/4/13 2:35 下午
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StateEngineExecutor {

    private final MetaCache metaCache;
    private final EventMetaCache eventMetaCache;
    private static final String INIT = "INIT";
    private static final String EXTRA = "extra";
    private static final String CONTEXT = "context";
    private final RuleEngineExecutor ruleEngineExecutor;
    private final DataStructDataApi dataStructDataApi;

    public void execute(Object model) {
        Object obj = null;
        if (model instanceof Collection) {
            if (((Collection<?>) model).size() == 0) {
                obj = new Object();
            } else {
                obj = ((Collection<?>) model).iterator().next();
            }
        } else {
            obj = model;
        }

        execute(model, obj.getClass());
    }

    public void execute(Object model, Class<?> modelClass) {
        String eventCode = EventRequestContext.getEventCode();
        if (!StringUtils.hasLength(eventCode)) {
            return;
        }

        EventStateConfigNode stateConfigByEventKey = getStateConfigByEventKey(eventCode);
        if (CollectionUtils.isEmpty(stateConfigByEventKey.getModelStateConfigNodes())) {
            return;
        }

        String modeAlias = getModeAlias(modelClass);

        List<EventStateConfigNode.ModelStateConfigNode> modelStateConfig = stateConfigByEventKey.getByModelAliasList(modeAlias);
        if (modelStateConfig == null) {
            return;
        }

        //同模型多字段的状态变更支持
        Map<String, List<EventStateConfigNode.ModelStateConfigNode>> fieldGroupConfig = modelStateConfig.stream().collect(Collectors.groupingBy(EventStateConfigNode.ModelStateConfigNode::getFieldAlisa));

        fieldGroupConfig.forEach((k, v) -> {
            EventStateConfigNode.ModelStateConfigNode modelStateConfigNode = v.get(0);
            if (modelStateConfigNode.getFieldAlisa().contains("_")) {
                String fieldKey = underlineToCamel(modelStateConfigNode.getFieldAlisa());
                modelStateConfigNode.setFieldAlisa(fieldKey);
            }

            if (model instanceof Collection) {
                for (Object o : (Collection<?>) model) {
                    changeStatus(o, modelStateConfigNode);
                    drawRelateModelIds(o, modelStateConfigNode);
                }
            } else {
                changeStatus(model, modelStateConfigNode);
                drawRelateModelIds(model, modelStateConfigNode);
            }

        });

        EventRequestContext.getEventInfoContext().stateMatch(modeAlias);
    }

    /**
     * 事件完成处理
     */
    public void eventCompleteHandle() {
        String eventCode = EventRequestContext.getEventCode();
        if (!StringUtils.hasLength(eventCode)) {
            return;
        }

        EventStateConfigNode stateConfigByEventKey = getStateConfigByEventKey(eventCode);
        if (CollectionUtils.isEmpty(stateConfigByEventKey.getModelStateConfigNodes())) {
            return;
        }

        // 判断是否有未执行的模型状态变更
        EventRequestContext.EventInfoContext eventInfoContext = EventRequestContext.getEventInfoContext();
        List<EventStateConfigNode.ModelStateConfigNode> unExecuteConfig = stateConfigByEventKey.getModelStateConfigNodes().stream()
            .filter(item -> !eventInfoContext.judgeModelStateExecute(item.getModelAlisa()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unExecuteConfig)) {
            return;
        }
        unExecuteConfig.forEach(item -> {
            String modelKey = KeyUtil.newKeyUnderModule(item.getModuleKey(), item.getModelAlisa());
            List<Object> modeIds = EventRequestContext.getEventInfoContext().getModeIds(modelKey);
            if (CollectionUtils.isEmpty(modeIds)) {
                log.warn("event complete but exist mode state un match and not found model ids, model is {}", item.getModelAlisa());
                return;
            }

            List<Map<String, Object>> modelData = findModelByIds(modeIds, KeyUtil.newKeyUnderModule(item.getModuleKey(), item.getModelAlisa()));
            if (modelData == null) {
                log.error("find model by ids is null, ids is {}", JSON.toJSONString(modeIds));
                return;
            }

            modelData.forEach(model -> {
                changeStatus(model, item);
                traceOplog(model, item.getModuleKey());
            });

            BatchUpdateObject updateObject = new BatchUpdateObject();
            updateObject.setDataList(modelData);
            updateObject.setTeamId(TrantorContext.getTeamId());
            updateObject.setModelAlias(KeyUtil.newKeyUnderModule(item.getModuleKey(), item.getModelAlisa()));
            dataStructDataApi.batchUpdateById(updateObject);
        });
    }

    private void drawRelateModelIds(Object model, EventStateConfigNode.ModelStateConfigNode modelStateConfig) {
        if (CollectionUtils.isEmpty(modelStateConfig.getModelRelationInfos())) {
            return;
        }

        for (EventStateConfigNode.ModelRelationInfo modelRelationInfo : modelStateConfig.getModelRelationInfos()) {
            Object fieldValue = getFieldValue(model, modelRelationInfo.getRelatedFieldAlias());
            EventRequestContext.getEventInfoContext().setModeIds(fieldValue, modelRelationInfo.getRelatedModelAlias());
        }
    }

    private List<Map<String, Object>> findModelByIds(List<Object> ids, String modelAlias) {
        QueryRequest request = new QueryRequest();
        request.setTeamId(TrantorContext.getTeamId());
        request.setModelAlias(modelAlias);
        request.setSelect(Lists.newArrayList(new Select("*")));
        request.setConditionGroup(new ConditionGroup(Lists.newArrayList(new ConditionGroup(Lists.newArrayList(new SingleCondition("id", ConditionType.IN, new ConditionValue(null, null, ids, null), null))))));
        Response<List<Map<String, Object>>> response = dataStructDataApi.find(request);
        return response.getData();
    }

    private String getModeAlias(Class<?> modelClass) {
        TableName tableName = modelClass.getAnnotation(TableName.class);
        return tableName != null ? tableName.value() : modelClass.getSimpleName();
    }

    private void changeStatus(Object model, EventStateConfigNode.ModelStateConfigNode modelStateConfig) {

        String sourceStatus = getSourceStatus(model, modelStateConfig.getFieldAlisa());

        EventStateConfigNode.StateTransitionNode transitionNode = routeTransition(sourceStatus, model, modelStateConfig);

        if (transitionNode == null) {
            statusMatchFailHandler(sourceStatus);
            return;
        }

        changeFieldValue(model, modelStateConfig.getFieldAlisa(), transitionNode.getTargetState());
        EventRequestContext.getEventInfoContext().addStateChangeLog(modelStateConfig.getModuleKey(), modelStateConfig.getFieldAlisa(), model, getId(model), sourceStatus, transitionNode.getTargetState());
    }

    private void statusMatchFailHandler(String sourceStatus) {
        Boolean enabledStatusVerify = EventRequestContext.getEventInfoContext().getEnabledStatusVerify();
        if (Boolean.TRUE.equals(enabledStatusVerify)) {
            throw DocEngineActionRuntimeException.valueOf(ErrorType.DOC_ENGINE_STATUS_VERIFY_FAIL, sourceStatus);
        }

        log.error("route transition is null current event {} no route transition, current source status is {}", EventRequestContext.getEventCode(), sourceStatus);
    }

    /**
     * 路由相关的状态转移规则
     *
     * @param sourceStatus         当前状态
     * @param model                需要变更状态的模型
     * @param modelStateConfigNode 状态配置
     * @return 命中的规则
     */
    private EventStateConfigNode.StateTransitionNode routeTransition(String sourceStatus, Object model, EventStateConfigNode.ModelStateConfigNode modelStateConfigNode) {
        List<EventStateConfigNode.StateTransitionNode> stateTransitionNodes = modelStateConfigNode.matchTransition(sourceStatus);
        if (CollectionUtils.isEmpty(stateTransitionNodes)) {
            log.error("current event {} no route transition, current source status is {}, model is {}", EventRequestContext.getEventCode(), sourceStatus, modelStateConfigNode.getModelAlisa());
            return null;
        }

        EventStateConfigNode.StateTransitionNode transitionNode = null;

        for (EventStateConfigNode.StateTransitionNode stateTransitionNode : stateTransitionNodes) {
            if (!StringUtils.hasLength(stateTransitionNode.getConditionExpress())) {
                transitionNode = stateTransitionNode;
                break;
            }

            Map<String, Object> conditionParam = new HashMap<>();
            if (StringUtils.hasLength(modelStateConfigNode.getModuleKey())) {
                conditionParam.put(ConditionConvertUtil.convertToCamelCase(KeyUtil.newKeyUnderModule(modelStateConfigNode.getModuleKey(), modelStateConfigNode.getModelAlisa())), this.extraModel(model));
            } else {
                conditionParam.put(modelStateConfigNode.getModelAlisa(), this.extraModel(model));
            }

            RuleEngineResult ruleEngineResult = ruleEngineExecutor.executeResult(stateTransitionNode.getConditionExpress(), conditionParam);
            if (Boolean.TRUE.equals(ruleEngineResult.getConditionResult())) {
                transitionNode = stateTransitionNode;
                break;
            }
        }

        return transitionNode;
    }

    /**
     * 模型数据中extra的数据展平处理
     *
     * @param model
     * @return
     */
    private Object extraModel(Object model) {
        if (model instanceof RootModel) {
            // PO
            RootModel rootModel = (RootModel) model;
            if (CollectionUtils.isEmpty(rootModel.getExtra())) {
                return model;
            }

            List<Field> fields = getAllNotStaticFields(model);
            Map<String, Object> map = Maps.newHashMapWithExpectedSize(fields.size() + rootModel.getExtra().size());
            map.putAll(rootModel.getExtra());
            for (Field field : fields) {
                if (field.getName().equals(EXTRA)) {
                    continue;
                }

                if (field.getName().equals(CONTEXT)) {
                    continue;
                }

                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(model));
                } catch (IllegalAccessException e) {
                    log.error("get field error:{}", e);
                    throw new DocEngineActionRuntimeException(ErrorType.SERVER_ERROR);
                }
            }

            return map;
        } else if (model instanceof Map) {
            // Map
            Map<String, Object> map = (Map<String, Object>) model;
            if (map.containsKey(EXTRA)) {
                map.putAll((Map<String, Object>) map.get(EXTRA));
                map.remove(EXTRA);
            }
            return map;
        } else {
            // 未知情况
            return model;
        }
    }

    private List<Field> getAllNotStaticFields(Object object) {
        Class clazz = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()))
                .stream().filter(field -> !Modifier.isStatic(field.getModifiers()))
                .forEach(fieldList::add);
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }

    /**
     * 改变当前对象的状态字段值
     *
     * @param request
     * @param fieldKey
     * @param targetStatus
     */
    private void changeFieldValue(Object request, String fieldKey, String targetStatus) {
        if (request instanceof Map) {
            ((Map<String, Object>) request).put(fieldKey, targetStatus);
        } else {
            setFieldValue(request, fieldKey, targetStatus);
        }
    }

    /**
     * 获取当前状态
     *
     * @param request
     * @param fieldAlias
     * @return
     */
    private String getSourceStatus(Object request, String fieldAlias) {
        String status;
        if (request instanceof Map) {
            status = (String) ((Map<?, ?>) request).get(fieldAlias);
        } else {
            Object fieldValue = getFieldValue(request, fieldAlias);
            status = (String) fieldValue;
        }

        if (status == null) {
            return INIT;
        }

        return status;
    }

    private Object getFieldValue(Object model, String fieldAlias) {
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            boolean matchResult = Arrays.stream(declaredFields).anyMatch(item -> item.getName().equals(fieldAlias));
            if (!matchResult && model instanceof RootModel) {
                RootModel rootModel = (RootModel) model;
                Map<String, Object> extra = rootModel.getExtra();
                if (CollectionUtils.isEmpty(rootModel.getExtra()) || !rootModel.getExtra().containsKey(fieldAlias)) {
                    return null;
                }
                return extra.get(fieldAlias);
            } else if (model instanceof Map) {
                return ((Map<?, ?>) model).get(fieldAlias);
            } else {
                Field field = model.getClass().getDeclaredField(fieldAlias);
                field.setAccessible(true);
                return field.get(model);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("get field error:{}", Throwables.getStackTraceAsString(e));
            throw new DocEngineActionRuntimeException(ErrorType.SERVER_ERROR);
        }
    }

    private Long getId(Object model) {
        Object fieldValue = getFieldValue(model, "id");
        if (fieldValue == null) {
            return null;
        }

        if (fieldValue instanceof Long) {
            return (Long) fieldValue;
        }

        if (fieldValue instanceof Integer) {
            return ((Integer) fieldValue).longValue();
        }

        return Long.valueOf(String.valueOf(fieldValue));
    }

    private void setFieldValue(Object model, String fieldAlias, Object value) {
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            boolean matchResult = Arrays.stream(declaredFields).anyMatch(item -> item.getName().equals(fieldAlias));
            if (!matchResult && model instanceof RootModel) {
                RootModel rootModel = (RootModel) model;
                if (Objects.isNull(rootModel.getExtra())) {
                    rootModel.setExtra(new HashMap<>(16));
                }
                Map<String, Object> extra = rootModel.getExtra();
                extra.put(fieldAlias, value);
            } else {
                Field field = model.getClass().getDeclaredField(fieldAlias);
                field.setAccessible(true);
                field.set(model, value);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("change source status error:{}", Throwables.getStackTraceAsString(e));
            throw new DocEngineActionRuntimeException(ErrorType.SERVER_ERROR);
        }
    }


    /**
     * 字段驼峰转换
     *
     * @param underLineString
     * @return
     */
    private String underlineToCamel(String underLineString) {
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (int i = 0; i < underLineString.length(); i++) {
            char currentChar = underLineString.charAt(i);
            if (currentChar == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(currentChar));
                }
            }
        }
        return result.toString();
    }


    /**
     * 通过事件key获取状态配置
     *
     * @param eventCode
     * @return
     */
    public EventStateConfigNode getStateConfigByEventKey(String eventCode) {
        EventStateConfigNode eventStateConfigNode = new EventStateConfigNode();
        eventStateConfigNode.setEventCode(eventCode);

        EventMeta eventMeta = eventMetaCache.get(eventCode);
        if (eventMeta == null || eventMeta.getProps() == null) {
            return eventStateConfigNode;
        }

        List<EventStateConfigNode.ModelStateConfigNode> modelStateConfigNodes;
        if (eventMeta.getProps().getStates() != null && !eventMeta.getProps().getStates().isEmpty()) {
            modelStateConfigNodes = EventProps.states2Config(eventMeta.getProps().getStates());
        } else {
            modelStateConfigNodes = new ArrayList<>();
        }

        modelStateConfigNodes.forEach(item -> {
            if (item.getModelAlisa().contains("$")) {
                item.setModuleKey(item.getModelAlisa().split("\\$")[0]);
            }
            item.setModelAlisa(KeyUtil.shortKey(item.getModelAlisa()));

        });
        eventStateConfigNode.setModelStateConfigNodes(modelStateConfigNodes);
        return eventStateConfigNode;
    }

    private void traceOplog(Object model, String modelKey) {
        try {
            String lowerUnderscoreModelKey = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, modelKey);
            OpLogTraceUtils.trace(model, OpLogTraceUtils.UPDATE_DATA_EVENT, lowerUnderscoreModelKey);
        } catch (Exception e) {
            log.error("State machine(Un execute config) tracing oplog error", e);
        }
    }
}
