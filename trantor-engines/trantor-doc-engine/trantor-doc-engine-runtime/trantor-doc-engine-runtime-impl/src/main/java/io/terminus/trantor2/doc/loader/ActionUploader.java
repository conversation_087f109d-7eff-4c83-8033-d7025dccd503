package io.terminus.trantor2.doc.loader;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.trantor2.doc.api.DocEngineActionApi;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.doc.api.dto.ActionFieldDTO;
import io.terminus.trantor2.doc.api.request.ActionUploadRequest;
import io.terminus.trantor2.doc.config.ActionAppConfiguration;
import io.terminus.trantor2.doc.config.ActionConfiguration;
import io.terminus.trantor2.doc.wrapper.ActionWrapper;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.doc.loader.TypeFieldParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static io.terminus.trantor2.meta.util.KeyUtil.EXT_ACTION_KEY_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActionUploader {

    @Resource
    private ActionConfiguration actionConfiguration;

    @Resource
    private DocEngineActionApi docEngineActionApi;

    @Resource
    private MetaCache metaCache;

    public void upLoadAction(ApplicationContext applicationContext) {
        if (Objects.isNull(actionConfiguration)
                || CollectionUtils.isEmpty(actionConfiguration.getApps())
                || !StringUtils.hasLength(actionConfiguration.getHost())) {
            log.error("[ActionLoader] action upload configuration error, stop upload");
            return;
        }
        if (!actionConfiguration.isUploadEnabled()) {
            log.info("[ActionLoader] action upload disabled, [trantor.engine.uploadEnabled] is false");
            return;
        }
        Map<String, Object> actionMap = applicationContext.getBeansWithAnnotation(RestController.class);
        Collection<Object> actionBeans = actionMap.values();

        if (CollectionUtils.isEmpty(actionBeans)) {
            return;
        }

        List<ActionDTO> dtoList = new ArrayList<>();
        Map<String, List<ActionFieldDTO>> classFiledMap = Maps.newHashMapWithExpectedSize(actionBeans.size() * 2);
        actionBeans.forEach(actionBean -> dtoList.addAll(this.buildAction(classFiledMap, actionBean)));
        classFiledMap.clear();
        if (CollectionUtils.isEmpty(dtoList)) {
            // 无Action
            return;
        }

        // 模块信息
        Map<String, ModuleMeta> moduleMetaMap = Maps.newHashMapWithExpectedSize(actionConfiguration.getApps().size());

        // 按app分组
        Map<String, ActionUploadRequest> requestMap = Maps.newHashMapWithExpectedSize(actionConfiguration.getApps().size());
        dtoList.forEach(dto -> {
            ActionAppConfiguration actionAppConfiguration;
            try {
                actionAppConfiguration = actionConfiguration.matchApp(dto.getActionPackage());
            } catch (Exception e) {
                log.error("[ActionLoader] action match app failed, action code : {}", dto.getCode());
                return;
            }

            if (!StringUtils.hasLength(actionAppConfiguration.getAppKey())) {
                log.error("[ActionLoader] action upload failed, app key is null, action code : {}", dto.getCode());
                return;
            }

            String moduleKey = actionAppConfiguration.getAppKey();
            ModuleMeta module = moduleMetaMap.computeIfAbsent(moduleKey, k -> {
                ModuleMeta newModule = ModuleMeta.convert(metaCache.get(k));
                if (!newModule.isNativeModule()) {
                    log.info("[ActionLoader] External module '{}' detected. Only actions with 'EXT_' prefix will be uploaded.", k);
                }
                return newModule;
            });
            boolean filterExt = !module.isNativeModule();
            if (filterExt && !dto.getCode().startsWith(EXT_ACTION_KEY_PREFIX)) {
                return;
            }

            if (requestMap.containsKey(actionAppConfiguration.getRequestKey())) {
                requestMap.get(actionAppConfiguration.getRequestKey()).getActions().add(dto);
            } else {
                ActionUploadRequest request = new ActionUploadRequest(
                        actionConfiguration.getUserId(),
                        actionAppConfiguration.getAppKey(),
                        actionAppConfiguration.getFolderPath(),
                        actionConfiguration.getTeamId(),
                        actionConfiguration.getTeamCode(),
                        Lists.newArrayList(dto)
                );
                requestMap.put(actionAppConfiguration.getRequestKey(), request);
            }
        });
        requestMap.forEach((moduleMey, request) -> {
            // 上报
            try {
                docEngineActionApi.upload(request);
            } catch (Exception e) {
                log.error("[ActionLoader] action upload failed, actions : {}", dtoList, e);
            }
        });
    }

    private List<ActionDTO> buildAction(Map<String, List<ActionFieldDTO>> classFiledMap, Object actionBean) {
        List<ActionDTO> dtoList = new ArrayList<>();
        Arrays.stream(AopUtils.getTargetClass(actionBean).getMethods())
                .filter(method -> Objects.nonNull(AnnotationUtils.findAnnotation(method, Action.class)))
                .forEach(method -> {
                    Action action = AnnotationUtils.findAnnotation(method, Action.class);
                    if (Boolean.FALSE.equals(action.enable())) {
                        return;
                    }

                    ActionWrapper actionWrapper = this.convert(actionBean, method, action);

                    if (Objects.isNull(method.getParameters()) || method.getParameters().length != 1) {
                        log.error("[ActionLoader] Action method parameters error, code : {}", action.value());
                        return;
                    }
                    Type requestType = method.getParameters()[0].getParameterizedType();
                    actionWrapper.setRequestType(requestType);

                    MethodParameter methodParameter = new MethodParameter(method, -1);
                    Type returnType = methodParameter.getNestedGenericParameterType();
                    if (returnType instanceof ParameterizedType) {
                        ParameterizedType responseType = (ParameterizedType) returnType;
                        actionWrapper.setResponseType(responseType.getActualTypeArguments()[0]);
                    } else if (returnType instanceof Class) {
                        log.warn("[ActionLoader] Action return define error, code : {}", action.value());
                        return;
                    }

                    // 本地Bean缓存
                    // ActionWrapperContainer.getActionMap().put(actionWrapper.getCode(), actionWrapper);
                    // 上报使用
                    dtoList.add(this.convert(classFiledMap, actionWrapper));

                    log.info("[ActionLoader] Action[{}] name : {}", action.value(), actionWrapper.getName());
                });
        return dtoList;
    }

    private ActionWrapper convert(Object actionBean, Method method, Action action) {
        ActionWrapper actionWrapper = new ActionWrapper();
        actionWrapper.setBean(actionBean);
        actionWrapper.setMethod(method);
        actionWrapper.setCode(action.value());
        actionWrapper.setName(action.name());
        actionWrapper.setOrder(action.order());
        if (StringUtils.hasLength(action.requestModelKey())) {
            actionWrapper.setRequestModelKey(action.requestModelKey());
        }
        return actionWrapper;
    }

    private ActionDTO convert(Map<String, List<ActionFieldDTO>> classFiledMap, ActionWrapper actionWrapper) {
        ActionDTO dto = new ActionDTO();
        dto.setBean(AopUtils.getTargetClass(actionWrapper.getBean()).getName());
        dto.setMethod(actionWrapper.getMethod().getName());
        dto.setName(actionWrapper.getName());
        dto.setCode(actionWrapper.getCode());
        dto.setOrder(actionWrapper.getOrder());
        dto.setRequestType(actionWrapper.getRequestType().getTypeName());
        dto.setResponseType(actionWrapper.getResponseType().getTypeName());
        dto.setActionPackage(actionWrapper.getBean().getClass().getPackage().getName());
        dto.setRequestModelKey(actionWrapper.getRequestModelKey());

        if (classFiledMap.containsKey(dto.getRequestType())) {
            dto.setInput(classFiledMap.get(dto.getRequestType()));
        } else {
            List<ActionFieldDTO> actionFields = TypeFieldParser.type2FieldList(actionWrapper.getRequestType(), 1);
            dto.setInput(actionFields);
            classFiledMap.put(dto.getRequestType(), actionFields);
        }

        if (classFiledMap.containsKey(dto.getResponseType())) {
            dto.setOutput(classFiledMap.get(dto.getResponseType()));
        } else {
            List<ActionFieldDTO> actionFields = TypeFieldParser.type2FieldList(actionWrapper.getResponseType(), 1);
            dto.setOutput(actionFields);
            classFiledMap.put(dto.getResponseType(), actionFields);
        }

        return dto;
    }



}
