package io.terminus.trantor2.doc.aop;

import com.baomidou.mybatisplus.annotation.TableName;
import io.terminus.trantor2.doc.state.StateEngineExecutor;
import io.terminus.trantor2.doc.util.OpLogTraceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2023/4/13 2:47 下午
 * @see "ActionRepositoryAop"
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class RepositoryAop {

    private final StateEngineExecutor stateEngineExecutor;

    @Pointcut("execution(* io..*Repo.insert*(..)) || execution(* com..*Repo.insert*(..))")
    public void insertMethod() {
    }

    @Pointcut("execution(* io..*Repo.update*(..)) || execution(* com..*Repo.update*(..))")
    public void updateMethod() {

    }

    @Pointcut("execution(* io..*Repo.delete*(..)) || execution(* com..*Repo.delete*(..))")
    public void deleteMethod() {

    }

    @Before("insertMethod() || updateMethod()")
    public void changeStatus(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        if (args.length != 1) {
            log.debug("{} args than one", methodName);
            return;
        }

        stateEngineExecutor.execute(args[0]);
    }

    @After("insertMethod()")
    public void insertAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, OpLogTraceUtils.CREATE_DATA_EVENT);
    }

    @After("updateMethod()")
    public void updateAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, OpLogTraceUtils.UPDATE_DATA_EVENT);
    }

    @After("deleteMethod()")
    public void deleteAfter(JoinPoint joinPoint) {
        oplogProcess(joinPoint, OpLogTraceUtils.DELETE_DATA_EVENT);
    }

    private void oplogProcess(JoinPoint joinPoint, String eventKey) {
        try {
            if (ArrayUtils.isEmpty(joinPoint.getArgs())) {
                return;
            }
            TableName tableNameAnnotation = getTableNameFromRepository(joinPoint);
            if (tableNameAnnotation == null) {
                return;
            }
            String lowerUnderscoreModelKey = tableNameAnnotation.value();
            OpLogTraceUtils.trace(joinPoint.getArgs()[0], eventKey, lowerUnderscoreModelKey);
        } catch (Exception e) {
            log.error("Do after in repository for building log error", e);
        }
    }

    private TableName getTableNameFromRepository(JoinPoint joinPoint) {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        Type[] genericInterfaces = targetClass.getGenericInterfaces();
        if (genericInterfaces.length == 0) {
            return null;
        }
        Class<?> genericInterface = (Class<?>) genericInterfaces[0];
        Type[] superInterfaces = genericInterface.getGenericInterfaces();
        if (superInterfaces.length == 0) {
            return null;
        }
        ParameterizedType superInterface = (ParameterizedType) superInterfaces[0];
        Type[] actualTypeArguments = superInterface.getActualTypeArguments();
        if (ArrayUtils.isEmpty(actualTypeArguments)) {
            return null;
        }
        Class<?> actualTypeArgumentClass = (Class<?>) actualTypeArguments[0];
        return actualTypeArgumentClass.getAnnotation(TableName.class);
    }
}
