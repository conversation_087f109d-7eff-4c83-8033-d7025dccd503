package io.terminus.trantor2.doc.executor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.api.dto.EventActionRelDTO;
import io.terminus.trantor2.doc.api.dto.EventModelDTO;
import io.terminus.trantor2.doc.context.EventContext;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.executor.extra.EventJsonExtraHandler;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.rule.engine.api.model.result.RuleEngineResult;
import io.terminus.trantor2.rule.engine.client.RuleEngineExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.xml.bind.DatatypeConverter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventExecuteTool {

    private static final String RESPONSE_DATA = "data";
    private static final String RESPONSE_INFO = "info";
    private static final String GROOVY_DEFAULT_FUNCTION = "convert";

    private final ConcurrentHashMap<String, Invocable> groovyScriptEngineMap = new ConcurrentHashMap<>();

    @Resource
    private RuleEngineExecutor ruleEngineExecutor;
    @Resource
    private MetaCache metaCache;
    @Resource
    private EventJsonExtraHandler jsonExtraHandle;

    public boolean conditionJudge(EventActionRelDTO eventActionRel, Map<String, Object> conditionParam) {
        return this.conditionJudge(eventActionRel.getConditionExpress(), conditionParam);
    }

    public boolean conditionJudge(String conditionExpress, Map<String, Object> conditionParam) {
        if(StringUtils.hasLength(conditionExpress)) {
            RuleEngineResult ruleEngineResult = ruleEngineExecutor.executeResult(conditionExpress, conditionParam);
            return (Boolean) ruleEngineResult.getConditionResult();
        }
        return true;
    }

    public Object convert(JSON request, String convert) {
        String md5 = md5(convert);
        Invocable invocable;
        if(groovyScriptEngineMap.containsKey(md5)) {
            invocable = groovyScriptEngineMap.get(md5);
        }else {
            ScriptEngineManager factory = new ScriptEngineManager();
            ScriptEngine engine = factory.getEngineByName("groovy");
            try {
                engine.eval(convert);
                invocable = (Invocable) engine;
                groovyScriptEngineMap.put(md5, invocable);
            } catch (ScriptException e) {
                log.error("[Event Convert] action groovy eval failed, convert : {}", convert, e);
                throw new DocEngineActionRuntimeException("event.convert.groovy.eval.failed");
            }
        }

        try {
            return invocable.invokeFunction(GROOVY_DEFAULT_FUNCTION, request);
        } catch (ScriptException e) {
            log.error("[Event Convert] convert groovy execute failed, param : {}, convert : \n{}", request, convert, e);
            throw new DocEngineActionRuntimeException("event.convert.groovy.invoke.failed");
        } catch (NoSuchMethodException e) {
            log.error("[Event Convert] convert groovy execute failed, no such method, convert : \n{}", convert, e);
            throw new DocEngineActionRuntimeException("event.convert.groovy.invoke.no.such.method");
        }
    }

    public static String md5(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(data.getBytes());
            byte[] digest = md.digest();
            return DatatypeConverter.printHexBinary(digest).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            log.info("encrypt data error, cause:{}", Throwables.getStackTraceAsString(e));
        }
        return "";
    }

    public void requestWriteBack(JSON request, Object param, String modelAlias) {
        if (request instanceof JSONObject) {
            JSONObject json = (JSONObject) JSONObject.parse(JSON.toJSONString((param)));
            jsonExtraHandle.jsonExtraHandle(modelAlias, json);
            this.coverMap((JSONObject) request, json);
        }

        if (request instanceof List) {
            JSONArray requestArray = (JSONArray) request;
            JSONArray paramJsonArray = (JSONArray) JSONArray.parse(JSONArray.toJSONString(param));

            int index = 0;
            Iterator<Object> iterator = requestArray.iterator();
            while (iterator.hasNext()) {
                JSONObject childRequest = (JSONObject) iterator.next();
                if(index < paramJsonArray.size()) {
                    JSONObject childParam = paramJsonArray.getJSONObject(index);
                    jsonExtraHandle.jsonExtraHandle(modelAlias, childParam);
                    this.coverMap(childRequest, childParam);
                } else {
                    iterator.remove();
                }
                index++;
            }
        }
    }

    private void coverMap(JSONObject targetJson, JSONObject sourceJson) {
        // 直接整个map putAll 会导致二开主子的一对多字段丢失 需要遍历数组元素每个字段去覆盖
        for (Map.Entry<String, Object> entry : sourceJson.entrySet()) {
            if (entry.getValue() instanceof JSONArray
                && targetJson.containsKey(entry.getKey())
                && targetJson.get(entry.getKey()) instanceof JSONArray) {

                JSONArray sourceJsonArray = (JSONArray) entry.getValue();
                JSONArray targetJsonArray = (JSONArray) targetJson.get(entry.getKey());

                for (int i = 0; i < CollectionUtils.size(sourceJsonArray); i++) {
                    Object sourceObject = sourceJsonArray.get(i);
                    if (i > CollectionUtils.size(targetJsonArray) - 1) {
                        // 多的直接add
                        targetJsonArray.add(sourceObject);
                        continue;
                    }

                    if(sourceObject instanceof JSONObject) {
                        this.coverMap(targetJsonArray.getJSONObject(i), sourceJsonArray.getJSONObject(i));
                    }
                }
                if (CollectionUtils.size(sourceJsonArray) < CollectionUtils.size(targetJsonArray)) {
                    // 删除了部分数组元素 所以也要同步删除
                    Iterator<Object> iterator = targetJsonArray.iterator();
                    int index = 0;
                    while (iterator.hasNext()) {
                        iterator.next();
                        if (index >= CollectionUtils.size(sourceJsonArray)) {
                            iterator.remove();
                        }
                        index ++;
                    }
                }
            } else {
                targetJson.put(entry.getKey(), entry.getValue());
            }
        }
    }

    public void responseWriteBack(String actionCode, EventModelDTO returnModel, Object result, EventContext eventContext) {
        if(Objects.isNull(result)) {
            return;
        }

        JSON resultJson = (JSON) JSON.parse(JSON.toJSONString(result));
        if(resultJson instanceof JSONObject) {
            JSONObject resultJsonObject = (JSONObject) resultJson;
            if(resultJsonObject.containsKey(RESPONSE_DATA)) {
                // 额外提示信息
                if(resultJsonObject.containsKey(RESPONSE_INFO)) {
                    eventContext.setInfo(JSONObject.toJavaObject(resultJsonObject.getJSONObject(RESPONSE_INFO), Response.Info.class));
                }
                // 去除Response包装类
                JSON data = (JSON) resultJsonObject.get(RESPONSE_DATA);
                this.responseWriteBack(data, returnModel, eventContext, actionCode);
            } else {
                this.responseWriteBack(resultJson, returnModel, eventContext, actionCode);
            }
        }else {
            this.responseWriteBack(resultJson, returnModel, eventContext, actionCode);
        }
    }

    public void responseWriteBack(JSON json, EventModelDTO returnModel, EventContext eventContext, String actionCode) {
        // extra 处理
        jsonExtraHandle.jsonExtraHandle(Objects.nonNull(returnModel) ? returnModel.getKey() : null, json);
        eventContext.setResponse(json);
        if(Objects.nonNull(returnModel)) {
            eventContext.getActionResponseMap().put(actionCode, new EventContext.ActionResponseInfo(returnModel.getKey(), json));
        }else {
            eventContext.getActionResponseMap().put(actionCode, new EventContext.ActionResponseInfo(json));
        }
    }

}
