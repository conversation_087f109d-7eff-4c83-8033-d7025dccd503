package io.terminus.trantor2.doc.loader;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.ActionMeta;
import io.terminus.trantor2.doc.api.dict.DocEngineActionLanguageTypeEnum;
import io.terminus.trantor2.doc.api.dto.*;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.api.props.EventActionRelProps;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.doc.api.request.ActionNodeRequest;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.exception.DocEngineActionRuntimeException;
import io.terminus.trantor2.doc.meta.EventMeta;
import io.terminus.trantor2.doc.meta.EventMetaCache;
import io.terminus.trantor2.doc.repo.ActionRuntimeRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.rule.engine.util.ConditionConvertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActionLoader {
    private final EventMetaCache eventMetaCache;
    private final ActionRuntimeRepo actionRuntimeRepo;

    public ActionExecuteNode load(ActionNodeRequest request) {
        MetaEditAndQueryContext context = EditUtil.ctxFromThreadLocal();
        EventMeta eventMetaNode = eventMetaCache.get(request.getEventCode());
        if(Objects.isNull(eventMetaNode)) {
            throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "event.not.existed");
        }

        ActionExecuteNode actionExecuteNode = this.buildActionNode(context, eventMetaNode);
        actionExecuteNode.setAppKey(KeyUtil.moduleKey(eventMetaNode.getKey()));
        actionExecuteNode.setTeamId(context.getTeamId());
        actionExecuteNode.setEventName(eventMetaNode.getName());
        return actionExecuteNode;
    }

    private ActionExecuteNode buildActionNode(MetaEditAndQueryContext context, EventMeta eventMetaNode) {
        EventProps eventProps = eventMetaNode.getProps();

        if(CollectionUtils.isEmpty(eventProps.getRelations())){
            ActionExecuteNode nullRelActionExecuteNode = this.initActionExecuteNode(eventProps);
            return nullRelActionExecuteNode;
        }

        Map<String, EventActionRelDTO> relationMap = this.getRelationMap(eventMetaNode.getKey(), eventProps.getRelations());
        Map<String, ActionDTO> actionMap = this.getActionMap(eventProps);
        Map<String, EventDTO> eventMap = this.getEventMap(context, eventProps);
        Map<String, ServiceDTO> serviceMap = this.getServiceMap(eventProps);
        Map<String, ApprovalDTO> approvalMap = this.getApprovalMap(eventProps);
        Map<String, ValidationDTO> validationMap = this.getValidationMap(eventProps);

        Set<String> nextCodes = eventProps.getRelations().stream()
            .map(EventActionRelProps::getNextCode)
            .filter(StringUtils::hasLength)
            .collect(Collectors.toSet());
        String firstCode = eventProps.getRelations().stream()
            .map(EventActionRelProps::getCode)
            .filter(code -> !nextCodes.contains(code))
            .findFirst().orElseThrow(() -> new DocEngineActionRuntimeException(ErrorType.SERVER_ERROR, "action.rel.error"));

        EventActionRelDTO rel = relationMap.get(firstCode);
        ActionExecuteNode rootNode = new ActionExecuteNode();
        ActionExecuteNode actionExecuteNode = rootNode;
        do{
            ActionExecuteNode nextNode = this.initActionExecuteNode(eventProps);
            nextNode.setEventActionRel(rel);
            if(DocEngineMetaTypeConstant.ACTION.equals(rel.getActionType())){
                ActionDTO action = actionMap.get(rel.getCode());
                action.setCode(action.getCode());
                nextNode.setAction(action);
                rel.setName(nextNode.getAction().getName());
            }else if(DocEngineMetaTypeConstant.EVENT.equals(rel.getActionType())) {
                nextNode.setEvent(eventMap.get(rel.getCode()));
            }else if(DocEngineMetaTypeConstant.SERVICE.equals(rel.getActionType())) {
                nextNode.setService(serviceMap.get(rel.getCode()));
            }else if(DocEngineMetaTypeConstant.APPROVAL_WORKFLOW.equals(rel.getActionType())) {
                nextNode.setApproval(approvalMap.get(rel.getCode()));
            }else if(DocEngineMetaTypeConstant.VALIDATION.equals(rel.getActionType())) {
                nextNode.setValidation(validationMap.get(rel.getCode()));
            }
            actionExecuteNode.setNextNode(nextNode);

            rel = relationMap.get(rel.getNextCode());
            actionExecuteNode = nextNode;

        }while (Objects.nonNull(rel));
        return rootNode.getNextNode();
    }

    private ActionExecuteNode initActionExecuteNode(EventProps eventProps) {
        ActionExecuteNode node = new ActionExecuteNode();
        node.setMainModelKey(eventProps.getModel().getKey());
        node.setMainModelName(eventProps.getModel().getName());
        node.setEnabledStatusVerify(eventProps.getEnabledStatusVerify());
        node.setReturnModelKey(Objects.nonNull(eventProps.getReturnModel()) ? eventProps.getReturnModel().getKey() : null);

        if(Objects.isNull(eventProps.getEnabledTransaction())) {
            node.setEnabledTransaction(true);
        } else {
            node.setEnabledTransaction(eventProps.getEnabledTransaction());
        }

        if(Objects.nonNull(eventProps.getNotice()) && CollectionUtils.isEmpty(eventProps.getNotices())) {
            // 历史数据割接处理
            node.setNoticePropsList(Lists.newArrayList(eventProps.getNotice()));
        }else {
            node.setNoticePropsList(eventProps.getNotices());
        }
        return node;
    }

    private Map<String, EventActionRelDTO> getRelationMap(String eventCode, List<EventActionRelProps> eventActionRelPropsList) {
        return eventActionRelPropsList.stream()
            .collect(Collectors.toMap(EventActionRelProps::getCode, eventActionRelProps -> {
                EventActionRelDTO rel = new EventActionRelDTO();
                if (!CollectionUtils.isEmpty(eventActionRelProps.getConditions())) {
                    rel.setConditionExpress(ConditionConvertUtil.convertConditionExpress(eventActionRelProps.getConditions()));
                }
                rel.setEventCode(eventCode);
                rel.setActionType(eventActionRelProps.getActionType());
                rel.setSourceCode(eventActionRelProps.getSourceCode());
                rel.setCode(eventActionRelProps.getCode());
                rel.setNextCode(eventActionRelProps.getNextCode());
                rel.setExpress(eventActionRelProps.getExpress());
                rel.setConvert(eventActionRelProps.getConvert());
                rel.setEnabledTransaction(Boolean.TRUE.equals(eventActionRelProps.getEnabledTransaction()));
                if (Objects.isNull(eventActionRelProps.getEnabledParamCheck())) {
                    // 入参校验默认关闭
                    rel.setEnabledParamCheck(false);
                } else {
                    rel.setEnabledParamCheck(eventActionRelProps.getEnabledParamCheck());
                }
                return rel;
            }));
    }

    private Map<String, ActionDTO> getActionMap(EventProps eventProps) {
        List<String> actionCodes = eventProps.getRelations().stream()
            .filter(rel -> DocEngineMetaTypeConstant.ACTION.equals(rel.getActionType()))
            .map(EventActionRelProps::getCode).collect(Collectors.toList());
        Map<String, ActionDTO> actionMap = Maps.newHashMapWithExpectedSize(actionCodes.size());
        if(!CollectionUtils.isEmpty(actionCodes)) {
            actionCodes.forEach(actionCode -> {
                ActionMeta actionMetaNode = actionRuntimeRepo.findOneByKey(actionCode);
                if(Objects.isNull(actionMetaNode)) {
                    log.error("[ActionLoader] action not existed, action code : {}", actionCode);
                    throw new DocEngineActionRuntimeException(ErrorType.RESOURCE_NOT_FOUND, "action.not.existed");
                }
                actionMap.put(actionMetaNode.getKey(), this.meta2Dto(actionMetaNode));
            });
        }
        return actionMap;
    }

    private ActionDTO meta2Dto(ActionMeta actionMeta) {
        ActionDTO actionDTO = new ActionDTO();
        actionDTO.setId(actionMeta.getId());
        actionDTO.setCode(actionMeta.getKey());
        actionDTO.setName(actionMeta.getName());
        actionDTO.setModule(actionMeta.getParentKey());

        ActionProps actionProps = actionMeta.getResourceProps();
        actionDTO.setDesc(actionProps.getDesc());
        actionDTO.setBean(actionProps.getBean());
        actionDTO.setMethod(actionProps.getMethod());
        actionDTO.setOrder(actionProps.getOrder());
        actionDTO.setRequestType(actionProps.getRequestType());
        actionDTO.setResponseType(actionProps.getResponseType());
        actionDTO.setReturnList(StringUtils.hasLength(actionProps.getResponseType()) && actionProps.getResponseType().startsWith(List.class.getName()));
        actionDTO.setStatus(actionProps.getStatus());
        actionDTO.setReturnModel(actionProps.getReturnModel());
        actionDTO.setAccess(actionMeta.getAccess());

        String languageType = StringUtils.hasLength(actionProps.getLanguageType()) ?
            actionProps.getLanguageType() : DocEngineActionLanguageTypeEnum.JAVA.getType();
        actionDTO.setLanguageType(languageType);

        actionDTO.setGroovyScript(actionProps.getGroovyScript());

        return actionDTO;
    }

    private Map<String, EventDTO> getEventMap(MetaEditAndQueryContext context, EventProps eventProps) {
        List<String> eventCodes = eventProps.getRelations().stream()
            .filter(rel -> DocEngineMetaTypeConstant.EVENT.equals(rel.getActionType()))
            .map(EventActionRelProps::getCode).collect(Collectors.toList());
        Map<String, EventDTO> eventMap = Maps.newHashMapWithExpectedSize(eventCodes.size());
        if(!CollectionUtils.isEmpty(eventCodes)) {
            eventCodes.forEach(eventCode -> {
                EventMeta eventMeta = eventMetaCache.get(eventCode);
                if (eventMeta == null) {
                    return;
                }
                EventDTO eventDTO = new EventDTO();
                eventDTO.setCode(eventMeta.getKey());
                EventProps actionEventProps = eventMeta.getProps();
                eventDTO.setReturnModel(actionEventProps.getReturnModel());
                eventMap.put(eventMeta.getKey(), eventDTO);
            });
        }
        return eventMap;
    }

    private Map<String, ServiceDTO> getServiceMap(EventProps eventProps) {
        return eventProps.getRelations().stream()
            .filter(rel -> DocEngineMetaTypeConstant.SERVICE.equals(rel.getActionType()))
            .collect(Collectors.toMap(EventActionRelProps::getCode, eventActionRelProps -> {
                ServiceDTO serviceDTO = new ServiceDTO();
                serviceDTO.setCode(eventActionRelProps.getCode());
                return serviceDTO;
            }));
    }

    private Map<String, ApprovalDTO> getApprovalMap(EventProps eventProps) {
        return eventProps.getRelations().stream()
            .filter(rel -> DocEngineMetaTypeConstant.APPROVAL_WORKFLOW.equals(rel.getActionType()))
            .collect(Collectors.toMap(EventActionRelProps::getCode, eventActionRelProps -> {
                ApprovalDTO approvalDTO = new ApprovalDTO();
                approvalDTO.setCode(eventActionRelProps.getCode());
                return approvalDTO;
            }));
    }

    private Map<String, ValidationDTO> getValidationMap(EventProps eventProps) {
        return eventProps.getRelations().stream()
            .filter(rel -> DocEngineMetaTypeConstant.VALIDATION.equals(rel.getActionType()))
            .collect(Collectors.toMap(EventActionRelProps::getCode, eventActionRelProps -> {
                ValidationDTO validationDTO = new ValidationDTO();
                validationDTO.setCode(eventActionRelProps.getCode());
                return validationDTO;
            }));
    }
}
