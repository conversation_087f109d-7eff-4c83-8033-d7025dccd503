package io.terminus.trantor2.doc.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor2.doc.api.dto.ValidationDTO;
import io.terminus.trantor2.doc.api.node.ActionExecuteNode;
import io.terminus.trantor2.doc.executor.EventExecuteTool;
import io.terminus.trantor2.service.runtime.api.request.ValidationRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventValidationActionExecutor {

    @Resource
    private EventExecuteTool eventExecuteTool;
//    @Resource
//    private ServiceExecuteApi serviceExecuteApi;

    public void execute(JSON request, ActionExecuteNode actionExecuteNode, Map<String, Object> conditionParam) {
        ValidationDTO validation = actionExecuteNode.getValidation();

        // 条件判断
        Boolean condition = eventExecuteTool.conditionJudge(actionExecuteNode.getEventActionRel(), conditionParam);
        if (Boolean.FALSE.equals(condition)) {
            log.info("[Validation Action Executor] action condition not pass, action code : {}", validation.getCode());
            return;
        }

        // convert
        String convert = actionExecuteNode.getEventActionRel().getConvert();
        Object param = request instanceof JSONObject ?
            ((JSONObject) request).getInnerMap() :
            ((JSONArray) request).stream().map(obj -> ((JSONObject) obj).getInnerMap()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(convert)) {
            param = eventExecuteTool.convert(request, convert);
        }

        ValidationRequest validationRequest = new ValidationRequest();
        validationRequest.setAppId(actionExecuteNode.getAppId());
        validationRequest.setModelAlias(actionExecuteNode.getActionModelKey());
        validationRequest.setRuleKey(validation.getCode());
        validationRequest.setModelValue(((JSONObject) JSONObject.toJSON(param)).getInnerMap());

        // 事件转服务后，事件就不在需要这个api,这里把这个接口收回
//        serviceExecuteApi.validateRule(validationRequest);

        // 校验规则无入参回写 无返回回写
    }
}
