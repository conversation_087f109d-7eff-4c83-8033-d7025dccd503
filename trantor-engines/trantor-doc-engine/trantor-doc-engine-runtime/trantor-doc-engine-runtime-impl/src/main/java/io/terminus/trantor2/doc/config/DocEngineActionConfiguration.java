package io.terminus.trantor2.doc.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ComponentScan({"io.terminus.trantor2.doc"})
@EnableFeignClients({"io.terminus.trantor2.doc.api"})
public class DocEngineActionConfiguration {
    public DocEngineActionConfiguration() {
    }
}
