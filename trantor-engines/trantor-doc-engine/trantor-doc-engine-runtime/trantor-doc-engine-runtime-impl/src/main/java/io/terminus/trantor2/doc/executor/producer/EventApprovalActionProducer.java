package io.terminus.trantor2.doc.executor.producer;

import io.terminus.common.rocketmq.autoconfigure.MQProperties;
import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.doc.executor.event.StartApprovalEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/10/9 5:23 PM
 */
@Component
@AllArgsConstructor
@Slf4j
public class EventApprovalActionProducer {

    private final TerminusMQProducer producer;

    private final MQProperties mqProperties;

    public void publish(StartApprovalEvent startApprovalEvent) {

        TerminusMessage terminusMessage = new TerminusMessage();
        terminusMessage.setBody(startApprovalEvent);
        terminusMessage.setTags(StartApprovalEvent.TAG);
        terminusMessage.setTopic(mqProperties.getTopic());

        log.info("[EventApprovalActionProducer] publish message to mq topic [{}], workflow event: {}", mqProperties.getTopic(), JsonUtil.toJson(startApprovalEvent));
        producer.send(terminusMessage);
    }

}
