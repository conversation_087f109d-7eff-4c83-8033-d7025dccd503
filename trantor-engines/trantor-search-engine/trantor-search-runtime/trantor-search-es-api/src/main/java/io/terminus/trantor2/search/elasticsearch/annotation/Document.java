package io.terminus.trantor2.search.elasticsearch.annotation;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 搜索引擎 文档标识
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Document {
    /**
     * 填写模型的key,格式：模块$模型，如test_module$test_search_model
     * 该属性已废弃，请使用modelKey属性
     */
    @Deprecated
    @AliasFor("modelKey")
    String index() default "";

    /**
     * 填写模型的key,格式：模块$模型，如test_module$test_search_model
     */
    @AliasFor("index")
    String modelKey() default "";

    /**
     * 刷新策略：false(不关注)/true(立即刷新)/wait_for(等待刷新)
     */
    String refreshPolicy() default "false";

    /**
     * 查询缓存：true(启用)/false(禁用)
     */
    boolean requestCache() default false;

    /**
     * 是否逻辑删除，默认：true
     * 如果是逻辑删除，系统会默认拼接deleted=0查询条件
     * 如果是物理删除或者模型中不存在deleted字段，那么该属性需要设置为false
     */
    boolean logicDelete() default true;

}
