package io.terminus.trantor2.search.elasticsearch.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Date;

public class DateSerializer extends JsonSerializer<Date> {

    @Override
    public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null == date) {
            return;
        }
        jsonGenerator.writeString(DateTimeUtils.formatDate(date));
    }

    @Override
    public Class<Date> handledType() {
        return Date.class;
    }
}
