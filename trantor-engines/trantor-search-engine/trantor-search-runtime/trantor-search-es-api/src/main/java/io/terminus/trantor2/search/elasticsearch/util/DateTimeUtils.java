package io.terminus.trantor2.search.elasticsearch.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateTimeUtils {
    private static final DateTimeFormatter LOCAL_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter LOCAL_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter LOCAL_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMATTER = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    public static Date parseDate(String dateStr) {
        if (null == dateStr || dateStr.isEmpty()) {
            return null;
        }
        try {
            return DATE_FORMATTER.get().parse(dateStr);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse date: " + dateStr + ",error:" + e.getMessage(), e);
        }
    }

    public static String formatDate(Date date) {
        if (null == date) {
            return null;
        }
        return DATE_FORMATTER.get().format(date);
    }

    public static LocalDate parseLocalDate(String localDateStr) {
        if (null == localDateStr || localDateStr.isEmpty()) {
            return null;
        }
        return LocalDate.parse(localDateStr, LOCAL_DATE_FORMATTER);
    }

    public static String formatLocalDate(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }
        return localDate.format(LOCAL_DATE_FORMATTER);
    }

    public static LocalDateTime parseLocalDateTime(String localDateTimeStr) {
        if (null == localDateTimeStr || localDateTimeStr.isEmpty()) {
            return null;
        }
        return LocalDateTime.parse(localDateTimeStr, LOCAL_DATETIME_FORMATTER);
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        return localDateTime.format(LOCAL_DATETIME_FORMATTER);
    }

    public static LocalTime parseLocalTime(String localTimeStr) {
        if (null == localTimeStr || localTimeStr.isEmpty()) {
            return null;
        }
        return LocalTime.parse(localTimeStr, LOCAL_TIME_FORMATTER);
    }

    public static String formatLocalTime(LocalTime localTime) {
        if (null == localTime) {
            return null;
        }
        return localTime.format(LOCAL_TIME_FORMATTER);
    }
}
