package io.terminus.trantor2.search.elasticsearch.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalTime;

public class LocalTimeDeSerializer extends JsonDeserializer<LocalTime> {


    @Override
    public LocalTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        return DateTimeUtils.parseLocalTime(jsonParser.getText());
    }
}
