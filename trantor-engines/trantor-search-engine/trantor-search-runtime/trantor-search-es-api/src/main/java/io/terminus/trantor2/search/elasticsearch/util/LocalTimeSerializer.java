package io.terminus.trantor2.search.elasticsearch.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalTime;

public class LocalTimeSerializer extends JsonSerializer<LocalTime> {

    @Override
    public void serialize(LocalTime localTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null == localTime) {
            return;
        }
        jsonGenerator.writeString(DateTimeUtils.formatLocalTime(localTime));
    }

    @Override
    public Class<LocalTime> handledType() {
        return LocalTime.class;
    }
}
