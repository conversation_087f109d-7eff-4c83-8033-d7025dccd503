package io.terminus.trantor2.search.elasticsearch.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * search api定制序列化反序列化json工具类
 * 定制了LocalDateTime、LocalDate、LocalTime、Date四种类型序列化反序列化实现
 */
public final class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final ObjectMapper OBJECT_MAPPER_INCLUDE_NULL = new ObjectMapper();

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(new LocalDateTimeSerializer());
        javaTimeModule.addSerializer(new LocalDateSerializer());
        javaTimeModule.addSerializer(new LocalTimeSerializer());
        javaTimeModule.addSerializer(new DateSerializer());
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeSerializer());
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeSerializer());
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeSerializer());
        javaTimeModule.addDeserializer(Date.class, new DateDeSerializer());
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.registerModule(javaTimeModule);
        OBJECT_MAPPER_INCLUDE_NULL.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER_INCLUDE_NULL.registerModule(javaTimeModule);
    }

    public static String toJson(Object o) {
        if (null != o) {
            try {
                return OBJECT_MAPPER.writeValueAsString(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String toJsonIncludeNull(Object o) {
        if (null != o) {
            try {
                return OBJECT_MAPPER_INCLUDE_NULL.writeValueAsString(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        if (null != json && json.length() > 0) {
            try {
                return OBJECT_MAPPER.readValue(json, clazz);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (null != json && json.length() > 0) {
            try {
                return OBJECT_MAPPER.readValue(json, typeReference);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T fromJson(String json, JavaType javaType) {
        if (null != json && json.length() > 0) {
            try {
                return OBJECT_MAPPER.readValue(json, javaType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T fromJson(InputStream input, JavaType javaType) {
        if (null != input) {
            try {
                return OBJECT_MAPPER.readValue(input, javaType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T fromJson(InputStream input, Class<T> clazz) {
        if (null != input) {
            try {
                return OBJECT_MAPPER.readValue(input, clazz);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    @SuppressWarnings({"rawtypes", "RedundantArrayCreation"})
    public static <T> List<T> fromJsonArray(String json, Class<? extends List> arrayClass, Class<T> clazz) {
        if (null != json && json.length() > 0) {
            try {
                JavaType classType = OBJECT_MAPPER.getTypeFactory().constructParametricType(arrayClass, new Class[]{clazz});
                return OBJECT_MAPPER.readValue(json, classType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T convert(Object object, JavaType javaType) {
        if (null != object) {
            try {
                return OBJECT_MAPPER.convertValue(object, javaType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T convert(Object object, Class<T> clazz) {
        if (null != object) {
            try {
                return OBJECT_MAPPER.convertValue(object, clazz);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T convert(Object object, TypeReference<T> typeReference) {
        if (null != object) {
            try {
                return OBJECT_MAPPER.convertValue(object, typeReference);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static ObjectMapper objectMapper() {
        return OBJECT_MAPPER;
    }

    public static ObjectMapper objectMapperIncludeNull() {
        return OBJECT_MAPPER_INCLUDE_NULL;
    }


    private JsonUtils() {
    }
}
