package io.terminus.trantor2.search.elasticsearch.model;

import io.terminus.common.api.model.Paging;
import lombok.Data;
import org.elasticsearch.search.aggregations.Aggregation;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class SearchResult<T> implements Serializable {
    private static final long serialVersionUID = 7052483925456165485L;

    private long tookInMillis;

    private float maxScore;

    private Paging<T> documents;

    private Map<String, Aggregation> aggregations;

    private List<Map<String, List<String>>> highlights;

    public T getFirst() {
        if (null == documents || CollectionUtils.isEmpty(documents.getData())) {
            return null;
        }
        return documents.getData().get(0);
    }

    public List<T> getData() {
        if (null == documents || null == documents.getData()) {
            return Collections.emptyList();
        }
        return documents.getData();
    }
}
