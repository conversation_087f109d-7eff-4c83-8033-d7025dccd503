package io.terminus.trantor2.search.elasticsearch.wrapper;

import io.terminus.common.api.model.BaseModel;
import io.terminus.common.api.model.OrderField;
import io.terminus.common.api.model.RootModel;
import io.terminus.common.api.request.AbstractPageRequest;
import io.terminus.trantor2.search.elasticsearch.annotation.*;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

public class SearchWrapper {
    private final BoolQueryBuilder queryBuilder;
    private RootModel model;
    private Integer from;
    private Integer size;
    private List<OrderField> orders;
    private HighlightBuilder highlightBuilder;
    private String modelKey;

    private static final String ES_TEXT_DEFAULT_KEYWORD_ENABLED = System.getenv("ES_TEXT_DEFAULT_KEYWORD_ENABLED");
    private static final String ES_TEXT_DEFAULT_KEYWORD_EXCLUDE_FIELDS = System.getenv("ES_TEXT_DEFAULT_KEYWORD_EXCLUDE_FIELDS");

    private SearchWrapper() {
        this.queryBuilder = QueryBuilders.boolQuery();
    }

    private SearchWrapper(RootModel model) {
        this.model = model;
        this.queryBuilder = QueryBuilders.boolQuery();
    }

    private SearchWrapper(String modelKey) {
        this.modelKey = modelKey;
        this.queryBuilder = QueryBuilders.boolQuery();
    }

    public SearchWrapper exists(String field) {
        queryBuilder.filter(QueryBuilders.existsQuery(field));
        return this;
    }

    public SearchWrapper notExists(String field) {
        queryBuilder.mustNot(QueryBuilders.existsQuery(field));
        return this;
    }

    public SearchWrapper equals(String field, Object value) {
        if (value instanceof String) {
            queryBuilder.filter(QueryBuilders.termQuery(processFieldName(field), value));
        } else {
            queryBuilder.filter(QueryBuilders.termQuery(field, value));
        }
        return this;
    }

    public SearchWrapper notEquals(String field, Object value) {
        if (value instanceof String) {
            queryBuilder.mustNot(QueryBuilders.termQuery(processFieldName(field), value));
        } else {
            queryBuilder.mustNot(QueryBuilders.termQuery(field, value));
        }
        return this;
    }

    public SearchWrapper contain(String field, Collection<?> values) {
        boolean allStrings = !values.isEmpty();
        for (Object value : values) {
            if (!(value instanceof String)) {
                allStrings = false;
                break;
            }
        }

        if (allStrings) {
            queryBuilder.filter(QueryBuilders.termsQuery(processFieldName(field), values));
        } else {
            queryBuilder.filter(QueryBuilders.termsQuery(field, values));
        }
        return this;
    }

    public SearchWrapper notContain(String field, Collection<?> values) {
        boolean allStrings = !values.isEmpty();
        for (Object value : values) {
            if (!(value instanceof String)) {
                allStrings = false;
                break;
            }
        }

        if (allStrings) {
            queryBuilder.mustNot(QueryBuilders.termsQuery(processFieldName(field), values));
        } else {
            queryBuilder.mustNot(QueryBuilders.termsQuery(field, values));
        }
        return this;
    }

    public SearchWrapper between(String field, Object lower, Object upper) {
        return between(field, lower, true, upper, true);
    }

    public SearchWrapper between(String field, Object lower, boolean includeLower, Object upper, boolean includeUpper) {
        queryBuilder.filter(QueryBuilders.rangeQuery(field).from(lower, includeLower).to(upper, includeUpper));
        return this;
    }

    public SearchWrapper greater(String field, Object lower) {
        return greater(field, lower, true);
    }

    public SearchWrapper greater(String field, Object lower, boolean includeLower) {
        queryBuilder.filter(QueryBuilders.rangeQuery(field).from(lower, includeLower));
        return this;
    }

    public SearchWrapper less(String field, Object upper) {
        return less(field, upper, true);
    }

    public SearchWrapper less(String field, Object upper, boolean includeUpper) {
        queryBuilder.filter(QueryBuilders.rangeQuery(field).to(upper, includeUpper));
        return this;
    }

    public SearchWrapper like(String field, String keyword) {
        queryBuilder.must(QueryBuilders.wildcardQuery(processFieldName(field), "*" + keyword + "*"));
        return this;
    }

    public SearchWrapper likePrefix(String field, String keyword) {
        queryBuilder.must(QueryBuilders.wildcardQuery(processFieldName(field), keyword + "*"));
        return this;
    }

    public SearchWrapper match(String field, Object keyword) {
        queryBuilder.must(QueryBuilders.matchQuery(processFieldName(field), keyword));
        return this;
    }

    public SearchWrapper match(String field, Object keyword, String analyzer) {
        queryBuilder.must(QueryBuilders.matchQuery(processFieldName(field), keyword).analyzer(analyzer));
        return this;
    }

    public SearchWrapper page(Integer pageNo, Integer pageSize) {
        if (null == pageNo) {
            pageNo = AbstractPageRequest.DEFAULT_PAGE_NO;
        }
        if (null == pageSize) {
            pageSize = AbstractPageRequest.DEFAULT_PAGE_SIZE;
        }
        this.from = Math.max((pageNo - 1) * pageSize, 0);
        this.size = pageSize;
        return this;
    }

    public SearchWrapper order(String field, boolean asc) {
        if (null == orders) {
            orders = new ArrayList<>();
        }
        orders.add(new OrderField(field, asc));
        return this;
    }

    public SearchWrapper orders(List<OrderField> orders) {
        this.orders = orders;
        return this;
    }

    public SearchWrapper highlight(String field, int fragmentSize, int numberOfFragments) {
        if (null == highlightBuilder) {
            highlightBuilder = new HighlightBuilder();
        }
        highlightBuilder.field(field, fragmentSize, numberOfFragments).preTags("").postTags("");
        return this;
    }

    public SearchWrapper highlightTags(String preTags, String postTags) {
        if (null == highlightBuilder) {
            highlightBuilder = new HighlightBuilder();
        }
        highlightBuilder.preTags(preTags).postTags(postTags);
        return this;
    }

    public SearchWrapper build() {
        return this;
    }

    public static SearchWrapper of() {
        return new SearchWrapper();
    }

    public static SearchWrapper of(String modelKey) {
        return new SearchWrapper(modelKey);
    }

    public static SearchWrapper of(RootModel model) {
        SearchWrapper wrapper = new SearchWrapper(model);
        // 构造默认查询方式
        if (null != model.getId()) {
            wrapper.equals("id", model.getId());
        }
        // 构造逻辑删除标志
        if (model instanceof BaseModel) {
            BaseModel base = (BaseModel) model;
            if (null != base.getDeleted()) {
                wrapper.equals("deleted", base.getDeleted());
            }
        }
        // 构造注解查询方式，后续可以缓存反射信息优化性能
        buildAnnotationQuery(wrapper, model);

        // 构造分页排序参数
        if (model instanceof AbstractPageRequest) {
            AbstractPageRequest request = (AbstractPageRequest) model;
            wrapper.page(request.getPageNo(), request.getPageSize())
                .orders(request.getOrders());
        }
        return wrapper;
    }

    public BoolQueryBuilder query() {
        return queryBuilder;
    }

    public Integer from() {
        if (null == from) {
            return 0;
        }
        return from;
    }

    public Integer size() {
        if (null == size) {
            return AbstractPageRequest.DEFAULT_PAGE_SIZE;
        }
        return size;
    }

    public List<OrderField> orders() {
        if (null == orders) {
            return Collections.emptyList();
        }
        return orders;
    }

    public HighlightBuilder highlight() {
        return highlightBuilder;
    }

    public RootModel getModel() {
        return model;
    }

    /**
     * 构造注解查询方式，后续可以缓存反射信息优化性能
     *
     * @param wrapper 搜索条件构造
     * @param model   模型条件
     */
    public static void buildAnnotationQuery(SearchWrapper wrapper, RootModel model) {
        Class<?> modelClass = model.getClass();
        try {
            while (modelClass != RootModel.class && modelClass != BaseModel.class) {
                Field[] fields = modelClass.getDeclaredFields();
                for (Field field : fields) {
                    if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers()) || !Modifier.isPrivate(field.getModifiers())) {
                        continue;
                    }
                    buildAnnotationQuery(wrapper, field, model);
                }
                modelClass = modelClass.getSuperclass();
            }
        } catch (Exception e) {
            throw new RuntimeException("elasticsearch.wrapper.failed", e);
        }
    }

    private static void buildAnnotationQuery(SearchWrapper wrapper, Field field, RootModel model) throws Exception {
        if (field.isAnnotationPresent(Exist.class)) {
            Exist exist = field.getAnnotation(Exist.class);
            wrapper.exists(StringUtils.hasText(exist.field()) ? exist.field() : field.getName());
        }
        if (field.isAnnotationPresent(NotExist.class)) {
            NotExist exist = field.getAnnotation(NotExist.class);
            wrapper.notExists(StringUtils.hasText(exist.field()) ? exist.field() : field.getName());
        }
        if (!field.isAccessible()) {
            field.setAccessible(true);
        }
        Object value = field.get(model);
        if (null == value) {
            return;
        }
        if (field.isAnnotationPresent(Equals.class)) {
            Equals equals = field.getAnnotation(Equals.class);
            if (value instanceof Collection) {
                wrapper.contain(StringUtils.hasText(equals.field()) ? equals.field() : field.getName(), (Collection<?>) value);
            } else {
                wrapper.equals(StringUtils.hasText(equals.field()) ? equals.field() : field.getName(), value);
            }
        }
        if (field.isAnnotationPresent(NotEquals.class)) {
            NotEquals equals = field.getAnnotation(NotEquals.class);
            if (value instanceof Collection) {
                wrapper.notContain(StringUtils.hasText(equals.field()) ? equals.field() : field.getName(), (Collection<?>) value);
            } else {
                wrapper.notEquals(StringUtils.hasText(equals.field()) ? equals.field() : field.getName(), value);
            }
        }
        if (field.isAnnotationPresent(Greater.class)) {
            Greater greater = field.getAnnotation(Greater.class);
            wrapper.greater(StringUtils.hasText(greater.field()) ? greater.field() : field.getName(), value, !greater.excludeEquals());
        }
        if (field.isAnnotationPresent(Less.class)) {
            Less less = field.getAnnotation(Less.class);
            wrapper.less(StringUtils.hasText(less.field()) ? less.field() : field.getName(), value, !less.excludeEquals());
        }
        if (field.isAnnotationPresent(Like.class)) {
            Like like = field.getAnnotation(Like.class);
            if (like.prefix()) {
                wrapper.likePrefix(StringUtils.hasText(like.field()) ? like.field() : field.getName(), value.toString());
            } else {
                wrapper.like(StringUtils.hasText(like.field()) ? like.field() : field.getName(), value.toString());
            }
        }
        if (field.isAnnotationPresent(Match.class)) {
            Match match = field.getAnnotation(Match.class);
            if (StringUtils.hasText(match.analyzer())) {
                wrapper.match(StringUtils.hasText(match.field()) ? match.field() : field.getName(), value.toString(), match.analyzer());
            } else {
                wrapper.match(StringUtils.hasText(match.field()) ? match.field() : field.getName(), value.toString());
            }
        }
    }

    /**
     * 处理字段名称，根据环境变量决定是否添加 .keyword 后缀
     *
     * @param field 原始字段名
     * @return 处理后的字段名
     */
    private String processFieldName(String field) {
        // 检查是否启用了默认 keyword 查询

        if (!"true".equalsIgnoreCase(ES_TEXT_DEFAULT_KEYWORD_ENABLED)) {
            return field;
        }

        // 检查字段是否已经包含 .keyword 后缀
        if (field.endsWith(".keyword")) {
            return field;
        }

        // 检查是否在排除列表中
        if (this.modelKey != null && ES_TEXT_DEFAULT_KEYWORD_EXCLUDE_FIELDS != null && !ES_TEXT_DEFAULT_KEYWORD_EXCLUDE_FIELDS.isEmpty()) {
            String[] excludeList = ES_TEXT_DEFAULT_KEYWORD_EXCLUDE_FIELDS.split(",");
            for (String excludeField : excludeList) {
                // 检查模型字段匹配
                // 例如：gyp_gen_sku_so_desc
                if (excludeField.trim().endsWith(this.modelKey + "_" + field)) {
                    return field;
                }
            }
        }

        // 添加 .keyword 后缀
        return field + ".keyword";
    }

}
