package io.terminus.trantor2.search.elasticsearch.repository;

import io.terminus.common.api.model.BaseModel;
import io.terminus.common.api.model.OrderField;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.AbstractPageRequest;
import io.terminus.common.api.util.JsonUtils;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.search.elasticsearch.annotation.Document;
import io.terminus.trantor2.search.elasticsearch.client.SearchClient;
import io.terminus.trantor2.search.elasticsearch.model.SearchResult;
import io.terminus.trantor2.search.elasticsearch.wrapper.SearchWrapper;
import io.terminus.trantor2.search.elasticsearch.wrapper.TrantorSearchRequest;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public abstract class BaseRepository<T extends BaseModel> {
    private Class<T> clazz;
    private String modelKey;
    private final String refreshPolicy;
    private final boolean requestCache;
    private final boolean logicDelete;
    private SearchClient client;

    private static final String MULTI_TENANT_ENABLED = System.getenv("MULTI_TENANT_ENABLED");

    public BaseRepository() {
        Class<?> clazz = this.getClass();
        Type superclass = clazz.getGenericSuperclass();
        if (superclass instanceof ParameterizedType) {
            //noinspection unchecked
            this.clazz = (Class<T>) ((ParameterizedType) superclass).getActualTypeArguments()[0];
        }
        if (this.clazz == null) {
            throw new IllegalStateException(String.format("%s couldn't parse model class from generic type ", clazz));
        }
        Document document = this.clazz.getAnnotation(Document.class);
        if (null != document) {
            modelKey = document.modelKey();
            if (StringUtils.isEmpty(modelKey)) {
                modelKey = document.index();
            }
            refreshPolicy = document.refreshPolicy();
            requestCache = document.requestCache();
            logicDelete = document.logicDelete();
        } else {
            throw new IllegalStateException(String.format("%s couldn't found model document annotation from generic type ", clazz));
        }

        if (StringUtils.isEmpty(modelKey)) {
            throw new IllegalStateException(String.format("%s couldn't found modelKey or index attribute from document annotation ", clazz));
        }
    }

    /**
     * 根据id查询
     *
     * @param id 主键id
     * @return 模型数据
     */
    public T selectById(Long id) {
        TrantorSearchRequest request = new TrantorSearchRequest();
        request.setTeamCode(TrantorContext.getTeamCode());
        request.setModelKey(modelKey);
        request.indices(index());
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(QueryBuilders.idsQuery().addIds(id.toString()));
        request.source(builder);
        request.requestCache(requestCache);
        SearchResult<T> result = getClient().search(request, clazz);
        return result.getFirst();
    }

    /**
     * 根据id集合批量查询
     *
     * @param ids id集合
     * @return 模型数据
     */
    public List<T> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        TrantorSearchRequest request = new TrantorSearchRequest();
        request.setTeamCode(TrantorContext.getTeamCode());
        request.setModelKey(modelKey);
        request.indices(index());
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(QueryBuilders.idsQuery().addIds(ids.stream().map(Object::toString).toArray(String[]::new)));
        request.source(builder);
        request.requestCache(requestCache);
        SearchResult<T> result = getClient().search(request, clazz);
        return result.getData();
    }

    /**
     * 根据指定条件分页查询
     *
     * @param so 模型查询条件
     * @return 模型数据
     */
    public Paging<T> selectPage(T so, Integer pageNo, Integer pageSize) {
        SearchWrapper wrapper = SearchWrapper.of(so).page(pageNo, pageSize).build();
        return selectPage(wrapper);
    }

    /**
     * 根据指定条件分页查询
     *
     * @param request 模型查询条件
     * @return 模型数据
     */
    public Paging<T> selectPage(AbstractPageRequest request) {
        SearchWrapper wrapper = SearchWrapper.of(request).build();
        return selectPage(wrapper);
    }

    /**
     * 根据自定义条件分页查询
     *
     * @param wrapper 模型查询条件
     * @return 模型数据
     */
    public Paging<T> selectPage(SearchWrapper wrapper) {
        SearchResult<T> result = search(wrapper);
        return result.getDocuments();
    }

    /**
     * 根据自定义条件搜索文档
     *
     * @param wrapper 模型查询条件
     * @return 搜索结果
     */
    public SearchResult<T> search(SearchWrapper wrapper) {
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.from(wrapper.from());
        builder.size(wrapper.size());

        BoolQueryBuilder boolQuery = innerQueryImprove(wrapper.query());

        builder.query(boolQuery);
        for (OrderField orderField : wrapper.orders()) {
            builder.sort(orderField.getFiled(), orderField.isAsc() ? SortOrder.ASC : SortOrder.DESC);
        }
        if (null != wrapper.highlight()) {
            builder.highlighter(wrapper.highlight());
        }
        TrantorSearchRequest request = new TrantorSearchRequest();
        request.setTeamCode(TrantorContext.getTeamCode());
        request.setModelKey(modelKey);
        request.indices(index());
        request.source(builder);
        request.requestCache(requestCache);
        return getClient().search(request, clazz);
    }

    /**
     * query增强，比如拼接逻辑删查询条件、拼接权限条件等
     *
     * @param query
     * @return
     */
    private BoolQueryBuilder innerQueryImprove(BoolQueryBuilder query) {
        // 如果是逻辑删除，需要拼接deleted=0查询条件
        if (logicDelete) {
            query.filter(QueryBuilders.termQuery("deleted", 0));
        }

        // TODO 暂时忽略掉数据权限
        // builder.query(DataPermissionQueryBuilders.commonQuery(index, wrapper.query()));

        return query;
    }

    /**
     * 单条保存，已存在则覆盖
     *
     * @param so 模型数据，必需指定id
     * @return 是否成功
     */
    public boolean insert(T so) {
        IndexRequest request = new IndexRequest();
        request.index(index());
        request.id(so.getId().toString());
        request.source(JsonUtils.toJson(so), XContentType.JSON);
        request.setRefreshPolicy(refreshPolicy);
        String id = getClient().index(request);
        return org.springframework.util.StringUtils.hasText(id);
    }

    /**
     * 批量保存，已存在则覆盖
     *
     * @param soList 模型数据，必需指定id
     * @return 成功条数
     */
    public int insertBatch(Collection<T> soList) {
        if (CollectionUtils.isEmpty(soList)) {
            return 0;
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (T so : soList) {
            IndexRequest request = new IndexRequest();
            request.index(index());
            request.id(so.getId().toString());
            request.source(JsonUtils.toJson(so), XContentType.JSON);
            request.setRefreshPolicy(refreshPolicy);
            bulkRequest.add(request);
        }
        return getClient().bulk(bulkRequest);
    }

    /**
     * 根据id更新
     *
     * @param so 模型数据
     * @return 是否成功
     */
    public boolean updateById(T so) {
        UpdateRequest request = new UpdateRequest();
        request.index(index());
        request.id(so.getId().toString());
        request.doc(JsonUtils.toJson(so), XContentType.JSON);
        request.setRefreshPolicy(refreshPolicy);
        return getClient().update(request);
    }

    /**
     * 根据id集合更新
     *
     * @param ids id集合
     * @param so  模型数据
     * @return 成功条数
     */
    public int updateByIds(Collection<Long> ids, T so) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        Long originalId = so.getId();
        BulkRequest bulkRequest = new BulkRequest();
        for (Long id : ids) {
            so.setId(null);
            UpdateRequest request = new UpdateRequest();
            request.index(index());
            request.id(id.toString());
            request.doc(JsonUtils.toJson(so), XContentType.JSON);
            request.setRefreshPolicy(refreshPolicy);
            bulkRequest.add(request);
        }
        try {
            return getClient().bulk(bulkRequest);
        } finally {
            so.setId(originalId);
        }
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 影响条数
     */
    public boolean deleteById(Long id) {
        DeleteRequest request = new DeleteRequest();
        request.index(index());
        request.id(id.toString());
        request.setRefreshPolicy(refreshPolicy);
        return getClient().delete(request);
    }

    /**
     * 根据id集合删除
     *
     * @param ids id集合
     * @return 影响条数
     */
    public int deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (Long id : ids) {
            DeleteRequest request = new DeleteRequest();
            request.index(index());
            request.id(id.toString());
            request.setRefreshPolicy(refreshPolicy);
            bulkRequest.add(request);
        }
        return getClient().bulk(bulkRequest);
    }

    /**
     * 获取搜索客户端
     *
     * @return 客户端对象
     */
    public SearchClient getClient() {
        if (null == client) {
            client = SpringContextHelper.getBean(SearchClient.class);
        }
        return client;
    }

    /**
     * 获取索引名称，使用时对应别名
     *
     * @return 索引名称
     */
    public String index() {
        // 与dump服务的命名约定：索引别名生成
        if (Objects.equals(MULTI_TENANT_ENABLED, "true")) {
            return ("TID" + TrantorContext.getTenantId() + "_" + TrantorContext.getTeamCode() + "_" + modelKey).toLowerCase();
        } else {
            return (TrantorContext.getTeamCode() + "_" + modelKey).toLowerCase();
        }
    }

    /**
     * 获取刷新策略
     *
     * @return 刷新策略
     */
    public String refreshPolicy() {
        return refreshPolicy;
    }

    /**
     * 是否启用查询缓存
     *
     * @return 是否
     */
    public boolean requestCache() {
        return requestCache;
    }

}
