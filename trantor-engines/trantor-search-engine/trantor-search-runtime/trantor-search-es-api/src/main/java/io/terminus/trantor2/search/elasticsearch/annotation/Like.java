package io.terminus.trantor2.search.elasticsearch.annotation;

import java.lang.annotation.*;

/**
 * 搜索引擎 wildcard 查询条件
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Like {
    /**
     * 对应搜索引擎中的文档field名，默认为字段变量名
     */
    String field() default "";

    /**
     * 头部通配符，如 * 匹配0或多个字符，影响性能，建议不使用头部通配符
     */
    boolean prefix() default false;
}
