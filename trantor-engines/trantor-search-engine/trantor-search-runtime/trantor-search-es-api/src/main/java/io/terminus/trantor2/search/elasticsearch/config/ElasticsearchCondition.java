package io.terminus.trantor2.search.elasticsearch.config;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.StringUtils;

import java.util.Objects;

public class ElasticsearchCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String url = context.getEnvironment().getProperty("trantor2.search-api.elasticsearch.url", "");
        return StringUtils.hasText(url) && !Objects.equals(":", url) && !Objects.equals("http://:", url);
    }
}
