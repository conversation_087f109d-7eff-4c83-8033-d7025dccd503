package io.terminus.trantor2.search.elasticsearch.config;

import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.properties.SearchApiProperties;
import io.terminus.trantor2.search.elasticsearch.client.SearchClient;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Base64;

@Configuration
@ComponentScan("io.terminus.trantor2.search")
@EnableConfigurationProperties(SearchApiProperties.class)
@Conditional(ElasticsearchCondition.class)
public class ElasticsearchAutoConfiguration {

    @Bean(destroyMethod = "close")
    public RestHighLevelClient restHighLevelClient(SearchApiProperties searchApiProperties) {
        SearchApiProperties.ElasticsearchProperties properties = searchApiProperties.getElasticsearch();
        String[] urls = properties.getUrl().split(",");
        RestClientBuilder builder = RestClient.builder(Arrays.stream(urls).map(HttpHost::create).toArray(HttpHost[]::new));
        if (StringUtils.hasText(properties.getUsername()) && StringUtils.hasText(properties.getPassword())) {
            String token = "Basic " + Base64.getEncoder().encodeToString((properties.getUsername() + ":" + properties.getPassword()).getBytes());
            builder.setDefaultHeaders(new Header[]{new BasicHeader("Authorization", token)});
        }
        SearchApiProperties.ElasticsearchProperties.HttpConfig config = properties.getHttp();
        builder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                .setConnectTimeout(config.getConnectTimeout())
                .setSocketTimeout(config.getSocketTimeout())
                .setConnectionRequestTimeout(config.getConnectionRequestTimeout()))
            .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder
                .setMaxConnTotal(config.getMaxConnections())
                .setMaxConnPerRoute(config.getMaxConnectionsPerRoute())
                .setKeepAliveStrategy((httpResponse, httpContext) -> config.getMaxKeepaliveTime()));
        return new RestHighLevelClient(builder);
    }

    @Bean
    public SearchClient searchClient(RestHighLevelClient client, SearchApiProperties properties, MetaCache metaCache) {
        return new SearchClient(client, properties, metaCache);
    }

}
