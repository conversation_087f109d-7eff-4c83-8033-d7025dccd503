package io.terminus.trantor2.search.elasticsearch.client;

import com.alibaba.fastjson.JSONObject;
import io.terminus.common.api.model.Paging;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelFieldStoreType;
import io.terminus.trantor2.model.management.meta.domain.search.es.EsJsonFieldType;
import io.terminus.trantor2.properties.SearchApiProperties;
import io.terminus.trantor2.search.elasticsearch.model.GetResult;
import io.terminus.trantor2.search.elasticsearch.model.SearchResult;
import io.terminus.trantor2.search.elasticsearch.util.JsonUtils;
import io.terminus.trantor2.search.elasticsearch.wrapper.TrantorSearchRequest;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Slf4j
public class SearchClient {
    public final static String DEFAULT_TYPE = "_doc";
    private final RestHighLevelClient client;
    private final SearchApiProperties properties;
    private final MetaCache metaCache;

    /**
     * 搜索
     *
     * @param request 搜索请求
     * @param clazz   文档模型
     * @return 搜索结果
     * @see SearchRequest#indices(String...)                     索引名称
     * @see SearchRequest#preference(String)                     路由偏好
     * @see SearchRequest#requestCache(Boolean)                  查询缓存
     * @see SearchSourceBuilder#fetchSource(boolean)             是否召回文档
     * @see SearchSourceBuilder#from(int)                        分页起始
     * @see SearchSourceBuilder#size(int)                        分页数量
     * @see SearchSourceBuilder#query(QueryBuilder)              查询语句
     * @see SearchSourceBuilder#sort(SortBuilder)                排序语句
     * @see SearchSourceBuilder#aggregation(AggregationBuilder)  聚合语句
     * @see SearchSourceBuilder#minScore(float)                  最小得分限制
     */
    public <T> SearchResult<T> search(SearchRequest request, Class<T> clazz) {
        try {
            SearchResponse response = doEsSearch(request);
            SearchResult<T> result = new SearchResult<>();
            result.setTookInMillis(response.getTook().millis());
            SearchHits searchHits = response.getHits();
            if (null != searchHits) {
                result.setMaxScore(searchHits.getMaxScore());
                long total = null != searchHits.getTotalHits() ? searchHits.getTotalHits().value : 0;
                List<T> dataList = (request instanceof TrantorSearchRequest) ?
                    parseSource(searchHits.getHits(), clazz, ((TrantorSearchRequest) request).getTeamCode(), ((TrantorSearchRequest) request).getModelKey())
                    : parseSource(searchHits.getHits(), clazz, null, null);
                result.setDocuments(Paging.of(total, dataList));
                if (null != request.source().highlighter()) {
                    result.setHighlights(parseHighlight(searchHits.getHits(), clazz));
                }
            }
            Aggregations aggregations = response.getAggregations();
            if (null != aggregations) {
                result.setAggregations(aggregations.asMap());
            }
            return result;
        } catch (Exception e) {
            String errMsg = "elasticsearch.search.failed,error:" + e.getMessage() + ", request:" + request.toString();
            log.error(errMsg, e);
            throw new TrantorRuntimeException(errMsg, e);
        }
    }

    /**
     * 独立es api搜索方法，方便自行处理es返回数据
     *
     * @param request
     * @return
     * @throws IOException
     */
    public SearchResponse doEsSearch(SearchRequest request) throws IOException {
        return client.search(request, RequestOptions.DEFAULT);
    }

    @SneakyThrows
    public <T> GetResult<T> get(GetRequest request, Class<T> clazz) {
        request.type(DEFAULT_TYPE);
        GetResponse getResponse = client.get(request, RequestOptions.DEFAULT);
        GetResult getResult = new GetResult();
        if (getResponse.isExists()) {
            getResult.setData(JsonUtils.fromJson(getResponse.getSourceAsString(), clazz));
        }
        return getResult;
    }


    /**
     * 聚合，不召回文档
     *
     * @param request 搜索请求
     * @return 聚合结果
     * @see SearchClient#search
     */
    public <T> SearchResult<T> aggregate(SearchRequest request) {
        SearchSourceBuilder builder = request.source();
        if (null != builder) {
            builder.fetchSource(false);
        }
        return search(request, null);
    }

    private <T> List<T> parseSource(SearchHit[] hits, Class<T> clazz, String teamCode, String modelKey) {
        if (null == hits || hits.length == 0 || null == clazz) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> documentMaps = new ArrayList<>(hits.length);
        for (SearchHit hit : hits) {
            documentMaps.add(hit.getSourceAsMap());
        }

        // 模型元信息查询
        DataStructNode dataStructNode = getDataStructNode(teamCode, modelKey);
        // json字段类型转换为字符串
        jsonFieldFormat(documentMaps, dataStructNode);

        List<T> documents = new ArrayList<>(hits.length);
        for (Map<String, Object> document : documentMaps) {
            documents.add(JsonUtils.fromJson(JsonUtils.toJson(document), clazz));
        }
        return documents;
    }

    /**
     * 如果模型字段用户自定义类型为json类型时，将对接字段查询结果转换为字符串
     *
     * @param documentMaps
     * @param dataStructNode
     */
    private void jsonFieldFormat(List<Map<String, Object>> documentMaps, DataStructNode dataStructNode) {
        if (null == dataStructNode) {
            return;
        }
        // key=fieldAlias, value=DataStructFieldNode
        Map<String, DataStructFieldNode> userDefineFieldMappings = dataStructNode.getChildren().stream()
            .filter(field -> null != field.getProps().getSearchModelFieldConfigMeta())
            .filter(field -> !Objects.equals(field.getProps().getSearchModelFieldConfigMeta().getFieldStoreType(),
                SearchModelFieldStoreType.NONE))
            .filter(field -> null != field.getProps().getSearchModelFieldConfigMeta().getMapping())
            .collect(Collectors.toMap(it -> it.getAlias(), Function.identity()));
        if (CollectionUtils.isEmpty(userDefineFieldMappings)) {
            return;
        }

        Map<String, EsJsonFieldType> esJsonFieldTypeMap = new HashMap<>();
        userDefineFieldMappings.keySet().forEach(fieldAlias -> {
            JSONObject userDefineMapping = userDefineFieldMappings.get(fieldAlias).getProps().getSearchModelFieldConfigMeta().getMapping();
            EsJsonFieldType type = EsJsonFieldType.findEsTypeByMapping(userDefineMapping);
            if (null != type) {
                esJsonFieldTypeMap.put(fieldAlias, type);
            }
        });

        documentMaps.forEach(dataMap -> {
            dataMap.keySet().forEach(fieldAlias -> {
                Object data = dataMap.get(fieldAlias);
                EsJsonFieldType esJsonFieldType = esJsonFieldTypeMap.get(fieldAlias);
                if (null != esJsonFieldType) {
                    data = esJsonFieldType.convertJson2String(data);
                }
                dataMap.put(fieldAlias, data);
            });
        });
    }

    /**
     * 模型元数据查询
     * 当modelKey不为空时，代表需要元数据介入，如果没有查询到元数据，直接对外抛异常
     * 当modelKey为空时，代表不需要元数据介入，直接返回
     *
     * @param teamCode
     * @param modelKey
     * @return
     */
    private DataStructNode getDataStructNode(String teamCode, String modelKey) {
        if (KeyUtil.isInvalidKey(modelKey)) {
            return null;
        }
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
        }
        TrantorContext.setTeamCode(teamCode);
        MetaTreeNodeExt node = metaCache.get(modelKey);
        if (node == null) {
            throw new RuntimeException("can not find DataStructNode by teamCode:" + teamCode + ",modelKey:" + modelKey);
        }
        return DataStructNode.newInstanceFrom(node);
    }

    private static <T> List<Map<String, List<String>>> parseHighlight(SearchHit[] hits, Class<T> clazz) {
        if (null == hits || hits.length == 0 || null == clazz) {
            return Collections.emptyList();
        }
        List<Map<String, List<String>>> highlights = new ArrayList<>(hits.length);
        for (SearchHit hit : hits) {
            Map<String, HighlightField> fields = hit.getHighlightFields();
            if (CollectionUtils.isEmpty(fields)) {
                highlights.add(Collections.emptyMap());
            } else {
                Map<String, List<String>> highlight = new LinkedHashMap<>(fields.size());
                for (Map.Entry<String, HighlightField> entry : fields.entrySet()) {
                    if (null == entry.getValue() || null == entry.getValue().getFragments() || 0 == entry.getValue().getFragments().length) {
                        highlight.put(entry.getKey(), Collections.emptyList());
                    } else {
                        highlight.put(entry.getKey(), Arrays.stream(entry.getValue().getFragments()).map(Text::string).collect(Collectors.toList()));
                    }
                }
                highlights.add(highlight);
            }
        }
        return highlights;
    }

    /**
     * 保存
     *
     * @param request 文档请求
     * @return 文档ID
     */
    public String index(IndexRequest request) {
        if (!StringUtils.hasText(request.type())) {
            request.type(DEFAULT_TYPE);
        }
        try {
            IndexResponse response = client.index(request, RequestOptions.DEFAULT);
            return response.getId();
        } catch (IOException e) {
            throw new RuntimeException("elasticsearch.index.failed", e);
        }
    }

    /**
     * 更新
     *
     * @param request 更新请求
     * @return 是否成功
     */
    public boolean update(UpdateRequest request) {
        if (!StringUtils.hasText(request.type())) {
            request.type(DEFAULT_TYPE);
        }
        try {
            UpdateResponse response = client.update(request, RequestOptions.DEFAULT);
            return DocWriteResponse.Result.UPDATED == response.getResult();
        } catch (IOException e) {
            throw new RuntimeException("elasticsearch.update.failed", e);
        }
    }

    /**
     * 删除
     *
     * @param request 删除请求
     * @return 是否成功
     */
    public boolean delete(DeleteRequest request) {
        if (!StringUtils.hasText(request.type())) {
            request.type(DEFAULT_TYPE);
        }
        try {
            DeleteResponse response = client.delete(request, RequestOptions.DEFAULT);
            return DocWriteResponse.Result.DELETED == response.getResult();
        } catch (IOException e) {
            throw new RuntimeException("elasticsearch.delete.failed", e);
        }
    }

    /**
     * 批量
     *
     * @param request 批量请求
     * @return 成功个数
     */
    public int bulk(BulkRequest request) {
        if (CollectionUtils.isEmpty(request.requests())) {
            return 0;
        }
        for (DocWriteRequest<?> req : request.requests()) {
            if (StringUtils.hasText(req.type())) {
                continue;
            }
            if (req instanceof IndexRequest) {
                ((IndexRequest) req).type(DEFAULT_TYPE);
            } else if (req instanceof UpdateRequest) {
                ((UpdateRequest) req).type(DEFAULT_TYPE);
            } else if (req instanceof DeleteRequest) {
                ((DeleteRequest) req).type(DEFAULT_TYPE);
            }
        }
        try {
            BulkResponse responses = client.bulk(request, RequestOptions.DEFAULT);
            int success = 0;
            for (BulkItemResponse response : responses.getItems()) {
                if (!response.isFailed()) {
                    success++;
                }
            }
            return success;
        } catch (IOException e) {
            throw new RuntimeException("elasticsearch.bulk.failed", e);
        }
    }

    private static <T> List<T> parseSource(SearchHit[] hits, Class<T> clazz) {
        if (null == hits || hits.length == 0 || null == clazz) {
            return Collections.emptyList();
        }
        List<T> documents = new ArrayList<>(hits.length);
        for (SearchHit hit : hits) {
            documents.add(io.terminus.common.api.util.JsonUtils.fromJson(hit.getSourceAsString(), clazz));
        }
        return documents;
    }
}
