package io.terminus.trantor2.search.elasticsearch.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDate;

public class LocalDateSerializer extends JsonSerializer<LocalDate> {

    @Override
    public void serialize(LocalDate localDate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null == localDate) {
            return;
        }
        jsonGenerator.writeString(DateTimeUtils.formatLocalDate(localDate));
    }

    @Override
    public Class<LocalDate> handledType() {
        return LocalDate.class;
    }
}
