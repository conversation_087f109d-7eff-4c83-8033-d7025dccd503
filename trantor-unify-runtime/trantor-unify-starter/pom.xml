<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-unify-runtime</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-unify-starter</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-unify-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.hedy.gravity</groupId>
            <artifactId>gravity-spring-boot-starter</artifactId>
            <version>${gravity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>test</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>staging</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>prod</id>
        </profile>
    </profiles>
    <build>
        <finalName>trantor2-unify-runtime</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
