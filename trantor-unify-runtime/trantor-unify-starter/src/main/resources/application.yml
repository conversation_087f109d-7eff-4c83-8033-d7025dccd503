debug: true
logging:
  level:
    org.hibernate.SQL: ${HIBERNATE_SQL_LOG_LEVEL:warn}
    org.hibernate.type.descriptor.sql.BasicBinder: ${HIBERNATE_SQL_BASIC_BINDER_LOG_LEVEL:warn}
    com.zaxxer.hikari: ${HIKARI_LOG_LEVEL:info}
    io.terminus.trantor2.model.runtime.meta.transaction: ${TRANTOR_TRANSACTION_LOG_LEVEL:info}
    io.terminus.iam.sdk: ${IAM_SDK_LOG_LEVEL:warn}
server:
  port: 8080
  compression:
    enabled: true
    mime-types:
      - application/json
    min-response-size: 10KB
  tomcat:
    basedir: logs
    accesslog:
      enabled: ${ACCESSLOG_ENABLED:false}
      pattern: '%{yyyy-MM-dd HH:mm:ss}t|[%I][%{reqId}i]-|cost=%D remoteIp=%a localIp=%A url=%U status=%s'
spring:
  application:
    name: trantor2-unify-runtime
  servlet:
    multipart:
      enabled: ${upload_file_standard:true}
      max-file-size: ${upload_max_file:500MB}
      max-request-size: ${upload_max_request:500MB}
#  flyway:
#    enabled: ${FLYWAY_ENABLED:false}
  cloud.nacos.discovery.enabled: ${NACOS_DISCOVERY_ENABLED:false}
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLE:false}
  packages-to-scan: io.terminus.trantor2
  swagger-ui:
    filter: true

trantor2:
  deployment:
    mode: integrated
  api-docs:
    title: ${DOCS_TITLE:Trantor2 API}
    version: ${DOCS_VERSION:1.0.0}
  runtime:
    default-mode: true
  trigger:
    group-prefix: Trigger_
    topic: ${terminus.topic}
    pool-threads: 0
    logger-enable: true
  license:
    mock-enable: true
  cloud.nacos.discovery.enable: ${NACOS_DISCOVERY_ENABLED:false}
trantor:
  engine:
    host:
    process:
      serverEndpointUrl: ${PROCESS_SERVER_ENDPOINT_URL:https://terminus-process-dev.app.terminus.io/engine-rest}

terminus:
  oplog.enable: false
