package io.terminus.trantor2.unify.runtime;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * trantor2 unify runtime starter
 */
@EnableCaching
@ConfigurationPropertiesScan(basePackages = "io.terminus.iam")
@SpringBootApplication(scanBasePackages = {"io.terminus.iam", "io.terminus.trantor2"}, exclude = JooqAutoConfiguration.class)
@EnableFeignClients(basePackages = {"io.terminus.trantor", "io.terminus.trantor2", "io.terminus.i18n.api.facade"})
public class TrantorRuntimeApplication {
    public static void main(String[] args) {
        SpringApplication.run(TrantorRuntimeApplication.class, args);
    }
}
