package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.TrantorBizException;

import static io.terminus.trantor2.common.exception.ErrorType.META_LOCKED;

/**
 * <AUTHOR>
 */
public class MetaLockedException extends TrantorBizException {
    private static final long serialVersionUID = 1314128214433177700L;

    public MetaLockedException(String userName) {
        super(META_LOCKED, String.format(META_LOCKED.getMessage(), userName), new Object[]{userName});
    }
}
