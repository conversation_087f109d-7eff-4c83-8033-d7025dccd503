package io.terminus.trantor2.meta.api.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.objects.tree.ResourceNodeIdentity;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * 2023/6/13 1:46 PM
 */
@Data
public class MetaTreeNodeObject extends MetaTreeNodeExt {

    private String sid;

    private List<ResourceNodeIdentity> children;

    private String propsJson;

    private String childrenJson;

    public void setChildren(List<ResourceNodeIdentity> children) {
        childrenJson = JsonUtil.toJson(children);
    }

    public List<ResourceNodeIdentity> deserializeChildren() {
        return JsonUtil.fromJson(childrenJson, new TypeReference<List<ResourceNodeIdentity>>() {
        });
    }

    public void setProps(ObjectNode props) {
        propsJson = JsonUtil.toJson(props);
    }

    public ObjectNode getProps() {
        return JsonUtil.fromJson(propsJson, ObjectNode.class);
    }
}
