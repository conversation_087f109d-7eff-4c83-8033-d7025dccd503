package io.terminus.trantor2.meta.platform;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR>
 */
@Jacksonized
@Builder
@Data
@AllArgsConstructor
public final class PlatformVersion {
    private final String number;
    private final String checksum;

    @JsonCreator
    public static PlatformVersion of(@JsonProperty String number, @JsonProperty String checksum) {
        return new PlatformVersion(number, checksum);
    }

    public static PlatformVersion from(String version) {
        ObjectNode deser = ObjectJsonUtil.deserialize(version);
        return new PlatformVersion(deser.get("number").asText(), deser.get("checksum").asText());
    }

    public String toVersion() {
        try {
            return ObjectJsonUtil.MAPPER.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public boolean isEqual(PlatformVersion other) {
        return this.number.equals(other.number) && this.checksum.equals(other.checksum);
    }
}
