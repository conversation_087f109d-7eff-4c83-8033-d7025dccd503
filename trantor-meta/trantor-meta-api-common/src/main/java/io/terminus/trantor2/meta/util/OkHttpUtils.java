package io.terminus.trantor2.meta.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import io.terminus.iam.api.exception.IAMUserNotLoginException;
import io.terminus.trantor2.common.TrantorCommonConstant;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.HttpErrorException;
import io.terminus.trantor2.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.ResponseBody;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("unchecked")
public class OkHttpUtils {
    public static final MediaType JSON_TYPE = MediaType.get("application/json;charset=utf-8");
    public static final TypeReference<Response> type = new TypeReference<Response>() {
    };
    public static final TypeReference<Response<Map<String, Object>>> map = new TypeReference<Response<Map<String, Object>>>() {
    };

    public static void handleError(okhttp3.Response response) {
        if (!response.isSuccessful()) {
            Response<?> errorResponse;
            String bodyStr = getBody(response);
            try {
                errorResponse = JsonUtil.fromJson(bodyStr, Response.class);
            } catch (Exception e) {
                log.error("Parser error json failure: {}", bodyStr);
                throw new HttpErrorException(response.code(), response.message());
            }
            if (errorResponse != null && !Strings.isNullOrEmpty(errorResponse.getErrMsg())) {
                throw new HttpErrorException(errorResponse);
            } else {
                throw new HttpErrorException(response.code(), response.message());
            }
        }
    }

    /**
     * 只能处理 Response<responseClass>
     */
    public static <T> T handleResponse(okhttp3.Response response, Class<T> responseClass) {
        checkIf401(response);
        handleError(response);
        String responseString = getBody(response);
        if (responseString == null) {
            log.info("HttpResponse body is null");
            return null;
        }
        return toResourceRes(responseClass, responseString).getData();
    }

    /**
     * 只能处理 Response<responseClass>
     */
    public static <T> T handleResponse(okhttp3.Response response, TypeReference<T> responseClass) {
        checkIf401(response);
        String responseString = getBody(response);
        if (responseString == null) {
            log.info("HttpResponse body is null");
            return null;
        }
        return toResourceRes(responseClass, responseString).getData();
    }

    public static void handleResponse(okhttp3.Response response) {
        checkIf401(response);
        String responseString = getBody(response);
        if (responseString == null) {
            log.info("HttpResponse body is null");
            return;
        }
        Response responseObj = toResponse(responseString);
        if (!responseObj.isSuccess()) {
            log.error("Error response: {}", responseObj.getErrMsg());
            throw new HttpErrorException(responseObj);
        }
    }

    private static void checkIf401(okhttp3.Response response) {
        if (!response.isSuccessful() && response.code() == 401) {
            throw new IAMUserNotLoginException();
        }
    }

    public static Map<String, String> makeJSONHeader() {
        return makeHeader();
    }

    private static Map<String, String> makeHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json;charset=utf-8");
        if (TrantorContext.getTeamId() != null) {
            header.put(TrantorCommonConstant.TEAM_HEADER_KEY, TrantorContext.getTeamId().toString());
        }
        return header;
    }

    private static Response getObjectResponse(String responseString) {
        log.debug("HttpResponse body: " + responseString);
        return JsonUtil.fromJson(responseString, type);
    }

    private static Response toResponse(String responseString) {
        return getObjectResponse(responseString);
    }

    private static <T> Response<T> toResourceRes(Class<T> clazz, String responseString) {
        Response response = getObjectResponse(responseString);
        if (response.isSuccess()) {
            if (response.getData() == null) {
                return response;
            }
            Object resource = response.getData();
            response.setData(JsonUtil.convert(resource, clazz));
        } else {
            log.error("Error response: {}", responseString);
            throw new HttpErrorException(response);
        }
        return response;
    }

    private static <T> Response<T> toResourceRes(TypeReference<T> typeReference, String responseString) {
        Response response = getObjectResponse(responseString);
        if (response.isSuccess()) {
            if (response.getData() == null) {
                return response;
            }
            Object resource = response.getData();
            response.setData(JsonUtil.convert(resource, typeReference));
        } else {
            log.error("Error response: {}", responseString);
            throw new HttpErrorException(response);
        }
        return response;
    }

    public static String getBody(okhttp3.Response response) {
        String responseString = null;
        if (response.body() != null) {
            try {
                ResponseBody responseBody = response.body();
                if (responseBody != null) responseString = new String(responseBody.bytes(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                log.error("Error when get http response", e);
            }
        }
        return responseString;
    }
}
