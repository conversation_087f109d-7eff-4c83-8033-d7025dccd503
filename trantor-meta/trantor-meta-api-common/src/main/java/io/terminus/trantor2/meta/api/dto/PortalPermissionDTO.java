package io.terminus.trantor2.meta.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PortalPermissionDTO {

    private String portalKey;

    /**
     * 所有菜单的权限
     */
    // TODO: implement this
    //private List<MenuPermissionDTO> menus;

    /**
     * 所有被菜单引用的场景视图的权限
     */
    private List<ViewPermissionDTO> views;

    /**
     * 游离服务/API的权限
     */
    private AccessControlList acl;

    @Data
    public static class AccessControlList {
        /**
         * 绑定的服务
         */
        private List<ViewPermissionDTO.BindServicePermissionDTO> bindServices;

        /**
         * 绑定的系统服务
         */
        private List<ViewPermissionDTO.BindSysServicePermissionDTO> bindSysServices;

        /**
         * 绑定的api
         */
        private List<ViewPermissionDTO.BindApiPermissionDTO> bindApis;
    }
}
