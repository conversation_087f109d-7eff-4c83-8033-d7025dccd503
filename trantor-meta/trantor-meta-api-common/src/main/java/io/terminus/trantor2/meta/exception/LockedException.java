package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.TrantorBizException;

import jakarta.annotation.Nonnull;
import java.util.Date;

import static io.terminus.trantor2.common.exception.ErrorType.LOCKED;

/**
 * <AUTHOR>
 */
public class LockedException extends TrantorBizException {
    private static final long serialVersionUID = 7830100453267257347L;

    public LockedException(@Nonnull String userName, @Nonnull Date date) {
        super(LOCKED, String.format(LOCKED.getMessage(),
                userName,
                userName,
                String.format("%tY-%tm-%td %tH:%tM:%tS", date, date, date, date, date, date)),
            new Object[]{userName, userName, String.format("%tY-%tm-%td %tH:%tM:%tS", date, date, date, date, date, date)}
        );
    }
}
