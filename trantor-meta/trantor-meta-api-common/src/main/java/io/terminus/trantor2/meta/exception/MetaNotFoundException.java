package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.TrantorBizException;

import static io.terminus.trantor2.common.exception.ErrorType.META_NOT_FOUND;

/**
 * <AUTHOR>
 */
public class MetaNotFoundException extends TrantorBizException {
    private static final long serialVersionUID = -7008485733677440484L;

    public MetaNotFoundException(String key) {
        super(META_NOT_FOUND, "meta not found: " + key, new Object[]{key});
    }

    public MetaNotFoundException(String metaType, String key) {
        super(META_NOT_FOUND, metaType + " not found: " + key, new Object[]{metaType, key});
    }

}
