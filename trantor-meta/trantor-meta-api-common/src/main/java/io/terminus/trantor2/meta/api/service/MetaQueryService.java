package io.terminus.trantor2.meta.api.service;

import io.terminus.trantor2.lang.meta.ViewContainer;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.PortalPermissionDTO;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.dto.ai.MetaSearchItem;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Meta Query Service.
 * <p>
 * 隐式传递 teamId & appId & userId: {@link io.terminus.trantor2.common.TrantorContext}
 *
 * <AUTHOR>
 */
public interface MetaQueryService {

    @Deprecated
    Optional<MetaTreeNodeExt> findById(Long id);

    @Deprecated
    Optional<String> findKeyById(Long id);

    @Deprecated
    List<MetaTreeNodeExt> findAllById(Collection<Long> ids);

    Optional<MetaTreeNodeExt> findByKey(MetaEditAndQueryContext ctx, String key);

    List<MetaTreeNodeExt> findSubTree(@Nonnull MetaEditAndQueryContext ctx, @Nonnull String key,
                                      @Nullable Collection<String> typesIn);

    List<MetaTreeNodeExt> findSubTree(@Nonnull MetaEditAndQueryContext ctx, @Nonnull String key);

    Map<String, List<ResourceNodeLite>> findIndexBunchFromRoot(MetaEditAndQueryContext ctx, Collection<String> keys);

    List<MetaTreeNodeExt> findByKeyIn(MetaEditAndQueryContext ctx, Collection<String> keys);

    default List<MetaTreeNode> findUsageByKey(MetaEditAndQueryContext ctx, String key) {
        return findUsageByKey(ctx, key, null);
    }

    List<MetaTreeNode> findUsageByKey(MetaEditAndQueryContext ctx, String key, Collection<String> types);

    Map<String, List<String>> findUsageKeysByKeys(MetaEditAndQueryContext ctx, List<String> keys);

    List<String> findUsageKeysByKey(MetaEditAndQueryContext ctx, String key, Collection<String> types);

    List<MetaTreeNode> findUsageMetaByTargetKey(MetaEditAndQueryContext ctx, String targetType, String targetKey);

    Map<String, Set<String>> findUsageKeysByTargetTypeAndTargetKeys(MetaEditAndQueryContext ctx, String targetType, Collection<String> targetKeys);

    Map<String, List<MetaTreeNodeExt>> findUsageMetaByTargetTypeAndTargetKeys(MetaEditAndQueryContext ctx, String targetType, Collection<String> targetKeys);

    Map<String, Set<String>> findReferenceByKeysAndTargetType(MetaEditAndQueryContext ctx, Collection<String> keys, MetaType targetType);

    Map<String, Set<String>> findReferenceByKeysAndTargetTypes(MetaEditAndQueryContext ctx, Collection<String> keys, Collection<String> targetTypes);

    ViewPermissionDTO findSingleViewPermissionsCached(MetaEditAndQueryContext ctx, String portalKey, String viewKey);

    List<ViewPermissionDTO> findViewPermissions(MetaEditAndQueryContext ctx, String portalKey, List<String> viewKeys);

    List<PortalPermissionDTO> findPortalPermissions(MetaEditAndQueryContext ctx, List<String> portalKeys);

    List<ViewPermissionDTO.BindServicePermissionDTO> findPermissionByServiceKey(MetaEditAndQueryContext ctx, List<ViewPermissionDTO.BindService> services);

    List<ViewPermissionDTO.BindSysServicePermissionDTO> findPermissionBySysService(MetaEditAndQueryContext ctx, List<ViewPermissionDTO.BindSysService> sysServices);

    List<ViewPermissionDTO.BindApiPermissionDTO> findPermissionByApi(MetaEditAndQueryContext ctx, String portalKey, List<ViewPermissionDTO.BindApi> apis);

    List<ViewPermissionDTO.BindAIAgentPermissionDTO> findPermissionByAIAgent(MetaEditAndQueryContext ctx, String portalKey, List<ViewPermissionDTO.BindAIAgent> aiAgents);

    Map<String, Set<String>> findPortalsBySceneKeys(MetaEditAndQueryContext ctx, Collection<String> sceneKeys);

    Map<String, Set<String>> findPortalsByViewKeys(MetaEditAndQueryContext ctx, Collection<String> viewKeys);

    Map<String, Set<String>> findPortalsByServiceKeys(MetaEditAndQueryContext ctx, Collection<String> serviceKeys);

    Map<String, List<ViewContainer>> findViewContainers(MetaEditAndQueryContext ctx, List<String> viewKeys);

    Map<String, Boolean> existKeys(MetaEditAndQueryContext ctx, Collection<String> keys);

    <T extends Serializable> QueryOp queryInTeam(T team);

    QueryOp queryInApp(MetaEditAndQueryContext ctx);

    Collection<MetaTreeNode> findSingleSummaryByBranchIdAndParentKeys(MetaEditAndQueryContext ctx,
                                                                      Collection<String> parentKeys);

    boolean isEnhanced(MetaEditAndQueryContext ctx, String moduleKey);

    List<MetaSearchItem> modelDependencyAnalysis(Long teamId, String modelKey, Collection<String> types);
}
