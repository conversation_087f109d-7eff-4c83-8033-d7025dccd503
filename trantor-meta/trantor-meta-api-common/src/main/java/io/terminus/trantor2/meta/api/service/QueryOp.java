package io.terminus.trantor2.meta.api.service;

import com.google.common.base.Strings;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public interface QueryOp extends CondBaseQueryOp {

    default boolean existsByKey(String key) {
        return findOne(Field.key().equal(key)).isPresent();
    }

    default boolean existsByParentKeyAndNameAndType(String parentKey, String name, String type) {
        return findOne(Field.parentKey().equal(parentKey).and(Field.name().equal(name)).and(Field.type().equal(type))).isPresent();
    }

    /**
     * load one level directed children by key
     *
     * @param key current node key
     * @return children
     */
    default List<MetaTreeNodeExt> findChildrenByKey(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return Collections.emptyList();
        }
        return findAll(Field.parentKey().equal(key));
    }

    /**
     * load one level directed children by key, only load children with specified type
     *
     * @param key   current node key
     * @param types node type
     * @return children
     */
    default List<MetaTreeNodeExt> findChildrenByKeyAndTypeIn(String key, Collection<String> types) {
        if (Strings.isNullOrEmpty(key)) {
            return Collections.emptyList();
        }
        Object[] ts = types.stream().distinct().toArray();
        if (ts.length == 0) {
            return Collections.emptyList();
        }
        return findAll(Field.parentKey().equal(key).and(Field.type().in(ts)));
    }

    default Optional<MetaTreeNodeExt> find(MetaType type, String key) {
        return findOne(Cond.and(Field.key().equal(key), Field.type().equal(type.name())));
    }

    default Optional<MetaTreeNodeExt> findRoot() {
        return findOne(Field.type().equal("Root"));
    }

    default Optional<MetaTreeNodeExt> findModule() {
        return findOne(Field.type().equal("Module"));
    }

    default Optional<MetaTreeNodeExt> findFolderRoot() {
        return findOne(Field.type().equal("FolderRoot"));
    }

    default String findFolderRootKey() {
        return findFolderRoot().map(MetaTreeNodeExt::getKey).orElseThrow(() -> new RuntimeException("folder root not found"));
    }

    default Optional<MetaTreeNodeExt> findMenuRoot() {
        return findOne(Field.type().equal("MenuRoot"));
    }

    default String findErrorCodeRootKey() {
        return findOne(Field.type().equal(MetaType.ErrorCodeRoot.name())).map(MetaTreeNode::getKey).orElseThrow(() -> new RuntimeException("error code root not found"));
    }

    default String findMenuRootKey() {
        return findMenuRoot().map(MetaTreeNodeExt::getKey).orElseThrow(() -> new RuntimeException("menu root not found"));
    }

    default Optional<MetaTreeNodeExt> findPresetFolder(String shortKey) {
        // TODO: type=Module is not a good way to find module, because ctx will across modules
        // TODO: move module-related query out
        return findOne(Field.type().equal("Module"))
            .map(it -> it.getKey() + "$" + shortKey)
            .flatMap(it -> findOne(Field.key().equal(it)));
    }

    default Optional<MetaTreeNodeExt> findUngroupFolder() {
        return findPresetFolder("ungroup");
    }

    default Optional<MetaTreeNodeExt> findSysFolder() {
        return findPresetFolder("sys");
    }

    List<String> findKeysByModuleAndType(@NotNull String moduleKey, @NotNull String type);
}
