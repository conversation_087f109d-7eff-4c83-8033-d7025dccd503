package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class MetaNameDuplicatedException extends TrantorBizException {
    private static final long serialVersionUID = -5246246275582452545L;

    public MetaNameDuplicatedException(String type, String name, String key, String parentKey, String parentName) {
        super(ErrorType.META_NAME_DUPLICATED, String.format(
            "name already exist: [%s]%s in %s(%s)",
            type, name, parentName, parentKey
        ));
    }
}
