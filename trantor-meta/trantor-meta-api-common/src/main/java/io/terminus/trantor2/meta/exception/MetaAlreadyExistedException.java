package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class MetaAlreadyExistedException extends TrantorBizException {
    private static final long serialVersionUID = 8667914377766427114L;

    public MetaAlreadyExistedException(String key) {
        super(ErrorType.META_ALREADY_EXISTED, "meta already exists: " + key, new Object[]{key});
    }
}
