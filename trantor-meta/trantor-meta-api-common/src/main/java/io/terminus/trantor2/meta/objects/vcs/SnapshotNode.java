package io.terminus.trantor2.meta.objects.vcs;

import io.terminus.trantor2.meta.objects.tree.ResourceAuditField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.List;

/**
 * @author: yang<PERSON><PERSON><PERSON>
 * @date: 2023/11/1 3:30 PM
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class SnapshotNode {
    private List<String> lastSnapshotOids;
    private String rootOid;
    private List<ResourceAuditField> resources;
    private Date timestamp;
}
