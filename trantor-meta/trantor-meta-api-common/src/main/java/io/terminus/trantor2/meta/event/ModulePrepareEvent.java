package io.terminus.trantor2.meta.event;

import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * <AUTHOR>
 * 2023/6/27 10:38 AM
 */
@Getter
public class ModulePrepareEvent extends ApplicationEvent {
    private static final long serialVersionUID = -748192854815386053L;

    private final Set<ModuleInfo> modules;
    private final boolean defaultMode;

    public ModulePrepareEvent(Set<ModuleInfo> modules, boolean defaultMode) {
        super(modules);
        this.modules = modules;
        this.defaultMode = defaultMode;
    }
}
