package io.terminus.trantor2.meta.api.dto.criteria;

import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class Field<T> {
    private String fieldName;

    private List<String> fieldPath;

    private Class<T> clazz;

    private Field(String fieldName) {
        this.fieldName = fieldName;
    }

    private Field<T> clazz(Class<T> clazz) {
        this.clazz = clazz;
        return this;
    }

    private Field<T> path(String... p) {
        if (this.fieldPath == null) {
            this.fieldPath = new ArrayList<>();
        }
        this.fieldPath.addAll(Arrays.asList(p));
        return this;
    }

    private Cond cond(Func func, Object... values) {
        return new FieldCond<>(this, func, values);
    }

    public static Field<String> key() {
        return new Field<String>("key").clazz(String.class);
    }

    public static Field<String> type() {
        return new Field<String>("type").clazz(String.class);
    }

    public static Field<String> subType() {
        return new Field<String>("sub_type").clazz(String.class);
    }

    public static Field<String> moduleKey() {
        return new Field<String>("module_key").clazz(String.class);
    }

    public static Field<String> name() {
        return new Field<String>("name").clazz(String.class);
    }

    public static Field<String> parentKey() {
        return new Field<String>("parent_key").clazz(String.class);
    }

    public static Field<MetaNodeAccessLevel> access() {
        return new Field<MetaNodeAccessLevel>("access").clazz(MetaNodeAccessLevel.class);
    }

    public static Field<Long> appId() {
        return new Field<Long>("appId").clazz(Long.class);
    }

    public static <T> Field<T> props(Class<T> clazz, String... p) {
        return new Field<T>("props").clazz(clazz).path(p);
    }

    @Deprecated
    public static Field<String> props() {
        return new Field<String>("props-string").clazz(String.class);
    }

    public static ExtField ext() {
        return new ExtField();
    }

    public static class ExtField {
        public Field<Long> modifiedBy() {
            return new Field<Long>("modified_by").clazz(Long.class);
        }

        public Field<Date> modifiedAt() {
            return new Field<Date>("modified_at").clazz(Date.class);
        }
    }

    public Cond equal(T value) {
        return cond(Func.EQUAL, value);
    }

    public Cond notEqual(T value) {
        return Cond.not(cond(Func.EQUAL, value));
    }

    public Cond greaterThan(T value) {
        return cond(Func.GREATER_THAN, value);
    }

    public Cond greaterThanOrEqual(Object value) {
        return Cond.not(cond(Func.LESS_THAN, value));
    }

    public Cond lessThan(Object value) {
        return cond(Func.LESS_THAN, value);
    }

    public Cond lessThanOrEqual(Object value) {
        return Cond.not(cond(Func.GREATER_THAN, value));
    }

    public Cond in(Object... values) {
        return cond(Func.IN, values);
    }

    public Cond in(Collection<?> values) {
        return cond(Func.IN, values.toArray());
    }

    public Cond notIn(Object... values) {
        return Cond.not(cond(Func.IN, values));
    }

    public Cond like(Object value) {
        return cond(Func.LIKE, value);
    }

    public Cond notLike(Object value) {
        return Cond.not(cond(Func.LIKE, value));
    }

    public Cond isNull() {
        return cond(Func.IS_NULL);
    }

    public Cond isNotNull() {
        return Cond.not(cond(Func.IS_NULL));
    }

    public Cond between(Object value1, Object value2) {
        return cond(Func.BETWEEN, value1, value2);
    }

    public Cond notBetween(Object value1, Object value2) {
        return Cond.not(cond(Func.BETWEEN, value1, value2));
    }
}
