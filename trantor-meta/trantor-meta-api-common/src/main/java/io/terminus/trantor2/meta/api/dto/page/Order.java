package io.terminus.trantor2.meta.api.dto.page;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public class Order {

    private List<String> fields;

    private String direction;

    private Order(String direction, String... fields) {
        this.direction = direction;
        if (fields != null && fields.length > 0) {
            this.fields = Arrays.asList(fields);
        }
    }

    public static Order byKey() {
        return new Order("ASC", "`key`");
    }

    public static Order byModifiedAt() {
        return new Order("DESC", "modified_at");
    }

    public Order asc() {
        this.direction = "ASC";
        return this;
    }

    public Order desc() {
        this.direction = "DESC";
        return this;
    }
}
