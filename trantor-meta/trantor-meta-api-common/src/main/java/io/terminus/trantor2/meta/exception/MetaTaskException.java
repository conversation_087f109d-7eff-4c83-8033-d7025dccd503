package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class MetaTaskException extends TrantorBizException {
    private static final long serialVersionUID = -3225960617669189874L;

    public MetaTaskException(String message) {
        super(ErrorType.META_TASK_EXEC_ERROR, message, new Object[]{message});
    }
}
