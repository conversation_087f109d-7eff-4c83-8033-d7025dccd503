package io.terminus.trantor2.meta.api.dto.criteria;

import java.util.Arrays;
import java.util.List;

class ChainCond extends Cond {
    private List<Cond> conds;

    public ChainCond(Func func, Cond... conds) {
        this.func = func;
        if (conds != null && conds.length > 0) {
            this.conds = Arrays.asList(conds);
        }
    }

    @Override
    public Field getField() {
        return null;
    }

    @Override
    public List<Object> getValues() {
        return null;
    }

    @Override
    public List<Cond> getSubConds() {
        return conds;
    }
}
