package io.terminus.trantor2.meta.event;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 **/
@Data
public class ModelShardingConfigUpdateEvent implements RetryableEvent, Serializable {
    public final static String TAG = "MODEL_SHARDING_CONFIG_UPDATE_EVENT";

    private Long teamId;
    private String teamCode;
    private String modelKey;
    private Boolean shardingEnable;
    private Integer shardingNum;

    public static ModelShardingConfigUpdateEvent of(Long teamId, String teamCode, String modelKey, Boolean shardingEnable, Integer shardingNum) {
        ModelShardingConfigUpdateEvent event = new ModelShardingConfigUpdateEvent();
        event.setTeamId(teamId);
        event.setTeamCode(teamCode);
        event.setModelKey(modelKey);
        event.setShardingEnable(shardingEnable);
        event.setShardingNum(shardingNum);
        return event;
    }

    @Override
    public String getTag() {
        return TAG;
    }
}
