package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class MetaParseException extends TrantorBizException {
    private static final long serialVersionUID = 1233067371834812028L;

    public MetaParseException(String message) {
        super(ErrorType.META_PARSE_ERROR, message);
    }
}
