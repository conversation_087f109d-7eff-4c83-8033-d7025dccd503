package io.terminus.trantor2.meta.api.dto;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * 2023/7/13 5:27 PM
 */
@Data
public class ResourceContext {
    private String teamCode;
    private Long userId;

    private ResourceContext(String teamCode, Long userId) {
        this.teamCode = teamCode;
        this.userId = userId;
    }


    public static ResourceContext ctxFromThreadLocal() {
        return new ResourceContext(
                TrantorContext.getTeamCode(),
                TrantorContext.safeGetCurrentUser().map(User::getId).orElse(null)
        );
    }

    public static ResourceContext newResourceCtx(MetaEditAndQueryContext context) {
        return new ResourceContext(Optional.ofNullable(context.getTeamCode()).orElse(TrantorContext.getTeamCode()), context.getUserId());
    }

    public static ResourceContext newResourceCtx(String teamCode, Long userId) {
        return new ResourceContext(Optional.ofNullable(teamCode).orElse(TrantorContext.getTeamCode()), userId);
    }
}
