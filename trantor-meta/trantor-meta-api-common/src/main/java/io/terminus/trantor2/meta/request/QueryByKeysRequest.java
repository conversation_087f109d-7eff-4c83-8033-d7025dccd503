package io.terminus.trantor2.meta.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.TrantorContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class QueryByKeysRequest<T extends Serializable> extends QueryByKeyRequest<T> {
    private static final long serialVersionUID = -6830121757770882763L;
    @Nonnull
    @Schema(description = "模型标识", required = true)
    private Collection<String> keys;

    @Nullable
    @SuppressWarnings("unchecked")
    public T getTeam() {
        return team != null ? team : (T) TrantorContext.getTeamCode();
    }
}
