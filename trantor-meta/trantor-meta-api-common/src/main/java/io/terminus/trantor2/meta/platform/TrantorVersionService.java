package io.terminus.trantor2.meta.platform;

import io.terminus.trantor2.common.feature.ComparableVersion;

/**
 * Trantor project version
 * SDK: Get trantor version from VersionConfig.
 * Console: Get trantor version from POM.xml.
 *
 * <AUTHOR>
 */
public interface TrantorVersionService {

    String getTrantorVersion();

    String getTrantorVersionWithDate();

    default ComparableVersion getTrantorComparableVersion() {
        return ComparableVersion.of(getTrantorVersion());
    }
}
