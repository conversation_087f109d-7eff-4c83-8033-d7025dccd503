package io.terminus.trantor2.meta.api.dto;

import lombok.Data;
import org.jetbrains.annotations.Nullable;

/**
 * 菜单权限
 *
 * <AUTHOR>
 */
@Data
public class MenuPermissionDTO {
    /**
     * 菜单key
     */
    private String menuKey;

    /**
     * 最终推倒出来的权限key
     * <p>
     * 1）如果菜单本身配了权限，那么resolvedPermissionKeys = menuPermissionKey,
     * see {@link #menuPermissionKey}
     * <p>
     * 2）如果菜单没有配权限，但是有绑定的视图，那么resolvedPermissionKeys = view.permissionKey,
     * see {@link #view}
     */
    private String resolvedPermissionKey;

    /**
     * 菜单自身的权限key
     */
    private String menuPermissionKey;

    /**
     * 视图绑定
     */
    @Nullable
    private BindView view;

    @Data
    public static class BindView {
        private String sceneKey;
        private String viewKey;
        private String permissionKey;
    }
}
