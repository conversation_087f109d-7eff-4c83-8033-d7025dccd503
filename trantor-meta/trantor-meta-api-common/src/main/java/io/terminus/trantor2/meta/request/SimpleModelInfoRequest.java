package io.terminus.trantor2.meta.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.TrantorContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.List;

/**
 * 简化版模型信息请求
 *
 * <AUTHOR>
 * @since 2025/4/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SimpleModelInfoRequest<T extends Serializable> extends QueryByKeyRequest<T> {
    @Nonnull
    @Schema(description = "模型关键字", required = true)
    private List<String> keywords;

    @Schema(description = "是否精确匹配关键字")
    private boolean exactMatch = false;

    @Schema(description = "是否需要返回系统字段")
    private boolean includeSystemFields = true;

    @Schema(description = "下钻关联关系深度", defaultValue = "1")
    private int cascadeDepth = 1;

    @Nullable
    @SuppressWarnings("unchecked")
    public T getTeam() {
        return team != null ? team : (T) TrantorContext.getTeamCode();
    }
}
