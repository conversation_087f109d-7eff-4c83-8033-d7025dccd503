package io.terminus.trantor2.meta.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.annotation.Nullable;
import java.io.Serializable;

/**
 * 元数据同步事件，元数据同步时触发缓存清理
 * 1. 模型引擎依赖此事件清理内部缓存
 */
@Getter
@Setter
@NoArgsConstructor
public class MetaSyncEvent implements Serializable {
    private static final long serialVersionUID = -266828999271748323L;

    /**
     * 模块导入时不为空
     */
    @Nullable
    private String moduleKey;

    public MetaSyncEvent(@Nullable String moduleKey) {
        this.moduleKey = moduleKey;
    }
}
