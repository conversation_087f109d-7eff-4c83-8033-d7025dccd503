package io.terminus.trantor2.meta.api.service;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.page.PageReq;

import java.util.List;
import java.util.Optional;

/**
 * {@link io.terminus.trantor2.meta.editor.aop.CondBaseQueryOpAspect}
 */
public interface CondBaseQueryOp {

    Optional<MetaTreeNodeExt> findOne(Cond cond);

    List<MetaTreeNodeExt> findAll(Cond cond);

    List<ResourceNodeLite> findAllLite(Cond cond);

    Paging<MetaTreeNodeExt> findPageData(Cond cond, PageReq page);

    Long count(Cond cond);

    Boolean exists(Cond cond);
}
