package io.terminus.trantor2.meta.objects.tree;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ResourceAuditField extends TreeNodeIdentity {
    private String updatedBy;
    @EqualsAndHashCode.Exclude
    private Date updatedAt;

    /**
     * 元数据所在目录路径.
     * <p>
     * 替换 parentKey 和 children，未来元数据从树改为扁平结构，path 作为元数据属性之一
     */
    private String path;
    @Deprecated
    private String parentKey;
    @Deprecated
    private List<String> children;
}
