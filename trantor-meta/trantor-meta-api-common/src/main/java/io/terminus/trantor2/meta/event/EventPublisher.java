package io.terminus.trantor2.meta.event;

import org.springframework.context.ApplicationEventPublisherAware;

/**
 * <AUTHOR>
 * 2023/6/19 3:07 PM
 */
public interface EventPublisher extends ApplicationEventPublisherAware {
    void publish(Event event);

    void publishLocal(Event event);

    default void publishRemoteAndLocal(Event event) {
        publishLocal(event);
        publish(event);
    }
}
