package io.terminus.trantor2.meta.api.service;

import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.ExtParams;
import org.jetbrains.annotations.NotNull;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ExtMetaService {
    /**
     * 拆分扩展属性，将扩展属性拆分到 extMeta 中
     *
     * @param node              一开节点
     * @param extParams 扩展信息
     * @param ctx               资源上下文
     * @return true 存在扩展属性，false 不存在扩展属性
     */
    Boolean splitExt(@Nonnull MetaTreeNodeExt node, @Nonnull ExtParams extParams, @Nonnull ResourceContext ctx);

    /**
     * 批量合并扩展元信息至一开节点属性
     *
     * @param nodes             一开节点列表
     * @param ctx               资源上下文
     */
    void mergeNodes(@Nullable List<MetaTreeNodeExt> nodes, @Nonnull ExtParams extParams, @Nonnull ResourceContext ctx);

    void mergeNode(@Nonnull MetaTreeNodeExt node, @Nonnull ExtParams extParams, @Nonnull ResourceContext ctx);

    /**
     * 删除扩展属性
     *
     * @param key 二开节点 key
     * @param ctx 资源上下文
     */
    void delete(@Nonnull String key, @NotNull ResourceContext ctx);

    /**
     * 批量删除扩展属性
     *
     * @param keys 二开节点 key 集合
     * @param ctx  资源上下文
     */
    void deleteAllByKeys(@NotNull Collection<String> keys, @NotNull ResourceContext ctx);

    /**
     * 保存扩展元信息，根据 {@link ExtMeta::getKey} 判断是否存在，存在则更新，不存在则新增
     *
     * @param extMeta 扩展属性
     * @param ctx     资源上下文
     */
    void save(@Nonnull ExtMeta extMeta, @NotNull ResourceContext ctx);

    /**
     * 根据一开节点 key 查询扩展属性
     *
     * @param originKey 一开
     * @param ctx       资源上下文
     * @return 扩展元信息
     */
    Optional<ExtMeta> findByOriginKey(@Nonnull String originKey, @Nonnull ResourceContext ctx);

    /**
     * 根据一开节点 key 查询扩展属性
     *
     * @param originKeys 一开节点 key 列表
     * @param ctx        资源上下文
     * @return 扩展元信息列表
     */
    List<ExtMeta> findAllByOriginKeys(@Nonnull Collection<String> originKeys, @Nonnull ResourceContext ctx);

}
