package io.terminus.trantor2.meta.api.dto;

import io.terminus.trantor2.meta.session.EditSessionContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MetaEditAndQueryContext {
    private Long teamId;
    private Long userId;

    private String teamCode;
    private String moduleKey;

    // Edit Session 相关字段
    private EditSessionContext editSessionContext;  // 编辑会话上下文，为 null 时表示普通修改

    public MetaEditAndQueryContext(Long teamId, Long userId) {
        this.teamId = teamId;
        this.userId = userId;
    }

    public MetaEditAndQueryContext(String teamCode, Long teamId, Long userId) {
        this.teamCode = teamCode;
        this.teamId = teamId;
        this.userId = userId;
    }

    public MetaEditAndQueryContext(Long teamId, Long userId, String teamCode, String moduleKey) {
        this.teamId = teamId;
        this.userId = userId;
        this.teamCode = teamCode;
        this.moduleKey = moduleKey;
    }
}
