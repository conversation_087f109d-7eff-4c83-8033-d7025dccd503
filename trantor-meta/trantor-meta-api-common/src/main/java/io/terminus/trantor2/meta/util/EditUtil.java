package io.terminus.trantor2.meta.util;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.PathFilter;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 方便的构造 {@link EditOpRequest} 的 Util
 *
 * <AUTHOR>
 */
public class EditUtil {

    public static EditOpRequest createNodeOp(MetaTreeNode cur, MoveTarget target) {
        return EditOpRequest.builder()
                .opType(EditOpType.CreateNode)
                .currentKey(cur.getKey())
                .changedTreeNodes(Lists.newArrayList(cur))
                .moveTarget(target)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .build();
    }

    public static EditOpRequest updateNodeOp(MetaTreeNode cur) {
        return EditOpRequest.builder()
                .opType(EditOpType.UpdateNode)
                .currentKey(cur.getKey())
                .changedTreeNodes(Lists.newArrayList(cur))
                .changeOperation(MetaChangeOperation.StandardOperation)
                .build();
    }

    public static EditOpRequest createTreeOp(List<MetaTreeNode> nodes, String currentKey, MoveTarget target) {
        return EditOpRequest.builder()
                .opType(EditOpType.CreateTree)
                .currentKey(currentKey)
                .changedTreeNodes(nodes)
                .moveTarget(target)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .build();
    }

    public static EditOpRequest updateTreeOp(List<MetaTreeNode> nodes, String currentKey) {
        return EditOpRequest.builder()
                .opType(EditOpType.UpdateTree)
                .currentKey(currentKey)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .changedTreeNodes(nodes).build();
    }

    public static EditOpRequest moveNodeOp(String key, MoveTarget target) {
        return EditOpRequest.builder()
                .opType(EditOpType.MoveNode)
                .currentKey(key)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .moveTarget(target).build();
    }

    public static EditOpRequest deleteNodeOp(String key, Boolean recursive) {
        return deleteNodeOp(key, recursive, false);
    }

    public static EditOpRequest deleteNodeOp(String key, Boolean recursive, Boolean verbose) {
        return deleteNodeOp(key, recursive, verbose, false);
    }

    public static EditOpRequest deleteNodeOp(String key, Boolean recursive, Boolean verbose, Boolean force) {
        return EditOpRequest.builder()
                .opType(EditOpType.DeleteNode)
                .currentKey(key)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .recursive(recursive).verbose(verbose).force(force).build();
    }

    public static EditOpRequest snapshotOp() {
        return EditOpRequest.builder()
                .opType(EditOpType.Snapshot)
                .changeOperation(MetaChangeOperation.DoSnapshot)
                .build();
    }

    public static EditOpRequest snapshotOp(String sourceBranchOid) {
        return EditOpRequest.builder()
                .opType(EditOpType.Snapshot)
                .changeOperation(MetaChangeOperation.DoSnapshot)
                .sourceBranchOid(sourceBranchOid).build();
    }

    public static EditOpRequest renameKeyOp(String from, String to) {
        return EditOpRequest.builder()
                .opType(EditOpType.RenameKey)
                .currentKey(from)
                .newKey(to)
                .changeOperation(MetaChangeOperation.StandardOperation)
                .build();
    }

    public static EditOpRequest resetFull(String oid) {
        return resetFull(oid, (String) null);
    }

    public static EditOpRequest resetFull(String oid, String skipModule) {
        return resetFull(oid, Lists.newArrayList(skipModule));
    }

    public static EditOpRequest resetFull(String oid, List<String> skipModules) {
        return EditOpRequest.builder()
                .opType(EditOpType.ResetFull)
                .resetRootOid(oid)
                .skipModules(skipModules)
                .changeOperation(MetaChangeOperation.SnapshotImport)
                .build();
    }

    public static EditOpRequest resetModule(String oid, List<String> resetModules) {
        return EditOpRequest.builder()
                .opType(EditOpType.ResetModule)
                .resetRootOid(oid)
                .resetModules(resetModules)
                .changeOperation(MetaChangeOperation.SnapshotImport)
                .build();
    }

    public static EditOpRequest resetPath(String oid, PathFilter resetPath) {
        return EditOpRequest.builder()
                .opType(EditOpType.ResetPath)
                .resetRootOid(oid)
                .resetPath(resetPath)
                .changeOperation(MetaChangeOperation.SnapshotImport)
                .build();
    }

    public static EditOpRequest rebuildIndexOp() {
        return EditOpRequest.builder()
                .opType(EditOpType.RebuildIndex)
                .build();
    }

    public static EditOpRequest resetKeysOp(String snapshotOid, List<String> resetKeys) {
        return EditOpRequest.builder()
                .opType(EditOpType.ResetKeys)
                .resetRootOid(snapshotOid)
                .resetKeys(resetKeys)
                .changeOperation(MetaChangeOperation.SnapshotImport)
                .build();
    }

    public static MetaEditAndQueryContext ctxFromThreadLocal() {
        return new MetaEditAndQueryContext(TrantorContext.getTeamId(), TrantorContext.safeGetCurrentUser().map(User::getId).orElse(null), TrantorContext.getTeamCode(), TrantorContext.getModuleKey());
    }

    public static MetaEditAndQueryContext newFrom(ResourceContext ctx) {
        MetaEditAndQueryContext context = new MetaEditAndQueryContext();
        context.setTeamCode(ctx.getTeamCode());
        if (ObjectUtils.isEmpty(ctx.getTeamCode())) {
            context.setTeamId(TrantorContext.getTeamId());
        }
        context.setUserId(ctx.getUserId());
        return context;
    }

    public static MetaEditAndQueryContext newCtx(Long teamId, Long userId) {
        return new MetaEditAndQueryContext(teamId, userId);
    }

    public static MetaEditAndQueryContext newCtx(Long teamId, String moduleKey, Long userId) {
        MetaEditAndQueryContext ctx = new MetaEditAndQueryContext(teamId, userId);
        ctx.setModuleKey(moduleKey);
        return ctx;
    }

    public static void validateContext(MetaEditAndQueryContext ctx) {
        if (ctx == null) {
            throw new RuntimeException("bad context");
        }
        if (ctx.getTeamId() == null && ctx.getTeamCode() == null) {
            throw new RuntimeException("bad team");
        }
        if (ctx.getUserId() == null) {
            throw new RuntimeException("not login");
        }
    }
}
