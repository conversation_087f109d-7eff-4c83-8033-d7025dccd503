package io.terminus.trantor2.meta.event;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.util.KeyUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

import static io.terminus.trantor2.meta.resource.ext.ExtNodeMeta.EXT_TYPE;

/**
 * <AUTHOR>
 * 2023/6/8 5:12 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaChangeEvent implements MergeableEvent<MetaChangeEvent>, RetryableEvent, Serializable {
    private static final long serialVersionUID = 6321788727713312833L;
    public final static String TAG = "META_CHANGE_EVENT";
    private final Long userId = TrantorContext.getCurrentUserId();

    private EditOpType editOpType;
    private String teamCode;
    private List<MetaId> oldMetas;

    @Override
    public String getTag() {
        return TAG;
    }

    @Override
    public List<MetaChangeEvent> merge(List<MetaChangeEvent> events) {
        List<MetaChangeEvent> mergedEvents = new ArrayList<>(events);
        boolean merged = false;

        for (MetaChangeEvent event : events) {
            if (Objects.equals(this.teamCode, event.getTeamCode())) {
                Set<MetaId> metaIdSet = new HashSet<>(this.oldMetas);
                Optional.ofNullable(event.getOldMetas()).ifPresent(metaIdSet::addAll);
                event.setOldMetas(new ArrayList<>(metaIdSet));
                merged = true;
                break;
            }
        }
        if (!merged) {
            mergedEvents.add(this);
        }
        return mergedEvents;
    }

    @Data
    @NoArgsConstructor
    public static final class MetaId implements Serializable {
        private static final long serialVersionUID = -9023013703021802278L;
        private String key;
        private String type;
        @Deprecated
        private Long appId;

        public MetaId(String key, String type, Long appId) {
            setKey(key);
            setType(type);
            this.appId = appId;
        }

        public void setKey(String key) {
            this.key = KeyUtil.originalKey(key);
        }

        public void setType(String type) {
            this.type = oriType(type);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MetaId metaId = (MetaId) o;
            return Objects.equals(key, metaId.key) &&
                    Objects.equals(type, metaId.type);
        }

        @Override
        public int hashCode() {
            return Objects.hash(key, type);
        }

        private String oriType(String type) {
            if (StringUtils.isNotBlank(type) && type.startsWith(EXT_TYPE)) {
                return type.substring(EXT_TYPE.length());
            }
            return type;
        }
    }
}
