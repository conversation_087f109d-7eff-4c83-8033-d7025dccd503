package io.terminus.trantor2.meta.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.customization.Customization;
import io.terminus.trantor2.meta.objects.tree.ResourceNodeIdentity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@RequiredArgsConstructor
public final class ResourceNodeLiteImpl implements ResourceNodeLite {
    private final String type;
    private final String key;
    private final String name;
    private final String description;
    private final MetaNodeAccessLevel access;
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ResourceNodeIdentity> children;
    @Setter
    private Customization customizedField;
}
