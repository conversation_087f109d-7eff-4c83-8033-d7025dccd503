package io.terminus.trantor2.meta.event;

import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ModuleConfigChangedEvent implements RetryableEvent, Serializable {
    private static final long serialVersionUID = 1126869274973759913L;
    public final static String TAG = "MODULE_CONFIG_CHANGED_EVENT";

    private List<ModuleInfo> appendAll;
    private List<ModuleInfo> removeAll;
    private String applicationName;

    @Override
    public String getTag() {
        return TAG;
    }
}
