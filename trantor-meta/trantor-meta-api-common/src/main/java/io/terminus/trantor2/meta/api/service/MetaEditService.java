package io.terminus.trantor2.meta.api.service;

import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;

import java.util.Map;

/**
 * Meta Edit Service.
 * <p>
 * 隐式传递 teamId & appId & userId: {@link io.terminus.trantor2.common.TrantorContext}
 *
 * <AUTHOR>
 */
public interface MetaEditService {

    /**
     * 提交一次编辑操作
     * <p>
     * 每次操作都会通过锁分支的方式进行阻塞，也就是同一时间只能处理一次编辑，防止产生脏数据
     *
     * @param ctx 操作上下文
     * @param req 操作请求, 操作类型参看 {@link io.terminus.trantor2.meta.api.dto.EditOpType}
     *            <p>方便的构造 req 的 Util: {@link io.terminus.trantor2.meta.util.EditUtil}
     * @return 所有被影响的 Node, Map: key -> Node
     */
    Map<String, MetaTreeNodeExt> submitOp(MetaEditAndQueryContext ctx, EditOpRequest req);
}
