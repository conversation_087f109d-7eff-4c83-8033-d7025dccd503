package io.terminus.trantor2.meta.management.service;

import java.util.Map;

/**
 * Meta change description service interface for generating change descriptions based on meta object changes
 */
public interface MetaChangeDescriptionService {

    /**
     * Generate a change description message based on the input data
     *
     * @param input The input data for generating the change description message
     * @return The generated change description message
     * @throws Exception If failed to generate the message
     */
    String generateMessage(Map<String, Object> input) throws Exception;
}
