package io.terminus.trantor2.meta.objects.vcs;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class SnapshotProps {

    private String rootOid;

    private String lastSnapshot;

    private Long timestamp;
}
