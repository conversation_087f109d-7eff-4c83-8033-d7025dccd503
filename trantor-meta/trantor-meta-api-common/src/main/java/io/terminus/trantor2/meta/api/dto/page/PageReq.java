package io.terminus.trantor2.meta.api.dto.page;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class PageReq {

    private final int page;
    private final int size;
    private final Order order;

    private PageReq(int page, int size, Order order) {
        this.page = page;
        this.size = size;
        this.order = order;
    }

    /**
     * Construct a PageReq with default order.
     *
     * @param page page number, start from 0
     * @param size page size
     * @return PageReq
     */
    public static PageReq of(int page, int size) {
        return new PageReq(page, size, null);
    }

    /**
     * Construct a PageReq with order.
     *
     * @param page  page number, start from 0
     * @param size  page size
     * @param order order
     * @return PageReq
     */
    public static PageReq of(int page, int size, Order order) {
        return new PageReq(page, size, order);
    }
}
