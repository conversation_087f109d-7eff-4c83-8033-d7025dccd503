package io.terminus.trantor2.meta.objects;

import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by hedy on 2023/4/17.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ResourceVO extends MetaTreeNodeExt {
    private List<ResourceNodeLite> path;
}
