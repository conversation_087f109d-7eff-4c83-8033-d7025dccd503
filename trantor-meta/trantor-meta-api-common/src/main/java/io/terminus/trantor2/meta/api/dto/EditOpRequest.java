package io.terminus.trantor2.meta.api.dto;

import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * Request structure for edit operation
 * <p>
 * Examples:
 * <p>
 * 1. CreateNode
 * <pre>
 * {@code
 *   {
 *     "opType": "CreateNode",
 *     "currentKey": "c",
 *     "changedTreeNodes": [
 *       {
 *         "type": "Service",
 *         "key": "c",
 *         "name": "svc1",
 *         "props": {},
 *         "parentKey": "p",
 *       }
 *     ],
 *     "moveTarget": {
 *       "targetKey": "p",
 *       "targetType": "ChildLast",
 *     }
 *   }
 * }
 * </pre>
 * <p>
 * 2. UpdateNode
 * <pre>
 * {@code
 *   {
 *     "opType": "UpdateNode",
 *     "currentKey": "c",
 *     "changedTreeNodes": [
 *       {
 *         "type":"Service",
 *         "key": "c",
 *         "name": "svc1-modified",
 *         "props": {},
 *         "parentKey": "p",
 *       }
 *     ]
 *   }
 * }
 * </pre>
 * 3. CreateTree
 * <pre>
 * p --> b --> b-1
 *         `-> b-2
 * </pre>
 * <pre>
 * {@code
 *   {
 *     "opType": "CreateTree",
 *     "currentKey": "b",
 *     "changedTreeNodes": [
 *       {
 *         "type":"Service",
 *         "key": "b",
 *         "name": "svc b",
 *         "props": null,
 *         "parentKey": "p"
 *       },
 *       {
 *         "type": "ServiceNode",
 *         "key": "b-1",
 *         "name": "b node 1",
 *         "props": {},
 *         "parentKey": "b"
 *       },
 *       {
 *         "type": "ServiceNode",
 *         "key": "b-2",
 *         "name": "b node 2",
 *         "props": {},
 *         "parentKey": "b"
 *       }
 *     ],
 *     "moveTarget": {
 *       "targetKey": "p",
 *       "targetType": "ChildLast",
 *     }
 *   }
 * }
 * </pre>
 * 4. UpdateTree
 * <pre>
 * p --> c --> sn1 --> sn1-1
 *         `-> sn2
 * </pre>
 * <pre>
 * {@code
 *   {
 *     "opType": "UpdateTree",
 *     "currentKey": "c",
 *     "changedTreeNodes": [
 *       {
 *         "type":"Service",
 *         "key": "c",
 *         "name": "svc1",
 *         "props": {},
 *         "parentKey": "p"
 *       },
 *       {
 *         "type":"ServiceNode",
 *         "key": "sn1",
 *         "name": "svc node 1",
 *         "props": {},
 *         "parentKey": "c"
 *       },
 *       {
 *         "type":"ServiceNode",
 *         "key": "sn1-1",
 *         "name": "svc node 1-1",
 *         "props": {},
 *         "parentKey": "sn1"
 *       },
 *       {
 *         "type":"ServiceNode",
 *         "key": "sn2",
 *         "name": "svc node 2",
 *         "props": {},
 *         "parentKey": "c"
 *       }
 *     ]
 *   }
 * }
 * </pre>
 * 5. MoveNode
 * <pre>
 * {@code
 *   {
 *     "opType": "MoveNode",
 *     "currentKey": "c",
 *     "moveTarget": {
 *       "targetKey": "p2",
 *       "targetType": "ChildLast"
 *     }
 *   }
 * }
 * </pre>
 * 6. DeleteNode
 * <pre>
 * {@code
 *   {
 *     "opType": "DeleteNode",
 *     "parentKey": "p",
 *     "currentKey": "c"
 *   }
 * }
 * </pre>
 * 7. Snapshot
 * <pre>
 * {@code
 *   {
 *     "opType": "Snapshot",
 *     "message": "first commit",
 *     "date": "2021-01-01 00:00:00"
 *   }
 * }
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EditOpRequest {
    /**
     * {@link EditOpType}
     */
    private EditOpType opType;
    private String currentKey;
    /**
     * all nodes for edit
     */
    private List<MetaTreeNode> changedTreeNodes;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#DeleteNode}
     * </pre>
     */
    private Boolean recursive;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#DeleteNode}
     * </pre>
     */
    private Boolean force;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#DeleteNode}
     * </pre>
     */
    private Boolean verbose;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#RenameKey}
     * </pre>
     */
    private String newKey;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#CreateNode}
     * {@link EditOpType#CreateTree}
     * {@link EditOpType#MoveNode}
     * </pre>
     */
    private MoveTarget moveTarget;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#Snapshot}
     * </pre>
     */
    private String message;
    /**
     * use for:
     * <pre>
     * {@link EditOpType#Snapshot}
     * </pre>
     */
    private Date date;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#ResetFull}
     * {@link EditOpType#ResetModule}
     * {@link EditOpType#ResetPath}
     * </pre>
     */
    private String resetRootOid;
    /**
     * use for:
     * <pre>
     * {@link EditOpType#ResetModule}
     * </pre>
     */
    private List<String> resetModules;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#ResetPath}
     * </pre>
     */
    private PathFilter resetPath;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#ResetFull}
     * </pre>
     */
    private List<String> skipModules;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#Snapshot}
     * </pre>
     */
    private String sourceBranchOid;

    /**
     * use for:
     * <pre>
     * {@link EditOpType#ResetKeys}
     * </pre>
     */
    private List<String> resetKeys;

    /**
     * meta change operation
     */
    private MetaChangeOperation changeOperation = MetaChangeOperation.Unknown;
}
