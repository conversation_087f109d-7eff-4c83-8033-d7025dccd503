package io.terminus.trantor2.meta.event;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/10 3:34 PM
 **/
@Data
public class DatasourceUpdateEvent implements RetryableEvent, Serializable {
    public final static String TAG = "DATASOURCE_UPDATE_EVENT";

    private Long teamId;
    private String teamCode;
    private String datasourceName;

    @Override
    public String getTag() {
        return TAG;
    }
}
