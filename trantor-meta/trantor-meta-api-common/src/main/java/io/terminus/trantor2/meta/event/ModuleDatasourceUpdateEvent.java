package io.terminus.trantor2.meta.event;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/10 3:16 PM
 **/
@Data
public class ModuleDatasourceUpdateEvent implements RetryableEvent, Serializable {
    public final static String TAG = "MODULE_DATASOURCE_UPDATE_EVENT";

    private Long teamId;
    private String teamCode;
    private Long moduleId;
    private String moduleKey;
    private String datasourceName;

    /**
     * 模块关联的向量数据源名称
     */
    private String vectorDatasource;

    @Override
    public String getTag() {
        return TAG;
    }
}
