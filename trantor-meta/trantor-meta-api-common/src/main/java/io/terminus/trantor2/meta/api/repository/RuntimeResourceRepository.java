package io.terminus.trantor2.meta.api.repository;

import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * 2023/7/18 2:22 PM
 */
public interface RuntimeResourceRepository<R extends ResourceBaseMeta<? extends ResourceProps>> {
    R findOneByKey(String key);

    List<R> findAll(Collection<String> keys);
}
