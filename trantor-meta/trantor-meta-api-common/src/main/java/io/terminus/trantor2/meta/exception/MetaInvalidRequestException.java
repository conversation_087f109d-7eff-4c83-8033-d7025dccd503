package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class MetaInvalidRequestException extends TrantorBizException {
    private static final long serialVersionUID = -872067544384028808L;

    public MetaInvalidRequestException(String message) {
        super(ErrorType.META_INVALID_REQUEST, new Object[]{message});
    }
}
