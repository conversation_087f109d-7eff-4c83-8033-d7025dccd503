package io.terminus.trantor2.meta.event;

import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/28 2:39 PM
 **/
@Data
public class ModuleDeleteEvent implements RetryableEvent, Serializable {
    public final static String TAG = "MODULE_DELETE_EVENT";

    private ModuleInfo module;

    @Override
    public String getTag() {
        return TAG;
    }
}
