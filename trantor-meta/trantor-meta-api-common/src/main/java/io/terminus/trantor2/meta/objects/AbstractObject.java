package io.terminus.trantor2.meta.objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import lombok.Data;

@Data
public abstract class AbstractObject<P> {
    /**
     * version of the schema (or struct) of the Object
     */
    private Long schemaVersion;

    /**
     * version of the platform (which related to the `props` struct)
     */
    @JsonProperty("platformVersion")
    private PlatformVersion platformVersionLegacy;

    private String type;

    private P props;

}
