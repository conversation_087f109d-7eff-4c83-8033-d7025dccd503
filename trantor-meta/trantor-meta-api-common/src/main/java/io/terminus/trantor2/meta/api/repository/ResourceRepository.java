package io.terminus.trantor2.meta.api.repository;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.*;
import java.util.function.Function;

/**
 * {@link io.terminus.trantor2.meta.editor.aop.ResourceRepositoryAspect}
 * <p> 约定：
 *     创建以 create 为前缀
 *     更新以 update 为前缀
 *     删除以 delete 为前缀
 *     查找单个以 findOne 为前缀
 *     查找多个以 findAll 为前缀
 * </p>
 * <AUTHOR>
 * 2023/7/6 5:41 PM
 */
public interface ResourceRepository<R extends ResourceBaseMeta<? extends ResourceProps>> {

    default Boolean existKey(String key, @Nonnull ResourceContext ctx) {
        return Optional.ofNullable(existKeys(Collections.singleton(key), ctx).get(key)).orElse(false);
    }

    Map<String, Boolean> existKeys(Collection<String> keys, @Nonnull ResourceContext ctx);

    Long create(@Nonnull R resource, @Nonnull ResourceContext ctx);

    Long create(@Nonnull R resource, @Nonnull ResourceContext ctx, @Nonnull MoveTarget moveTarget);

    void update(R resource, ResourceContext ctx);

    void deleteByKey(String key, ResourceContext ctx);

    void deleteByKey(String key, Boolean recursive, Boolean verbose, Boolean force, ResourceContext ctx);

    Long count(Cond cond, ResourceContext ctx);

    default Optional<R> findOneByKey(String key, ResourceContext ctx) {
        return findOne(Field.key().equal(key), ctx);
    }

    @Deprecated
    Optional<R> findOneById(Long id);

    Optional<R> findOne(Cond cond, ResourceContext ctx);

    List<R> findAll(Cond cond, ResourceContext ctx);

    default List<R> findAllByKeys(Collection<String> keys, ResourceContext ctx) {
        return findAll(Field.key().in(keys), ctx);
    }
    @Deprecated
    List<R> findAllByIds(Collection<Long> ids);

    default List<R> findAllByParent(String parentKey, ResourceContext ctx) {
        return findAll(Field.parentKey().equal(parentKey), ctx);
    }

    Paging<R> findAll(Cond cond, PageReq pageReq, ResourceContext ctx);

    Paging<R> findAll(Cond cond, PageReq pageReq, ResourceContext ctx, Function<MetaTreeNodeExt, R> customConvert);
}
