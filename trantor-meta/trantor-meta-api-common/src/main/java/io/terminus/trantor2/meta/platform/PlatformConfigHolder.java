package io.terminus.trantor2.meta.platform;

import java.util.Map;

/**
 * <AUTHOR>
 */
public final class PlatformConfigHolder {

    private static final String CONFIG_PREFIX = "platform.";
    public static final String CONFIG_PLATFORM_VERSION = CONFIG_PREFIX + "version";
    public static final String CONFIG_PLATFORM_DEBUG = CONFIG_PREFIX + "debug";
    public static final String CONFIG_PLATFORM_TYPE = CONFIG_PREFIX + "type";
    public static final String CONFIG_PLATFORM_KEY = CONFIG_PREFIX + "key";

    private PlatformConfigHolder() {
    }

    private static Map<String, Object> info;

    public static void setInfo(Map<String, Object> info) {
        PlatformConfigHolder.info = info;
    }

    public static Map<String, Object> getInfoAll() {
        if (info == null) {
            throw new RuntimeException("PlatformConfigHolder not init");
        }
        return info;
    }

    public static <T> T getInfo(String key) {
        if (info == null) {
            throw new RuntimeException("PlatformConfigHolder not init");
        }
        return (T) info.get(key);
    }

    public static String getPlatformKey() {
        return getInfo(CONFIG_PLATFORM_KEY);
    }

    public static PlatformVersion getPlatformVersion() {
        return getInfo(CONFIG_PLATFORM_VERSION);
    }
}
