package io.terminus.trantor2.meta.api.cache;

import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.MapUtils;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/7/21 2:27 PM
 **/
public abstract class AbstractCache<K, V> implements Cache<K, V> {

    private volatile Map<K, V> currentCache;
    private final Map<K, V> cacheA = new ConcurrentHashMap<>();
    private final Map<K, V> cacheB = new ConcurrentHashMap<>();

    @Override
    public V get(K key) {
        return currentCache.computeIfAbsent(key, this::load);
    }

    @Override
    public V get(K key, Function<K, V> function) {
        return currentCache.computeIfAbsent(key, function);
    }

    @Override
    public List<V> get(List<K> keys) {
        return keys.stream().map(this::get).collect(Collectors.toList());
    }

    public synchronized final void init() {
        Map<K, V> map = load();
        if (MapUtils.isEmpty(map)) {
            return;
        }
        Map<K, V> freeCache = currentCache == cacheA ? cacheB : cacheA;
        freeCache.putAll(map);
        currentCache = freeCache;

        freeCache = currentCache == cacheA ? cacheB : cacheA;
        freeCache.clear();
    }

    /**
     * 缓存预热实现该方法
     * @return 初始化的缓存数据
     */
    public Map<K, V> load() {
        return Maps.newHashMap();
    }

    /**
     * 延迟加载实现改方法
     * @param key key
     * @return value
     */
    public V load(K key) {
        return null;
    }
}
