package io.terminus.trantor2.meta.task;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TaskOutputResult {
    private ResultSummary summary = new ResultSummary();
    private List<ResultDetail> details = new ArrayList<>();
    private List<ResultFile> files = new ArrayList<>();
    private Object data;

    @Data
    public static class ResultSummary {
        private Integer total = 0;
        private Integer success = 0;
        private Integer failed = 0;
    }

    @Data
    public static class ResultDetail {
        private String title;
        private Boolean success;
        private List<String> messages;

        public static ResultDetail success(String title, String... message) {
            ResultDetail detail = new ResultDetail();
            detail.setTitle(title);
            detail.setSuccess(true);
            if (message != null && message.length > 0) {
                detail.setMessages(Lists.newArrayList(message));
            }
            return detail;
        }

        public static ResultDetail failed(String title, List<String> messages) {
            ResultDetail detail = new ResultDetail();
            detail.setTitle(title);
            detail.setSuccess(false);
            detail.setMessages(messages);
            return detail;
        }

        public static ResultDetail failed(String title, String... message) {
            List<String> messages = null;
            if (message != null && message.length > 0) {
                messages = Lists.newArrayList(message);
            }
            return failed(title, messages);
        }
    }

    @Data
    public static class ResultFile {
        private String name;
        private String url;
    }
}
