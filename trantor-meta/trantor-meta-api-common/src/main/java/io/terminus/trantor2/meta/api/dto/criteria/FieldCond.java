package io.terminus.trantor2.meta.api.dto.criteria;

import java.util.Arrays;
import java.util.List;

class FieldCond<T> extends Cond {

    private final Field field;

    private List<Object> values;

    protected FieldCond(Field<T> field, Func func, List<Object> values) {
        this.field = field;
        this.func = func;
        this.values = values;
    }

    protected FieldCond(Field<T> field, Func func, Object... values) {
        this.field = field;
        this.func = func;
        if (values != null && values.length > 0) {
            this.values = Arrays.asList(values);
        }
    }

    @Override
    public Field getField() {
        return field;
    }

    @Override
    public List<Object> getValues() {
        return values;
    }

    @Override
    public List<Cond> getSubConds() {
        return null;
    }
}
