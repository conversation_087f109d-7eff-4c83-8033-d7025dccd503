package io.terminus.trantor2.meta.session;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Edit session context
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EditSessionContext {
    /**
     * MCP 专用上下文，用于代理到正确的 console
     */
    private String baseUrl;

    /**
     * MCP 专用上下文，用于定位项目
     */
    private Long teamId;

    /**
     * 编辑会话 ID
     */
    private String sessionId;

    /**
     * 检查点 ID
     */
    private String checkpointId;

    /**
     * 前一个检查点 ID
     */
    private String preCheckpointId;
}
