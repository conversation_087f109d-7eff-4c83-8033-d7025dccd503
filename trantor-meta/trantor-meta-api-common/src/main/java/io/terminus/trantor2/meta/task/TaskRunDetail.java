package io.terminus.trantor2.meta.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.Date;
import java.util.List;

@Builder
@Jacksonized
@Getter
@AllArgsConstructor
public final class TaskRunDetail {
    private final String taskCode;
    @Deprecated
    private final String taskName;
    private final Long teamId;
    private final String teamCode;
    private final Long taskRunId;
    private final Boolean dryRun;
    private final String comment;
    private final String runDisplayName;
    private final String execType;
    private final Long execUserId;
    private final String status;
    @Deprecated
    private final Progress progress;
    private final String options;
    private final TaskOutputResult result;
    private final List<String> logs;
    @Deprecated
    private final List<String> outputs;
    private final String panicMessage;
    @Deprecated
    private final List<TaskRunDetail> subTasks;
    @Deprecated
    private final Long createdBy;
    private final Date startAt;
    private final Date endAt;
    private final Date createdAt;
    @Deprecated
    private final Date finishedAt;

    @Builder
    @Jacksonized
    @Getter
    @AllArgsConstructor
    @ToString
    @EqualsAndHashCode
    public static final class Progress {
        private final int total;
        private final int success;
        private final int failed;
    }
}
