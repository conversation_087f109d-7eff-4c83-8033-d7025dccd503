package io.terminus.trantor2.meta.api.dto;

/**
 * <AUTHOR>
 **/
public enum MetaChangeOperation {
    /**
     * 标准操作 create、update、delete
     */
    StandardOperation,
    /**
     * 快照导入
     */
    SnapshotImport,
    /**
     * 模块导入
     */
    ModuleImport,
    /**
     * 元数据修复
     */
    FixMeta,
    /**
     * 做快照
     */
    DoSnapshot,
    /**
     * 回退操作
     */
    Rollback,
    /**
     * 其他
     */
    Unknown,
    ;
}
