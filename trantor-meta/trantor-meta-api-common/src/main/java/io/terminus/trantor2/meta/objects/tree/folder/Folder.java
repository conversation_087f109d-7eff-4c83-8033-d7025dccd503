package io.terminus.trantor2.meta.objects.tree.folder;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.common.dto.AppRequest;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.module.meta.FolderMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by hedy on 2023/4/11.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Folder extends AppRequest {

    private static final long serialVersionUID = -1708276824288514854L;

    public static final String TYPE = "Folder";

    public static final String ROOT_TYPE = "FolderRoot";

    /**
     * 父文件夹
     */
    private String parentKey;

    /**
     * 文件夹标识
     */
    private String key;

    /**
     * 文件夹名称
     */
    private String label;

    /**
     * 资源类型
     * only work for argument v2=true
     */
    private String type;

    /**
     * 资源子类型
     * only work for argument v2=true
     */
    private String subType;

    /**
     * 是否为根节点
     */
    private Boolean root;

    /**
     * 子文件夹
     */
    private List<Folder> children;

    /**
     * 文件夹位置
     */
    private MoveTarget moveTarget;

    /*
     * 是否扩展
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Boolean extended = false;

    public FolderMeta covert() {
        FolderMeta meta = new FolderMeta();
        meta.setKey(getKey());
        meta.setName(getLabel());
        meta.setParentKey(getParentKey());
        return meta;
    }
}
