package io.terminus.trantor2.meta.api.dto.criteria;

import lombok.Data;

import java.util.List;

@Data
public abstract class Cond {

    private Boolean enhanced;
    protected Func func;

    public static Cond not(Cond cond) {
        return new ChainCond(Func.NOT, cond);
    }

    public static Cond and(Cond... conds) {
        return new ChainCond(Func.AND, conds);
    }

    public static Cond or(Cond... conds) {
        return new ChainCond(Func.OR, conds);
    }

    public static Cond all() {
        return new ChainCond(Func.AND);
    }

    public static Cond moduleOf(String key) {
        // module itself also has moduleKey filed, which is the key of the module
        return Field.moduleKey().equal(key);
    }

    public abstract Field getField();

    public abstract List<Object> getValues();

    public abstract List<Cond> getSubConds();

    public Cond and(Cond cond) {
        if (cond == null) {
            return this;
        }
        return Cond.and(this, cond);
    }

    public Cond or(Cond cond) {
        if (cond == null) {
            return this;
        }
        return Cond.or(this, cond);
    }
}
