package io.terminus.trantor2.meta.service;

import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface MetaRuntimeService {
    /**
     * 获取元数据
     *
     * @param key 元数据key
     * @return 元数据
     */
    MetaTreeNodeExt get(String key);

    /**
     * 获取元数据
     *
     * @param keys 元数据key
     * @return 元数据
     */
    List<MetaTreeNodeExt> get(Collection<String> keys);


    /**
     * 查找引用
     *
     * @param key       被引用的资源的标识
     * @param loadProps 是否加载资源 props
     * @param types     引用资源类型
     * @return 引用资源
     */
    List<MetaTreeNodeExt> findUsages(MetaEditAndQueryContext ctx, String key, Boolean loadProps, List<String> types);
}
