<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-lang</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq.trial-java-8</groupId>
            <artifactId>jooq</artifactId>
        </dependency>
        <dependency>
            <!-- TODO: remove this -->
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-condition-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-model-api-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-iam-adapter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-impl-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>workflow-common</artifactId>
                    <groupId>io.terminus.trantor.workflow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-model-api-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-model-api-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>test-container</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-api</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-model-api-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-iam-adapter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>workflow-common</artifactId>
                    <groupId>io.terminus.trantor.workflow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.i18n</groupId>
            <artifactId>terminus-i18n-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.apidocs</groupId>
                    <artifactId>swagger2-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-impl-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.json-unit</groupId>
            <artifactId>json-unit</artifactId>
        </dependency>
        <!-- to execute parser that is written in javascript -->
        <dependency>
            <groupId>org.graalvm.js</groupId>
            <artifactId>js</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-blackbird</artifactId>
        </dependency>
    </dependencies>
</project>
