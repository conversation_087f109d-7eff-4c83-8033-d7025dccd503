/**
 * @typedef {Object} ITreeNode
 * @property {string} key - The unique key of the node.
 * @property {string} type - The type of the node.
 * @property {string} name - The name of the node.
 * @property {Object} props - The props of the node.
 * @property {string} displayName - The display name of the node.
 * @property {ITreeNode[]} children - The child nodes.
 */

/**
 * @typedef {Object} IField
 * @property {string} fieldKey - The unique key of the field.
 * @property {string} fieldLabel - The label of the field.
 * @property {ITargetService[]} targetServices - The target service of the field.
 */

/**
 * @typedef {Object} ITargetService
 * @property {string} serviceKey - The unique key of the service.
 * @property {string} parentPath - The parent path of the service field.
 * @property {string} type - The type of the service value of 'Input' or 'Output'.
 */

/**
 * @typedef {Object} IResult
 * @property {string} containerKey - The unique key of the node.
 * @property {string} containerLabel - The label of the node.
 * @property {string} modelKey - The model alias of the node.
 * @property {IField[]} fields - The fields of the node.
 * @property {ITargetService[]} targetServices - The target service of the node.
 */

/**
 * @typedef {Object} IFlow
 * @property {string} type - The type of the flow value of 'InvokeService' or 'InvokeSystemService'.
 * @property {string} serviceKey - The name of the flow.
 * @property {string} modelAlias - The model alias of the flow.
 * @property {Object[]} params - The params of the flow.
 * @property {Object[]} outputParams - The output params of the flow.
 */

/**
 * Create container fields
 * @param {ITreeNode} root - The root node of the tree structure.
 * @returns {IResult[]} The container fields.
 */
function parse(root) {
  /**
   * @type {IResult[]}
   */
  const result = []
  const treeMap = createTreeMap(root)

  /**
   * @type {ITreeNode[]}
   */
  const testNodes = Object.values(treeMap).filter((node) => {
    return Object.keys(nameMap).includes(node.name) && testHasFlow(node)
  })

  // 构造出只有target的Button
  const testButtons = Object.values(treeMap).filter((node) => {
    return node.name === 'Button'
  })

  testNodes.forEach((testNode) => {
    const containerKey = testNode.key
    const containerLabel =
      testNode.props.label ??
      testNode.displayName ??
      nameMap[testNode.name] ??
      testNode.name
    /**
     * @type {string}
     */
    const modelKey = testNode.props.modelAlias
    // 提前准备好flows
    /**
     *
     * @type {IFlow[]}
     */
    const readFlows =
      testNode.props.flow.type === 'Condition'
        ? testNode.props.flow.children?.map((child) => child)
        : testNode.props.flow.type === 'Conditions'
          ? testNode.props.flow.items?.map((item) => item.flow)
          : [testNode.props.flow]

    const readTargetServices = readFlows.map((flow) => {
      const result = {
        type: 'Output',
        serviceKey: flow.serviceKey,
      }

      if (
        !flow.outputParams ||
        !flow.outputParams.type ||
        flow.outputParams.type === 'service'
      ) {
        const suffix = testNode.name === 'Table' ? 'data.data' : 'data'
        result.parentPath = '' + suffix
      } else if (
        flow.outputParams.type === 'expression' &&
        flow.outputParams.expression
      ) {
        const suffix = testNode.name === 'Table' ? 'data' : ''
        result.parentPath = [flow.outputParams.expression, suffix]
          .filter(Boolean)
          .join('.')
      } else if (flow.outputParams.type === 'script') {
        result.parentPath = ''
      } else {
        const suffix = testNode.name === 'Table' ? 'data.data' : 'data'
        result.parentPath = '' + suffix
      }
      return result
    })

    const relatedButtons = testButtons.filter((button) => {
      const propsString = JSON.stringify(button.props)
      return (
        propsString.includes(`"target":"${containerKey}"`) &&
        button.props?.actionConfig
      )
    })

    const writeTargetServices = relatedButtons.map((button) => {
      const actionConfig = button.props.actionConfig
      if (actionConfig.bindServiceConfig) {
        return createTargetServiceForBindXXXConfig(
          actionConfig.bindServiceConfig,
          containerKey,
        )
      }
      if (actionConfig.bindFlowConfig) {
        return createTargetServiceForBindXXXConfig(
          actionConfig.bindFlowConfig,
          containerKey,
        )
      }
    })

    /**
     * @type {IField[]}
     */
    const fields = []
    /**
     *
     * @param {ITreeNode} node
     * @param {string} prefix
     */
    function findFields(node, prefix) {
      if (testHasFlow(node)) {
        return
      }
      if (
        node.name === 'FormField' ||
        node.name === 'DetailField' ||
        node.name === 'Field'
      ) {
        fields.push({
          fieldKey: `${prefix}${node.props.name}`,
          fieldLabel: node.props.label,
          targetServices: [...readTargetServices, ...writeTargetServices],
        })
      } else if (
        node.name === 'Table' ||
        node.name === 'SubTable' ||
        node.name === 'TableForm'
      ) {
        if (node.props.fieldName) {
          prefix = `${prefix}${node.props.fieldName}.`
        }
      }

      if (node.children) {
        node.children.forEach((child) => {
          findFields(child, prefix)
        })
      }
    }

    testNode.children?.forEach((child) => {
      findFields(child, '')
    })

    result.push({
      containerKey,
      containerLabel,
      modelKey,
      fields,
    })
  })
  return result
}

/**
 *
 * @param {ITreeNode} node
 * @returns
 */
function testHasFlow(node) {
  return node.props?.flow && !testRelationFlow(node)
}

/**
 *
 * @param {ITreeNode} node
 * @returns
 */
function testRelationFlow(node) {
  if (node.props?.flow?.name) {
    // 支持最新的flow
    const flow = node.props.flow
    return flow.type === 'RelationData'
  }
  return false
}

/**
 * @param {ITreeNode} root
 * @returns {Object<string, ITreeNode>} The tree map.
 */
function createTreeMap(root) {
  /**
   * @type {Object<string, ITreeNode>}
   */
  const treeMap = {}
  const stack = [root]
  while (stack.length) {
    const node = stack.pop()
    if (node) {
      treeMap[node.key] = node
      if (node.children) {
        stack.push(...node.children)
      }
    }
  }
  return treeMap
}

function isString(value) {
  return typeof value === 'string'
}

const nameMap = {
  FormGroup: '表单组',
  FormList: '表单列表',
  Detail: '详情',
  Field: '字段',
  DetailField: '详情字段',
  FormField: '表单字段',
  Table: '表格',
  TableForm: '表格表单',
  SubTable: '子表',
}

function createTargetServiceForBindXXXConfig(config, containerKey) {
  const targetService = {
    type: 'Input',
    serviceKey: config.service,
  }
  function traverseParam(param, path) {
    if (param.valueConfig?.action?.target === containerKey) {
      path.push(param.fieldAlias)
      targetService.parentPath = path.join('.')
    } else {
      if (param.elements) {
        path.push(param.fieldAlias)
        param.elements.forEach((element) => traverseParam(element, path))
      }
    }
  }
  config.params?.forEach((param) => traverseParam(param, []))
  return targetService
}
