package io.terminus.trantor2.workflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsPatch;
import io.terminus.trantor2.meta.resource.ext.patch.workflow.WorkflowPatch;
import io.terminus.trantor2.workflow.props.WorkflowGroupMetaProps;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@FieldNameConstants
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowGroupMeta extends ExtModuleBaseMeta<WorkflowGroupMetaProps> {
    private static final long serialVersionUID = -2974099468326592214L;

    @Override
    public MetaType getMetaType() {
        return MetaType.WorkflowGroup;
    }

    @Override
    protected List<ExtPropsPatch> getPatches() {
        List<ExtPropsPatch> patches = new ArrayList<>();
        ServiceLoader<WorkflowPatch> services = ServiceLoader.load(WorkflowPatch.class);
        services.forEach(patches::add);
        return patches;
    }
}

