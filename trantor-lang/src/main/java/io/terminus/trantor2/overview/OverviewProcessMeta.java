package io.terminus.trantor2.overview;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.meta.resource.TeamBaseMeta;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OverviewProcessMeta extends TeamBaseMeta<OverviewProcessMeta.OverviewProcessProps> {

    private static final long serialVersionUID = 3738203308899031468L;

    @Override
    public MetaType getMetaType() {
        return MetaType.OverviewProcess;
    }

    @Data
    public static class OverviewProcessProps implements ResourceProps {

        private String businessDesc;

        private List<OverviewMeta> overviews;

        private List<OverviewModel> models;

        private List<OverviewProcessNode> processNodes;

        private String bpmnXml;

        private String bpmnScreenshot;

    }
}
