package io.terminus.trantor2.overview;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.meta.resource.TeamBaseMeta;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OverviewMeta extends TeamBaseMeta<OverviewMeta.OverviewProps> {

    private static final long serialVersionUID = 4536353338666113540L;

    @Override
    public MetaType getMetaType() {
        return MetaType.Overview;
    }

    @Data
    public static class OverviewProps implements ResourceProps {

        // 概念内容
        private String content;

    }

}
