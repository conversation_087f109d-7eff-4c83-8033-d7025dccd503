package io.terminus.trantor2.overview;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OverviewProcessNode {

    private String serviceDefinitionKey;
    private String serviceDefinitionName;

    private String participant;

    private String viewKey;
    private String viewName;

    private String sceneKey;
    private String sceneName;

    private String portalKey;
    private String portalName;

    private List<String> screenshot;

    private String overviewKey;
    private String content;

}
