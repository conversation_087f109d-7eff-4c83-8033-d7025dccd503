package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.enums.AIRoundsStrategy;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.FieldEntry;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.ServiceProperties;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 服务 DSL 模型
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public class ServiceDefinition extends ServiceElement<ServiceProperties> implements ServiceElementsContainer, Serializable {

    private static final long serialVersionUID = 8509965108724958370L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    private List<String> headNodeKeys;
    private List<ServiceElement<?>> children;

    private List<Field> input;
    private List<Field> output;

    public ServiceDefinition(String key, String name) {
        this.key = key;
        this.name = name;
        this.props = new ServiceProperties();
    }

    public void addInput(Field field) {
        if (input == null) {
            input = new ArrayList<>();
        }
        input.add(field);
    }

    public void addOutput(Field field) {
        if (output == null) {
            output = new ArrayList<>();
        }
        output.add(field);
    }

    public void addGlobalVariable(Field field) {
        Optional<StartNode> startNode = startNode();
        if (startNode.isPresent()) {
            if (startNode.get().getProps().getGlobalVariable() == null) {
                startNode.get().getProps().setGlobalVariable(new ArrayList<>());
            }
            if (startNode.get().getProps().getGlobalVariable().stream()
                    .noneMatch(f -> f.getFieldKey().equals(field.getFieldKey()))) {
                startNode.get().getProps().getGlobalVariable().add(field);
            }
        }
    }

    @JsonIgnore
    public void setTransactionPropagation(Propagation transactionPropagation) {
        this.props.setTransactionPropagation(transactionPropagation);
    }

    @JsonIgnore
    public Boolean isAiService() {
        return this.props.getAiService() != null && this.props.getAiService();
    }

    @JsonIgnore
    public Boolean isAiChatMode() {
        return this.props.getAiChatMode() != null && this.props.getAiChatMode();
    }

    @JsonIgnore
    public AIRoundsStrategy getAiRoundsStrategy() {
        return this.props.getAiRoundsStrategy();
    }

    @JsonIgnore
    public Boolean enableSchedulerTask() {
        return this.props.getSchedulerJob() != null && this.props.getSchedulerJob().isEnable();
    }

    public List<Field> getInput() {
        Optional<StartNode> startNode = startNode();
        if (startNode.isPresent()) {
            if (startNode.get().getProps() != null && startNode.get().getProps().getInput() != null) {
                return startNode.get().getProps().getInput();
            }
        }
        return input;
    }

    public List<Field> getOutput() {
        Optional<StartNode> startNode = startNode();
        Optional<EndNode> endNode = endNode();

        List<Field> collectOutputFields = new ArrayList<>();
        if (startNode.isPresent()) {
            if (startNode.get().getProps() != null && startNode.get().getProps().getOutput() != null) {
                collectOutputFields.addAll(startNode.get().getProps().getOutput());
            }
        }

        if (endNode.isPresent()) {
            if (endNode.get().getProps() != null && endNode.get().getProps().getOutputMapping() != null) {
                List<Field> outputFields = endNode.get().getProps().getOutputMapping().stream()
                        .map(FieldEntry::getField).collect(Collectors.toList());
                for (Field out : outputFields) {
                    if (collectOutputFields.stream().noneMatch(o -> o.getFieldKey().equals(out.getFieldKey()))) {
                        collectOutputFields.add(out);
                    }
                }
            }
        }

        if (!collectOutputFields.isEmpty()) {
            return collectOutputFields;
        }

        return output;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        super.validate(errorContext);
        if (hasChildren()) {
            for (ServiceElement<?> child : children) {
                child.validate(errorContext);
            }
        }
    }

    public Optional<StartNode> startNode() {
        if (hasChildren()) {
            return getChildren().stream().filter(n -> n instanceof StartNode).map(n -> (StartNode) n).findFirst();
        } else {
            return Optional.empty();
        }
    }

    public Optional<EndNode> endNode() {
        if (hasChildren()) {
            return getChildren().stream().filter(n -> n instanceof EndNode).map(n -> (EndNode) n).findFirst();
        } else {
            return Optional.empty();
        }
    }

    public List<Field> collectAllVariables() {
        List<Field> variables = new ArrayList<>();

        List<Field> input = getInput();
        if (CollectionUtils.isNotEmpty(input)) {
            variables.add(new ObjectField(VariableType.REQUEST.getKey(), input));
        }

        Optional<StartNode> startNode = startNode();
        if (startNode.isPresent() && CollectionUtils.isNotEmpty(startNode.get().getProps().getGlobalVariable())) {
            variables.add(new ObjectField(VariableType.GLOBAL.getKey(), startNode.get().getProps().getGlobalVariable()));
        }

        List<Field> output = getOutput();
        if (CollectionUtils.isNotEmpty(output)) {
            variables.add(new ObjectField(VariableType.OUTPUT.getKey(), output));
        }

        return variables;
    }

    /**
     * 转换成新DSL，从330开始支持
     */
    public void convertToNew() {
        // 不为空，说明是老的DSL
        if (CollectionUtils.isNotEmpty(getHeadNodeKeys())) {
            convertToNew(this);
            setHeadNodeKeys(null);
        }
    }

    private void convertToNew(ServiceElementsContainer container) {
        List<ServiceElement<?>> children = container.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            Iterator<ServiceElement<?>> iterator = children.iterator();
            while (iterator.hasNext()) {
                ServiceElement<?> node = iterator.next();
                // 如果是二节点，且没有指针指向它，则直接移除该节点（二开DSL会有孤立的二开节点）
                if (node instanceof ServiceNode) {
                    if (((ServiceNode<?>) node).isExt() && children.stream().noneMatch(n -> node.getKey().equals(((ServiceNode<?>) n).getNextNodeKey()))) {
                        iterator.remove();
                        continue;
                    }
                }
                if (node instanceof BranchNode) {
                    ((BranchNode<?>) node).rebuildBranchNode();
                }
                if (node instanceof HasChildrenNode) {
                    convertToNew(((HasChildrenNode<?>) node));
                }
            }
        }
    }
}
