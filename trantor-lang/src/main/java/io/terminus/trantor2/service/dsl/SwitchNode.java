package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.SwitchProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * SwitchNode
 *
 * <AUTHOR> Created on 2023/11/21 14:09
 */
@Setter
@Getter
public class SwitchNode extends BranchNode<SwitchProperties> {
    private static final long serialVersionUID = 2718012991359201720L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("switchVariable", props.getSwitchVariable());
        return variables;
    }
}
