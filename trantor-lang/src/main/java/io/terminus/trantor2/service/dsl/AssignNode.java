package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.AssignEntry;
import io.terminus.trantor2.service.dsl.properties.AssignProperties;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * AssignNode
 *
 * <AUTHOR> Created on 2023/2/16 21:03
 */
public class AssignNode extends ServiceNode<AssignProperties> {
    private static final long serialVersionUID = -3653446035847418703L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        if (props.getAssignments() != null) {
            for (AssignEntry assign : props.getAssignments()) {
                variables.put(assign.getField().joinVars(), assign.getValue());
            }
        }
        return variables;
    }
}
