package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.properties.ReferenceAgentProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ReferenceAgent
 *
 * <AUTHOR> Created on 2025/3/15 23:30
 */
@Setter
@Getter
public class ReferenceAgent extends ServiceElement<ReferenceAgentProperties> implements ServiceElementsContainer {
    private static final long serialVersionUID = 5652853324979216023L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Handoffs> handoffs;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ServiceElement<?>> children;

    /**
     * 冗余的信息
     */
    private Agent targetAgent;
}
