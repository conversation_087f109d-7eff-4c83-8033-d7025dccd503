package io.terminus.trantor2.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/3/28 2:51 下午
 */
@Getter
@AllArgsConstructor
public enum ServiceType {
    /**
     * 编码规则
     */
    @Deprecated
    CODE_RULE("取号服务"),
    /**
     * 事件服务
     */
    EVENT("事件服务"),
    /**
     * 可编排服务
     */
    PROGRAMMABLE("编排服务"),
    /**
     * 系统服务
     */
    SYSTEM("系统服务"),

    /**
     * TService服务
     */
    SPI("SPI服务"),

    /**
     * 智能体
     */
    AGENT("智能体"),

    ;

    private final String display;

}
