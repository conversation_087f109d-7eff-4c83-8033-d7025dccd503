package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.properties.AbstractProperties;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * HasChildrenNode
 *
 * <AUTHOR> Created on 2024/3/9 19:15
 */
@Setter
@Getter
public abstract class HasChildrenNode<T extends AbstractProperties> extends ServiceNode<T> implements ServiceElementsContainer {
    private static final long serialVersionUID = -6948948844602441162L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    protected List<String> headNodeKeys;
    protected List<ServiceElement<?>> children;

    @Override
    public void validate(ValidatorContext errorContext) {
        super.validate(errorContext);
        if (children != null && !children.isEmpty()) {
            for (ServiceElement<?> child : children) {
                child.validate(errorContext);
            }
        }
    }
}
