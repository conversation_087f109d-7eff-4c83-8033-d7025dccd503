package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.service.dsl.properties.AbstractProperties;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * ServiceElement
 *
 * <AUTHOR> Created on 2023/2/16 17:11
 */
@Setter
@Getter
@JsonSubTypes({
        @JsonSubTypes.Type(value = StartNode.class, name = "StartNode"),
        @JsonSubTypes.Type(value = EndNode.class, name = "EndNode"),
        @JsonSubTypes.Type(value = ExclusiveConditionNode.class, names = {"ExclusiveConditionNode",/*后期废除，先兼容*/"ConditionNode"}),
        @JsonSubTypes.Type(value = ParallelConditionNode.class, name = "ParallelConditionNode"),
        @JsonSubTypes.Type(value = ConditionElseNode.class, names = {"ConditionElseNode", "SwitchDefaultNode"}),
        @JsonSubTypes.Type(value = CreateDataNode.class, name = "CreateDataNode"),
        @JsonSubTypes.Type(value = DeleteDataNode.class, name = "DeleteDataNode"),
        @JsonSubTypes.Type(value = ExclusiveBranchNode.class, name = "ExclusiveBranchNode"),
        @JsonSubTypes.Type(value = MultiCreateDataNode.class, name = "MultiCreateDataNode"),
        @JsonSubTypes.Type(value = MultiRetrieveDataNode.class, name = "MultiRetrieveDataNode"),
        @JsonSubTypes.Type(value = RetrieveDataNode.class, name = "RetrieveDataNode"),
        @JsonSubTypes.Type(value = PagingDataNode.class, name = "PagingDataNode"),
        @JsonSubTypes.Type(value = ScriptNode.class, name = "ScriptNode"),
        @JsonSubTypes.Type(value = UpdateDataNode.class, name = "UpdateDataNode"),
        @JsonSubTypes.Type(value = CallServiceNode.class, name = "CallServiceNode"),
        @JsonSubTypes.Type(value = HttpServiceNode.class, name = "HttpServiceNode"),
        @JsonSubTypes.Type(value = AssignNode.class, name = "AssignNode"),
        @JsonSubTypes.Type(value = LoopNode.class, name = "LoopNode"),
        @JsonSubTypes.Type(value = ActionNode.class, names = {"SPINode", "ActionNode"}),
        @JsonSubTypes.Type(value = GenerateCodeNode.class, name = "GenerateCodeNode"),
        @JsonSubTypes.Type(value = CascadeCreateDataNode.class, name = "CascadeCreateDataNode"),
        @JsonSubTypes.Type(value = CascadeDeleteDataNode.class, name = "CascadeDeleteDataNode"),
        @JsonSubTypes.Type(value = CascadeUpdateDataNode.class, name = "CascadeUpdateDataNode"),
        @JsonSubTypes.Type(value = CascadeFindDataNode.class, name = "CascadeFindDataNode"),
        @JsonSubTypes.Type(value = FindTreeDataNode.class, name = "FindTreeDataNode"),
        @JsonSubTypes.Type(value = ErrorNode.class, name = "ErrorNode"),
        @JsonSubTypes.Type(value = NoticeNode.class, name = "NoticeNode"),
        @JsonSubTypes.Type(value = ValidationNode.class, name = "ValidationNode"),
        @JsonSubTypes.Type(value = ConvertDataNode.class, name = "ConvertDataNode"),
        @JsonSubTypes.Type(value = AINode.class, name = "AINode"),
        @JsonSubTypes.Type(value = SqlNode.class, name = "SqlNode"),
        @JsonSubTypes.Type(value = AIKnowledgeNode.class, name = "AIKnowledgeNode"),
        @JsonSubTypes.Type(value = ConnectorNode.class, name = "ConnectorNode"),
        @JsonSubTypes.Type(value = SwitchNode.class, name = "SwitchNode"),
        @JsonSubTypes.Type(value = SwitchCaseNode.class, name = "SwitchCaseNode"),
        @JsonSubTypes.Type(value = CacheNode.class, name = "CacheNode"),
        @JsonSubTypes.Type(value = EventNode.class, name = "EventNode"),
        @JsonSubTypes.Type(value = WorkflowNode.class, names = {"ApprovalNode", "WorkflowNode"}),
        @JsonSubTypes.Type(value = StateNode.class, name = "StateNode"),
        @JsonSubTypes.Type(value = ParallelBranchNode.class, name = "ParallelBranchNode"),
        @JsonSubTypes.Type(value = CallEventServiceNode.class, name = "CallEventServiceNode"),
        @JsonSubTypes.Type(value = GenericNode.class, name = "GenericNode"),
        @JsonSubTypes.Type(value = CodeBlockNode.class, name = "CodeBlockNode"),
        @JsonSubTypes.Type(value = ServiceDefinition.class, name = "ServiceDefinition"),
        @JsonSubTypes.Type(value = Agent.class, name = "Agent"),
        @JsonSubTypes.Type(value = ReferenceAgent.class, name = "ReferenceAgent"),
        @JsonSubTypes.Type(value = RetrieveMetaNode.class, name = "RetrieveMetaNode"),
        @JsonSubTypes.Type(value = CallAgentNode.class, name = "CallAgentNode"),
        @JsonSubTypes.Type(value = KnowledgeBase.class, name = "KnowledgeBase"),
        @JsonSubTypes.Type(value = StreamOutputNode.class, name = "StreamOutputNode"),
})
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", defaultImpl = ServiceDefinition.class)
public abstract class ServiceElement<P extends AbstractProperties> extends BaseElement implements Validator {
    @Serial
    private static final long serialVersionUID = 8667313807678146006L;

    protected String key;
    protected String name;
    protected String desc;
    protected P props;

    public boolean hasErrorConfigs() {
        return props != null && props.getErrorConfigs() != null && !props.getErrorConfigs().isEmpty();
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        errorContext.setCurNode(this);
        if (Objects.isNull(key)) {
            errorContext.addErrorInfo("节点Key未配置");
        }
        if (Objects.isNull(props)) {
            errorContext.addErrorInfo("节点'props'未配置");
        } else {
            props.validate(errorContext);
            if (hasErrorConfigs()) {
                errorContext.addTag("异常处理");
                props.getErrorConfigs().forEach(e -> e.validate(errorContext));
                errorContext.removeTag();
            }
        }
    }

    /**
     * 返回当前元素的变量定义
     *
     * @return 节点变量，key-value, k为变量名(用于展示)，v为变量path
     */
    @JsonIgnore
    public Map<String, Object> getCurrentServiceElementVariables() {
        return Collections.emptyMap();
    }
}
