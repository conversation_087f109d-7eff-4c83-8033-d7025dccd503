package io.terminus.trantor2.service.dsl;

import java.util.List;

/**
 * ServiceElementsContainer
 *
 * <AUTHOR> Created on 2025/3/31 19:27
 */
public interface ServiceElementsContainer {

    List<ServiceElement<?>> getChildren();

    default boolean hasChildren() {
        List<ServiceElement<?>> children = getChildren();
        return children != null && !children.isEmpty();
    }

    default ServiceElement<?> findServiceElement(String elementKey) {
        List<ServiceElement<?>> children = getChildren();
        if (children == null || children.isEmpty()) {
            return null;
        }
        for (ServiceElement<?> child : children) {
            if (child.getKey().equals(elementKey)) {
                return child;
            } else if (child instanceof ServiceElementsContainer) {
                ServiceElement<?> node = ((ServiceElementsContainer) child).findServiceElement(elementKey);
                if (node != null) {
                    return node;
                }
            }
        }
        return null;
    }
}
