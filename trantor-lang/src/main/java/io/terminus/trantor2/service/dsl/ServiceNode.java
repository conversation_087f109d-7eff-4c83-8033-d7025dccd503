package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.properties.AbstractProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 服务抽象节点
 *
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class ServiceNode<T extends AbstractProperties> extends ServiceElement<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 2889818725017442317L;

    /**
     * 后置节点 Key
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    protected String nextNodeKey;

    /**
     * 是否二开
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    protected boolean ext;
}
