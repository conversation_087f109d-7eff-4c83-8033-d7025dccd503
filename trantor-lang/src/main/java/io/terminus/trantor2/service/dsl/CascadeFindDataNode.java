package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.CascadeFindDataProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * CascadeDeleteDataNode
 *
 * <AUTHOR> Created on 2023/3/24 11:18
 */
@Deprecated
@Setter
@Getter
public class CascadeFindDataNode extends DataProcessingNode<CascadeFindDataProperties> {
    private static final long serialVersionUID = 4304110284109962033L;
}
