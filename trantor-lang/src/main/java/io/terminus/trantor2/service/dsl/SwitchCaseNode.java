package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.ConstValue;
import io.terminus.trantor2.service.dsl.properties.SwitchCaseProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SwitchCaseNode
 *
 * <AUTHOR> Created on 2023/11/21 14:19
 */
@Setter
@Getter
public class SwitchCaseNode extends BranchConditionNode<SwitchCaseProperties> {
    private static final long serialVersionUID = -3450071468014818471L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("caseValue", props.getCaseValues().stream().map(ConstValue::getConstValue).collect(Collectors.toList()));
        return variables;
    }
}
