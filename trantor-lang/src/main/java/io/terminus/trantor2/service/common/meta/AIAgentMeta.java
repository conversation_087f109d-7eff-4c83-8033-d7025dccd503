package io.terminus.trantor2.service.common.meta;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.AgentProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;
import java.util.Optional;

/**
 * AIAgentMeta
 *
 * <AUTHOR> Created on 2025/3/13 17:13
 */
@Setter
@Getter
public class AIAgentMeta extends ModuleBaseMeta<AIAgentMeta.Props> {
    private static final long serialVersionUID = 7371081674553617873L;

    @Override
    public MetaType getMetaType() {
        return MetaType.AIAgent;
    }

    @Data
    public static class Props implements ResourceProps {
        /**
         * agent
         */
        private Agent agent;
        /**
         * 是否为启用状态
         */
        private Boolean isEnabled = true;

        @JsonIgnore
        public String getPermissionKey() {
            return Optional.ofNullable(agent)
                    .map(ServiceElement::getProps)
                    .map(AgentProperties::getPermissionKey)
                    .orElse(null);
        }

        @JsonIgnore
        public void setPermissionKey(String permissionKey) {
            if (Objects.nonNull(agent) && Objects.nonNull(agent.getProps())) {
                agent.getProps().setPermissionKey(permissionKey);
            }
        }
    }
}
