package io.terminus.trantor2.service.common.meta;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.lang.meta.Structure;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsPatch;
import io.terminus.trantor2.meta.resource.ext.patch.service.ServicePatch;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/3 2:41 PM
 **/
@Data
public class ServiceMeta extends ExtModuleBaseMeta<ServiceProps> {
    private static final long serialVersionUID = -1356880272950517176L;

    @Override
    public void from(MetaTreeNodeExt node) {
        super.from(node);
        // super.from will set teamId
        if (getResourceProps() != null && getResourceProps().getServiceDslJson() != null) {
            ServiceDefinition dsl = getResourceProps().getServiceDslJson();
            // super.key always right
            dsl.setKey(super.getKey());
//            if (dsl.getProps() != null) {
//                dsl.getProps().setTeamId(super.getTeamId());
//            }
        }
    }

    @Override
    public boolean extractStructure(MetaTreeNode node, Structure structure) {
        parseServiceDefinition(structure, node.getProps());
        return true;
    }

    @Override
    public ObjectNode extractLiteProps(MetaTreeNode node) {
        ObjectNode props = node.getProps();
        if (props == null) {
            return null;
        }
        JsonNode dsl = props.path("serviceDslJson");
        if (dsl.isObject()) {
            ((ObjectNode) dsl).remove("children");
        }
        return props;
    }

    @Override
    protected List<ExtPropsPatch> getPatches() {
        List<ExtPropsPatch> patches = new ArrayList<>();
        ServiceLoader<ServicePatch> services = ServiceLoader.load(ServicePatch.class);
        services.forEach(patches::add);
        return patches;
    }

    @Override
    public String getPropsExtFieldName() {
        return ServiceProps.Fields.serviceDslJson;
    }

    @Override
    public MetaType getMetaType() {
        return MetaType.ServiceDefinition;
    }

    private void parseServiceDefinition(Structure st, ObjectNode props) {
        if (props == null) {
            return;
        }
        JsonNode children = props.at("/serviceDslJson/children");
        parseServiceDefinitionInnerNode(st, "", children);
    }

    private void parseServiceDefinitionInnerNode(Structure st, String innerParentKey, JsonNode node) {
        if (node.isArray()) {
            for (JsonNode n : node) {
                parseServiceDefinitionInnerNode(st, innerParentKey, n);
            }
        } else if (node.isObject()) {
            // parse innerNode
            Structure.InnerNode innerNode = new Structure.InnerNode();
            innerNode.setInnerKey(ObjectJsonUtil.textOrNull(node.at("/key")));
            innerNode.setInnerType(ObjectJsonUtil.textOrNull(node.at("/type")));
            innerNode.setInnerName(ObjectJsonUtil.textOrNull(node.at("/name")));
            innerNode.setInnerParentKey(innerParentKey);
            st.getInnerNodes().add(innerNode);
            parseServiceDefinitionInnerNode(st, innerNode.getInnerKey(), node.at("/children"));
        }
    }
}
