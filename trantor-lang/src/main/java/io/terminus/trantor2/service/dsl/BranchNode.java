package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.AbstractProperties;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分支节点
 *
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class BranchNode<T extends AbstractProperties> extends HasChildrenNode<T> {

    private static final long serialVersionUID = -3270146241955215271L;

    public void rebuildBranchNode() {
        List<ServiceElement<?>> branchNodes = super.getChildren();
        if (CollectionUtils.isEmpty(branchNodes)) {
            return;
        }

        if (branchNodes.stream().allMatch(this::isBranchConditionNode)) {
            return;
        }

        Map<String, ServiceElement<?>> sourceChildrenMap = branchNodes.stream()
                .collect(Collectors.toMap(ServiceElement::getKey, Function.identity()));

        List<ServiceElement<?>> branchChildren = new ArrayList<>();

        for (ServiceElement<?> branch : branchNodes) {
            if (isBranchConditionNode(branch)) {

                HasChildrenNode<?> childrenNode = (HasChildrenNode<?>) branch;

                List<ServiceElement<?>> newChildren = new ArrayList<>();
                collectNodes(childrenNode.getNextNodeKey(), sourceChildrenMap, newChildren);

                childrenNode.setNextNodeKey(null);
                childrenNode.setChildren(newChildren);

                branchChildren.add(childrenNode);
            }
        }

        this.setChildren(branchChildren);
    }

    private void collectNodes(String key, Map<String, ServiceElement<?>> sourceChildrenMap, List<ServiceElement<?>> newChildren) {
        if (StringUtils.isNotBlank(key)) {
            ServiceElement<?> node = sourceChildrenMap.get(key);
            if (node != null) {
                newChildren.add(node);
                if (node instanceof ServiceNode) {
                    collectNodes(((ServiceNode<?>) node).getNextNodeKey(), sourceChildrenMap, newChildren);
                }
            }
        }
    }

    private boolean isBranchConditionNode(ServiceElement<?> node) {
        return node instanceof BranchConditionNode;
    }
}
