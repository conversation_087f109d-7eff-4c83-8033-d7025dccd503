package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.CascadeUpdateDataProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * CascadeDeleteDataNode
 *
 * <AUTHOR> Created on 2023/3/24 11:18
 */
@Setter
@Getter
public class CascadeUpdateDataNode extends DataProcessingNode<CascadeUpdateDataProperties> {
    private static final long serialVersionUID = 4304110284109962033L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("modelKey", props.getModelKey());
        variables.put("modelValue", props.getModelValue());
        return variables;
    }
}
