package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.CallServiceProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 调用通用服务节点
 */
@Setter
@Getter
public class CallServiceNode extends ServiceNode<CallServiceProperties> {

    private static final long serialVersionUID = -8055887900319664467L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("serviceKey", props.getServiceKey());
        if (props.getInputMapping() != null) {
            variables.put("inputMapping", props.getInputMapping());
        }
        return variables;
    }
}
