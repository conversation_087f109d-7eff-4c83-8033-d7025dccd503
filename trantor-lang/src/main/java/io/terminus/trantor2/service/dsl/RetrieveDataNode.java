package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.RetrieveDataProperties;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 查找数据节点
 *
 * <AUTHOR>
 */
public class RetrieveDataNode extends DataProcessingNode<RetrieveDataProperties> {

    private static final long serialVersionUID = 7482272711992928222L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("modelKey", props.getModelKey());
        variables.put("queryModelFields", props.getQueryModelFields());
        if (props.isPaging()) {
            variables.put("pageable", props.getPageable());
        } else if (props.isArray()) {
            variables.put("dynamicCondition", props.getDynamicCondition());
            variables.put("maximum", props.getMaximum());
        } else {
            variables.put("dynamicCondition", props.getDynamicCondition());
        }

        variables.put("conditionGroup", props.getConditionGroup());
        variables.put("sortOrders", props.getSortOrders());
        return variables;
    }

}
