package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.CallEventServiceProperties;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * CallEventServiceNode
 *
 * <AUTHOR> Created on 2024/7/17 17:49
 */
public class CallEventServiceNode extends ServiceNode<CallEventServiceProperties> {

    private static final long serialVersionUID = 3581568465769338956L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("eventServiceKey", props.getServiceKey());
        variables.put("convert", props.getConvert());
        variables.put("express", props.getExpress());
        variables.put("conditionGroup", props.getConditionGroup());

        return variables;
    }
}
