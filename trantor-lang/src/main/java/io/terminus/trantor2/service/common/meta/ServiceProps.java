package io.terminus.trantor2.service.common.meta;

import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.meta.resource.ext.ExtResourceProps;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Map;

/**
 * @author: yangyuqiang
 * @date: 2023/8/3 2:43 PM ResourceProps
 **/
@Data
@FieldNameConstants
public class ServiceProps implements ExtResourceProps {
    /**
     * 服务类型
     */
    private ServiceType serviceType;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 关联模型 Key
     */
    private String modelKey;
    /**
     * 服务名称
     */
    private String serviceDslMd5;
    /**
     * 服务定义 DSL
     */
    private ServiceDefinition serviceDslJson;
    /**
     * 是否为启用状态
     */
    private Boolean isEnabled;
    /**
     * 是否被删除
     */
    private Boolean isDeleted;
    /**
     * 服务类型为事件时，事件属性
     */
    private EventProps eventProps;
    /**
     * 服务权限
     * key: modelKey
     */
    private Map<String, PermissionDTO> permissions;
}
