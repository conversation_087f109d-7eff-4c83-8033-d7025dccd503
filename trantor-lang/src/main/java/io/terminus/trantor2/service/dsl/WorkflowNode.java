package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.WorkflowProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * WorkflowNode
 *
 * <AUTHOR> Created on 2024/2/28 14:08
 */
@Setter
@Getter
public class WorkflowNode extends ServiceNode<WorkflowProperties> {
    private static final long serialVersionUID = 1748188728923539119L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("workflowGroupKey", props.getWorkflowGroupKey());
        variables.put("modelKey", props.getModelKey());
        if (props.getInputMapping() != null) {
            variables.put("inputMapping", props.getInputMapping());
        }
        if (props.getCustomVariables() != null) {
            variables.put("customVariables", props.getCustomVariables());
        }
        return variables;
    }
}
