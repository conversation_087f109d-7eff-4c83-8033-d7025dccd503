package io.terminus.trantor2.service.common.meta;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.service.dsl.KnowledgeBase;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * AIAgentMeta
 *
 * <AUTHOR> Created on 2025/3/13 17:13
 */
@Setter
@Getter
public class KnowledgeBaseMeta extends ModuleBaseMeta<KnowledgeBaseMeta.Props> {
    private static final long serialVersionUID = 2919853919439281898L;

    @Override
    public MetaType getMetaType() {
        return MetaType.KnowledgeBase;
    }

    @Data
    public static class Props implements ResourceProps {
        /**
         * 知识库
         */
        private KnowledgeBase knowledgeBase;
        /**
         * 是否为启用状态
         */
        private Boolean isEnabled = true;

        @JsonIgnore
        public String getPermissionKey() {
            // todo Get KnowledgeBase permissionKey
            return this.knowledgeBase.getKey() + "_permission";
        }
    }
}
