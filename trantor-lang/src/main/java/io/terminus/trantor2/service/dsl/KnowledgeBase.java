package io.terminus.trantor2.service.dsl;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.properties.knowledgebase.KnowledgeBaseProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * Knowledge
 *
 * <AUTHOR> Created on 2025/3/15 16:07
 */
@Setter
@Getter
public class KnowledgeBase extends ServiceElement<KnowledgeBaseProperties> implements ServiceElementsContainer, Serializable {
    private static final long serialVersionUID = -6068604271992788023L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ServiceElement<?>> children;
}
