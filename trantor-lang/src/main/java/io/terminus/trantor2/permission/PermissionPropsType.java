package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权限元数据props属性类型
 *
 * <AUTHOR>
 * 2024/5/21 15:33
 **/
@AllArgsConstructor
@Getter
@Schema(description = "权限元数据props属性类型")
public enum PermissionPropsType {

    @Schema(description = "权限元数据props属性类型")
    FUNCTION_ITEM("功能权限项"),
    @Schema(description = "权限规则")
    DATA_CONDITION("权限规则"),
    @Schema(description = "字段规则")
    FIELD_RULE("字段规则"),
    @Schema(description = "数据控权维度")
    DATA_CONTROL_DIMENSION("数据控权维度"),
    @Schema(description = "接口访问控制规则")
    API_ACCESS_CONTROL("接口访问控制规则");

    private final String label;
}
