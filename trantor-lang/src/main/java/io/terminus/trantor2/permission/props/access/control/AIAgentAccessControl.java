package io.terminus.trantor2.permission.props.access.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门户访问控制接口 - AI智能体
 *
 * <AUTHOR>
 * 2024/3/26 8:35 下午
 **/
@Data
@Schema(description = "资源访问控制 - AI智能体")
public class AIAgentAccessControl extends ResourceAccessControl {

    @Schema(description = "应用程序接口类型")
    private ApiType apiType = ApiType.AIAgent;

    public String getAgentKey() {
        return getResourceKey();
    }
} 