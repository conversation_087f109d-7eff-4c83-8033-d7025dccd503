package io.terminus.trantor2.permission.props.access.control;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * 2023/8/16 11:18 上午
 **/
@Getter
@RequiredArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.STRING)
@Schema(description = "应用程序接口类型")
public enum ApiType {

    // 用户访问接口类型 ↓↓↓

    @Schema(description = "系统服务")
    SystemService("系统服务", false),

    @Schema(description = "编排服务/事件服务")
    Service("自定义服务", false),

    @Schema(description = "自定义接口")
    Api("自定义", false),

    @Schema(description = "AI智能体")
    AIAgent("AI智能体", false),

    // OpenApi访问接口类型 ↓↓↓

    @Schema(description = "开放服务")
    OpenService("开放服务", true),

    @Schema(description = "开放Api")
    OpenApi("开放Api", true),

    @Schema(description = "开放AI智能体")
    OpenAIAgent("开放AI智能体", true);

    private final String label;
    /**
     * 对应类型的接口是否是OpenApi
     */
    private final boolean isOpen;
}
