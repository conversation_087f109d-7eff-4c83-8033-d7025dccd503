package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/7/29 16:31
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Schema(description = "自定义服务属性")
public class SystemServiceProps extends ApiMetaProps {

    @Schema(description = "服务标识")
    private String serviceKey;

    @Schema(description = "模型标识")
    private String modelKey;

    @Override
    public ApiType getApiType() {
        return ApiType.SystemService;
    }

    @Override
    public String toSummaryString() {
        return "serviceKey: " + serviceKey + ", modelKey: " + modelKey;
    }

    @Override
    public String getResourceKey() {
        return KeyUtil.assembleSysService(getServiceKey(), getModelKey());
    }
}
