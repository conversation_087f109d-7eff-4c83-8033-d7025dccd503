package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/7/29 16:31
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Schema(description = "自定义服务属性")
public class ServiceProps extends ApiMetaProps {

    @Schema(description = "服务标识")
    protected String serviceKey;

    @Override
    public ApiType getApiType() {
        return ApiType.Service;
    }

    @Override
    public String toSummaryString() {
        return "serviceKey: " + serviceKey;
    }

    @Override
    public String getResourceKey() {
        return getServiceKey();
    }
}
