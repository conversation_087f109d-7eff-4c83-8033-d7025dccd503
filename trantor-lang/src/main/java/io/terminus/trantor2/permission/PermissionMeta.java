package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.utils.StandardPermissionKeyHelper;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.permission.props.FunctionItemPermissionProps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * 2024/5/21 15:19
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限项元数据")
public class PermissionMeta extends ExtModuleBaseMeta<FunctionItemPermissionProps> {
    private static final long serialVersionUID = 6923982930436873067L;

    @Override
    public MetaType getMetaType() {
        return MetaType.Permission;
    }

    /**
     * @return 是否是标准化权限项
     */
    public boolean isStandard() {
        return Objects.nonNull(this.getResourceProps())
                && Objects.nonNull(this.getResourceProps().getSourceModelKey())
                && this.getKey().startsWith(this.getResourceProps().getSourceModelKey())
                && StandardPermissionKeyHelper.isStandard(this.getKey());
    }
}
