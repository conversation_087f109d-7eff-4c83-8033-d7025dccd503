package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsPatch;
import io.terminus.trantor2.meta.resource.ext.patch.permission.DataConditionPatch;
import io.terminus.trantor2.permission.props.DataConditionPermissionProps;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据规则")
public class DataConditionMeta extends ExtModuleBaseMeta<DataConditionPermissionProps> {
    private static final long serialVersionUID = 4404399261110045777L;

    @Override
    public MetaType getMetaType() {
        return MetaType.DataCondition;
    }

    @Override
    protected List<ExtPropsPatch> getPatches() {
        List<ExtPropsPatch> patches = new ArrayList<>();
        ServiceLoader<DataConditionPatch> services = ServiceLoader.load(DataConditionPatch.class);
        services.forEach(patches::add);
        return patches;
    }
}
