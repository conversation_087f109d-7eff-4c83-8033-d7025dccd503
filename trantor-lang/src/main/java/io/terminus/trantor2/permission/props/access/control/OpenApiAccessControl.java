package io.terminus.trantor2.permission.props.access.control;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import lombok.Data;
import org.springframework.http.HttpMethod;

import jakarta.validation.constraints.NotNull;

/**
 * 门户访问控制接口 - 自定义API
 *
 * <AUTHOR>
 * 2023/8/15 8:35 下午
 **/
@Data
@Schema(description = "资源访问控制 - 自定义API")
public class OpenApiAccessControl extends ResourceAccessControl {

    @Schema(description = "应用程序接口类型")
    private ApiType apiType = ApiType.OpenApi;

    @Schema(description = "接口请求方法", required = true)
    @NotNull(message = "httpMethod can not be null")
    @SchemaProperty
    private HttpMethod httpMethod;

    /**
     * 权限点标识
     * <p>
     * 仅当 {@code getResourceType() == BackendResourceType.Api} 时有效
     */
    @Schema(description = "权限点标识")
    private String permissionKey;

    public String getHttpPath() {
        return getResourceKey();
    }
}
