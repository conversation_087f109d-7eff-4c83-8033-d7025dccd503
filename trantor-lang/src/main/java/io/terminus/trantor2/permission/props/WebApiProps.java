package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/7/29 16:31
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Schema(description = "WebAPI Props")
public class WebApiProps extends ApiMetaProps {

    @Schema(description = "http方法")
    protected String httpMethod;

    @Schema(description = "http路径")
    protected String httpPath;

    // 当 apiType = Api 时必填
    @Schema(description = "权限项标识")
    protected String permissionKey;

    @Override
    public ApiType getApiType() {
        return ApiType.Api;
    }

    @Override
    public String toSummaryString() {
        return httpMethod + " " + httpPath;
    }

    @Override
    public String getResourceKey() {
        return KeyUtil.assembleApi(getHttpMethod(), getHttpPath());
    }
}
