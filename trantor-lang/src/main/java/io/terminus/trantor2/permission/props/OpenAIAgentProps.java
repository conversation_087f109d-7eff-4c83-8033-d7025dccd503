package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/3/26 8:35 下午
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Schema(description = "AI智能体 Props")
public class OpenAIAgentProps extends AIAgentProps {

    @Override
    public ApiType getApiType() {
        return ApiType.OpenAIAgent;
    }

    @Override
    public String toSummaryString() {
        return getApiType() + " " + agentKey;
    }
}
