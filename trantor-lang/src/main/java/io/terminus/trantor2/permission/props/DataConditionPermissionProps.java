package io.terminus.trantor2.permission.props;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.resource.ext.ExtResourceProps;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.Map;

/**
 * <AUTHOR>
 * 2024/5/21 15:32
 **/
@Setter
@Getter
@EqualsAndHashCode
@Schema(description = "数据规则")
@FieldNameConstants
public class DataConditionPermissionProps implements ExtResourceProps {

    @Schema(description = "模型标识")
    private String modelKey;

    /*
    {
        "type": "ConditionGroup",
        "conditions": [
            {
                "type": "ConditionGroup",
                "conditions": [
                    {
                        "type": "ConditionLeaf",
                        ...
                    },
                    {
                        "type": "ConditionLeaf",
                        ...
                    }
                ],
                "logicOperator": "AND"
            }
        ],
        "logicOperator": "OR"
    }
     */
    @Schema(description = "数据条件规则")
    private ConditionGroup condition;

    @Schema(description = "拓展字段")
    private Map<String, Object> componentProps;
}
