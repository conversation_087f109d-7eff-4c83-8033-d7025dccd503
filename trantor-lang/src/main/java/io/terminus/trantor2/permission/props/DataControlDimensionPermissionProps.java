package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.resource.ext.ExtResourceProps;
import io.terminus.trantor2.permission.props.data.control.dimension.DataControlDimensionType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024/5/21 15:32
 **/
@Setter
@Getter
@EqualsAndHashCode
@Schema(description = "数据控权维度")
public class DataControlDimensionPermissionProps implements ExtResourceProps {

    @Schema(description = "维度类型")
    private DataControlDimensionType dimensionType;

    @Schema(description = "数据模型")
    private String modelKey;

    @Schema(description = "查询服务")
    private String serviceKey;

    @Schema(description = "映射模型信息")
    private List<MappingModel> mappingModels;

    @Schema(description = "拓展字段")
    private Map<String, Object> componentProps;

    @Getter
    @Setter
    @EqualsAndHashCode
    @Schema(description = "数据控权维度映射模型信息")
    public static class MappingModel {
        @Schema(description = "模型标识")
        private String key;

        @Schema(description = "模型名称")
        private String name;

        @Schema(description = "字段标识")
        private String fieldKey;

        @Schema(description = "字段名称")
        private String fieldName;
    }
}
