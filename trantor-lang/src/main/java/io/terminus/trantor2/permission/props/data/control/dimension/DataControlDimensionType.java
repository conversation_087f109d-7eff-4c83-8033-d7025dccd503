package io.terminus.trantor2.permission.props.data.control.dimension;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 2024/6/3 11:14
 **/
@Getter
@AllArgsConstructor
@Schema(description = "数据控权维度类型")
public enum DataControlDimensionType {

    @Schema(description = "对象")
    OBJECT("对象");

    private final String label;
}
