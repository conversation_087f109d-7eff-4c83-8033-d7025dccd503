package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.permission.props.ApiMetaProps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 2024/7/29 13:41
 **/

@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用程序接口元数据")
public class ApiMeta extends ModuleBaseMeta<ApiMetaProps> {

    public static final String SUFFIX = ":api_access_control";

    @Override
    public MetaType getMetaType() {
        return MetaType.Api;
    }
}
