package io.terminus.trantor2.permission.props;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.Objects;

/**
 * <AUTHOR>
 * 2024/7/29 13:42
 **/
@Setter
@Getter
@EqualsAndHashCode
@Schema(description = "应用程序接口元数据属性")
@FieldNameConstants
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "apiType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = SystemServiceProps.class, name = "SystemService"),
        @JsonSubTypes.Type(value = ServiceProps.class, name = "Service"),
        @JsonSubTypes.Type(value = WebApiProps.class, name = "Api"),
        @JsonSubTypes.Type(value = OpenServiceProps.class, name = "OpenService"),
        @JsonSubTypes.Type(value = OpenApiProps.class, name = "OpenApi"),
        @JsonSubTypes.Type(value = AIAgentProps.class, name = "AIAgent"),
        @JsonSubTypes.Type(value = OpenAIAgentProps.class, name = "OpenAIAgent")
})
public abstract class ApiMetaProps implements ResourceProps {

    @Schema(description = "接口类型")
    private ApiType apiType;

    @Schema(description = "是否开启身份认证")
    private Boolean authenticationEnabled;

    @Schema(description = "是否开启鉴权校验")
    private Boolean authorizationEvaluateEnabled;

    @Schema(description = "是否是OpenAPI")
    private Boolean openApi;

    public Boolean getOpenApi() {
        return Objects.nonNull(openApi) ? openApi : apiType.isOpen();
    }

    public String getUniqueKey() {
        return Boolean.TRUE.equals(getOpenApi()) ? "openapi:" + getResourceKey() : getResourceKey();
    }

    /**
     * resourceKey 与 uniqueKey 存在区别
     * resourceKey： 系统服务/编排服务/http接口转换而来的资源标识符，没有区分openapi和非openapi
     * uniqueKey：全局唯一的标识符，区分了openapi和非openapi，openapi类型通常带有前缀
     */
    @JsonIgnore
    public abstract String getResourceKey();

    /**
     * 与key不同，生成可被用户理解的文本描述
     */
    @JsonIgnore
    public abstract String toSummaryString();
}
