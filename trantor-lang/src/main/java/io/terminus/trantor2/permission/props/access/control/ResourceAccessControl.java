package io.terminus.trantor2.permission.props.access.control;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 门户访问控制接口
 *
 * <AUTHOR>
 * 2023/8/15 7:19 下午
 **/
@Data
@FieldNameConstants
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "resourceType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = SysServiceAccessControl.class, name = "SystemService"),
        @JsonSubTypes.Type(value = ServiceAccessControl.class, name = "Service"),
        @JsonSubTypes.Type(value = ApiAccessControl.class, name = "Api"),
        @JsonSubTypes.Type(value = OpenServiceAccessControl.class, name = "OpenService"),
        @JsonSubTypes.Type(value = OpenApiAccessControl.class, name = "OpenApi"),
        @JsonSubTypes.Type(value = AIAgentAccessControl.class, name = "AIAgent"),
        @JsonSubTypes.Type(value = OpenAIAgentAccessControl.class, name = "OpenAIAgent")
})
@Schema(description = "资源访问控制", subTypes = {
        SysServiceAccessControl.class,
        ServiceAccessControl.class,
        ApiAccessControl.class,
        OpenServiceAccessControl.class,
        OpenApiAccessControl.class,
        AIAgentAccessControl.class,
        OpenAIAgentAccessControl.class
})
public abstract class ResourceAccessControl implements Serializable {

    public static final Boolean DEFAULT_OPENAPI = Boolean.FALSE;

    /**
     * @see io.terminus.trantor2.permission.ApiMeta#key
     */
    @Deprecated // 元数据化，不需要id
    @Schema(description = "元数据资源标识", deprecated = true)
    private String resourceId;

    @Deprecated // 元数据化，不需要id
    @Schema(description = "关联权限点ID", deprecated = true)
    private Long permissionId;

    @Schema(description = "接口类型")
    private String resourceType;

    @Schema(description = "接口标识", required = true)
    @NotNull(message = "resourceKey can not be null")
    private String resourceKey;

    @Schema(description = "授权接口名称", required = true)
    @NotNull(message = "resourceName can not be null")
    private String resourceName;

    @Schema(description = "功能描述")
    private String resourceDesc;

    @Schema(description = "是否开启身份认证")
    private Boolean authenticationEnabled;

    @Schema(description = "是否开启鉴权")
    private Boolean authorizationEvaluateEnabled;

    @Schema(description = "是否是OpenAPI")
    private Boolean openApi = DEFAULT_OPENAPI;

    @Schema(description = "接口类型", required = true)
    @NotNull(message = "resourceType can not be null")
    public abstract ApiType getApiType();

    // 兼容前端参数
    public void setUserAuthenticationEnabled(Boolean userAuthenticationEnabled) {
        this.authenticationEnabled = userAuthenticationEnabled;
    }

    // 兼容前端参数
    public void setUserAuthorizationEvaluateEnabled(Boolean userAuthorizationEvaluateEnabled) {
        this.authorizationEvaluateEnabled = userAuthorizationEvaluateEnabled;
    }

    // 兼容前端参数
    public Boolean getUserAuthenticationEnabled() {
        return authenticationEnabled;
    }

    // 兼容前端参数
    public Boolean getUserAuthorizationEvaluateEnabled() {
        return authorizationEvaluateEnabled;
    }
}
