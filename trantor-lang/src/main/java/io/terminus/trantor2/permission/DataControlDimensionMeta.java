package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.permission.props.DataControlDimensionPermissionProps;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据控权维度")
public class DataControlDimensionMeta extends ExtModuleBaseMeta<DataControlDimensionPermissionProps> {
    private static final long serialVersionUID = -205124554722012332L;

    @Override
    public MetaType getMetaType() {
        return MetaType.DataControlDimension;
    }
}
