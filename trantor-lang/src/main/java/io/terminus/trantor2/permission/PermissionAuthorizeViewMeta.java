package io.terminus.trantor2.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.permission.props.PermissionAuthorizeViewProps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 2025/3/26 20:03
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限授权视图元数据对象")
public class PermissionAuthorizeViewMeta extends ExtModuleBaseMeta<PermissionAuthorizeViewProps> {
    @Override
    public MetaType getMetaType() {
        return MetaType.PermissionView;
    }
}
