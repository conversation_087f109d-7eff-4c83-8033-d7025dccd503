package io.terminus.trantor2.permission.props;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.response.permission.FieldRule;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

/**
 * 字段访问规则
 *
 * <AUTHOR>
 * 2024/7/5 15:18
 **/
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
@Schema(description = "字段规则")
public class FieldPermissionRule implements Serializable {

    @Schema(description = "权限标识")
    private String permissionKey;

    @Schema(description = "服务标识")
    private String serviceKey;

    @Schema(description = "服务名称")
    private String serviceName;

    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "字段权限规则列表", deprecated = true)
    private Collection<FieldRule> fieldRules;

    @Schema(description = "请求参数字段权限规则")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Collection<FieldRule> inputFieldRules;

    @Schema(description = "响应数据字段权限规则")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Collection<FieldRule> outputFieldRules;

    @Schema(description = "服务所在前端组件路径")
    private Collection<String> componentPaths;

    public FieldPermissionRule(String permissionKey, String serviceKey, String serviceName, Collection<FieldRule> fieldRules) {
        this.permissionKey = permissionKey;
        this.serviceKey = serviceKey;
        this.serviceName = serviceName;
        this.fieldRules = fieldRules;
    }
}
