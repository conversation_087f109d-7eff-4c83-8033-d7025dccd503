package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/7/29 16:31
 **/
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Schema(description = "WebAPI Props")
public class OpenApiProps extends WebApiProps {

    @Override
    public ApiType getApiType() {
        return ApiType.OpenApi;
    }

    @Override
    public String toSummaryString() {
        return getApiType() + " " + httpMethod + " " + httpPath;
    }
}
