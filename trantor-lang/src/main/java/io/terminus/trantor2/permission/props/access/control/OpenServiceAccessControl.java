package io.terminus.trantor2.permission.props.access.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门户访问控制接口 - 编排服务/事件服务
 *
 * <AUTHOR>
 * 2023/8/15 8:35 下午
 **/
@Data
@Schema(description = "资源访问控制 - 自定义服务")
public class OpenServiceAccessControl extends ResourceAccessControl {

    @Schema(description = "应用程序接口类型")
    private ApiType apiType = ApiType.OpenService;

    public String getServiceKey() {
        return getResourceKey();
    }
}
