package io.terminus.trantor2.permission.props.access.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门户访问控制接口 - 系统服务
 *
 * <AUTHOR>
 * 2023/8/15 8:15 下午
 **/
@Deprecated // 系统服务 0530 下线
@Data
@Schema(description = "资源访问控制 - 系统服务")
public class SysServiceAccessControl extends ResourceAccessControl {

    @Schema(description = "模型表示", required = true)
    private String modelKey;

    @Schema(description = "应用程序接口类型")
    private ApiType apiType = ApiType.SystemService;

    public String getServiceKey() {
        return getResourceKey();
    }
}
