package io.terminus.trantor2.permission.props;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.resource.ext.ExtResourceProps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * 2024/5/21 15:32
 **/
@Setter
@Getter
@EqualsAndHashCode
@FieldNameConstants
@Schema(description = "功能权限项")
public class FunctionItemPermissionProps implements ExtResourceProps {

    @Schema(description = "来源模型")
    private String sourceModelKey;
}
