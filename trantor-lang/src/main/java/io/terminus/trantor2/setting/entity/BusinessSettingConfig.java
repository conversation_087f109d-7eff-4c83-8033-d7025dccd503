package io.terminus.trantor2.setting.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Schema(title = "业务配置的配置")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSettingConfig implements Serializable {
    @Schema(title = "是否内置 status 字段")
    private boolean statusEnabled;
}
