package io.terminus.trantor2.rule.engine.api.model.dto.condition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 因子值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FactorValueDTO implements Serializable {
    private static final long serialVersionUID = 5289613743566890316L;

    /**
     * 因子key
     */
    private String factorKey;

    /**
     * 字段类型。
     * Text-文本（RichText-富文本、Email-邮箱）
     * Boolean-布尔值
     * Number-数字
     * DateTime-日期
     * Time-时间
     * Enum-枚举
     */
    private String fieldType;

    /**
     * 因子名称
     */
    private String factorName;

    @JsonIgnore
    public String getKey() {
        if (factorKey == null) {
            return null;
        }
        // factorKey 的括号去掉
        int idx = factorKey.indexOf("(");
        return idx == -1 ? factorKey : factorKey.substring(0, idx);
    }

//    /**
//     * 函数参数DTO
//     */
//    private List<ParamDTO> paramDTOList;
//
//    /**
//     * 函数参数DTO
//     * <AUTHOR>
//     * @createTime 2023/2/7 11:00 上午
//     */
//    @Data
//    @AllArgsConstructor
//    @NoArgsConstructor
//    public class ParamDTO implements Serializable {
//
//        /**
//         * 值类型。CONSTANT-常量、MODEL-模型、FACTOR-因子
//         */
//        private String valueType;
//
//        /**
//         * 值。值类型为固定值时必填
//         */
//        private String value;
//
//        /**
//         * 因子值。值类型为因子时必填
//         */
//        private FactorValueDTO factor;
//
//        /**
//         * 模型值。值类型为模型时必填
//         */
//        private ModelValueDTO model;
//
//        @Data
//        @AllArgsConstructor
//        @NoArgsConstructor
//        public class ModelDTO implements Serializable {
//
//            public ModelDTO(Boolean hasRelationModel, String modelName, String modelKey, String modelAlias, Boolean identification) {
//                this.hasRelationModel = hasRelationModel;
//                this.modelName = modelName;
//                this.modelKey = modelKey;
//                this.modelAlias = modelAlias;
//                this.identification = identification;
//            }
//
//            public ModelDTO(Boolean hasRelationModel, String modelKey, String modelAlias, String relationModelKey, String relationModelAlias, String relationModelField, String relationModelFieldKey, String relationModelFieldAlias) {
//                this.hasRelationModel = hasRelationModel;
//                this.modelKey = modelKey;
//                this.modelAlias = modelAlias;
//                this.relationModelKey = relationModelKey;
//                this.relationModelAlias = relationModelAlias;
//                this.relationModelField = relationModelField;
//                this.relationModelFieldKey = relationModelFieldKey;
//                this.relationModelFieldAlias = relationModelFieldAlias;
//            }
//
//            /**
//             * 是否是关联模型
//             */
//            private Boolean hasRelationModel;
//
//            /**
//             * 模型名称
//             */
//            private String modelName;
//
//            /**
//             * 模型key
//             */
//            private String modelKey;
//
//            /**
//             * 模型别名
//             */
//            private String modelAlias;
//
//            /**
//             * 关联模型key
//             */
//            private String relationModelKey;
//
//            /**
//             * 关联模型别名
//             */
//            private String relationModelAlias;
//
//            /**
//             * 关联模型字段
//             */
//            private String relationModelField;
//
//            /**
//             * 关联模型字段key
//             */
//            private String relationModelFieldKey;
//
//            /**
//             * 关联模型字段别名
//             */
//            private String relationModelFieldAlias;
//
//            private Boolean identification;
//        }
//
//        @Data
//        @AllArgsConstructor
//        @NoArgsConstructor
//        public class FactorDTO implements Serializable {
//
//            /**
//             * 因子名称
//             */
//            private String factorName;
//
//            /**
//             * 因子key
//             */
//            private String factorKey;
//
//            /**
//             * 字段类型。
//             * Text-文本（RichText-富文本、Email-邮箱）
//             * Boolean-布尔值
//             * Number-数字
//             * DateTime-日期
//             * Time-时间
//             * Enum-枚举
//             */
//            private String fieldType;
//
//        }
//    }

}

