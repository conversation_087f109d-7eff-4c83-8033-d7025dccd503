package io.terminus.trantor2.meta.model.management;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingConfig;
import io.terminus.trantor2.model.management.meta.domain.sharding.type.SubStringProperties;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ShardingConfigTest {
    @Test
    void shardingConfigSerializing() throws JsonProcessingException {
        {
            String configJson = "{\n" +
                "  \"enabled\": true,\n" +
                "  \"shardingFields\": [{\n" +
                "    \"field\": \"suffix_field\",\n" +
                "    \"shardingType\": \"SUFFIX\"\n" +
                "  }, {\n" +
                "    \"field\": \"substring_field\",\n" +
                "    \"shardingType\": \"SUBSTRING\",\n" +
                "    \"shardingProperties\": {\n" +
                "      \"start\":3,\n" +
                "      \"end\": 5\n" +
                "    }\n" +
                "  }, {\n" +
                "    \"field\": \"hash_field\",\n" +
                "    \"shardingType\": \"HASH\"\n" +
                "  }]\n" +
                "}\n";
            ShardingConfig shardingConfig = ObjectJsonUtil.MAPPER.readValue(configJson, ShardingConfig.class);
            Assertions.assertNotNull(shardingConfig);
            Assertions.assertTrue(shardingConfig.isEnabled());
            Assertions.assertNotNull(shardingConfig.getShardingFields());
            Assertions.assertEquals(3, shardingConfig.getShardingFields().size());

            shardingConfig.getShardingFields().forEach(shardingField -> {
                if (shardingField.getField().equals("suffix_field")) {
                    Assertions.assertNull(shardingField.getShardingProperties());
                } else if (shardingField.getField().equals("substring_field")) {
                    Assertions.assertInstanceOf(SubStringProperties.class, shardingField.getShardingProperties());
                } else {
                    Assertions.assertNull(shardingField.getShardingProperties());
                }
            });
        }
    }
}
