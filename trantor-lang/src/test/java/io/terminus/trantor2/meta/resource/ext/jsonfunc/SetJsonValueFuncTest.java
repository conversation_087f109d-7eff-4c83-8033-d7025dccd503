package io.terminus.trantor2.meta.resource.ext.jsonfunc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * {
 *   "a": {
 *     "key": "a",
 *     "name": "a",
 *     "props": {
 *       "title": "a"
 *     }
 *   },
 *   "e": {
 *     "key": "e",
 *     "name": "e"
 *   }
 * }
 * <AUTHOR>
 */
class SetJsonValueFuncTest {
    private final Map<String, JsonNode> key2ObjectNodMap = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/key2objectNodeMap.json", new TypeReference<Map<String, JsonNode>>() {
    });

    @Test
    void execute_jsonValue() {
        SetJsonValueFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/set_json_value_func1.json", SetJsonValueFunc.class);

        JsonNode jsonNode = key2ObjectNodMap.get("e");
        assertNull(jsonNode.get("props"));
        func.execute("e", "props/title", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("e");
        assertEquals("a", jsonNode.get("props").get("title").asText());
        assertEquals("e", jsonNode.get("key").asText());
        assertEquals("e", jsonNode.get("name").asText());
    }

    @Test
    void execute_constValue() {
        SetJsonValueFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/set_json_value_func2.json", SetJsonValueFunc.class);

        JsonNode jsonNode = key2ObjectNodMap.get("e");
        assertNull(jsonNode.get("props"));
        func.execute("e", "props/title", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("e");
        assertEquals("e", jsonNode.get("props").get("title").asText());
        assertEquals("e", jsonNode.get("key").asText());
        assertEquals("e", jsonNode.get("name").asText());
    }

    @Test
    void execute_with_recursive() {
        SetJsonValueFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/set_json_value_func3.json", SetJsonValueFunc.class);
        func.execute("TB2B$mall_distributor_addr", "props/editComponentProps/options/value::apple/label", key2ObjectNodMap);

        JsonNode jsonNode = key2ObjectNodMap.get("TB2B$mall_distributor_addr");
        String text = jsonNode.at("/props/editComponentProps/options/0/label").asText();
        assertEquals("iphone", text);
    }
}