package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ExtConditionGroupPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtConditionGroupPatch patch = new ExtConditionGroupPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_condition_group_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_condition_group_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        props.setOriginType(MetaType.View);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);
        assertEquals(6, snippets.size());
        assertEquals(2, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND).count());
        assertEquals(1, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() == null).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() == null).forEach(it -> {
            ObjectNode jsonNode = (ObjectNode) it.getContent().get(0);
            assertEquals("add", jsonNode.get("add").get("id").asText());
        });

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() != null).forEach(it -> {
            assertEquals("vsYy_KuMOTrWA5_g5TT8k", it.getKey());
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("add", content.get(0).get("id").asText());
        });

        assertEquals(2, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT
                && snippet.getKey().equals("Bp4CgRv2HDGF8g6t0f2WW")).forEach(it -> {
            assertEquals("edit", it.getParentKey());
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("Bp4CgRv2HDGF8g6t0f2WW", content.get(0).get("id").asText());
        });
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT
                && snippet.getKey().equals("edit$edit")).forEach(it -> {
            assertEquals("vsYy_KuMOTrWA5_g5TT8k", it.getParentKey());
            assertEquals("edit$edit", it.getKey());
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("abc", content.get(0).get("rightValue").get("constValue").asText());
        });

        assertEquals(2, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE).count());
        assertEquals(1, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() == null).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() == null)
                .forEach(it -> assertEquals("remove", it.getNode()));

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() != null).forEach(it -> {
            assertEquals("vsYy_KuMOTrWA5_g5TT8k", it.getParentKey());
            assertEquals("remove", it.getKey());
        });
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_condition_group_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_condition_group_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        props.setOriginType(MetaType.View);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);
        assertEquals(6, snippets.size());

        patch.applySnippets("batch$student_setting_test:list", oriNode, snippets);

        assertJsonEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER, TREATING_NULL_AS_ABSENT));
    }
}