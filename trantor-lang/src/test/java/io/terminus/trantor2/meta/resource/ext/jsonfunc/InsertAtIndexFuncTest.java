package io.terminus.trantor2.meta.resource.ext.jsonfunc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.InsertRelativePosition;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
class InsertAtIndexFuncTest {
    private final Map<String, JsonNode> key2ObjectNodMap = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/key2objectNodeMap.json", new TypeReference<Map<String, JsonNode>>() {
    });

    @Test
    void inset_multi_after() {
        InsertAtIndexFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/insert_at_index_func1.json", InsertAtIndexFunc.class);
        JsonNode jsonNode = key2ObjectNodMap.get("b");

        ArrayNode children = (ArrayNode) jsonNode.get("children");

        ArrayNode contentNode = ObjectJsonUtil.MAPPER.createArrayNode();
        ObjectNode new1 = ObjectJsonUtil.MAPPER.createObjectNode();
        new1.set("key", TextNode.valueOf("new1"));
        ObjectNode new2 = ObjectJsonUtil.MAPPER.createObjectNode();
        new2.set("key", TextNode.valueOf("new2"));
        contentNode.add(new1);
        contentNode.add(new2);

        func.execute(children, contentNode);

        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("new1", children.get(2).get("key").asText());
        assertEquals("new2", children.get(3).get("key").asText());
        assertEquals("b3", children.get(4).get("key").asText());
    }

    @Test
    void inset_after_2() {
        InsertAtIndexFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/insert_at_index_func1.json", InsertAtIndexFunc.class);
        InsertRelativePosition params = (InsertRelativePosition) func.getParams();
        params.setOffset(2);
        JsonNode jsonNode = key2ObjectNodMap.get("b");

        ArrayNode children = (ArrayNode) jsonNode.get("children");

        ArrayNode contentNode = ObjectJsonUtil.MAPPER.createArrayNode();
        ObjectNode new1 = ObjectJsonUtil.MAPPER.createObjectNode();
        new1.set("key", TextNode.valueOf("new1"));
        contentNode.add(new1);


        func.execute(children, contentNode);

        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());
        assertEquals("new1", children.get(3).get("key").asText());
        assertEquals("b4", children.get(4).get("key").asText());
    }

    @Test
    void inset_before() {
        InsertAtIndexFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/insert_at_index_func1.json", InsertAtIndexFunc.class);
        InsertRelativePosition params = (InsertRelativePosition) func.getParams();
        params.setOffset(-1);
        JsonNode jsonNode = key2ObjectNodMap.get("b");

        ArrayNode children = (ArrayNode) jsonNode.get("children");

        ArrayNode contentNode = ObjectJsonUtil.MAPPER.createArrayNode();
        ObjectNode new1 = ObjectJsonUtil.MAPPER.createObjectNode();
        new1.set("key", TextNode.valueOf("new1"));
        contentNode.add(new1);

        func.execute(children, contentNode);

        assertEquals("b1", children.get(0).get("key").asText());
        assertEquals("new1", children.get(1).get("key").asText());
        assertEquals("b2", children.get(2).get("key").asText());

    }
}