package io.terminus.trantor2.meta.resource.ext.patch.workflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.JsonFuncType;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class ExtAttachmentConfigPatchTest {

    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtAttachmentConfigPatch patch = new ExtAttachmentConfigPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);
        assertTrue(oriNode.get("attachmentConfig").get("maxNumberOfFiles").isNull());

        patch.applySnippets(patch.getRootField(), oriNode, snippets);
        assertEquals(3, oriNode.get("attachmentConfig").get("maxNumberOfFiles").asInt());
    }

    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertEquals(patch.getRootField(), snippet.getRootField());
        assertEquals(ExtMeta.ActionType.EDIT, snippet.getAction());
        assertEquals(JsonFuncType.SET_JSON_VALUE, snippet.getFunc().getType());
    }


    @Test
    void fetchSnippets_empty() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        ObjectNode attachmentConfigNode = (ObjectNode) extNode.get("attachmentConfig");
        attachmentConfigNode.set("maxNumberOfFiles", JsonNodeFactory.instance.nullNode());
        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(0, snippets.size());
    }
}