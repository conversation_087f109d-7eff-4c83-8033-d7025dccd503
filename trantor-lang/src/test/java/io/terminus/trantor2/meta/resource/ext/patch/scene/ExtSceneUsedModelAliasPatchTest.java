package io.terminus.trantor2.meta.resource.ext.patch.scene;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.*;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class ExtSceneUsedModelAliasPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtSceneUsedModelAliasPatch patch = new ExtSceneUsedModelAliasPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets_then_merge() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Scene);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("abc$xyz", extNode, oriNode, props);
        assertNotNull(snippets);
        assertEquals(2, snippets.size());
        assertEquals(1, snippets.stream().filter(snippet -> {
            if (snippet.getAction() == ExtMeta.ActionType.APPEND) {
                assertEquals(patch.getRootField(), snippet.getRootField());
                assertEquals(1, snippet.getContent().size());
                snippet.getContent().elements().forEachRemaining(node -> assertTrue(node.asText().contains("add")));
                return true;
            }
            return false;
        }).count());

        assertEquals(1, snippets.stream().filter(snippet -> {
            if (snippet.getAction() == ExtMeta.ActionType.REMOVE) {
                assertEquals(patch.getRootField(), snippet.getRootField());
                assertEquals(1, snippet.getContent().size());
                snippet.getContent().elements().forEachRemaining(node -> assertTrue(node.asText().contains("delete")));
                return true;
            }
            return false;
        }).count());
        assertJsonNotEquals(extNode.at("/" + patch.getRootField()), oriNode.at("/" + patch.getRootField()), when(IGNORING_ARRAY_ORDER));
        patch.applySnippets("abc$xyz", oriNode, snippets);
        assertJsonEquals(extNode.at("/" + patch.getRootField()), oriNode.at("/" + patch.getRootField()), when(IGNORING_ARRAY_ORDER));
    }

    @Test
    void fetchSnippets_extIsNull() {
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Scene);

        ExtSceneI18nKeySetPatch patch = new ExtSceneI18nKeySetPatch();
        ObjectNode extNode = mapper.createObjectNode();
        extNode.put("key", "abc$xyz");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("abc$xyz", extNode, oriNode, props);

        assertNotNull(snippets);
        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertSame(snippet.getAction(), ExtMeta.ActionType.REMOVE);
        assertEquals(patch.getRootField(), snippet.getRootField());
        assertNull(snippet.getContent());

        assertTrue(extNode.at("/" + patch.getRootField()).isMissingNode());
        assertFalse(oriNode.at("/" + patch.getRootField()).isMissingNode());
        patch.applySnippets("abc$xyz", oriNode, snippets);
        assertTrue(oriNode.at("/" + patch.getRootField()).isMissingNode());
    }

    @Test
    void fetchSnippets_oriIsNull() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ext.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Scene);

        ExtSceneI18nKeySetPatch patch = new ExtSceneI18nKeySetPatch();
        ObjectNode oriNode = mapper.createObjectNode();
        extNode.put("key", "abc$xyz");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("abc$xyz", extNode, oriNode, props);

        assertNotNull(snippets);
        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertSame(snippet.getAction(), ExtMeta.ActionType.APPEND);
        assertEquals(38, snippet.getContent().size());
        assertEquals(patch.getRootField(), snippet.getRootField());

        assertFalse(extNode.at("/" + patch.getRootField()).isMissingNode());
        assertTrue(oriNode.at("/" + patch.getRootField()).isMissingNode());

        assertJsonNotEquals(extNode.at("/" + patch.getRootField()), oriNode.at("/" + patch.getRootField()), when(IGNORING_ARRAY_ORDER));
        patch.applySnippets("abc$xyz", oriNode, snippets);
        assertJsonEquals(extNode.at("/" + patch.getRootField()), oriNode.at("/" + patch.getRootField()), when(IGNORING_ARRAY_ORDER));
    }
}