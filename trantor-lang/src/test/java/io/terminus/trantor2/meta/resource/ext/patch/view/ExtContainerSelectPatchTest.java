package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static net.javacrumbs.jsonunit.core.Option.TREATING_NULL_AS_ABSENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ExtContainerSelectPatchTest {

    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtContainerSelectPatch patch = new ExtContainerSelectPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }


    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_container_select_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_container_select_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        props.setOriginType(MetaType.View);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);
        assertNotNull(snippets);
        assertEquals(5, snippets.size());

        assertEquals(2, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND).count());
        assertEquals(1, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() == null).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() == null).forEach(it -> {
            ObjectNode jsonNode = (ObjectNode) it.getContent().get(0);
            ArrayNode arrayNode = (ArrayNode) jsonNode.get("batch$addGroup");
            assertEquals("name", arrayNode.get(0).get("field").asText());
        });

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND && snippet.getKey() != null).forEach(it -> {
            assertEquals("batch$edit", it.getKey());
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("add", content.get(0).get("field").asText());
        });

        assertEquals(1, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT).forEach(it -> {
            assertEquals("batch$edit", it.getKey());
            assertEquals("setting", it.getNode());
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("setting", content.get(0).get("field").asText());
        });

        assertEquals(2, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE).count());
        assertEquals(1, snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() == null).count());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() == null).forEach(it -> {
            assertEquals("batch$deleteGroup", it.getNode());
        });

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE && snippet.getKey() != null).forEach(it -> {
            assertEquals("batch$edit", it.getKey());
            assertEquals("remove", it.getNode());
        });
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_container_select_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_container_select_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        props.setOriginType(MetaType.View);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);

        patch.applySnippets("batch$student_setting_test:list", oriNode, snippets);

        assertJsonEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER, TREATING_NULL_AS_ABSENT));
    }
}