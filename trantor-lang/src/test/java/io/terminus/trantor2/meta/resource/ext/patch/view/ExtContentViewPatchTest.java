package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.ExtStep;
import io.terminus.trantor2.meta.resource.ext.ExtValidScope;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class ExtContentViewPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtContentViewPatch patch = new ExtContentViewPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void testParseExtSteps() {
        JsonNode view = ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_view.json", JsonNode.class).get("content");

        List<ExtStep> extSteps = ReflectionTestUtils.invokeMethod(patch, "parseExtSteps", view);
        assertEquals(7, extSteps.size());
        assertEquals(2, extSteps.stream().filter(it -> it.getKey().equals("batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC")).count());
        assertEquals(1, extSteps.stream().filter(it -> it.getKey().equals("batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC")
                && it.getValidScope().equals(ExtValidScope.RUNTIME)).count());
        assertEquals(2, extSteps.stream().filter(it -> it.getKey().equals("batch$jjjkk-3dxPKEvHEQUWUaWQH1Jf2")).count());
        assertEquals(1, extSteps.stream().filter(it -> it.getKey().equals("batch$jjjkk-3dxPKEvHEQUWUaWQH1Jf2")
                && it.getValidScope().equals(ExtValidScope.RUNTIME)).count());

        assertEquals(2, extSteps.stream().filter(it -> it.getAction().equals(ExtMeta.ActionType.EDIT)
                && it.getValidScope() == ExtValidScope.ALL).count());
        assertEquals(2, extSteps.stream().filter(it -> it.getAction().equals(ExtMeta.ActionType.REMOVE)
                && it.getValidScope().equals(ExtValidScope.RUNTIME)).count());

    }


    @Test
    void testAppendExtSteps() {
        JsonNode extView = ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_view.json", JsonNode.class);

        ArrayNode extStepsExpected = ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/view_extSteps.json", ArrayNode.class);
        patch.fetchSnippets("batch$jjjkk:list", (ObjectNode) extView, JsonNodeFactory.instance.objectNode(), new ExtMeta.Props());

        ArrayNode extStepNode = (ArrayNode) extView.get("content").get("extSteps");
        assertNotNull(extStepNode);
        assertEquals(7, extStepNode.size());
        assertJsonEquals(extStepsExpected, extStepNode, when(IGNORING_ARRAY_ORDER));
    }

}