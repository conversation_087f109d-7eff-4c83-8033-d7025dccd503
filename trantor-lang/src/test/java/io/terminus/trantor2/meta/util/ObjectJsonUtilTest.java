package io.terminus.trantor2.meta.util;

import com.fasterxml.jackson.databind.JsonNode;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
class ObjectJsonUtilTest {

    @Test
    public void testDeserSer() {
        // {"k": "v", "k2": "v2"}
        String jsonWithIndent = "{\"k\": \"v\",    \"k2\": \"v2\"}";
        assertEquals(
            "{\"k\":\"v\",\"k2\":\"v2\"}",
            ObjectJsonUtil.serialize(
                ObjectJsonUtil.deserialize(jsonWithIndent)
            )
        );
    }

    @Test
    public void testBooleanValue() {
        String json = "{\n" +
                "\t\"type\": \"Module\",\n" +
                "\t\"config\": null,\n" +
                "\t\"logoUrl\": null,\n" +
                "\t\"endpointType\": null,\n" +
                "\t\"moduleConfig\": false,\n" +
                "\t\"nativeModule\": true\n" +
                "}";
        JsonNode nativeModule = ObjectJsonUtil.deserialize(json).get("nativeModule");
        assertTrue(ObjectJsonUtil.booleanValue(nativeModule, false));
        assertTrue(ObjectJsonUtil.booleanValue(nativeModule, true));

        JsonNode moduleConfig = ObjectJsonUtil.deserialize(json).get("moduleConfig");
        assertFalse(ObjectJsonUtil.booleanValue(moduleConfig, false));
        assertFalse(ObjectJsonUtil.booleanValue(moduleConfig, true));

        JsonNode type = ObjectJsonUtil.deserialize(json).get("type");
        assertFalse(ObjectJsonUtil.booleanValue(type, false));
        assertTrue(ObjectJsonUtil.booleanValue(type, true));

        JsonNode noneNode = ObjectJsonUtil.deserialize(json).get("none");
        assertTrue(ObjectJsonUtil.booleanValue(noneNode, true));
        assertFalse(ObjectJsonUtil.booleanValue(noneNode, false));
    }
}
