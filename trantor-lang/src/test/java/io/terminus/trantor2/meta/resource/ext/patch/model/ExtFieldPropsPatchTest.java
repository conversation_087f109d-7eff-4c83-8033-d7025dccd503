package io.terminus.trantor2.meta.resource.ext.patch.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.FuncParam;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.GetConstValue;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */

class ExtFieldPropsPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtFieldPropsPatch patch = new ExtFieldPropsPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode modelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_model.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Model);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_model.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("moduleA$amodel", extModelProps, modelProps, props);
        assertEquals(2, snippets.size());
        ExtMeta.Snippet snippet1 = snippets.get(0);
        assertEquals("textte",snippet1.getKey()) ;
        GetConstValue params1 = (GetConstValue) snippet1.getFunc().getParams();
        assertTrue(params1.getConstant().asBoolean());
        ExtMeta.Snippet snippet2 = snippets.get(1);
        assertEquals("textte",snippet1.getKey()) ;
        GetConstValue params2 = (GetConstValue) snippet2.getFunc().getParams();
        assertEquals("DES_ID_CARD", params2.getConstant().asText());
    }

    @Test
    void applySnippets() {
        ObjectNode modelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_model.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Model);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_model.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("moduleA$amodel", extModelProps, modelProps, props);

        patch.applySnippets("moduleA$amodel", modelProps, snippets);

        ObjectNode field = (ObjectNode) modelProps.get("children").get(1);
        assertEquals("DES_ID_CARD", field.at("/props/desensitizedRule").asText());
        assertTrue(field.at("/props/encrypted").asBoolean());
    }
}