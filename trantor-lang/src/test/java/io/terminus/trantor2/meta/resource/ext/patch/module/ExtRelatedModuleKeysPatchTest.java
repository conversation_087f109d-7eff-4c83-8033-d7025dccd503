package io.terminus.trantor2.meta.resource.ext.patch.module;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.ShadowModuleMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static net.javacrumbs.jsonunit.core.Option.TREATING_NULL_AS_ABSENT;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
public class ExtRelatedModuleKeysPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtRelatedModuleKeysPatch patch = new ExtRelatedModuleKeysPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode moduleProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_module.json", ObjectNode.class).get("props");

        ShadowModuleMeta.Props props = new ShadowModuleMeta.Props();
        props.setOriginType(MetaType.Module);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_module.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch", extModelProps, moduleProps, props);

        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertEquals(ExtMeta.ActionType.APPEND, snippet.getAction());
        assertEquals(1, snippet.getContent().size());
        assertEquals("new_dependency", snippet.getContent().get(0).asText());
    }

    @Test
    void applySnippets() {
        ObjectNode moduleProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_module.json", ObjectNode.class).get("props");

        ShadowModuleMeta.Props props = new ShadowModuleMeta.Props();
        props.setOriginType(MetaType.Module);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_module.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch", extModelProps, moduleProps, props);

        patch.applySnippets("batch", moduleProps, snippets);
        assertJsonEquals(moduleProps, extModelProps, when(IGNORING_ARRAY_ORDER, TREATING_NULL_AS_ABSENT));
    }
}
