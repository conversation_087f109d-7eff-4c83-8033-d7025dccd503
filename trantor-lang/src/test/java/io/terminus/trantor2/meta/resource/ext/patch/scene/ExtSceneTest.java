package io.terminus.trantor2.meta.resource.ext.patch.scene;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static net.javacrumbs.jsonunit.JsonAssert.*;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
public class ExtSceneTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void spilt_then_merge() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/scene_config_ori.json", ObjectNode.class);
        assertJsonNotEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER));

        SceneMeta sceneMeta = new SceneMeta();
        List<ScenePatch> patches = sceneMeta.getExtParams().getPatches().stream().map(it -> (ScenePatch) it).collect(Collectors.toList());

        ExtMeta.Props extProps = new ExtMeta.Props();
        extProps.setOriginType(MetaType.Scene);
        List<ExtMeta.Snippet> snippets = new ArrayList<>();
        extProps.setSnippets(snippets);
        patches.forEach(patch -> snippets.addAll(patch.fetchSnippets("abc$xyz", extNode, oriNode, extProps)));

        assertNotNull(snippets);
        assertEquals(6, snippets.size());

        patches.forEach(patch -> {
            List<ExtMeta.Snippet> patchSnippets = snippets;
            if (patch.getRootField() != null) {
                patchSnippets = snippets.stream().filter(snippet -> patch.getRootField().equals(snippet.getRootField()))
                        .collect(Collectors.toList());
            }
            patch.applySnippets("abc$xyz", oriNode, patchSnippets);
        });

        assertJsonEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER));
    }
}
