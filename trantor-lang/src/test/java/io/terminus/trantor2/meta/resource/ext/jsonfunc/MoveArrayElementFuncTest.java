package io.terminus.trantor2.meta.resource.ext.jsonfunc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.MoveRelativePosition;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
class MoveArrayElementFuncTest {
    private final Map<String, JsonNode> key2ObjectNodMap = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/key2objectNodeMap.json", new TypeReference<Map<String, JsonNode>>() {
    });

    @Test
    void not_move() {
        MoveArrayElementFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/move_array_ele_func1.json", MoveArrayElementFunc.class);
        JsonNode jsonNode = key2ObjectNodMap.get("b");
        ArrayNode children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());

        func.execute("b", "children", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("b");
        children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());
    }

    @Test
    void move_after() {
        MoveArrayElementFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/move_array_ele_func1.json", MoveArrayElementFunc.class);
        MoveRelativePosition params = (MoveRelativePosition) func.getParams();
        params.setCur("b2");
        params.setOrigin("b3");

        JsonNode jsonNode = key2ObjectNodMap.get("b");
        ArrayNode children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());

        func.execute("b", "children", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("b");
        children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(2).get("key").asText());
        assertEquals("b3", children.get(1).get("key").asText());
    }

    @Test
    void move_before() {
        MoveArrayElementFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/move_array_ele_func1.json", MoveArrayElementFunc.class);
        MoveRelativePosition params = (MoveRelativePosition) func.getParams();
        params.setCur("b2");
        params.setOrigin("b3");
        params.setOffset(-2);

        JsonNode jsonNode = key2ObjectNodMap.get("b");
        ArrayNode children = (ArrayNode) jsonNode.get("children");
        assertEquals("b1", children.get(0).get("key").asText());
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());

        func.execute("b", "children", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("b");
        children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(0).get("key").asText());
        assertEquals("b1", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());
    }

    @Test
    void move_before_2() {
        MoveArrayElementFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/move_array_ele_func1.json", MoveArrayElementFunc.class);
        MoveRelativePosition params = (MoveRelativePosition) func.getParams();
        params.setCur("b4");
        params.setOrigin("b3");
        params.setOffset(-2);

        JsonNode jsonNode = key2ObjectNodMap.get("b");
        ArrayNode children = (ArrayNode) jsonNode.get("children");
        assertEquals("b1", children.get(0).get("key").asText());
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());

        func.execute("b", "children", key2ObjectNodMap);

        // b1, b4, b2, b3
        jsonNode = key2ObjectNodMap.get("b");
        children = (ArrayNode) jsonNode.get("children");
        assertEquals("b1", children.get(0).get("key").asText());
        assertEquals("b4", children.get(1).get("key").asText());
        assertEquals("b2", children.get(2).get("key").asText());
    }

    @Test
    void move_absolute() {
        MoveArrayElementFunc func = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/ext/move_array_ele_func2.json", MoveArrayElementFunc.class);

        JsonNode jsonNode = key2ObjectNodMap.get("b");
        ArrayNode children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(1).get("key").asText());
        assertEquals("b3", children.get(2).get("key").asText());

        func.execute("b", "children", key2ObjectNodMap);

        jsonNode = key2ObjectNodMap.get("b");
        children = (ArrayNode) jsonNode.get("children");
        assertEquals("b2", children.get(2).get("key").asText());
        assertEquals("b3", children.get(1).get("key").asText());
    }
}