package io.terminus.trantor2.meta.resource.ext.patch.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class ExtModelDictPropsPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtModelDictPropsPatch patch = new ExtModelDictPropsPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode modelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_model.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Model);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_model.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("moduleA$amodel", extModelProps, modelProps, props);
        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertEquals("enumenu", snippet.getKey());
        assertEquals(ExtMeta.ActionType.APPEND, snippet.getAction());
        assertEquals(ExtModelDictPropsPatch.ARRAY_PATH, snippet.getNode());
        assertEquals("cherry", snippet.getContent().get(0).get("value").asText());
        assertEquals("durian", snippet.getContent().get(1).get("value").asText());
    }

    @Test
    void applySnippets() {
        ObjectNode modelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_model.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.Model);

        ObjectNode extModelProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_model.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("moduleA$amodel", extModelProps, modelProps, props);

        patch.applySnippets("moduleA$amodel", modelProps, snippets);

        ObjectNode enumObj = (ObjectNode) modelProps.get("children").get(2);
        assertNotNull(enumObj.get("extProps"));
        assertTrue(enumObj.at("/extProps/edited").asBoolean());
        ArrayNode dictValues = (ArrayNode) enumObj.at("/props/dictPros/dictValues");

        assertEquals(5, dictValues.size());
        ObjectNode cherry = (ObjectNode) dictValues.get(3);
        assertEquals("cherry", cherry.get("value").asText());
        assertTrue(cherry.at("/extProps/appended").asBoolean());

        ObjectNode durian = (ObjectNode) dictValues.get(4);
        assertEquals("durian", durian.get("value").asText());
        assertTrue(durian.at("/extProps/appended").asBoolean());
    }
}