package io.terminus.trantor2.meta.resource.ext.patch.permission;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.ExtValidScope;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.SetJsonValueFunc;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.GetConstValue;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ExtDataConditionComponentPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtDataConditionComponentPatch patch = new ExtDataConditionComponentPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode oriProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_datacondition.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.DataCondition);

        ObjectNode extProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_datacondition.json", ObjectNode.class).get("props");

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("zjt_test$adfasdfa:cccc", extProps, oriProps, props);

        assertEquals(1, snippets.size());
        ExtMeta.Snippet snippet = snippets.get(0);
        assertEquals(ExtMeta.ActionType.EDIT, snippet.getAction());
        assertEquals(patch.getRootField(), snippet.getRootField());
        assertEquals(ExtValidScope.ALL, snippet.getValidScope());
        assertInstanceOf(SetJsonValueFunc.class, snippet.getFunc());
        SetJsonValueFunc func = snippet.getFunc();
        assertInstanceOf(GetConstValue.class, func.getParams());
        GetConstValue params = (GetConstValue) func.getParams();
        assertJsonEquals(extProps.get(patch.getRootField()), params.getConstant());
    }

    @Test
    void applySnippets() {
        ObjectNode oriProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_datacondition.json", ObjectNode.class).get("props");

        ObjectNode extProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_datacondition.json", ObjectNode.class).get("props");

        GetConstValue getConstValue = new GetConstValue();
        getConstValue.setConstant(extProps.get(patch.getRootField()));
        SetJsonValueFunc func = new SetJsonValueFunc();
        func.setParams(getConstValue);
        ExtMeta.Snippet snippet = ExtMeta.Snippet.builder()
                .action(ExtMeta.ActionType.EDIT)
                .func(func)
                .rootField(patch.getRootField())
                .build();
        patch.applySnippets("zjt_test$adfasdfa:cccc", oriProps, Collections.singletonList(snippet));
        assertJsonEquals(extProps.get(patch.getRootField()), oriProps.get(patch.getRootField()));
    }

    @Test
    void spitThenMerge() {
        ObjectNode extProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ext_datacondition.json", ObjectNode.class).get("props");
        ObjectNode oriProps = (ObjectNode) ResourceHelper.readValueFromResource(mapper, getClass(),
                "json/ext/ori_datacondition.json", ObjectNode.class).get("props");

        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.DataCondition);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("zjt_test$adfasdfa:cccc", extProps, oriProps, props);

        patch.applySnippets("zjt_test$adfasdfa:cccc", oriProps, snippets);

        assertJsonEquals(extProps.get(patch.getRootField()), oriProps.get(patch.getRootField()));
    }
}