package io.terminus.trantor2.meta.resource.ext.patch.service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_EXTRA_FIELDS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class ExtServicePermissionsPatchTest {
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtServicePermissionsPatch patch = new ExtServicePermissionsPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_service_permission.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_service_permission.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("whatever", extNode, oriNode, null);

        patch.applySnippets("whatever", oriNode, snippets);

        assertJsonEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER, IGNORING_EXTRA_FIELDS));
    }

    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_service_permission.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_service_permission.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("whatever", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(3, snippets.size());
    }

}