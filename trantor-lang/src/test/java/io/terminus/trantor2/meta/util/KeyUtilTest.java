package io.terminus.trantor2.meta.util;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class KeyUtilTest {
    @Test
    public void testShortKey() {
        assertEquals("abc", KeyUtil.shortKey("M$abc"));
        assertEquals("abc$def", KeyUtil.shortKey("M$abc$def"));
    }

    @Test
    public void testSysShortKey() {
        assertFalse(KeyUtil.isSysShortKey("abc"));
        assertFalse(KeyUtil.isSysShortKey("abc$def"));
        assertFalse(KeyUtil.isSysShortKey("abc$sysdef"));
        assertTrue(KeyUtil.isSysShortKey("abc$sys_def"));
        assertTrue(KeyUtil.isSysShortKey("abc$SYS_def"));
    }

    @Test
    public void testCheckCode() {
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode(null));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode(""));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode("123abc"));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode("_123"));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode("_abc"));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode("abc#123"));
        assertThrows(TrantorRuntimeException.class, () -> KeyUtil.checkCode("abc-123"));

        assertFalse(KeyUtil.isValidRegex(null));
        assertFalse(KeyUtil.isValidRegex(""));
        assertFalse(KeyUtil.isValidRegex("123abc"));
        assertFalse(KeyUtil.isValidRegex("_123"));
        assertFalse(KeyUtil.isValidRegex("_abc"));
        assertFalse(KeyUtil.isValidRegex("abc#123"));
        assertFalse(KeyUtil.isValidRegex("abc-123"));

        assertTrue(KeyUtil.isInvalidKey("abc_123"));
    }
}
