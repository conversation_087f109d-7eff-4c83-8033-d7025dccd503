package io.terminus.trantor2.meta.resource.ext.jsonfunc.params;

import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
class MoveRelativePositionTest {

    @Test
    @SuppressWarnings("all")
    void getIndex() {
        MoveRelativePosition moveRelativePosition = new MoveRelativePosition();
        assertTrue(Objects.equals(2,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 3, 1, -1)));
        assertTrue(Objects.equals(0,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 0, 1, -1)));
        assertTrue(Objects.equals(3,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 3, 1, 1)));
        assertTrue(Objects.equals(3,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 4, 2, -1)));
        assertTrue(Objects.equals(2,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 1, 3, 1)));
        assertTrue(Objects.equals(0,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 2, 1, -2)));
        assertTrue(Objects.equals(1,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 2, 3, -2)));
        assertTrue(Objects.equals(2,
                ReflectionTestUtils.invokeMethod(moveRelativePosition, "targetIndex", 1, 2, 1)));

    }
}