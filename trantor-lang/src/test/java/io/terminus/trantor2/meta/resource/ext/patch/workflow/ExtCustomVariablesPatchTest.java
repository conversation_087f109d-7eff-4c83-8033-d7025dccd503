package io.terminus.trantor2.meta.resource.ext.patch.workflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.GetConstValue;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.IGNORING_ARRAY_ORDER;
import static net.javacrumbs.jsonunit.core.Option.TREATING_NULL_AS_ABSENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class ExtCustomVariablesPatchTest {

    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtCustomVariablesPatch patch = new ExtCustomVariablesPatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(3, snippets.size());
        assertEquals(ExtMeta.ActionType.APPEND, snippets.get(0).getAction());
        assertEquals(ExtMeta.ActionType.REMOVE, snippets.get(1).getAction());
        assertEquals(ExtMeta.ActionType.EDIT, snippets.get(2).getAction());
        assertEquals("a", ((GetConstValue) snippets.get(2).getFunc().getParams()).getConstant().get("fieldKey").asText());
        assertEquals("a", ((GetConstValue) snippets.get(2).getFunc().getParams()).getConstant().get("fieldName").asText());
    }

    @Test
    void fetchSnippets_ori_isEmpty() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = JsonNodeFactory.instance.objectNode();
        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(1, snippets.size());
        assertEquals(ExtMeta.ActionType.APPEND, snippets.get(0).getAction());
        assertEquals(3, snippets.get(0).getContent().size());
        assertEquals("a", snippets.get(0).getContent().get(0).get(patch.getArrayMainField()).asText());
    }

    @Test
    void fetchSnippets_ext_isEmpty() {
        ObjectNode extNode = JsonNodeFactory.instance.objectNode();

        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(1, snippets.size());
        assertEquals(ExtMeta.ActionType.REMOVE, snippets.get(0).getAction());
        assertEquals(3, snippets.get(0).getContent().size());
        assertEquals("a", snippets.get(0).getContent().get(0).asText());
    }

    @Test
    void fetchSnippets_empty() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);

        assertNotNull(snippets);
        assertEquals(0, snippets.size());
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ext_workflow.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/ori_workflow.json", ObjectNode.class);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("mall_management_module$approval", extNode, oriNode, null);
        patch.applySnippets(patch.getRootField(), oriNode, snippets);
        assertJsonEquals(extNode.at("/" + patch.getRootField()), oriNode.at("/" + patch.getRootField()), when(IGNORING_ARRAY_ORDER, TREATING_NULL_AS_ABSENT));
    }
}