package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals;
import static net.javacrumbs.jsonunit.JsonAssert.when;
import static net.javacrumbs.jsonunit.core.Option.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ExtResourcePatchTest {

    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;
    private static final ExtResourcePatch patch = new ExtResourcePatch();

    static {
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Test
    void fetchSnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_resources_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_resources_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        props.setOriginType(MetaType.View);

        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);
        assertNotNull(snippets);
        assertEquals(3, snippets.size());
        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.APPEND).forEach(it -> {
            ArrayNode content = it.getContent();
            assertEquals(2, content.size());
            assertEquals("batch$add2", content.get(0).get("key").asText());
            assertEquals("batch$add1", content.get(1).get("key").asText());
        });

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.EDIT).forEach(it -> {
            String key = it.getKey();
            assertNotNull(key);
            assertEquals("batch$edit", key);
            ArrayNode content = it.getContent();
            assertEquals(1, content.size());
            assertEquals("编辑", content.get(0).get("label").asText());
        });

        snippets.stream().filter(snippet -> snippet.getAction() == ExtMeta.ActionType.REMOVE).forEach(it -> {
            ArrayNode content = it.getContent();
            assertEquals(2, content.size());
            assertEquals("batch$remove1", content.get(0).asText());
            assertEquals("batch$remove2", content.get(1).asText());
        });
    }

    @Test
    void applySnippets() {
        ObjectNode extNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_resources_ext.json", ObjectNode.class);
        ObjectNode oriNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/ext/view_resources_ori.json", ObjectNode.class);
        ExtMeta.Props props = new ExtMeta.Props();
        props.setOriginType(MetaType.View);
        props.setSnippets(new ArrayList<>());
        props.getSnippets().add(new ExtMeta.Snippet());
        List<ExtMeta.Snippet> snippets = patch.fetchSnippets("batch$student_setting_test:list", extNode, oriNode, props);

        patch.applySnippets("batch$student_setting_test:list", oriNode, snippets);

        assertJsonEquals(extNode, oriNode, when(IGNORING_ARRAY_ORDER, TREATING_NULL_AS_ABSENT));
    }
}