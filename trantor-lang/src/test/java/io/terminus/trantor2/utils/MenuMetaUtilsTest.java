package io.terminus.trantor2.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
class MenuMetaUtilsTest {

    @Test
    void findMenuByKey() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });
        Optional<MenuMeta> portalCode$b = MenuMetaUtils.findMenuByKey("portalCode$b", menus);
        Assertions.assertTrue(portalCode$b.isPresent());
        Assertions.assertEquals("portalCode$b", portalCode$b.get().getKey());

        Optional<MenuMeta> portalCode$notFound = MenuMetaUtils.findMenuByKey("portalCode$notFound", menus);

        Assertions.assertFalse(portalCode$notFound.isPresent());
    }

    @Test
    void findMenusByKeys() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        List<MenuMeta> found = MenuMetaUtils.findMenusByKeys(
                Arrays.asList("portalCode$a", "portalCode$b", "portalCode$c"),
                menus);
        Assertions.assertFalse(CollectionUtils.isEmpty(found));
        Assertions.assertEquals(2, found.size());
        Assertions.assertTrue(found.stream().anyMatch(menu -> "portalCode$a".equals(menu.getKey())));
        Assertions.assertTrue(found.stream().anyMatch(menu -> "portalCode$b".equals(menu.getKey())));
        Assertions.assertTrue(found.stream().noneMatch(menu -> "portalCode$c".equals(menu.getKey())));
    }

    @Test
    void findMenuBoundScene() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        Optional<MenuMeta> menu = MenuMetaUtils.findMenuBoundScene("module$scene1", menus);
        Assertions.assertTrue(menu.isPresent());
        Assertions.assertEquals("portalCode$a1", menu.get().getKey());
    }

    @Test
    void checkAndCompareMenuTree() {
        List<MenuMeta> oldMenuTree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });
        List<MenuMeta> sameMenuTree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        Map<String, List<String>> diff = MenuMetaUtils.checkAndCompareMenuTree(oldMenuTree, sameMenuTree);
        Assertions.assertTrue(CollectionUtils.isEmpty(diff));

        List<MenuMeta> newMenuTree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus_diff.json", new TypeReference<List<MenuMeta>>() {
        });


        diff = MenuMetaUtils.checkAndCompareMenuTree(oldMenuTree, newMenuTree);
        Assertions.assertFalse(CollectionUtils.isEmpty(diff));
        Assertions.assertEquals(3, diff.size());
        Assertions.assertTrue(diff.containsKey("ADD"));
        Assertions.assertTrue(diff.containsKey("UPDATE"));
        Assertions.assertTrue(diff.containsKey("DELETE"));
        Assertions.assertEquals(1, diff.get("ADD").size());
        Assertions.assertEquals(1, diff.get("UPDATE").size());
        Assertions.assertEquals(1, diff.get("DELETE").size());
    }

    @Test
    void findParentNode() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        Optional<MenuMeta> parentNode = MenuMetaUtils.findParentNode("portalCode$a1", menus);
        Assertions.assertTrue(parentNode.isPresent());
        Assertions.assertEquals("portalCode$a", parentNode.get().getKey());

    }

    @Test
    void findAncestors() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });

        Map<String, List<String>> testCases = ImmutableMap.<String, List<String>>builder()
                .put("portalCode$a", Lists.newArrayList("portalCode$a"))
                .put("portalCode$a1", Lists.newArrayList("portalCode$a", "portalCode$a1"))
                .put("portalCode$a2", Lists.newArrayList("portalCode$a", "portalCode$a2"))
                .put("portalCode$a21", Lists.newArrayList("portalCode$a", "portalCode$a2", "portalCode$a21"))
                .put("portalCode$a22", Lists.newArrayList("portalCode$a", "portalCode$a2", "portalCode$a22"))
                .put("portalCode$b", Lists.newArrayList("portalCode$b"))
                .build();

        testCases.forEach((key, expected) -> {
            List<MenuMeta> ancestors = MenuMetaUtils.findAncestors(key, menus);
            Assertions.assertEquals(expected, ancestors.stream().map(MenuMeta::getKey).collect(Collectors.toList()), "key: " + key);
        });
    }

    @Test
    void flatMenus() {
        List<MenuMeta> menus = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu/menus.json", new TypeReference<List<MenuMeta>>() {
        });
        List<MenuMeta> menuMetas = MenuMetaUtils.flatMenus(menus);
        Assertions.assertFalse(CollectionUtils.isEmpty(menuMetas));
        Assertions.assertEquals(6, menuMetas.size());
    }
}
