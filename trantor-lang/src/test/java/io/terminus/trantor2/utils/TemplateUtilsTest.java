package io.terminus.trantor2.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import io.terminus.trantor2.meta.resource.ext.ExtStep;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class TemplateUtilsTest {
    @Test
    void testAppendViewNode() throws TemplateException, IOException {
        Template template = TemplateUtils.getTemplate("APPEND_VIEW_NODE");
        StringWriter out = new StringWriter();
        Map<String, String> dataModel = new HashMap<>();
        dataModel.put("key", "key");
        template.process(dataModel, out);
        List<ExtStep> extSteps = ObjectJsonUtil.MAPPER.readValue(out.toString(), new TypeReference<List<ExtStep>>() {
        });
        assertNotNull(extSteps);
        assertEquals(1, extSteps.size());
        assertNotNull(extSteps.get(0).getKey());
        assertNull(extSteps.get(0).getFunc());

        dataModel.put("preNodeKey", "preNodeKey");
        out = new StringWriter();
        template.process(dataModel, out);

        extSteps = ObjectJsonUtil.MAPPER.readValue(out.toString(), new TypeReference<List<ExtStep>>() {
        });
        assertNotNull(extSteps);
        assertEquals(1, extSteps.size());
        assertNotNull(extSteps.get(0).getKey());
        assertNotNull(extSteps.get(0).getFunc());
    }
}