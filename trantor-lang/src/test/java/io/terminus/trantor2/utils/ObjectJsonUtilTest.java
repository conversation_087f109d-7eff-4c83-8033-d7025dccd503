package io.terminus.trantor2.utils;

import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 **/
public class ObjectJsonUtilTest {
    @Test
    public void testSerializeWithCustomIndent() {
        String sourceJson = ResourceHelper.getResourceAsString(getClass(), "json/source.json");
        String targetJson = ResourceHelper.getResourceAsString(getClass(), "json/target.json");
        String serializeJson = ObjectJsonUtil.serializeWithCustomIndent(ObjectJsonUtil.deserialize(sourceJson));
        Assertions.assertEquals(targetJson, serializeJson);

        serializeJson = ObjectJsonUtil.serializeIndent(ObjectJsonUtil.deserialize(sourceJson));
        Assertions.assertNotEquals(targetJson, serializeJson);
    }
}
