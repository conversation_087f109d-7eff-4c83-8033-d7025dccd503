//package io.terminus.trantor2.service.dsl.properties;
//
//import com.blazebit.persistence.parser.SQLLexer;
//import com.blazebit.persistence.parser.SQLParser;
//import com.blazebit.persistence.parser.SQLParserBaseListener;
//import com.blazebit.persistence.parser.antlr.CharStream;
//import com.blazebit.persistence.parser.antlr.CharStreams;
//import com.blazebit.persistence.parser.antlr.CommonTokenStream;
//import com.blazebit.persistence.parser.antlr.tree.ParseTree;
//import com.blazebit.persistence.parser.antlr.tree.ParseTreeWalker;
//import org.apache.commons.collections.CollectionUtils;
//import org.junit.jupiter.api.Test;
//
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//
///**
// * SqlPropertiesTest
// *
// * <AUTHOR> Created on 2024/4/7 10:30
// */
//class SqlPropertiesTest {
//
//    @Test
//    void validate() {
//        String sql = "select * from `tables` join mysql.db on tables.id = db.id where tables.id = #{id} and ${pageable}";
//        SqlProperties properties = new SqlProperties();
//
//        List<String> b = properties.getSystemTables(sql);
//        assertTrue(CollectionUtils.isNotEmpty(b));
//
//    }
//
////    @Test
////    void validateSchema() {
////        String sql = "select * from information_schema.tables join mysql.db on tables.id = db.id where tables.id = #{id} and ${pageable}";
////        SqlProperties properties = new SqlProperties();
////
////        boolean supportSql = properties.isSupportSql(sql);
////        assertTrue(supportSql);
////
////        List<String> b = properties.getSystemTables(sql);
////        assertTrue(CollectionUtils.isNotEmpty(b));
////
////        boolean insertSql = properties.isInsertSql(sql);
////        assertFalse(insertSql);
////
////        boolean containWhere = properties.isContainWhere(sql);
////        assertTrue(containWhere);
////    }
//
////    @Test
////    void validateSubFrom() {
////        String sql = "select * from (select * from user where id in (#{ids})) as tables where tables.id = #{id} and ${pageable}";
////        SqlProperties properties = new SqlProperties();
////
////        boolean supportSql = properties.isSupportSql(sql);
////        assertTrue(supportSql);
////
////        List<String> b = properties.getSystemTables(sql);
////        assertTrue(CollectionUtils.isNotEmpty(b));
////
////        boolean insertSql = properties.isInsertSql(sql);
////        assertFalse(insertSql);
////
////        boolean containWhere = properties.isContainWhere(sql);
////        assertTrue(containWhere);
////    }
//
//    @Test
//    void testAntlr() {
//        String sql = "SELECT users.id, users.name FROM users JOIN information_schema.tables ON users.id = information_schema.tables.table_id";
//
//        // 创建ANTLR输入流
//        CharStream charStream = CharStreams.fromString(sql);
//
//        // 创建SQL词法分析器
//        SQLLexer lexer = new SQLLexer(charStream);
//
//        // 创建词法分析器的输入缓冲区
//        CommonTokenStream tokens = new CommonTokenStream(lexer);
//
//        // 创建SQL语法分析器
//        SQLParser parser = new SQLParser(tokens);
//
//        // 开始语法分析
//        ParseTree tree = parser.select_statement();
//
//        // 创建遍历解析树的访问者
//        ParseTreeWalker walker = new ParseTreeWalker();
//
//        // 遍历解析树并执行我们的访问者
//        walker.walk(new SQLParserBaseListener() {
//            @Override
//            public void enterParseFrom(SQLParser.ParseFromContext ctx) {
//                System.out.println(ctx.getText());
//                System.out.println("FROM子句被处理");
//            }
//
//            @Override
//            public void enterParseSelectStatement(SQLParser.ParseSelectStatementContext ctx) {
//                String tableName = ctx.getText();
//                if (tableName.startsWith("mysql.")) {
//                    System.out.println("MySQL系统表被处理: " + tableName);
//                }
//            }
//        }, tree);
//    }
//}
