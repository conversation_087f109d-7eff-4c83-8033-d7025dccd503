{"title": "list", "key": "batch$student_setting_test:list", "type": "LIST", "conditionGroups": {"edit": {"type": "ConditionGroup", "id": "edit", "conditions": [{"type": "ConditionGroup", "id": "vsYy_KuMOTrWA5_g5TT8k", "conditions": [{"type": "ConditionLeaf", "id": "edit$edit", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "aa"}, "rightValues": null}, {"type": "ConditionLeaf", "id": "remove", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "NEQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "OR"}, {"type": "ConditionGroup", "id": "Bp4CgRv2HDGF8g6t0f2WW", "conditions": [{"type": "ConditionLeaf", "id": "xsM-SGCc0g1MKOg145JbL", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "remove": {"type": "ConditionGroup", "id": "remove", "conditions": [{"type": "ConditionGroup", "id": "removeGroup", "conditions": [{"type": "ConditionLeaf", "id": "remove1", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "aa"}, "rightValues": null}, {"type": "ConditionLeaf", "id": "remove", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "NEQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "OR"}], "logicOperator": "OR"}}}