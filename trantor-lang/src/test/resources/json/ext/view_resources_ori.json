{"title": "list", "key": "batch$student_setting_test:list", "type": "LIST", "resources": [{"type": "Container", "description": null, "key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}], "relations": [{"type": "SystemService", "key": "batch$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "batch$test_tem_is_282_3", "httpMethod": null}}, {"type": "SystemService", "key": "batch$SYS_PagingDataService", "name": null, "props": {"modelAlias": "batch$test_tem_is_282_3", "httpMethod": null}}, {"type": "SystemService", "key": "batch$SYS_PagingDataService", "name": null, "props": {"modelAlias": "batch$student_setting_relation", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$remove1", "label": "新建", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$remove2", "label": "新建", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$student_setting_test-batch-actions-1-button-2", "label": "删除", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_BatchDeleteDataService", "name": null, "props": {"modelAlias": "batch$student_setting_relation", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$edit", "label": "导出", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$student_setting_test-batch-actions-1-button-3/exportButtonService", "label": "组件调用服务", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "batch$student_setting_test-batch-actions-1-button-3", "label": "导出", "type": "ExportButton"}], "relations": [{"type": "Service", "key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$student_setting_test-record-actions-1-button-1", "label": "查看", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$student_setting_test-record-actions-1-button-2", "label": "编辑", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$student_setting_test-record-actions-1-button-3", "label": "删除", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_DeleteDataByIdService", "name": null, "props": {"modelAlias": "batch$student_setting_relation", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$student_setting_test-list-batch$student_setting_relation-logs", "label": "日志", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-toolbar-actions-1", "label": "按钮组", "type": "ToolbarActions"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$student_setting_test-list-batch$student_setting_relation-logs/logsService", "label": "组件调用服务", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-toolbar-actions-1", "label": "按钮组", "type": "ToolbarActions"}, {"key": "batch$student_setting_test-list-batch$student_setting_relation-logs", "label": "日志", "type": "Logs"}], "relations": [{"type": "Service", "key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "batch$student_setting_test-pPoVJurnS7KpWV0SsKqAG", "label": "名称", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-g9BeFCe46CK8GnFX8ptZD", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$student_setting_test-oULSjUMAYDCpkHsVMV9oE", "label": "配置", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-g9BeFCe46CK8GnFX8ptZD", "label": "字段组", "type": "Fields"}], "relations": [{"type": "SystemService", "key": "batch$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "batch$test_tem_is_282_3", "httpMethod": null}}, {"type": "SystemService", "key": "batch$SYS_PagingDataService", "name": null, "props": {"modelAlias": "batch$test_tem_is_282_3", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "batch$student_setting_test-pylh5kOAyGhxkayHQqZzc", "label": "创建人", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-g9BeFCe46CK8GnFX8ptZD", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$student_setting_test-r9Z4egbtjPYbwzKUWzGWf", "label": "用户名", "path": [{"key": "batch$student_setting_test-list", "label": "页面", "type": "Page"}, {"key": "batch$student_setting_test-table-container-batch$student_setting_relation", "label": "表格", "type": "Table"}, {"key": "batch$student_setting_test-g9BeFCe46CK8GnFX8ptZD", "label": "字段组", "type": "Fields"}], "relations": []}]}