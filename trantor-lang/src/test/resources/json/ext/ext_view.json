{"frontendConfig": {"modules": ["base"]}, "title": "list", "key": "batch$jjjkk:list", "content": {"children": [{"extProps": {"appended": true}, "children": [], "key": "addNewChildViewExpectAppendLast", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "batch$jjjkk-R3XExLAOU17itS2ynO-Fg", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"draggable": true, "page": {"key": "create_modal_simple_define-batch$sales_order_1114006", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "Modal"}}, "label": "编辑"}}, {"children": [], "key": "batch$jjjkk-NU3OG2BcN9fPdTyc2OZVk", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "batch$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "batch$sales_order_1114006-table"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用", "show$": "record?.status === \"UNENABLED\" || record?.status === \"DISENABLED\""}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-xvF3C_uujzHuHzr-Jn3W-", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "batch$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "batch$sales_order_1114006-table"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用", "show$": "record?.status === \"ENABLED\""}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-ycd9Z12rLRKUblZQD6_zL", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "batch$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "label": "删除"}}, {"children": [], "key": "batch$jjjkk-diY1DJCuIv_ktgvmLUYmD", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"draggable": true, "page": {"key": "create_modal_simple_define-batch$sales_order_1114006", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record?.id", "name": "copyId", "type": "expression"}], "type": "Modal"}}, "label": "复制", "type": "text"}, "type": "Widget"}], "key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "name": "RecordActions", "props": {"label": "操作"}}, {"children": [{"children": [], "key": "batch$jjjkk-dgCt90kgptGgR5dBCYTN0", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"draggable": true, "page": {"key": "create_modal_simple_define-batch$sales_order_1114006", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "Modal"}}, "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-Wn5fhqK_3v1RVVspsAt1P", "name": "<PERSON><PERSON>", "props": {"actionConfig1": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "batch$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "batch$sales_order_1114006-table"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "停用"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-4IdZijaBImyrJTTmHITzK", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "batch$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "batch$sales_order_1114006-table"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "启用"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-vxqgNIYv0Jw4aBOOHHigg", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "batch$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "删除"}, "type": "Widget"}], "key": "batch$jjjkk-C-Ip0szY1aVJNSUTuRqKQ", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "batch$jjjkk-NpVdHVQnw5T0OY0782_QA", "name": "Field", "props": {"componentProps": {"fieldAlias": "documentName", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入"}, "label": "单据名称", "name": "documentName", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-ytI1Nmq1IvzfHEJjXqNJy", "name": "Field", "props": {"componentProps": {"fieldAlias": "documentNumber", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入"}, "label": "单据编号", "name": "documentNumber", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-sSgpnIr75T4ugOt-51orL", "name": "Field", "props": {"componentProps": {"fieldAlias": "documentDate", "modelAlias": "batch$sales_order_1114006", "placeholder": "请选择"}, "label": "单据日期", "name": "documentDate", "type": "DATE", "width": 134}, "type": "Widget"}, {"ext": true, "children": [], "key": "batch$add", "name": "Field", "props": {"componentProps": {"fieldAlias": "documentDate", "modelAlias": "batch$sales_order_1114006", "placeholder": "请选择"}, "label": "新家的字段", "name": "documentDate", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-X1aSG7X1H6VOsAyP7II46", "name": "Field", "props": {"componentProps": {"fieldAlias": "exchangeRate", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入", "precision": 6}, "label": "汇率", "name": "exchangeRate", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-BeNBv3mmnjEkawxlo6Q44", "name": "Field", "props": {"componentProps": {"fieldAlias": "approvalStatus", "modelAlias": "batch$sales_order_1114006", "placeholder": "请选择"}, "label": "审批状态", "name": "approvalStatus", "type": "SELECT", "width": 116}, "type": "Widget"}], "key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "name": "Fields", "props": {}, "type": "Meta"}], "key": "batch$sales_order_1114006-table", "name": "Table", "props": {"flow$": "params => invokeSystemServiceQuery('batch$SYS_PagingDataService', 'batch$sales_order_1114006', params)", "modelAlias": "batch$sales_order_1114006"}, "type": "Container"}, {"extProps": {"appended": true}, "children": [], "key": "addNewChildView", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false}, "type": "Container"}, {"children": [{"children": [{"children": [{"key": "hhhhhh", "props": {}, "extProps": {"removed": true}}], "key": "batch$jjjkk-3dxPKEvHEQUWUaWQH1Jf2", "name": "Text", "props": {"value$": "!!parentRecord?.id && !params?.copyId ? \"编辑销售订单1114006\" : \"新建销售订单1114006\""}, "extProps": {"removed": true}}], "key": "batch$jjjkk-jQG39wivDUexU4F0Dj-At", "name": "Child<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC", "name": "FormField", "props": {"componentProps": {"fieldAlias": "documentName", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入"}, "label": "单据名称", "name": "documentName", "rules": [{"message": "请输入单据名称", "required": true}], "type": "TEXT"}, "extProps": {"removed": true}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-ck3-Ci5lJ6XQgmNsGhtvR", "name": "FormField", "props": {"componentProps": {"fieldAlias": "documentNumber", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入"}, "label": "单据编号", "name": "documentNumber", "rules": [{"message": "请输入单据编号", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-IlNw-xjIkF4iIOm5Z9wui", "name": "FormField", "props": {"componentProps": {"fieldAlias": "documentDate", "modelAlias": "batch$sales_order_1114006", "placeholder": "请选择"}, "label": "单据日期", "name": "documentDate", "rules": [{"message": "请输入单据日期", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-diJ2Sz86Wi75N6wpCYIMj", "name": "FormField", "props": {"componentProps": {"fieldAlias": "exchangeRate", "modelAlias": "batch$sales_order_1114006", "placeholder": "请输入", "precision": 6}, "label": "汇率", "name": "exchangeRate", "rules": [{"message": "请输入汇率", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-_6vlwGeOmUcak3pX6wedL", "name": "FormField", "props": {"componentProps": {"fieldAlias": "approvalStatus", "modelAlias": "batch$sales_order_1114006", "placeholder": "请选择"}, "label": "审批状态", "name": "approvalStatus", "rules": [{"message": "请输入审批状态", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "batch$jjjkk-fd8JEhUxWPED43edI7O9n", "name": "FormField", "props": {"hidden": "true", "label": "id", "name": "id", "type": "Input"}, "type": "Widget"}], "key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "name": "FormGroupItem", "props": {}, "type": "Layout"}], "key": "simple_define_form_batch$sales_order_1114006", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "simple_define_form_batch$sales_order_1114006", "modelAlias": "batch$sales_order_1114006", "params$": "{ id: parentRecord.id }", "serviceKey": "batch$SYS_FindDataByIdService", "test": "!!parentRecord?.id", "type": "InvokeSystemService"}, {"containerKey": "simple_define_form_batch$sales_order_1114006", "modelAlias": "batch$sales_order_1114006", "params$": "{ id: params?.copyId }", "serviceKey": "batch$SYS_CopyDataConverterService", "test": "!!params?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}}, "type": "Container"}], "key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "batch$jjjkk-3GpOKFGhLgERyAN6ToqR0", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": "ROOT"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消"}, "type": "Widget"}, {"children": [{"children": [], "key": "batch$jjjkk-CHsyquBL1ymW9wdONJLqx", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"action": {"target": "simple_define_form_batch$sales_order_1114006"}, "name": "request", "type": "action"}], "service": "batch$SYS_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "batch$sales_order_1114006-table"}, {"action": "Refresh", "target": "simple_define_form_batch$sales_order_1114006"}, {"action": "Message", "level": "success", "message": "新建成功"}], "executeLogic": "BindService"}, "buttonType": "default", "label": "提交并新建"}, "type": "Widget"}], "key": "batch$jjjkk-SY0eE5AFvOuuksL0z1cwt", "name": "Show", "props": {"value$": "params?.copyId || !parentRecord?.id"}, "type": "Meta"}, {"children": [], "key": "batch$jjjkk-Z_2R_fQHYUQjrhf4_oPvm", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "batch$sales_order_1114006"}, {"action": {"target": "simple_define_form_batch$sales_order_1114006"}, "name": "request", "type": "action"}], "service": "batch$SYS_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": "ROOT"}, {"action": "Message", "level": "success", "message$": "!!parentRecord?.id && !params?.copyId ? \"编辑成功\" : \"新建成功\""}, {"action": "Refresh", "target": "batch$sales_order_1114006-table"}], "executeLogic": "BindService"}, "buttonType": "primary", "label": "提交"}, "type": "Widget"}], "key": "batch$jjjkk--f98kBWXNvibNLZVYMPfX", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "create_modal_simple_define-batch$sales_order_1114006", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false}, "type": "Container"}], "key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "name": "Page", "props": {"showHeader": false, "title": "测试1111"}, "type": "Container"}, "type": "LIST", "resources": [{"type": "Container", "description": null, "key": "batch$sales_order_1114006-table", "label": "表格", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-R3XExLAOU17itS2ynO-Fg", "label": "编辑", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "label": "按钮组", "type": "RecordActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-NU3OG2BcN9fPdTyc2OZVk", "label": "启用", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "label": "按钮组", "type": "RecordActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_MasterData_EnableDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-xvF3C_uujzHuHzr-Jn3W-", "label": "停用", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "label": "按钮组", "type": "RecordActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_MasterData_DisableDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-ycd9Z12rLRKUblZQD6_zL", "label": "删除", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "label": "按钮组", "type": "RecordActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_DeleteDataByIdService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-diY1DJCuIv_ktgvmLUYmD", "label": "复制", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-fAdiIrNETeCTbmYj-J59J", "label": "按钮组", "type": "RecordActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-dgCt90kgptGgR5dBCYTN0", "label": "新建", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-C-Ip0szY1aVJNSUTuRqKQ", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-Wn5fhqK_3v1RVVspsAt1P", "label": "停用", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-C-Ip0szY1aVJNSUTuRqKQ", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-4IdZijaBImyrJTTmHITzK", "label": "启用", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-C-Ip0szY1aVJNSUTuRqKQ", "label": "按钮组", "type": "BatchActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_MasterData_MultiEnableDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-vxqgNIYv0Jw4aBOOHHigg", "label": "删除", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-C-Ip0szY1aVJNSUTuRqKQ", "label": "按钮组", "type": "BatchActions"}], "relations": [{"type": "SystemService", "key": "batch$SYS_BatchDeleteDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "batch$jjjkk-NpVdHVQnw5T0OY0782_QA", "label": "单据名称", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-ytI1Nmq1IvzfHEJjXqNJy", "label": "单据编号", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-sSgpnIr75T4ugOt-51orL", "label": "单据日期", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-X1aSG7X1H6VOsAyP7II46", "label": "汇率", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-BeNBv3mmnjEkawxlo6Q44", "label": "审批状态", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "batch$sales_order_1114006-table", "label": "表格", "type": "Table"}, {"key": "batch$jjjkk-DGmJbBbbVXUJZHSDR8Y-W", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"type": "SystemService", "key": "batch$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}, {"type": "SystemService", "key": "batch$SYS_CopyDataConverterService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-3GpOKFGhLgERyAN6ToqR0", "label": "取消", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk--f98kBWXNvibNLZVYMPfX", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-Z_2R_fQHYUQjrhf4_oPvm", "label": "提交", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk--f98kBWXNvibNLZVYMPfX", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"type": "SystemService", "key": "batch$SYS_SaveDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "batch$jjjkk-CHsyquBL1ymW9wdONJLqx", "label": "提交并新建", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk--f98kBWXNvibNLZVYMPfX", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-SY0eE5AFvOuuksL0z1cwt", "label": "展示逻辑", "type": "Show"}], "relations": [{"type": "SystemService", "key": "batch$SYS_SaveDataService", "name": null, "props": {"modelAlias": "batch$sales_order_1114006", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC", "label": "单据名称", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-ck3-Ci5lJ6XQgmNsGhtvR", "label": "单据编号", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-IlNw-xjIkF4iIOm5Z9wui", "label": "单据日期", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-diJ2Sz86Wi75N6wpCYIMj", "label": "汇率", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-_6vlwGeOmUcak3pX6wedL", "label": "审批状态", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "batch$jjjkk-fd8JEhUxWPED43edI7O9n", "label": "id", "path": [{"key": "batch$jjjkk-tkKbq-qj7yVQYMJBy6mg0", "label": "页面", "type": "Page"}, {"key": "create_modal_simple_define-batch$sales_order_1114006", "label": "弹窗", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "batch$jjjkk-xKYfvCj2gCYhkcsigHFCh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "simple_define_form_batch$sales_order_1114006", "label": "表单组", "type": "FormGroup"}, {"key": "batch$jjjkk-NgkmCNDfTY2SqRdxv0EOQ", "label": "表单组元素", "type": "FormGroupItem"}], "relations": []}], "containerSelect": {"batch$sales_order_1114006-table": [{"field": "documentName", "selectFields": null}, {"field": "documentNumber", "selectFields": null}, {"field": "documentDate", "selectFields": null}, {"field": "exchangeRate", "selectFields": null}, {"field": "approvalStatus", "selectFields": null}], "simple_define_form_batch$sales_order_1114006": []}}