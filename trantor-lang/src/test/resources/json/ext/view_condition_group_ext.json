{"title": "list", "key": "batch$student_setting_test:list", "type": "LIST", "conditionGroups": {"edit": {"type": "ConditionGroup", "id": "edit", "conditions": [{"type": "ConditionGroup", "id": "vsYy_KuMOTrWA5_g5TT8k", "conditions": [{"type": "ConditionLeaf", "id": "edit$edit", "key": null, "leftValue": {"type": "VarValue", "id": null, "scope": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "abc"}, "rightValues": null}, {"type": "ConditionLeaf", "id": "add", "key": null, "leftValue": {"scope": null, "type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "NEQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "OR"}, {"type": "ConditionGroup", "id": "Bp4CgRv2HDGF8g6t0f2WW", "conditions": [{"type": "ConditionLeaf", "id": "xsM-SGCc0g1MKOg145JbL", "key": null, "leftValue": {"scope": null, "type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "OR"}], "logicOperator": "OR"}, "add": {"type": "ConditionGroup", "id": "add", "conditions": [{"type": "ConditionGroup", "id": "add1", "conditions": [{"type": "ConditionLeaf", "id": "add$add", "key": null, "leftValue": {"scope": null, "type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "abc"}, "rightValues": null}, {"type": "ConditionLeaf", "id": "add", "key": null, "leftValue": {"scope": null, "type": "VarValue", "id": null, "varValue": [{"valueKey": "name", "valueName": "name", "fieldType": null}], "valueType": "VAR", "fieldType": "Text"}, "operator": "NEQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "a"}, "rightValues": null}], "logicOperator": "OR"}], "logicOperator": "OR"}}}