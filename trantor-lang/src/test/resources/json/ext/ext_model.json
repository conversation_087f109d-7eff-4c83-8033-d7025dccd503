{"oid": "40f59b19be4a570fa55c301eaa9571b59d04799b36a13bb881d659b5fb305430", "key": "moduleA$amodel", "name": "一个模型", "createdBy": 435397539820229, "createdAt": 1719282779214, "updatedBy": 435397539820229, "updatedAt": 1719368288104, "parentKey": "moduleA$ungroup", "teamId": 1203, "teamCode": "wjxtest", "type": "Model", "props": {"children": [{"key": "number", "name": "数字", "alias": "number", "props": {"fieldType": "NUMBER", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "数字", "columnName": "number", "intLength": 20, "scale": 6, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "textte", "name": "文本", "alias": "textte", "props": {"fieldType": "TEXT", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "文本", "columnName": "textte", "length": 256, "encrypted": true, "desensitizedRule": "DES_ID_CARD"}, "ext": false, "type": "DataStructField"}, {"key": "enumenu", "name": "枚举", "alias": "enumenu", "props": {"fieldType": "ENUM", "required": false, "unique": false, "compositeKey": false, "isSystemField": false, "autoGenerated": false, "comment": "枚举", "columnName": "enumenu", "length": 256, "dictPros": {"multiSelect": false, "properties": null, "dictValues": [{"_row_id_": "ggLosux", "label": "苹果", "value": "apple"}, {"_row_id_": "buGKx0s", "label": "香蕉", "value": "banana"}, {"_row_id_": "ogXN8r2", "label": "蓝莓", "value": "blueberry"}, {"_row_id_": "ogXN8r1", "label": "樱桃", "value": "cherry"}, {"_row_id_": "ogXN8r3", "label": "榴莲", "value": "durian"}]}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "id", "name": "ID", "alias": "id", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "ID", "columnName": "id", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "created_by", "name": "创建人", "alias": "created<PERSON>y", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建人", "columnName": "created_by", "length": 20, "relationMeta": {"relationKey": null, "relationType": "LINK", "currentModelAlias": "moduleA$amodel", "currentModelFieldAlias": "created<PERSON>y", "relationModelAlias": "moduleA$user", "linkModelFieldAlias": null, "sync": false, "relationModelKey": "moduleA$user", "linkModelAlias": null}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "updated_by", "name": "更新人", "alias": "updatedBy", "props": {"fieldType": "OBJECT", "required": false, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新人", "columnName": "updated_by", "length": 20, "relationMeta": {"relationKey": null, "relationType": "LINK", "currentModelAlias": "moduleA$amodel", "currentModelFieldAlias": "updatedBy", "relationModelAlias": "moduleA$user", "linkModelFieldAlias": null, "sync": false, "relationModelKey": "moduleA$user", "linkModelAlias": null}, "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "created_at", "name": "创建时间", "alias": "createdAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "创建时间", "columnName": "created_at", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "updated_at", "name": "更新时间", "alias": "updatedAt", "props": {"fieldType": "DATE", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "comment": "更新时间", "columnName": "updated_at", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "version", "name": "版本号", "alias": "version", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "版本号", "columnName": "version", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}, {"key": "deleted", "name": "逻辑删除标识", "alias": "deleted", "props": {"fieldType": "NUMBER", "required": true, "unique": true, "compositeKey": false, "isSystemField": true, "autoGenerated": false, "defaultValue": 0, "comment": "逻辑删除标识", "columnName": "deleted", "length": 20, "intLength": 20, "numberDisplayType": "digit", "encrypted": false}, "ext": false, "type": "DataStructField"}], "desc": null, "alias": "moduleA$amodel", "props": {"config": {"persist": false, "system": false, "self": false, "selfRelationFieldAlias": null}, "tableName": "amodel", "mainField": "textte", "type": "PERSIST", "physicalDelete": false, "originOrgIdEnabled": false, "orderNumberEnabled": false, "shardingConfig": {"enabled": false, "shardingSuffixLength": 3, "shardingFields": null}, "searchModel": false, "mainFieldAlias": "textte"}}}