[{"action": "APPEND", "key": "addNewChildViewExpectAppendLast", "validScope": "ALL"}, {"action": "APPEND", "func": {"params": {"offset": 1, "origin": "batch$jjjkk-sSgpnIr75T4ugOt-51orL", "type": "RelativePosition", "fallbackIndex": -1}, "type": "INSERT_AT_INDEX"}, "key": "batch$add", "validScope": "ALL"}, {"action": "APPEND", "func": {"params": {"offset": 1, "origin": "batch$sales_order_1114006-table", "type": "RelativePosition", "fallbackIndex": -1}, "type": "INSERT_AT_INDEX"}, "key": "addNewChildView", "validScope": "ALL"}, {"key": "batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC", "action": "EDIT", "path": "extProps/removed", "validScope": "ALL", "func": {"type": "SET_JSON_VALUE", "params": {"type": "Const", "constant": true}}}, {"key": "batch$jjjkk-kZdvz2Q6apJaT8aCV4hrC", "action": "REMOVE", "validScope": "RUNTIME"}, {"key": "batch$jjjkk-3dxPKEvHEQUWUaWQH1Jf2", "action": "EDIT", "path": "extProps/removed", "validScope": "ALL", "func": {"type": "SET_JSON_VALUE", "params": {"type": "Const", "constant": true}}}, {"key": "batch$jjjkk-3dxPKEvHEQUWUaWQH1Jf2", "action": "REMOVE", "validScope": "RUNTIME"}]