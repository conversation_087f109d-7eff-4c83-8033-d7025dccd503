{"key": "abc$xyz", "sceneConfig": {"i18nConfig": {"i18nKeySet": ["delete", "复制", "表格", "系统信息", "自动查询测试模型列表", "新建", "删除成功", "delete2", "请输入版本号", "名称", "查看", "是否删除选中单据？", "请输入创建时间", "删除", "保存", "请输入ID", "ID", "创建人", "年龄", "请输入更新时间", "自动查询测试模型详情", "逻辑删除标识", "自动查询测试模型", "请选择", "版本号", "更新时间", "更新人", "编辑", "所属组织", "创建", "选择创建人", "请输入", "取消", "创建时间", "请输入逻辑删除标识", "确认删除吗？", "保存成功！"], "i18nScanPaths": ["Table.props.filterFields.componentProps.label", "Field.props.label", "Field.props.componentProps.placeholder", "FormField.props.label", "FormField.props.componentProps.placeholder", "DetailField.props.componentProps.label", "Table.props.filterFields.componentProps.placeholder", "@exp:PageTitle.props.title", "Button.props.actionConfig.endLogicOtherConfig.message", "DetailField.props.label", "FormField.props.componentProps.label", "Button.props.actionConfig.beforeLogicConfig.text", "Table.props.filterFields.label", "Field.props.componentProps.label", "Button.props.label", "Table.props.label", "DetailField.props.componentProps.placeholder", "Tabs.props.items.label", "FormField.props.rules.delete"]}, "usedModelAlias": ["batch$user", "delete", "batch$created_by_relation_test"]}}