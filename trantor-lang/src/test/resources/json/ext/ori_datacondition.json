{"oid": "3328bacdd932d8e83c664349f899705ae012675a1bd43605d24c582f22072473", "key": "zjt_test$adfasdfa:cccc", "name": "测试", "id": 3434620, "createdBy": 435397539820229, "createdAt": 1725591008873, "updatedBy": 435397539820229, "updatedAt": 1725938960496, "parentKey": "zjt_test", "teamId": 123, "teamCode": "PJ0724", "appId": 3333462, "customExt": false, "extensible": false, "metaType": "DataCondition", "props": {"modelKey": "zjt_test$adfasdfa", "condition": {"type": "ConditionGroup", "id": "uiz-M3KIId7f_wOQiNQn5", "conditions": [{"type": "ConditionGroup", "id": "_BldHy4l4joNO8wyBk0z0", "conditions": [{"type": "ConditionLeaf", "id": "5X130Y07wPQhFyZ4OFTZi", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "version", "valueName": "版本号", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": null, "rightValues": null}, {"type": "ConditionLeaf", "id": "RfbRMqH1qLbCg7NSOib9R", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "deleted", "valueName": "逻辑删除标识", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": null, "rightValues": null}], "logicOperator": "AND"}, {"type": "ConditionGroup", "id": "QR5GVCjDu_AiziiEhX-4I", "conditions": [{"type": "ConditionLeaf", "id": "ILsH0rSlBGsUdAzeJxY3K", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "version", "valueName": "版本号", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "Number"}, "operator": "IN", "rightValue": null, "rightValues": null}, {"type": "ConditionLeaf", "id": "yAnrd9-E7yakMzOk92sVY", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "version", "valueName": "版本号", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": null, "rightValues": null}, {"type": "ConditionLeaf", "id": "aIDd_Y7AdaQuOC7K-y4zp", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "updatedAt", "valueName": "更新时间", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "DateTime"}, "operator": "EQ", "rightValue": null, "rightValues": null}], "logicOperator": "OR"}, {"type": "ConditionGroup", "id": "90zwX34K9YdRS28Wnq4HD", "conditions": [{"type": "ConditionLeaf", "id": "VwRsd5cqyyicYtg0bNhWa", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "updatedAt", "valueName": "更新时间", "fieldType": null, "modelAlias": "zjt_test$adfasdfa"}], "scope": null, "valueType": "MODEL", "fieldType": "DateTime"}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "componentProps": {"showType": "relationSelect", "fields": ["username"], "filterFields": ["username"]}}}