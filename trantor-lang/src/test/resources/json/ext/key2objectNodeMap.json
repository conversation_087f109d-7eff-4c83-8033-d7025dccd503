{"a": {"key": "a", "name": "a", "props": {"title": "a"}}, "b": {"key": "b", "name": "b", "children": [{"key": "b1", "name": "b1", "props": {"title": "b1"}}, {"key": "b2", "name": "b2", "props": {"title": "b2"}}, {"key": "b3", "name": "b3", "props": {"title": "b3"}}, {"key": "b4", "name": "b4", "props": {"title": "b4"}}], "props": {"title": "b"}}, "b1": {"key": "b1", "name": "b1", "props": {"title": "b1"}}, "b2": {"key": "b2", "name": "b2", "props": {"title": "b2"}}, "b3": {"key": "b3", "name": "b3", "props": {"title": "b3"}}, "b4": {"key": "b4", "name": "b4", "props": {"title": "b4"}}, "e": {"key": "e", "name": "e"}, "TB2B$mall_distributor_addr": {"key": "TB2B$mall_distributor_addr", "name": "Table", "props": {"label": "old字段名", "acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "editComponentProps": {"options": [{"label": "苹果", "value": "apple", "enabled": true, "isDefault": false}, {"label": "香蕉", "value": "banana", "enabled": true, "isDefault": false}], "showScope": "all", "tableCondition": null, "tableConditionContext$": null, "labelField": "addrName"}, "filterFields": [{"label": "地址类型", "name": "addressId.addrType", "type": "SELECT", "lookup": [{"key": "AJgbUrQNhpXGLgqtZmM39", "fieldRules": {"readOnly": false, "hidden": false, "required": false}, "valueRules": null, "trigger": "auto"}], "width": 120, "componentProps": {"placeholder": "请选择", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "fieldAlias": "addrType"}, "editComponentType": "Select", "editComponentProps": {"options": [{"label": "省", "value": "PROVINCE", "enabled": true, "isDefault": false}, {"label": "市", "value": "CITY", "enabled": true, "isDefault": false}, {"label": "区", "value": "DISTRICT", "enabled": true, "isDefault": false}], "showScope": "all", "tableCondition": null, "tableConditionContext$": null, "labelField": "addrName"}}]}}}