{"desc": "12312", "relatedModelConfig": null, "relatedModel": null, "trantorEventListeners": [{"key": null, "name": "11231", "workflowEvent": {"eventType": "PROCESS_STARTED"}, "relatedEvent": {"eventCode": "mall_management_module$MALL_ITEM_SHELFON", "eventName": "商品上下架管理-上架", "inputModelKey": "mall_management_module$gen_item_item_md"}, "relatedEventService": null, "relatedService": null, "carryApprovalRemark": null}, {"key": null, "name": "chief12", "workflowEvent": {"eventType": "PROCESS_COMPLETED"}, "relatedEvent": {"eventCode": "mall_management_module$MER_CREATE_SHORTCUT_EVENT", "eventName": "新建商家", "inputModelKey": "mall_management_module$gen_mer_merchant_md"}, "relatedEventService": null, "relatedService": null, "carryApprovalRemark": null}], "decisionServiceConfig": null, "nodeCallbackServiceConfig": null, "customVariables": [{"fieldKey": "a", "fieldName": "b", "fieldType": "Text", "required": false}, {"fieldKey": "c", "fieldName": "c", "fieldType": "Number", "required": true}, {"fieldKey": "d", "fieldName": "d", "fieldType": "Number", "required": true}], "channelType": "TRANTOR_WORKFLOW", "formTemplate": null, "attachmentConfig": {"maxNumberOfFiles": null, "maxFileSize": null, "allowedFileFormats": ["Images", "Documents"], "customFormats": null, "inputPrompt": null}}