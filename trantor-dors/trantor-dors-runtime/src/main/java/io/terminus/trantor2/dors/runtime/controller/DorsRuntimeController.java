package io.terminus.trantor2.dors.runtime.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.dors.common.param.DorsDataResult;
import io.terminus.trantor2.dors.common.param.DorsModuleExtCondition;
import io.terminus.trantor2.dors.common.param.DorsQueryParam;
import io.terminus.trantor2.dors.common.service.DorsQueryService;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/19
 */
@RestController
@Slf4j
@RequestMapping("/api/trantor/runtime/report")
@RequiredArgsConstructor
@TrantorServiceRegistry
public class DorsRuntimeController {
    private static final String API_TITLE = "报表-";
    private final DorsQueryService dorsQueryService;

    @PostMapping("/query")
    @Operation(summary = API_TITLE + "查询")
    public Response<DorsDataResult> query(@RequestBody @Validated DorsQueryParam param, HttpServletRequest request) {
        List<DorsModuleExtCondition> dorsModuleExtConditions = new ArrayList<>();
        String msg = null;
        try {
            dorsModuleExtConditions = dorsQueryService.queryExtCondition(request, param.getModuleKey());
        } catch (Exception e) {
            msg = e.getMessage();
        }
        param.setModuleExtConditions(dorsModuleExtConditions);
        DorsDataResult query = dorsQueryService.query(param, TrantorContext.getTeamCode());
        query.setSql(null);// 运行时不返回sql
        if (msg != null) {
            query.setMsg(query.getMsg() + msg);
        }
        return Response.ok(query);
    }
}
