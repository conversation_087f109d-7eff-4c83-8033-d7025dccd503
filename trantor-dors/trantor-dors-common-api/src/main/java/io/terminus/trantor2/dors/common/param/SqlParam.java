package io.terminus.trantor2.dors.common.param;

import lombok.Data;

import java.util.Objects;


/**
 * <AUTHOR>
 **/
@Data
public class SqlParam {
    private String key;
    private String type;
    private String format;
    private Object defaultValue;
    private Object testValue;

    public Object getParamsValue() {
        if (Objects.isNull(testValue) && Objects.isNull(defaultValue)) {
            return "";
        }
        return testValue != null ? testValue : defaultValue;
    }
}
