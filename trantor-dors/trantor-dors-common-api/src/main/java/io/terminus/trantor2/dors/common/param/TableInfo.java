package io.terminus.trantor2.dors.common.param;

import io.terminus.trantor2.dors.common.util.TableFieldTypeUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
public class TableInfo {
    private String sql;
    private List<TableColumn> fields;
    private List<Map<String, Object>> values;
    private String error;

    public static TableInfo from(String sql, List<Map<String, Object>> result) {
        TableInfo tableInfo = new TableInfo();
        tableInfo.setSql(sql);
        tableInfo.setValues(result);
        List<TableColumn> fields = new ArrayList<>();
        tableInfo.setFields(fields);
        if (CollectionUtils.isEmpty(result)) {
            return tableInfo;
        }
        result.get(0).forEach((k, v) -> {
            TableColumn column = new TableColumn();
            column.setName(k);
            column.setDataType(TableFieldTypeUtil.dataType(v).name());
            fields.add(column);
        });
        return tableInfo;
    }
}
