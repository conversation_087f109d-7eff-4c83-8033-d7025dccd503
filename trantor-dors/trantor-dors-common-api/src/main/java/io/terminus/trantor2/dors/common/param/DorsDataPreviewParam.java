package io.terminus.trantor2.dors.common.param;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/19
 */
@Data
public class DorsDataPreviewParam implements Serializable {

    /**
     * 数据源
     */
    @NotBlank(message = "数据源不能为空")
    private String source;

    /**
     * 页序，如果不传则是第一页
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer pageSize;

}
