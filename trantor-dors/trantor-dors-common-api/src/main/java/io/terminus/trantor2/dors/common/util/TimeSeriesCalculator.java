package io.terminus.trantor2.dors.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.time.temporal.WeekFields;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 累计，同比，环比计算工具类
 * 包含日期格式转换和时间序列计算（累计、同比、环比）
 *
 * <AUTHOR>
 * @since 2024/11/06
 */
@UtilityClass
public class TimeSeriesCalculator {
    private static final String WEEK_PATTERN = "W";
    private static final String QUARTER_PATTERN = "Q";

    public enum TimeFormat {
        DATE,     // 日期: 2024-12-12 (最小粒度)
        WEEK,     // 周: 2024W34
        MONTH,    // 月: 2024-12
        QUARTER,  // 季度: 2024Q2
        YEAR,     // 年: 2024 (最大粒度)
        ALL       // 历年（仅累计计算使用）
    }

    public enum ComputingType {
        accumulate,     // 累计  week mouth，quarter，year,all
        onBasis,        // 同比  week,mouth，year
        relativeBasis   // 环比  和当前日期维度一致
    }

    public enum ComputingMethod {
        difference,        // 对比差值
        specificPercent,   // 比值百分比
        differencePercent  // 差值百分比
    }

    /**
     * 处理累计计算
     * 将数据按目标时间维度进行累计，同一时间维度内的数据累加
     * 当 targetTimeDimension 为 "ALL" 时，进行历史累计，从最早时间开始累加到最后
     *
     * @param sqlData             原始数据列表
     * @param timeKeyAlias        时间字段别名
     * @param valueKeyAlias       值字段别名
     * @param targetTimeDimension 目标时间维度，特殊值"ALL"表示历史累计
     * @throws IllegalArgumentException 当进行向下转换时抛出异常
     */
    public static void handleAccumulate(List<Map<String, Object>> sqlData, String timeKeyAlias,
                                        String valueKeyAlias, String targetTimeDimension) {
        if (CollectionUtil.isEmpty(sqlData)) {
            return;
        }

        // 1. 按时间排序
        sortByTime(sqlData, timeKeyAlias);

        // 2. 判断是否是历史累计
        if (TimeFormat.ALL.name().equalsIgnoreCase(targetTimeDimension)) {
            // 历史累计：从第一条数据开始一直累加到最后
            BigDecimal accumulator = BigDecimal.ZERO;
            for (Map<String, Object> row : sqlData) {
                Object value = row.get(valueKeyAlias);
                if (value != null) {
                    BigDecimal currentValue = new BigDecimal(value.toString());
                    accumulator = accumulator.add(currentValue);
                    row.put(valueKeyAlias, accumulator);
                }
            }
            return;
        }

        // 3. 按时间维度累计
        // 获取当前数据的时间维度和目标维度
        String firstTimeStr = String.valueOf(sqlData.get(0).get(timeKeyAlias));
        TimeFormat currentFormat = getFormat(firstTimeStr);
        if (currentFormat == null) {
            throw new IllegalArgumentException("无效的时间格式: " + firstTimeStr);
        }
        TimeFormat targetFormat = TimeFormat.valueOf(targetTimeDimension.toUpperCase());

        // 检查是否是向下转换
        if (isDownwardConversion(currentFormat, targetFormat)) {
            throw new IllegalArgumentException(
                    String.format("不支持向下转换累计: 从 %s 转换到 %s", currentFormat, targetFormat));
        }

        // 进行累计计算
        Map<String, BigDecimal> periodAccumulators = new HashMap<>();

        for (Map<String, Object> row : sqlData) {
            String timeStr = String.valueOf(row.get(timeKeyAlias));
            Object value = row.get(valueKeyAlias);
            if (value != null) {
                String periodInTargetDimension = convert(timeStr, targetFormat);
                BigDecimal currentValue = new BigDecimal(value.toString());
                BigDecimal accumulator = periodAccumulators.getOrDefault(periodInTargetDimension, BigDecimal.ZERO);
                accumulator = accumulator.add(currentValue);
                periodAccumulators.put(periodInTargetDimension, accumulator);
                row.put(valueKeyAlias, accumulator);
            }
        }
    }

    /**
     * 处理时间序列比较计算（同比，环比）
     * 根据目标时间维度进行比较计算，支持跨维度计算
     * 例如：
     * 1. 日期数据(2024-03-15)按月维度比较时，与上月同日(2024-02-15)比较
     * 2. 月份数据(2024-03)按年维度比较时，与去年同月(2023-03)比较
     * 3. 当目标维度与原始数据维度相同时：
     * - 日期数据(2024-03-15)按日维度比较，与前一天(2024-03-14)比较
     * - 月份数据(2024-03)按月维度比较，与上月(2024-02)比较
     * 4. 当目标维度为null时，使用原始数据的时间维度作为目标维度
     *
     * @param sqlData             原始数据列表
     * @param timeKeyAlias        时间字段别名
     * @param valueKeyAlias       值字段别名
     * @param targetTimeDimension 目标时间维度，决定比较间隔（DATE/WEEK/MONTH/QUARTER/YEAR）。如果为null，则使用原始数据的时间维度（也就是环比）
     * @param computingType       计算类型（difference/specificPercent/differencePercent）
     */
    public static void handleTimeSeriesComparison(List<Map<String, Object>> sqlData, String timeKeyAlias,
                                                  String valueKeyAlias, String targetTimeDimension, String computingType) {
        if (CollectionUtil.isEmpty(sqlData)) {
            return;
        }

        // 1. 获取当前数据的时间维度
        String firstTimeStr = String.valueOf(sqlData.get(0).get(timeKeyAlias));
        TimeFormat currentFormat = getFormat(firstTimeStr);
        if (currentFormat == null) {
            throw new IllegalArgumentException("无效的时间格式: " + firstTimeStr);
        }

        // 2. 确定目标维度：如果targetTimeDimension为null，使用当前数据的时间维度（环比）
        TimeFormat targetFormat = CharSequenceUtil.isBlank(targetTimeDimension) ?
                currentFormat : TimeFormat.valueOf(targetTimeDimension.toUpperCase());

        // 3. 检查是否是向下转换
        if (isDownwardConversion(currentFormat, targetFormat)) {
            throw new IllegalArgumentException(
                    String.format("不支持向下转换: 从 %s 转换到 %s", currentFormat, targetFormat));
        }

        // 4. 按时间排序
        sortByTime(sqlData, timeKeyAlias);

        // 5. 构建时间-值映射
        Map<String, BigDecimal> timeValueMap = new HashMap<>();
        for (Map<String, Object> row : sqlData) {
            String timeStr = String.valueOf(row.get(timeKeyAlias));
            Object value = row.get(valueKeyAlias);
            if (value != null) {
                timeValueMap.put(timeStr, new BigDecimal(value.toString()));
            }
        }

        // 6. 计算比较结果
        for (Map<String, Object> row : sqlData) {
            String currentTimeStr = String.valueOf(row.get(timeKeyAlias));
            Object currentValue = row.get(valueKeyAlias);
            if (currentValue != null) {
                // 获取比较时间
                String compareTime = getCompareTime(currentTimeStr, currentFormat, targetFormat);

                // 计算并更新结果
                BigDecimal compareValue = timeValueMap.get(compareTime);
                if (compareValue != null && compareValue.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal result = calculateComparisonResult(
                            new BigDecimal(currentValue.toString()),
                            compareValue,
                            computingType
                    );
                    row.put(valueKeyAlias, result);
                } else {
                    row.put(valueKeyAlias, null);
                }
            }
        }
    }


    /**
     * 获取上一期时间
     * 根据目标时间维度获取上一期的时间
     *
     * @param timeStr       时间字符串
     * @param currentFormat 当前时间格式
     * @param targetFormat  目标时间格式（决定比较间隔）
     * @return 上一期时间字符串（保持原始时间格式）
     */
    private static String getCompareTime(String timeStr, TimeFormat currentFormat, TimeFormat targetFormat) {
        LocalDate date = parseToLocalDate(timeStr, currentFormat);
        LocalDate compareDate;

        switch (targetFormat) {
            case DATE:
                compareDate = date.minusDays(1);
                break;
            case WEEK:
                compareDate = date.minusWeeks(1);
                break;
            case MONTH:
                compareDate = date.minusMonths(1);
                break;
            case QUARTER:
                compareDate = date.minusMonths(3);
                break;
            case YEAR:
                compareDate = date.minusYears(1);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间格式: " + targetFormat);
        }

        return formatDate(compareDate, currentFormat);
    }


    /**
     * 将输入的时间字符串转换为目标格式
     *
     * @param dateStr      输入的时间字符串
     * @param targetFormat 目标格式
     * @return 转换后的时间字符串
     */
    private static String convert(String dateStr, TimeFormat targetFormat) {
        // 首先确定输入格式
        TimeFormat sourceFormat = getFormat(dateStr);
        if (sourceFormat == null) {
            throw new IllegalArgumentException("无效的输入格式: " + dateStr);
        }

        // 检查是否是向下转换
        if (isDownwardConversion(sourceFormat, targetFormat)) {
            throw new IllegalArgumentException(
                    String.format("不支持向下转换: 从 %s 转换到 %s", sourceFormat, targetFormat));
        }

        // 如果源格式和目标格式相同，直接返回
        if (sourceFormat == targetFormat) {
            return dateStr;
        }

        // 转换逻辑
        LocalDate date = parseToLocalDate(dateStr, sourceFormat);
        return formatDate(date, targetFormat);
    }

    /**
     * 根据给定的时间字符串，获取其对应的时间格式。
     *
     * @param dateStr 需要判断格式的时间字符串
     * @return 返回对应的时间格式枚举值，如果无法匹配任何格式则返回null
     */
    private static TimeFormat getFormat(String dateStr) {
        if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) return TimeFormat.DATE;
        if (dateStr.matches("\\d{4}" + WEEK_PATTERN + "\\d{2}")) return TimeFormat.WEEK;
        if (dateStr.matches("\\d{4}-\\d{2}")) return TimeFormat.MONTH;
        if (dateStr.matches("\\d{4}" + QUARTER_PATTERN + "[1-4]")) return TimeFormat.QUARTER;
        if (dateStr.matches("\\d{4}")) return TimeFormat.YEAR;
        return null;
    }

    private static boolean isDownwardConversion(TimeFormat source, TimeFormat target) {
        return source.ordinal() > target.ordinal();
    }

    /**
     * 将给定的日期字符串解析为LocalDate对象，根据指定的时间格式进行解析。
     *
     * @param dateStr 需要解析的日期字符串
     * @param format  指定的时间格式
     * @return 解析后的LocalDate对象
     * @throws IllegalArgumentException 如果提供的格式不被支持，将抛出此异常
     */
    private static LocalDate parseToLocalDate(String dateStr, TimeFormat format) {
        switch (format) {
            case DATE:
                return LocalDate.parse(dateStr);
            case WEEK:
                return parseWeek(dateStr);
            case MONTH:
                return LocalDate.parse(dateStr + "-01");
            case QUARTER:
                int year = Integer.parseInt(dateStr.substring(0, 4));
                int quarter = Integer.parseInt(dateStr.substring(5));
                return LocalDate.of(year, (quarter - 1) * 3 + 1, 1);
            case YEAR:
                return LocalDate.of(Integer.parseInt(dateStr), 1, 1);
            default:
                throw new IllegalArgumentException("不支持的格式: " + format);
        }
    }

    /**
     * 根据指定的时间格式，将给定的LocalDate对象格式化为字符串。
     *
     * @param date   需要格式化的日期
     * @param format 指定的时间格式
     * @return 格式化后的日期字符串
     * @throws IllegalArgumentException 如果提供的格式不被支持，将抛出此异常
     */
    private static String formatDate(LocalDate date, TimeFormat format) {
        switch (format) {
            case YEAR:
                // 返回年份的字符串表示。
                return String.valueOf(date.getYear());
            case QUARTER:
                //返回年份和季度的字符串表示，格式为"年份Q季度"。
                int quarter = date.get(IsoFields.QUARTER_OF_YEAR);
                return date.getYear() + QUARTER_PATTERN + quarter;
            case MONTH:
                // 返回年份和月份的字符串表示，格式为"yyyy-MM"。
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case WEEK:
                // 返回ISO周年份和周数的字符串表示，格式为"年份W周数"。
                WeekFields weekFields = WeekFields.ISO;
                int weekNumber = date.get(weekFields.weekOfWeekBasedYear());
                int weekBasedYear = date.get(weekFields.weekBasedYear());
                return String.format("%d" + WEEK_PATTERN + "%02d", weekBasedYear, weekNumber);
            case DATE:
                // 返回ISO格式的日期字符串，格式为"yyyy-MM-dd"。
                return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
            default:
                // 如果提供的格式不被支持，则抛出IllegalArgumentException异常。
                throw new IllegalArgumentException("不支持的格式: " + format);
        }
    }


    /**
     * 根据给定的周字符串解析出对应的日期。
     *
     * <p>输入的周字符串应为"YYYYWww"格式，其中"YYYY"表示年份，"W"是字面字符，"ww"表示周数。</p>
     *
     * <p>函数首先提取字符串中的年份和周数，然后根据ISO周日期系统计算出该周的第一天，并返回该日期。</p>
     *
     * @param weekStr 输入的周字符串，格式为"YYYYWww"
     * @return 解析出的日期，表示输入周字符串对应周的第一天
     * @throws NumberFormatException 如果输入的周字符串格式不正确，无法解析为整数
     */

    private static LocalDate parseWeek(String weekStr) {
        int year = Integer.parseInt(weekStr.substring(0, 4));
        int week = Integer.parseInt(weekStr.substring(5));
        return LocalDate.of(year, 1, 1)
                .with(WeekFields.ISO.weekBasedYear(), year)
                .with(WeekFields.ISO.weekOfWeekBasedYear(), week);
    }

    /**
     * 按时间字段对数据进行排序
     * 确保数据按时间顺序处理，便于累计计算和环比计算
     *
     * @param sqlData      待排序的数据列表
     * @param timeKeyAlias 时间字段别名
     */
    private static void sortByTime(List<Map<String, Object>> sqlData, String timeKeyAlias) {
        sqlData.sort((a, b) -> {
            String timeA = String.valueOf(a.get(timeKeyAlias));
            String timeB = String.valueOf(b.get(timeKeyAlias));
            return timeA.compareTo(timeB);
        });
    }

    /**
     * 根据计算类型计算比较结果
     * 支持三种计算方式：
     * 1. difference: 当前值 - 基准值
     * 2. specificPercent: (当前值 / 基准值) * 100%
     * 3. differencePercent: ((当前值 - 基准值) / 基准值) * 100%
     *
     * @param currentValue    当前值
     * @param baseValue       基准值（去年同期或上期）
     * @param computingMethod 计算类型
     * @return 计算结果，保留2位小数
     * @throws IllegalArgumentException 当计算类型不支持时抛出异常
     */
    private static BigDecimal calculateComparisonResult(BigDecimal currentValue, BigDecimal baseValue,
                                                        String computingMethod) {
        ComputingMethod type = ComputingMethod.valueOf(computingMethod);
        switch (type) {
            case difference:
                return currentValue.subtract(baseValue).setScale(2, RoundingMode.HALF_UP);
            case specificPercent:
                return currentValue.divide(baseValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"))
                        .setScale(2, RoundingMode.HALF_UP);
            case differencePercent:
                return currentValue.subtract(baseValue)
                        .divide(baseValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"))
                        .setScale(2, RoundingMode.HALF_UP);
            default:
                throw new IllegalArgumentException("不支持的计算类型: " + computingMethod);
        }
    }
}
