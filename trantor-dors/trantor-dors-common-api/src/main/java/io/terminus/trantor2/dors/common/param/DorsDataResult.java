package io.terminus.trantor2.dors.common.param;

import io.terminus.trantor2.dors.common.dto.DorsColumn;
import io.terminus.trantor2.dors.common.dto.DorsRow;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/19
 */
@Data
@Builder
public class DorsDataResult implements Serializable {

    /**
     * columns
     */
    private List<DorsColumn> columns;

    /**
     * rows
     */
    private List<List<DorsRow>> rows;

    /**
     * sql
     */
    private String sql;

    /**
     * total
     */
    private Long total;


    /**
     * msg
     */
    private String msg;

}
