package io.terminus.trantor2.dors.common.enums;

import cn.hutool.core.text.CharSequenceUtil;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import lombok.Getter;

import java.util.Arrays;

/**
 * 报表字段聚合函数枚举
 *
 * <AUTHOR> qianjin
 * @since : 2024/8/21
 */
@Getter
public enum DorsColumnAggregateEnum {

    /**
     * 求和
     */
    sum,

    /**
     * 计数
     */
    count,

    /**
     * 平均值
     */
    avg,

    /**
     * 最大值
     */
    max,

    /**
     * 最小值
     */
    min;

    public static void checkSupport(String param) {
        // 判断是否支持参数 转换成小写比较
        if (CharSequenceUtil.isNotBlank(param)
                && Arrays.stream(DorsColumnAggregateEnum.values()).noneMatch(value -> value.name().equals(param.toLowerCase()))) {
            throw new TrantorRuntimeException("不支持的聚合函数: " + param);
        }
    }
}
