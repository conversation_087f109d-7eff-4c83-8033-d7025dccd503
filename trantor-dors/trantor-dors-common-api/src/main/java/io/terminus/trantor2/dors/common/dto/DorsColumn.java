package io.terminus.trantor2.dors.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.dors.common.enums.DorsColumnAggregateEnum;
import io.terminus.trantor2.dors.common.enums.DorsColumnTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 报表字段
 *
 * <AUTHOR> qianjin
 * @since : 2024/8/20
 */
@NoArgsConstructor
@Data
public class DorsColumn {
    /**
     * 字段名称
     */
    private String name;

    /**
     * 字段key  唯一标识
     */
    private String key;

    /**
     * 指定as的字段名
     */
    private String alias;

    /**
     * 是否为计算字段 (计算字段需要)
     */
    @JsonIgnore
    private Boolean calculate;

    /**
     * type  'dimension'：维度 | 'measure'：度量
     * <p>
     * dimension: 维度，用于分类和分组数据。例如，日期、产品名称、地区等。维度通常是离散的，可以用来切片和筛选数据。
     * measure: 度量，用于表示数值型数据，可以进行聚合计算。例如，销售额、利润、数量等。度量通常是连续的，可以进行求和、平均等操作。
     *
     * @see DorsColumnTypeEnum
     */
    private String type;

    /**
     * 是否去重
     */
    private Boolean distinct;

    /**
     * 聚合函数  ‘sum’ | ‘count’ | ‘avg’ | ‘max’ | ‘min’
     *
     * @see DorsColumnAggregateEnum
     */
    private String aggregate;

    /**
     * 计算函数   例如：季度、月份、年份等时间换算
     */
    private List<String> function;

    /**
     * 高级计算
     * 字段计算函数计算完之后才进行的计算，比如季度环比，需要先计算季度，再进行环比计算
     */
    private DorsColumnComputing computing;

    /**
     * 动态定义
     */
    private String define;

    /**
     * 排序类型
     */
    private DorsColumnOrder order;


    public String getUniqueColumnKey() {
        return this.key + "_" + this.type;
    }


}
