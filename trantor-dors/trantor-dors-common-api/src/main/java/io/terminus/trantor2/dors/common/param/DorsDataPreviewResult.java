package io.terminus.trantor2.dors.common.param;

import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/19
 */
@Data
@Builder
public class DorsDataPreviewResult implements Serializable {

    /**
     * 预览数据
     */
    private List<Map<String, Object>> rows;

    /**
     * 数据模型
     */
    private DataStructNode model;


    private  String sql;
    /**
     * total
     */
    private Long total;

}
