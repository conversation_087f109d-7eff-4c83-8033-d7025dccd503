package io.terminus.trantor2.dors.common.service;


import io.terminus.trantor2.dors.common.param.DorsDataPreviewParam;
import io.terminus.trantor2.dors.common.param.DorsDataPreviewResult;
import io.terminus.trantor2.dors.common.param.DorsDataResult;
import io.terminus.trantor2.dors.common.param.DorsModuleExtCondition;
import io.terminus.trantor2.dors.common.param.DorsQueryEvaluateFieldParam;
import io.terminus.trantor2.dors.common.param.DorsQueryEvaluateFieldResult;
import io.terminus.trantor2.dors.common.param.DorsQueryParam;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/19
 */

public interface DorsQueryService {

    DorsDataResult query(DorsQueryParam param, String teamCode);

    DorsQueryEvaluateFieldResult query(DorsQueryEvaluateFieldParam param, String teamCode);

    DorsDataPreviewResult queryPreviewData(DorsDataPreviewParam param, String teamCode);

    List<DorsModuleExtCondition> queryExtCondition(HttpServletRequest request, String moduleKey);
}
