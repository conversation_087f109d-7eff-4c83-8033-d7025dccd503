package io.terminus.trantor2.dors.common.dto;

import io.terminus.trantor2.dors.common.enums.DorsColumnOrderEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/22
 */
@Data
@NoArgsConstructor
public class DorsColumnOrder {

    /**
     * 排序类型  desc | asc | custom  ,   custom排序包含多个排序字段
     *
     * @see DorsColumnOrderEnum
     */
    private String type;

    /**
     * 排序数据
     */
    private List<String> items;
}
