package io.terminus.trantor2.dors.common.query;

import io.terminus.trantor2.dors.common.param.DorsQueryParam;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticValueSubstitution;
import lombok.Data;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.ResultQuery;
import org.jooq.SQLDialect;
import org.jooq.conf.ParamType;
import org.jooq.impl.DSL;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/6
 */
@Data
public class QueryContext {

    private DorsQueryParam dorsQueryParam;                                                       // 查询参数
    private DataStructNode modelMeta;                                                            // 数据模型
    private DSLContext dslContext;                                                               // jooq的上下文
    private ResultQuery<Record> queryStep;                                                       // sql构造的中间结果
    private Map<String, String> columnAliasMap = new HashMap<>();                                // 字段key和别名的映射 [columKey1:c_1], [columKey2:c_2],,
    private List<Map<String, Object>> sqlData = new ArrayList<>();                               // sql查询结果
    private Long total = -1L;                                                                    // 分页查询时的数据总数
    private Map<String, List<StatisticValueSubstitution>> valueSubstitutionMap = new HashMap<>();// 字段值替换配置
    private Boolean groupByFlag = false;                                                         // 是否是分组查询


    public QueryContext(DorsQueryParam param, DataStructNode modelMeta) {
        this.dorsQueryParam = param;
        this.modelMeta = modelMeta;
        this.dslContext = DSL.using(SQLDialect.MYSQL_5_7);
    }

    public QueryContext setSQLDialect(SQLDialect sqlDialect) {
        this.dslContext = DSL.using(sqlDialect);
        return this;
    }

    public String getSql() {
        return queryStep.getSQL(ParamType.INLINED);
    }

    public ResultQuery<Record> getQueryStep() {
        return queryStep == null ? dslContext.select() : queryStep;
    }
}
