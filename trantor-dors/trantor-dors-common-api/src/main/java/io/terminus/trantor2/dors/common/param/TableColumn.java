package io.terminus.trantor2.dors.common.param;

import io.terminus.trantor2.dors.common.util.TableFieldTypeUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldProperties;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
public class TableColumn {
    private String name;
    private String dataType;

    public DataStructFieldNode convert() {
        DataStructFieldNode field = new DataStructFieldNode();
        field.setAlias(name);
        field.setName(name);
        field.setKey(name);
        field.setProps(new DataStructFieldProperties());
        DataStructFieldProperties props = field.getProps();
        props.setFieldType(TableFieldTypeUtil.fieldType(dataType));
        props.setColumnName(name);
        return field;
    }
}
