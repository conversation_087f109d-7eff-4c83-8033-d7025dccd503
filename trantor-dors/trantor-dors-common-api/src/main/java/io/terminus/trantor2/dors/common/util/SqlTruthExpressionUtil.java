package io.terminus.trantor2.dors.common.util;

import cn.hutool.core.collection.CollectionUtil;
import io.terminus.trantor2.model.common.util.PlaceholderUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Function;
import net.sf.jsqlparser.expression.JdbcParameter;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.Between;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.AllColumns;
import net.sf.jsqlparser.statement.select.AllTableColumns;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SelectExpressionItem;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.select.SubSelect;

import java.util.stream.Collectors;

/**
 * 把sql的占位符转换为真值表达式
 *
 * <AUTHOR> qianjin
 * @since : 2025/2/14
 */
@Slf4j
public class SqlTruthExpressionUtil {

    /**
     * 替换 SQL 中的条件表达式，若包含占位符 `${xxx}` 则替换为 `dors = dors`。
     */
    public static String replace(String sql) {
        try {
            String tempSql = PlaceholderUtil.replaceDollarPlaceholders(sql, "?");
            // 解析 SQL 为 AST
            Select select = (Select) CCJSqlParserUtil.parse(tempSql);
            // 处理 SELECT 中的所有子查询和 WHERE 子句
            processSelectBody(select.getSelectBody());
            // 返回修改后的 SQL
            return select.toString();
        } catch (Exception e) {
            log.error("把sql的占位符转换为真值表达式，若包含占位符 `${xxx}` 则替换为 `dors = dors`。 解析失败：", e);
            return sql;  // 如果解析失败，直接返回原 SQL
        }
    }

    /**
     * 递归处理 SELECT 语句的子查询和 WHERE 子句。
     */
    private static void processSelectBody(SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;

            // 处理 SELECT 的子查询
            plainSelect.getSelectItems().forEach(selectItem -> {
                if (selectItem instanceof AllColumns || selectItem instanceof AllTableColumns) {
                    return;
                }
                Expression expression = ((SelectExpressionItem) selectItem).getExpression();

                if (expression instanceof SubSelect) {
                    processSelectBody(((SubSelect) (((SelectExpressionItem) selectItem).getExpression())).getSelectBody());
                }
            });


            // 处理 FROM 子句中的子查询
            if (plainSelect.getFromItem() instanceof SubSelect) {
                processSelectBody(((SubSelect) plainSelect.getFromItem()).getSelectBody());
            }

            //  处理 joins 子句
            if (plainSelect.getJoins() != null) {
                plainSelect.getJoins().forEach(join -> {
                    if (join.getRightItem() != null && join.getRightItem() instanceof SubSelect) {
                        processSelectBody(((SubSelect) join.getRightItem()).getSelectBody());
                    }

                    if (CollectionUtil.isNotEmpty(join.getOnExpressions())) {
                        join.setOnExpressions(join.getOnExpressions().stream()
                                .map(SqlTruthExpressionUtil::processExpression).collect(Collectors.toList()));
                    }
                });
            }

            // 处理 WHERE 子句
            Expression where = plainSelect.getWhere();
            if (where != null) {
                plainSelect.setWhere(processExpression(where));
            }

            // 处理having
            Expression having = plainSelect.getHaving();
            if (having != null) {
                plainSelect.setHaving(processExpression(having));
            }

        } else if (selectBody instanceof SetOperationList) {
            // 处理 UNION 和其他 SET 操作
            SetOperationList setOperationList = (SetOperationList) selectBody;
            for (SelectBody setOperationSelectBody : setOperationList.getSelects()) {
                processSelectBody(setOperationSelectBody);
            }
        }
    }


    private static boolean containsPlaceholder(Expression expression) {
        return (expression instanceof StringValue || expression instanceof Column || expression instanceof Function || expression instanceof JdbcParameter)
                && expression.toString().contains("?");
    }

    private static Expression processExpression(Expression expression) {
        if (expression instanceof SubSelect) {
            // 处理子查询
            processSelectBody(((SubSelect) expression).getSelectBody());
        } else if (expression instanceof BinaryExpression) {
            // 处理二元表达式
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            if (containsPlaceholder(binaryExpression.getLeftExpression()) || containsPlaceholder(binaryExpression.getRightExpression())) {
                return getEqualsTo();
            }
            binaryExpression.setLeftExpression(processExpression(binaryExpression.getLeftExpression()));
            binaryExpression.setRightExpression(processExpression(binaryExpression.getRightExpression()));
            return binaryExpression;
        } else if (expression instanceof Parenthesis) {
            // 处理括号表达式
            return new Parenthesis(processExpression(((Parenthesis) expression).getExpression()));
        } else if (expression instanceof ExistsExpression) {
            // 处理 EXISTS 子查询
            ExistsExpression existsExpression = (ExistsExpression) expression;
            existsExpression.setRightExpression(processExpression(existsExpression.getRightExpression()));
        } else if (expression instanceof InExpression) {
            // 处理 IN 子查询
            InExpression inExpression = (InExpression) expression;
            if (containsPlaceholder(inExpression.getLeftExpression()) || containsPlaceholder(inExpression.getRightExpression())) {
                return getEqualsTo();
            }
            inExpression.setLeftExpression(processExpression(inExpression.getLeftExpression()));
            inExpression.setRightExpression(processExpression(inExpression.getRightExpression()));
        } else if (expression instanceof Between) {
            // 处理Between 表达式
            Between between = (Between) expression;
            if (containsPlaceholder(between.getLeftExpression()) || containsPlaceholder(between.getBetweenExpressionStart()) || containsPlaceholder(between.getBetweenExpressionEnd())) {
                return getEqualsTo();
            }
            between.setLeftExpression(processExpression(between.getLeftExpression()));
            between.setBetweenExpressionStart(processExpression(between.getBetweenExpressionStart()));
            between.setBetweenExpressionEnd(processExpression(between.getBetweenExpressionEnd()));
        }
        return expression;
    }

    private static EqualsTo getEqualsTo() {
        return new EqualsTo(new StringValue("dors"), new StringValue("dors"));
    }

}
