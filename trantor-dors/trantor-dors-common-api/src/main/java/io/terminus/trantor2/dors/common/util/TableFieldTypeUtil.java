package io.terminus.trantor2.dors.common.util;

import io.terminus.trantor2.dors.common.enums.FieldDataType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;

import java.time.temporal.Temporal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
public class TableFieldTypeUtil {

    public static FieldDataType dataType(Object value) {
        if (value == null) {
            return FieldDataType.String;
        } else if (value instanceof Number) {
            return FieldDataType.Number;
        } else if (value instanceof CharSequence) {
            return FieldDataType.String;
        } else if (value instanceof Date || value instanceof Temporal) {
            return FieldDataType.DateTime;
        }

        return FieldDataType.String;
    }

    public static FieldType fieldType(String dataType) {
        switch (dataType) {
            case "String":
                return FieldType.TEXT;
            case "Number":
                return FieldType.NUMBER;
            case "DateTime":
                return FieldType.DATE;
            default:
                return FieldType.TEXT;
        }
    }
}
