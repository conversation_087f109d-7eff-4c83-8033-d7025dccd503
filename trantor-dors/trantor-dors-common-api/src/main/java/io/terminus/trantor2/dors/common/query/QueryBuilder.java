package io.terminus.trantor2.dors.common.query;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.dors.Condition;
import io.terminus.trantor2.dors.LogicalCondition;
import io.terminus.trantor2.dors.common.dto.DorsColumn;
import io.terminus.trantor2.dors.common.enums.DorsColumnAggregateEnum;
import io.terminus.trantor2.dors.common.enums.DorsColumnOrderEnum;
import io.terminus.trantor2.dors.common.enums.DorsColumnTypeEnum;
import io.terminus.trantor2.dors.common.param.DorsQueryParam;
import io.terminus.trantor2.dors.common.util.SqlTruthExpressionUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.statistic.ImportType;
import io.terminus.trantor2.model.management.meta.domain.statistic.StatisticConfig;
import io.terminus.trantor2.model.management.meta.domain.statistic.StatisticSQLParam;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticFieldConfig;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticValueSubstitution;
import org.jooq.CaseValueStep;
import org.jooq.CaseWhenStep;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.case_;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/5
 */
@Component
public class QueryBuilder {
    private static final String CONCAT_FUNCTION_TEMPLATE = "concat(year(%s),'Q', quarter(%s))";
    private static final String SELECT_COUNT = "count";
    public static final String LOGIC_OPERATOR = "logicOperator";
    public static final String CONDITIONS = "conditions";
    public static final String OPERATOR = "operator";
    public static final String LEFT_VALUE = "leftValue";
    public static final String RIGHT_VALUE = "rightValue";

    /**
     * 构建sql
     */
    public QueryContext buildSql(DorsQueryParam param, DataStructNode modelMeta) {
        QueryContext queryContext = new QueryContext(param, modelMeta);
        init(queryContext);
        buildSelect(queryContext);
        buildFrom(queryContext);
        buildWhere(queryContext);
        buildGroupBy(queryContext);
        buildOrderBy(queryContext);
        buildLimit(queryContext);
        return queryContext;
    }

    /**
     * 构建count sql
     */
    public QueryContext buildCountSql(DorsQueryParam param, DataStructNode modelMeta) {
        QueryContext queryContext = new QueryContext(param, modelMeta);
        SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
        selectStep.select(DSL.count().as(SELECT_COUNT));
        queryContext.setQueryStep(selectStep);
        buildFrom(queryContext);
        buildWhere(queryContext);
        buildGroupBy(queryContext);
        return queryContext;
    }

    /**
     * 判断是否分页
     */
    public boolean isPage(Integer page, Integer pageSize) {
        return page != null && page > 0 && pageSize != null && pageSize > 0;
    }

    public String replaceVariableAndWithQuotes(String str, String key, Object value) {
        if (CharSequenceUtil.isBlank(str) || CharSequenceUtil.isBlank(key) || isNullObject(value)) {
            return str;
        }

        if ((value instanceof List<?>)) {
            // 处理数组索引占位符：${key.get(index)}
            str = replaceArrayIndexPlaceholders(str, key, value);

            // 同时需要先替换掉带单引号，双引号的占位符
            str = str.replace(wrapDoubleQuotesTargetPlaceholder(key), wrapWithParentheses(String.join(",", parseListValue((List<?>) value))));
            str = str.replace(wrapQuotesTargetPlaceholder(key), wrapWithParentheses(String.join(",", parseListValue((List<?>) value))));
            return str.replace(wrapTargetPlaceholder(key), wrapWithParentheses(String.join(",", parseListValue((List<?>) value))));
        }
        // 需要先替换掉带单引号，双引号的占位符
        str = str.replace(wrapDoubleQuotesTargetPlaceholder(key), wrapWithQuotes(value.toString()));
        str = str.replace(wrapQuotesTargetPlaceholder(key), wrapWithQuotes(value.toString()));
        return str.replace(wrapTargetPlaceholder(key), wrapWithQuotes(value.toString()));
    }

    /**
     * 替换字符串中的数组索引占位符
     * 支持格式：${key.get(index)}、'${key.get(index)}'、"${key.get(index)}"
     *
     * @param str   待替换的字符串
     * @param key   占位符的key
     * @param value 对应的值（已确保是List类型）
     * @return 替换后的字符串
     * @throws TrantorRuntimeException 当索引超出边界时抛出异常
     */
    private String replaceArrayIndexPlaceholders(String str, String key, Object value) {
        if (CharSequenceUtil.isBlank(str) || CharSequenceUtil.isBlank(key)) {
            return str;
        }

        // 预检查：快速判断是否包含数组索引占位符模式
        String arrayIndexPattern = "${" + key + ".get(";
        if (!str.contains(arrayIndexPattern)) {
            return str;
        }

        // 构建正则表达式来匹配数组索引占位符（支持负数索引）
        // 匹配 ${key.get(数字)}, '${key.get(数字)}', "${key.get(数字)}"
        String basePattern = "\\$\\{" + key + "\\.get\\((-?\\d+)\\)\\}";
        String quotedPattern = "'\\$\\{" + key + "\\.get\\((-?\\d+)\\)\\}'";
        String doubleQuotedPattern = "\"\\$\\{" + key + "\\.get\\((-?\\d+)\\)\\}\"";

        // 处理双引号包围的占位符
        str = replaceArrayIndexPattern(str, doubleQuotedPattern, key, value);

        // 处理单引号包围的占位符
        str = replaceArrayIndexPattern(str, quotedPattern, key, value);

        // 处理无引号的占位符
        str = replaceArrayIndexPattern(str, basePattern, key, value);

        return str;
    }

    /**
     * 替换特定模式的数组索引占位符
     *
     * @param str     待替换的字符串
     * @param pattern 正则表达式模式
     * @param key     占位符的key
     * @param value   对应的值
     * @return 替换后的字符串
     */
    private String replaceArrayIndexPattern(String str, String pattern, String key, Object value) {
        java.util.regex.Pattern compiledPattern = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher matcher = compiledPattern.matcher(str);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String indexStr = matcher.group(1);
            int index = Integer.parseInt(indexStr);
            String replacement = getArrayValueAtIndex(key, value, index);
            matcher.appendReplacement(result, java.util.regex.Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 获取数组指定索引位置的值
     *
     * @param key   占位符的key，用于错误信息
     * @param value 数组值（已确保是List类型）
     * @param index 索引位置
     * @return 指定索引位置的值字符串
     * @throws TrantorRuntimeException 当索引超出边界或数组为空时抛出异常
     */
    private String getArrayValueAtIndex(String key, Object value, int index) {
        List<?> list = (List<?>) value;

        // 验证数组不为空
        if (CollectionUtil.isEmpty(list)) {
            throw new TrantorRuntimeException(String.format("占位符 ${%s.get(%d)} 对应的数组为空，无法获取索引 %d 的值", key, index, index));
        }

        // 验证索引边界
        if (index < 0) {
            throw new TrantorRuntimeException(String.format("占位符 ${%s.get(%d)} 的索引不能为负数", key, index));
        }

        if (index >= list.size()) {
            throw new TrantorRuntimeException(String.format("占位符 ${%s.get(%d)} 的索引超出数组边界。数组长度：%d，请求索引：%d",
                    key, index, list.size(), index));
        }

        Object arrayValue = list.get(index);

        // 验证数组元素不为null
        if (arrayValue == null) {
            throw new TrantorRuntimeException(String.format("占位符 ${%s.get(%d)} 对应的数组元素为null", key, index));
        }

        String result = arrayValue.toString();

        // 添加引号
        return wrapWithQuotes(result);
    }


    /**
     * 初始化
     */
    private void init(QueryContext queryContext) {
        initColumns(queryContext);
    }

    /**
     * 初始化字段
     */
    private void initColumns(QueryContext queryContext) {
        List<DorsColumn> columns = queryContext.getDorsQueryParam().getColumns();
        // 元数据字段映射
        Map<String, DataStructFieldNode> dataFieldMap = queryContext.getModelMeta().getChildren().stream()
                .collect(Collectors.toMap(DataStructFieldNode::getKey, b -> b));
        for (int i = 0; i < columns.size(); i++) {
            DorsColumn column = columns.get(i);
            if (Boolean.TRUE.equals(column.getCalculate())) {
                // 计算字段单独查询处理
                column.setName(wrapWithParentheses(column.getName()));
            } else if (CharSequenceUtil.isNotBlank(column.getDefine())) {
                // 判断是否有动态字段
                column.setName(wrapWithParentheses(column.getDefine()));
                column.setAlias("c_" + i);
            } else {
                // 维度字段处理
                DataStructFieldNode dataStructFieldNode = dataFieldMap.get(column.getKey());
                if (dataStructFieldNode == null) {
                    throw new TrantorRuntimeException("字段不存在,key：" + column.getKey());
                }

                // 初始化字段alias
                if (CharSequenceUtil.isBlank(column.getAlias())) {
                    column.setAlias("c_" + i);
                }

                // 初始化字段name
                ImportType importType = queryContext.getModelMeta().getProps().getStatisticConfig().getImportType();
                if (importType == null) {
                    throw new TrantorRuntimeException("统计模型ImportType配置为空");
                }
                column.setName(wrapWithBackticks(dataStructFieldNode.getKey()));

                // 初始化字段特殊配置
                StatisticFieldConfig statisticFieldConfig = dataStructFieldNode.getProps().getStatisticFieldConfig();
                if (Objects.nonNull(statisticFieldConfig)) {
                    // 初始化持久化的计算字段
                    if (Boolean.TRUE.equals(statisticFieldConfig.isComputeField())) {
                        column.setName(wrapWithParentheses(statisticFieldConfig.getComputeFieldExpression()));
                    }
                    // 初始化字段枚举值信息
                    List<StatisticValueSubstitution> valueSubstitutions = statisticFieldConfig.getValueSubstitutions();
                    if (CollectionUtil.isNotEmpty(valueSubstitutions)) {
                        queryContext.getValueSubstitutionMap().put(column.getAlias(), valueSubstitutions);
                    }
                }

            }
        }
        queryContext.getDorsQueryParam().setColumns(columns);
    }

    /**
     * 构建select
     */
    private void buildSelect(QueryContext queryContext) {
        SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
        List<DorsColumn> columns = queryContext.getDorsQueryParam().getColumns();
        for (DorsColumn column : columns) {
            selectStep = selectStep.select(handleField(column).as(column.getAlias()));
            queryContext.getColumnAliasMap().put(column.getUniqueColumnKey(), column.getAlias());
        }
        queryContext.setQueryStep(selectStep);
    }

    /**
     * 构建from
     */
    private void buildFrom(QueryContext queryContext) {
        // 如果统计表的类型是SQL，那么需要取值sql语句否则取值表名
        StatisticConfig statisticConfig = queryContext.getModelMeta().getProps().getStatisticConfig();
        String fromContext;
        if (Objects.nonNull(statisticConfig) && Objects.requireNonNull(statisticConfig.getImportType()) == ImportType.SQL) {
            String ddl = queryContext.getModelMeta().getProps().getDdl();
            List<StatisticSQLParam> statisticSQLParams = statisticConfig.getStatisticSQLParam();
            if (CollectionUtil.isNotEmpty(statisticSQLParams)) {
                // 运行时对SQL中替换参数 ${key1} -> 具体参数值2，${key2} -> 具体参数值2
                Map<String, Object> statisticSQLParam = statisticSQLParams.stream()
                        .filter(p -> CharSequenceUtil.isNotBlank(p.getKey()) && Objects.nonNull(p.getDefaultValue()))
                        .collect(Collectors.toMap(StatisticSQLParam::getKey, StatisticSQLParam::getDefaultValue));
                ddl = ddlReplaceNotEmptyValue(ddl, statisticSQLParam);
            }
            // 把没有替换的参数替换成真值表达式
            ddl = SqlTruthExpressionUtil.replace(ddl);
            fromContext = wrapWithParentheses(ddl);
        } else {
            fromContext = queryContext.getModelMeta().getProps().getTableName();
        }
        SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
        SelectJoinStep<Record> from = selectStep.from(table(fromContext).as("t_0"));
        queryContext.setQueryStep(from);
    }

    public String ddlReplaceNotEmptyValue(String ddl, Map<String, Object> statisticSQLParam) {
        if (CharSequenceUtil.isBlank(ddl) || CollectionUtil.isEmpty(statisticSQLParam)) {
            return ddl;
        }
        for (Map.Entry<String, Object> entry : statisticSQLParam.entrySet()) {
            // 如果是空字符串，不替换
            if (CharSequenceUtil.isBlank(entry.getValue().toString())) {
                continue;
            }
            ddl = replaceVariableAndWithQuotes(ddl, entry.getKey(), entry.getValue());
        }
        return ddl;
    }

    /**
     * 构建where
     */
    private void buildWhere(QueryContext queryContext) {
        DorsQueryParam dorsQueryParam = queryContext.getDorsQueryParam();
        LogicalCondition filters = dorsQueryParam.getFilters();
        StatisticConfig statisticConfig = queryContext.getModelMeta().getProps().getStatisticConfig();
        if (Objects.nonNull(filters) || statisticConfig != null) {
            // 查询带了过滤条件
            SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
            if (Objects.nonNull(statisticConfig) && Objects.nonNull(statisticConfig.getFilters())) {
                LogicalCondition sysFilters = statisticConfig.getFilters();
                // 如果有系统过滤条件，那么需要合并
                filters = mergeCondition(filters, sysFilters);
            }
            if (Objects.isNull(filters)) {
                return;
            }
            // filters转换成map，方便把conditions中的object再转换成对应的condition类型
            Map<String, Object> map = BeanUtil.beanToMap(filters);
            // 转换
            LogicalCondition logicalCondition = (LogicalCondition) mapToCondition(map);
            // 解析
            String condition = removeOuterParentheses(parseCondition(logicalCondition));
            if (CharSequenceUtil.isNotBlank(condition)) {
                queryContext.setQueryStep(selectStep.where(condition));
            }
        }
    }

    public LogicalCondition mergeCondition(LogicalCondition original, LogicalCondition addCondition) {
        // 如果原始条件为空，直接返回新条件
        if (Objects.isNull(original)) {
            return addCondition;
        }
        // 如果新条件为空，直接返回原始条件
        if (Objects.isNull(addCondition)) {
            return original;
        }
        // 合并条件
        return new LogicalCondition("AND", Arrays.asList(original, addCondition));
    }

    /**
     * 构建分组
     */
    private void buildGroupBy(QueryContext queryContext) {
        DorsQueryParam dorsQueryParam = queryContext.getDorsQueryParam();
        List<DorsColumn> columns = dorsQueryParam.getColumns();
        int index = 0;
        for (DorsColumn column : columns) {
            if (DorsColumnTypeEnum.dimension.name().equals(column.getType())) {
                String alias = queryContext.getColumnAliasMap().get(column.getUniqueColumnKey());
                SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
                if (CharSequenceUtil.isBlank(alias)) {
                    // 如果没有别名，则是select count(*)的情况，需要把字段处理计算一次并添加在select中
                    alias = "c_" + index;
                    selectStep = selectStep.select(handleField(column).as(alias));
                    index++;
                }
                selectStep.groupBy(field(wrapWithBackticks(alias)));
                queryContext.setQueryStep(selectStep);
                if (Boolean.FALSE.equals(queryContext.getGroupByFlag())) {
                    // 标记分组查询, select count(*) 需要判断是否有分组
                    queryContext.setGroupByFlag(true);
                }
            }
        }
    }

    /**
     * 构建排序
     */
    private void buildOrderBy(QueryContext queryContext) {
        DorsQueryParam dorsQueryParam = queryContext.getDorsQueryParam();
        List<DorsColumn> columns = dorsQueryParam.getColumns();
        columns.stream().filter(c -> Objects.nonNull(c.getOrder())).forEach(column -> {
            String alias = queryContext.getColumnAliasMap().get(column.getUniqueColumnKey());
            SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
            String type = column.getOrder().getType();

            if (Objects.equals(type, DorsColumnOrderEnum.asc.name())) {
                // 升序
                selectStep.orderBy(field(wrapWithBackticks(alias)).asc());

            } else if (Objects.equals(type, DorsColumnOrderEnum.desc.name())) {
                // 降序
                selectStep.orderBy(field(wrapWithBackticks(alias)).desc());

            } else if (Objects.equals(type, DorsColumnOrderEnum.custom.name())) {
                // 自定义排序
                List<String> items = column.getOrder().getItems();
                if (CollectionUtil.isNotEmpty(items)) {
                    // case when
                    CaseValueStep<String> stringCaseValueStep = case_(alias);
                    CaseWhenStep<String, Integer> caseWhenStep = null;
                    for (int i = 0; i < items.size(); i++) {
                        String v = items.get(i);
                        if (CharSequenceUtil.isBlank(v)) {
                            continue;
                        }
                        if (caseWhenStep == null) {
                            caseWhenStep = stringCaseValueStep.when(v, i);
                        } else {
                            caseWhenStep = caseWhenStep.when(v, i);
                        }
                    }
                    // 最后需要else
                    if (caseWhenStep != null) {
                        caseWhenStep = (CaseWhenStep<String, Integer>) caseWhenStep.otherwise(items.size());
                        selectStep.orderBy(caseWhenStep);
                    }
                }

            } else {
                throw new TrantorRuntimeException("不支持的排序类型：" + type);
            }
            queryContext.setQueryStep(selectStep);
        });
    }

    /**
     * 构建limit
     */
    private void buildLimit(QueryContext queryContext) {
        DorsQueryParam dorsQueryParam = queryContext.getDorsQueryParam();
        Integer page = dorsQueryParam.getPage();
        Integer pageSize = dorsQueryParam.getPageSize();
        SelectSelectStep<Record> selectStep = (SelectSelectStep<Record>) queryContext.getQueryStep();
        if (isPage(page, pageSize)) {
            selectStep.limit((page - 1) * pageSize, pageSize.intValue());
        } else {
            if (pageSize != null && pageSize > 0) {
                selectStep.limit(pageSize);
            } else {
                selectStep.limit(1000);
            }
        }
        queryContext.setQueryStep(selectStep);
    }


    /**
     * 处理字段
     */
    private Field<Object> handleField(DorsColumn column) {
        String result = column.getName();
        // 处理计算函数或聚合函数（每次只存在一个）
        if (CollectionUtil.isNotEmpty(column.getFunction())) {
            result = handleFieldFunction(column, result);
        } else if (CharSequenceUtil.isNotBlank(column.getAggregate())) {
            result = handleFieldAggregate(column, result);
        }
        return field(result);
    }

    /**
     * 处理聚合函数
     */
    private String handleFieldAggregate(DorsColumn column, String name) {
        String aggregate = column.getAggregate();
        if (CharSequenceUtil.isNotBlank(aggregate)) {
            // 处理聚合函数
            DorsColumnAggregateEnum.checkSupport(aggregate);
            //是否去重
            if (Boolean.TRUE.equals(column.getDistinct())) {
                name = "distinct " + name;
            }
            return aggregate + wrapWithParentheses(name);
        }
        return name;
    }

    /**
     * 处理计算函数 (处理日期函数)
     */
    private String handleFieldFunction(DorsColumn column, String name) {
        List<String> function = column.getFunction();
        if (CollectionUtil.isNotEmpty(function)) {
            // 第一个是函数名，后面的是参数。需要拼接的形式是：func(字段名，参数))
            String functionName = function.get(0).toLowerCase();
            if ("quarter".equals(functionName)) {
                // 处理季度
                return String.format(CONCAT_FUNCTION_TEMPLATE, name, name);
            } else {
                StringBuilder builder = new StringBuilder(functionName + "(" + name);
                // 判断是否有参数
                if (function.size() > 1) {
                    for (int i = 1; i < function.size(); i++) {
                        builder.append(",").append("'").append(function.get(i)).append("'");
                    }
                }
                builder.append(")");
                return builder.toString();
            }
        }
        return name;
    }

    /**
     * 把Object条件值转换成对应的Condition对象
     *
     * @param map 条件值的Map表示，其中键可以是逻辑操作符(LOGIC_OPERATOR)、条件列表(CONDITIONS)、操作符(OPERATOR)、左值(LEFT_VALUE)和右值(RIGHT_VALUE)
     * @return 根据map内容生成的Condition或LogicalCondition对象
     */
    public Object mapToCondition(Map<String, Object> map) {
        // 检查是否为LogicalCondition
        if (map.containsKey(LOGIC_OPERATOR) && map.containsKey(CONDITIONS)) {
            String logicOperator = (String) map.get(LOGIC_OPERATOR);
            List<Object> conditions = (List<Object>) map.get(CONDITIONS);

            // 递归处理conditions
            List<Object> parsedConditions = new ArrayList<>();
            for (Object condition : conditions) {
                if (condition instanceof Map) {
                    parsedConditions.add(mapToCondition((Map<String, Object>) condition));
                } else if (condition instanceof LogicalCondition) {
                    parsedConditions.add(mapToCondition(BeanUtil.beanToMap(condition)));
                } else if (condition instanceof Condition) {
                    parsedConditions.add(condition);
                }
            }
            return new LogicalCondition(logicOperator, parsedConditions);
        }

        // 否则处理为Condition
        String operator = (String) map.get(OPERATOR);
        String leftValue = (String) map.get(LEFT_VALUE);
        Object rightValue = map.get(RIGHT_VALUE);

        return new Condition(operator, leftValue, rightValue);
    }

    /**
     * 解析过滤条件
     *
     * @param condition 条件对象，可以是简单的条件对象（Condition）或逻辑条件对象（LogicalCondition）
     * @return 解析后的SQL条件字符串，如果条件对象不是Condition或LogicalCondition类型，则返回空字符串
     */
    private String parseCondition(Object condition) {
        if (condition instanceof Condition) {
            return wrapWithParentheses(parseSimpleCondition((Condition) condition));
        } else if (condition instanceof LogicalCondition) {
            return wrapWithParentheses(parseLogicalCondition((LogicalCondition) condition));
        }
        return "";
    }

    public void parseReplaceCondition(Object condition, String key, Object value) {
        if (CharSequenceUtil.isBlank(key) || Objects.isNull(value) || Objects.isNull(condition)) {
            return;
        }
        if (condition instanceof Condition) {
            Condition cond = (Condition) condition;
            String targetKey = wrapTargetPlaceholder(key);
            // 匹配key,替换value
            if (targetKey.equals(cond.getRightValue().toString())) {
                cond.setRightValue(value);
            }
        } else if (condition instanceof LogicalCondition) {
            LogicalCondition logicalCond = (LogicalCondition) condition;
            for (Object condObj : logicalCond.getConditions()) {
                parseReplaceCondition(condObj, key, value); // 递归调用
            }
        }
    }


    /**
     * 解析逻辑条件
     *
     * @param logicalCondition 逻辑条件对象
     * @return 解析后的SQL逻辑条件字符串
     */
    private String parseLogicalCondition(LogicalCondition logicalCondition) {
        String logicOperator = logicalCondition.getLogicOperator().toUpperCase();
        List<Object> conditions = logicalCondition.getConditions();

        if (CollectionUtil.isEmpty(conditions)) {
            return "";
        }
        if (conditions.size() == 1) {
            return parseCondition(conditions.get(0));
        }
        return conditions.stream()
                .map(this::parseCondition)
                .filter(CharSequenceUtil::isNotBlank)
                .reduce((c1, c2) -> String.format("%s %s %s", c1, logicOperator, c2))
                .orElse("");
    }


    /**
     * 解析简单条件
     *
     * @param condition 待解析的条件对象
     * @return 解析后的SQL条件字符串
     * @throws TrantorRuntimeException 如果条件操作符不支持，则抛出此异常
     */
    private String parseSimpleCondition(Condition condition) {
        String leftValue = condition.getLeftValue();
        String operator = condition.getOperator();
        Object rightValue = condition.getRightValue();
        // 如果operator，leftValue 为空，直接返回
        if (CharSequenceUtil.isBlank(operator) || CharSequenceUtil.isBlank(leftValue)) {
            return "";
        }
        // 如果rightValue为空或者空数组，直接返回
        if (isNullObject(rightValue)) {
            return "";
        }

        String columnName = wrapWithBackticks(leftValue);

        switch (operator) {
            case "EQ":
                return columnName + " = " + wrapWithQuotes(rightValue.toString());
            case "NEQ":
                return columnName + " != " + wrapWithQuotes(rightValue.toString());
            case "GT":
                return columnName + " > " + wrapWithQuotes(rightValue.toString());
            case "GTE":
                return columnName + " >= " + wrapWithQuotes(rightValue.toString());
            case "LT":
                return columnName + " < " + wrapWithQuotes(rightValue.toString());
            case "LTE":
                return columnName + " <= " + wrapWithQuotes(rightValue.toString());
            case "IS_NOT_NULL":
                return columnName + " is not null";
            case "IS_NULL":
                return columnName + " is null";
            case "CONTAINS":
                return columnName + " like '%" + rightValue + "%'";
            case "NOT_CONTAINS":
                return columnName + " not like '%" + rightValue + "%'";
            case "IN":
                return columnName + " in " + handleConditionValue(operator, rightValue);
            case "NOT_IN":
                return columnName + " not in " + handleConditionValue(operator, rightValue);
            case "BETWEEN_AND":
                return columnName + " between " + handleConditionValue(operator, rightValue);
            case "PARAM":
                return handleDateEnumConditionValue(columnName, rightValue);
            default:
                throw new TrantorRuntimeException("不支持的条件操作符：" + operator);
        }
    }

    private boolean isNullObject(Object value) {
        return Objects.isNull(value) || CharSequenceUtil.isBlank(value.toString())
                // 列表的所有元素都是null（但不是空列表）
                || (value instanceof List && !CollectionUtil.isEmpty((List<?>) value) && ((List<?>) value).stream().allMatch(Objects::isNull));
    }

    /**
     * 处理复杂条件值
     *
     * @param operator 操作符，如"in"、"notin"、"between"等
     * @param value    操作符对应的值，预期为List类型
     * @return 根据操作符和值生成的SQL条件字符串
     * @throws TrantorRuntimeException 如果操作符值类型不是List或操作符不支持，则抛出此异常
     */
    private String handleConditionValue(String operator, Object value) {
        if (!(value instanceof List<?>)) {
            throw new TrantorRuntimeException("无效的操作符值类型，预期为列表");
        }

        List<String> valueList = parseListValue((List<?>) value);

        switch (operator) {
            case "IN":
            case "NOT_IN":
                return wrapWithParentheses(String.join(",", valueList));
            case "BETWEEN_AND":
                if (valueList.size() != 2) {
                    throw new TrantorRuntimeException("between操作符需要两个值");
                }
                return String.join(" and ", valueList);
            default:
                throw new TrantorRuntimeException("不支持的操作符：" + operator);
        }
    }

    private List<String> parseListValue(List<?> value) {
        return value.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(this::wrapWithQuotes)
                .collect(Collectors.toList());
    }


    /**
     * 处理日期枚举条件值，根据给定的列名和枚举值生成相应的日期范围条件字符串。
     *
     * @param columnName 列名
     * @param value      枚举值，表示不同的时间范围，如"Today"、"Yesterday"等
     * @return 根据枚举值生成的日期范围条件字符串，形如"columnName between '开始日期 00:00:00' and '结束日期 23:59:59'"
     * @throws TrantorRuntimeException 如果传入的枚举值不支持，则抛出此异常
     */
    private String handleDateEnumConditionValue(String columnName, Object value) {
        String valueString = value.toString();
        DateTime dateNow = DateUtil.date();
        String dateNowFormat = DatePattern.NORM_DATE_FORMAT.format(dateNow);
        switch (valueString) {
            case "Today":
                return createDateRangeCondition(columnName, dateNowFormat, dateNowFormat);
            case "Yesterday":
                DateTime yesterday = DateUtil.yesterday();
                return createDateRangeCondition(columnName, yesterday.toDateStr(), yesterday.toDateStr());
            case "Tomorrow":
                DateTime tomorrow = DateUtil.tomorrow();
                return createDateRangeCondition(columnName, tomorrow.toDateStr(), tomorrow.toDateStr());
            case "Nearlysevendays":
                return createDateRangeCondition(columnName, DateUtil.offsetDay(dateNow, -7).toDateStr(), dateNowFormat);
            case "Thisweek":
                return createDateRangeCondition(columnName, DateUtil.beginOfWeek(dateNow).toDateStr(), DateUtil.endOfWeek(dateNow).toDateStr());
            case "Lastweek":
                DateTime lastWeek = DateUtil.lastWeek();
                return createDateRangeCondition(columnName, DateUtil.beginOfWeek(lastWeek).toDateStr(), DateUtil.endOfWeek(lastWeek).toDateStr());
            case "Nextweek":
                DateTime nextWeek = DateUtil.nextWeek();
                return createDateRangeCondition(columnName, DateUtil.beginOfWeek(nextWeek).toDateStr(), DateUtil.endOfWeek(nextWeek).toDateStr());
            case "Thismonth":
                return createDateRangeCondition(columnName, DateUtil.beginOfMonth(dateNow).toDateStr(), DateUtil.endOfMonth(dateNow).toDateStr());
            case "Lastmonth":
                DateTime lastMonth = DateUtil.lastMonth();
                return createDateRangeCondition(columnName, DateUtil.beginOfMonth(lastMonth).toDateStr(), DateUtil.endOfMonth(lastMonth).toDateStr());
            case "Nextmonth":
                DateTime nextMonth = DateUtil.nextMonth();
                return createDateRangeCondition(columnName, DateUtil.beginOfMonth(nextMonth).toDateStr(), DateUtil.endOfMonth(nextMonth).toDateStr());
            case "Thisquarter":
                return createDateRangeCondition(columnName, DateUtil.beginOfQuarter(dateNow).toDateStr(), DateUtil.endOfQuarter(dateNow).toDateStr());
            case "Lastquarter":
                return createDateRangeCondition(columnName, DateUtil.beginOfQuarter(DateUtil.offsetMonth(dateNow, -3)).toDateStr(), DateUtil.endOfQuarter(DateUtil.offsetMonth(dateNow, -3)).toDateStr());
            case "Nextquarter":
                return createDateRangeCondition(columnName, DateUtil.beginOfQuarter(DateUtil.offsetMonth(dateNow, 3)).toDateStr(), DateUtil.endOfQuarter(DateUtil.offsetMonth(dateNow, 3)).toDateStr());
            case "Thisyear":
                return createDateRangeCondition(columnName, DateUtil.beginOfYear(dateNow).toDateStr(), DateUtil.endOfYear(dateNow).toDateStr());
            case "Lastyear":
                return createDateRangeCondition(columnName, DateUtil.beginOfYear(DateUtil.offset(dateNow, DateField.YEAR, -1)).toDateStr(), DateUtil.endOfYear(DateUtil.offset(dateNow, DateField.YEAR, -1)).toDateStr());
            case "Nextyear":
                return createDateRangeCondition(columnName, DateUtil.beginOfYear(DateUtil.offset(dateNow, DateField.YEAR, 1)).toDateStr(), DateUtil.endOfYear(DateUtil.offset(dateNow, DateField.YEAR, 1)).toDateStr());
            default:
                throw new TrantorRuntimeException("不支持的时间枚举值：" + valueString);
        }
    }

    /**
     * 根据列名、开始日期和结束日期创建日期范围条件字符串
     *
     * @param columnName 列名
     * @param startDate  开始日期，格式为yyyy-MM-dd
     * @param endDate    结束日期，格式为yyyy-MM-dd
     * @return 日期范围条件字符串，格式为：columnName between '开始日期 00:00:00' and '结束日期 23:59:59'
     */
    private String createDateRangeCondition(String columnName, String startDate, String endDate) {
        return String.format("%s between '%s 00:00:00' and '%s 23:59:59'", columnName, startDate, endDate);
    }

    /**
     * 给字符串参数加上括号
     *
     * @param param 需要加括号的字符串
     * @return 加上括号后的字符串，如果param为空或只包含空白字符，则返回空字符串
     */
    private String wrapWithParentheses(String param) {
        if (CharSequenceUtil.isBlank(param)) {
            return "";
        }
        return "(" + param + ")";
    }

    /**
     * 移除字符串参数最外层的括号
     *
     * @param param 待处理的字符串参数
     * @return 移除最外层括号后的字符串，如果param为空或没有最外层括号，则返回原字符串
     */
    private String removeOuterParentheses(String param) {
        if (CharSequenceUtil.isBlank(param)) {
            return null;
        }
        if (param.startsWith("(") && param.endsWith(")")) {
            return param.substring(1, param.length() - 1);
        }
        return param;
    }


    /**
     * 为字符串参数加上反引号
     *
     * @param param 需要加反引号的字符串
     * @return 加上反引号后的字符串，如果param为空或只包含空白字符，则返回空字符串
     */
    private String wrapWithBackticks(String param) {
        if (CharSequenceUtil.isBlank(param)) {
            return "";
        }
        return "`" + param + "`";
    }

    /**
     * 为字符串参数加上单引号
     *
     * @param param 需要加单引号的字符串
     * @return 加上单引号后的字符串，如果param为空或只包含空白字符，则返回空字符串
     */
    private String wrapWithQuotes(String param) {
        if (CharSequenceUtil.isBlank(param)) {
            return "";
        }
        // 处理单引号转义
        return "'" + param.replaceAll("'", "''") + "'";
    }

    private String wrapTargetPlaceholder(String key) {
        if (CharSequenceUtil.isBlank(key)) {
            return key;
        }
        return "${" + key + "}";
    }

    private String wrapQuotesTargetPlaceholder(String key) {
        if (CharSequenceUtil.isBlank(key)) {
            return key;
        }
        return "'${" + key + "}'";
    }

    private String wrapDoubleQuotesTargetPlaceholder(String key) {
        if (CharSequenceUtil.isBlank(key)) {
            return key;
        }
        return "\"${" + key + "}\"";
    }

}
