package io.terminus.trantor2.dors.common.param;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/12/24
 */
@Data
public class DorsModuleExtConditionHandleResult {

    List<String> moduleExtConditionKeys;

    List<String> ddlReplace;

    List<String> statisticConfigFilterReplace;

    List<String> dynamicConditionsReplace;

    List<String> dynamicConditionsKeys;


    @Override
    public String toString() {
        return "传入的动态替换key列表:" + dynamicConditionsKeys + "，ddl被替换动态条件key:" + dynamicConditionsReplace +
                "。获取外部扩展条件的Key列表:" + moduleExtConditionKeys + (CollectionUtil.isEmpty(ddlReplace) && CollectionUtil.isEmpty(statisticConfigFilterReplace)
                ? "，无外部扩展条件处理。"
                : "，DDL被替换的变量:" + ddlReplace + ", 统计模型全局过滤条件被替换的变量:" + statisticConfigFilterReplace + "。");
    }
}
