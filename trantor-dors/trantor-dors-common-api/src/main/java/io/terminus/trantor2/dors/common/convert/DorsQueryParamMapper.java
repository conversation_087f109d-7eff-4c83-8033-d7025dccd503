package io.terminus.trantor2.dors.common.convert;

import io.terminus.trantor2.dors.common.param.DorsQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/14
 */
@Mapper(mappingControl = DeepClone.class)
public interface DorsQueryParamMapper {
    DorsQueryParamMapper INSTANCE = Mappers.getMapper(DorsQueryParamMapper.class);

    DorsQueryParam deepCopy(DorsQueryParam param);

}
