package io.terminus.trantor2.dors.common.dto;

import io.terminus.trantor2.dors.common.util.TimeSeriesCalculator;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 高级计算：同比、环比、累计
 *
 * <AUTHOR> qianjin
 * @since : 2024/10/04
 */
@Data
@NoArgsConstructor
public class DorsColumnComputing {

    /**
     * 计算类型
     *
     * @see TimeSeriesCalculator.ComputingType
     */
    private String type;

    /**
     * 时间维度
     *
     * @see TimeSeriesCalculator.TimeFormat
     */
    private String timeDimension;

    /**
     * 同比、环比的计算方式
     *
     * @see TimeSeriesCalculator.ComputingMethod
     */
    private String computingMethod;
}
