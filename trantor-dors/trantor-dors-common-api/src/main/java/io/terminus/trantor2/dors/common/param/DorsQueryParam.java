package io.terminus.trantor2.dors.common.param;

import io.terminus.trantor2.dors.LogicalCondition;
import io.terminus.trantor2.dors.common.dto.DorsColumn;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/20
 */
@NoArgsConstructor
@Data
public class DorsQueryParam implements Serializable {
    /**
     * 数据源key
     */
    @NotBlank(message = "数据源不能为空")
    private String source;
    /**
     * 字段配置
     */
    @NotEmpty(message = "字段不能为空")
    private List<DorsColumn> columns;
    /**
     * 过滤配置
     */
    private LogicalCondition filters;

    /**
     * 页序，如果不传则是第一页
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer pageSize;


    /**
     * 模块key portal侧使用
     */
    private String moduleKey;


    /**
     * 动态条件
     */
    private List<DorsDynamicCondition> dynamicConditions;


    /**
     * 模块扩展条件 (内部逻辑传参使用)
     */
    private List<DorsModuleExtCondition> moduleExtConditions;
}
