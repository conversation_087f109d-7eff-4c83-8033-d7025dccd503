package io.terminus.trantor2.dors.common.param;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR> qianjin
 * @since : 2024/9/5
 */
@NoArgsConstructor
@Data
public class DorsQueryEvaluateFieldParam implements Serializable {
    @NotBlank(message = "数据源不能为空")
    private String source;
    @NotBlank(message = "sql不能为空")
    private String sql;
    @NotBlank(message = "字段名不能为空")
    private String name;
    @NotBlank(message = "字段类型不能为空")
    private String type;
    private String desc;
    private Integer page;
    private Integer pageSize;

}
